import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import { GoogleAPIResponse } from "../../types/responses/customResponse";
import { verifyDomainExistsService, Messages } from "./verifyDomainExistsService";
import { getGoogleCustomerByIdService } from "./getGoogleCustomerByIdService";

// Helper function to normalize string comparison (matching .NET ComparableString extension)
function comparableString(str?: string): string {
  return (str || "").trim().toUpperCase();
}

/**
 * Verify and Get Google Customer By Domain
 * Matches .NET VerifyAndGetGoogleCustomerByDomain service method
 */
export async function verifyAndGetGoogleCustomerByDomainService(
  domain: string,
  storeId: string,
  isExistingCustomer: boolean,
  channelPartnerId?: string,
  req?: MedusaRequest
): Promise<GoogleAPIResponse> {
  logger.info(`Entered into VerifyAndGetGoogleCustomerByDomain service method with domain: ${domain}, storeId: ${storeId}, isExistingCustomer: ${isExistingCustomer}, channelPartnerId: ${channelPartnerId}`);

  let googleAPIResponse: GoogleAPIResponse = {
    Message: "Success",
    StatusCode: 200,
    IsError: false
  };

  try {
    // Call VerifyDomainExists service (matching .NET: await VerifyDomainExists(domain, storeId, isExistingCustomer))
    logger.info(`Going to call VerifyDomainExists with domain: ${domain}, storeId: ${storeId}, isExistingCustomer: ${isExistingCustomer}`);
    const verifyDomainResponse = await verifyDomainExistsService(domain, storeId, isExistingCustomer, req);
    logger.info(`VerifyDomainExists response: ${JSON.stringify(verifyDomainResponse)}`);

    if (verifyDomainResponse?.IsLinkable === true) {
      // Extract cloud identity account info (matching .NET logic)
      const cloudIdentityAccountInfo = verifyDomainResponse.Data?.cloudIdentityAccounts?.[0];
      logger.info(`Fetched cloudIdentityAccountInfo from verifyDomainResponse ${JSON.stringify(cloudIdentityAccountInfo)}`);

      logger.info(`Entered in section where domain exists and owned.`);
      
      // Extract customer ID from CustomerName (matching .NET: cloudIdentityAccountInfo?.CustomerName?.Split('/')?.Last())
      const customerID = cloudIdentityAccountInfo?.CustomerName?.split('/').pop();
      logger.info(`Fetched Google customer id ${customerID}.`);

      if (customerID) {
        logger.info(`Going to call GetCustomerById using storeid ${storeId}, customerID ${customerID}.`);
        
        // Call GetGoogleCustomerById service (matching .NET: await GetGoogleCustomerById(storeId, customerID))
        const customerDetail = await getGoogleCustomerByIdService(storeId, customerID, req);
        logger.info(`GetGoogleCustomerById response: ${JSON.stringify(customerDetail)}`);

        // Extract details for comparison (matching .NET logic)
        const channelPartnerIDFromResponse = customerDetail?.Data?.channelPartnerId;
        const googleCustomerId = customerDetail?.Data?.googleCustomerId;
        const domainFromResponse = customerDetail?.Data?.domain;
        const name = customerDetail?.Data?.name;
        
        logger.info(`Fetched details for comparison channelPartnerIDFromResponse ${channelPartnerIDFromResponse}, googleCustomerId ${googleCustomerId}, domainFromResponse ${domainFromResponse}, name ${name}.`);

        // As per CQ-7743, Match the following fields (matching .NET comparison logic)
        // 1. Channel partner ID with channel partner ID who initiated linking request,
        // 2. Redington ID,
        // 3. Domain,
        // 4. Customer cloud ID
        const channelPartnerMatches = comparableString(channelPartnerIDFromResponse) === comparableString(channelPartnerId);
        const customerIdMatches = comparableString(customerID) === comparableString(googleCustomerId);
        const domainMatches = comparableString(domainFromResponse) === comparableString(domain);
        const nameMatches = comparableString(name) === comparableString(cloudIdentityAccountInfo?.CustomerName);

        logger.info(`Comparison results - channelPartnerMatches: ${channelPartnerMatches}, customerIdMatches: ${customerIdMatches}, domainMatches: ${domainMatches}, nameMatches: ${nameMatches}`);

        if (customerDetail.IsError || (channelPartnerMatches && customerIdMatches && domainMatches && nameMatches)) {
          logger.info(`All validations passed or customer detail has error, returning customer detail response`);
          googleAPIResponse = customerDetail;
        } else {
          logger.info(`Validation failed, customer existing details do not match`);
          googleAPIResponse = {
            IsError: true,
            Message: Messages.CustomerExistingNotMatched(domain),
            StatusCode: 422 // UnprocessableEntity
          };
        }
      } else {
        logger.error(`Failed to extract customer ID from CustomerName: ${cloudIdentityAccountInfo?.CustomerName}`);
        googleAPIResponse = {
          IsError: true,
          Message: "Failed to extract customer ID from domain verification response",
          StatusCode: 422 // UnprocessableEntity
        };
      }
    } else {
      logger.info(`Domain is not linkable, returning verify domain response`);
      googleAPIResponse = {
        IsError: true,
        Message: verifyDomainResponse.Message,
        StatusCode: verifyDomainResponse.StatusCode
      };
    }

  } catch (error: any) {
    logger.error(`Error in VerifyAndGetGoogleCustomerByDomain service: ${error.message}`);
    
    googleAPIResponse = {
      Message: Messages.ExceptionMessage(error.message),
      StatusCode: 500,
      IsError: true
    };
  }

  logger.info(`Going to return VerifyAndGetGoogleCustomerByDomain API response- ${JSON.stringify(googleAPIResponse)}`);
  return googleAPIResponse;
}
