import {
  GoogleDetails,
} from "../../../config/microsoftEndpoints";
import logger from "../../../utils/logger";
import sql from "mssql";
import { getSqlServerConnection } from "../../../utils/sqlServerClient";
import { PostMethodFormEncoded } from "../../externalService/externalEndPointService";

interface TokenRequestHeaders {
  granttype: string;
  Clientid: string;
  Clientsecret: string;
  Resource: string;
  Token: string;
  RedirectUri: string;
  StoreDomain: string;
  Brand: string;
  Username: string;
  uPassword: string;
}

export async function getGoogleTokenService(
  req: any,
  storeId: string
): Promise<any> {
  try {
    logger.info(
      `Entered into get Google Token Service with storeId=${storeId}`
    );

    const brandName = GoogleDetails.BrandNameInDB;

    const brandId = await GetBrandIdByName(brandName);
    console.log("Resolved BrandId:", brandId);

    const provisionType = GoogleDetails.ProvisionTypeInDB;

    const storeDetails = await getTokenDetailsByStoreAndBrand(
      storeId,
      brandId,
      provisionType
    );
    console.log("Store token info:", storeDetails);

    logger.info(`Prepare Google Token headers`);
    const headers = getGoogleTokenApiHeaders(storeDetails);

    logger.info(
      `Going to hit Google token endpoint with headers: ${JSON.stringify(
        headers
      )}`
    );

    const configModule = req.scope.resolve("configModule");

    const g_url = `https://accounts.google.com/o/oauth2/token`;

    const response = await PostMethodFormEncoded(
      {
        url: g_url,
        data: headers,
        isVendorHit: true,
        module: "GetGoogleToken",
      },
      configModule
    );

    if (!response.isSuccessStatusCode) {
      throw new Error(
        `Google token generation failed with status ${response.httpStatusCode}`
      );
    }

    const parsed = JSON.parse(response.content);
    const token = parsed.access_token;
    logger.info(`Google token generated successfully`);
    return token;
  } catch (error) {
    logger.error(`Google Token Error :`);
    throw error;
  }
}

export async function GetBrandIdByName(
  storeId: string,
  credentialType: string | null = null
): Promise<any> {
  logger.info(
    `Entered into get Google TokenService with storeId=${storeId}, credentialType=${credentialType}`
  );
  const brandName = GoogleDetails.BrandNameInDB;

  const pool = await getSqlServerConnection();
  const request = pool.request();

  request.input("iBrandName", sql.VarChar(100), brandName);
  logger.info(
    `Calling stored procedure spGetBrandIdByName with iBrandName=${brandName}`
  );
  const result = await request.execute("spGetBrandIdByName");
  console.log("result---------->", result);

  logger.info(
    `Stored procedure executed successfully. Raw result: ${JSON.stringify(
      result
    )}`
  );
  const brandId = result?.recordset?.[0]?.BrandId || result?.recordset?.[0];
  console.log("brandId-------->", brandId);

  if (!brandId) {
    throw new Error(
      `No BrandId returned from spGetBrandIdByName for brand=${brandName}`
    );
  }

  logger.info(`BrandId retrieved: ${brandId}`);
  return brandId;
}

export async function getTokenDetailsByStoreAndBrand(
  storeId: string,
  brandId: string,
  provisionType: string | null = null
): Promise<TokenRequestHeaders> {
  logger.info(
    `Entered into getStoreTokenDetails with storeId=${storeId}, brandId=${brandId}, provisionType=${provisionType}`
  );
  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();

    request.input("iStoreId", sql.VarChar(50), storeId);
    request.input("iBrandId", sql.VarChar(50), brandId);
    request.input("iProvisionType", sql.VarChar(50), provisionType);
    request.output("oRetVal", sql.VarChar(100));

    const result = await request.execute("Usp_GetStoreDetails");
    const response = result.recordset?.[0];
    if (!response) {
      throw new Error(
        `No store token details found for storeId=${storeId}, brandId=${brandId}`
      );
    }
    logger.info(`Store procedure response: ${JSON.stringify(response)}`);
    return response as TokenRequestHeaders;
  } catch (error: any) {
    logger.error(
      `Error in Get Token Details By Store And Brand: ${error.message}`
    );
    throw error;
  }
}

export function getGoogleTokenApiHeaders(
  storeDetails: TokenRequestHeaders
): Record<string, string> {
  logger.info(
    `Entered into Get Google Token Api Headers with Store Details: ${JSON.stringify(
      storeDetails
    )}`
  );

  return {
    client_id: storeDetails.Clientid,
    client_secret: storeDetails.Clientsecret,
    grant_type: storeDetails.granttype,
    refresh_token: storeDetails.Token,
  };
}
