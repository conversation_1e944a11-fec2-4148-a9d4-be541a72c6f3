openapi: 3.0.0
info:
  title: Medusa Auth API
  version: 1.0.0
  description: >-
    Authentication API for Medusa partners to securely manage login, tokens, and
    session validation.
servers:
  - url: http://localhost:8081
    description: Local development server
paths:
  /v1/auth/country/phone-codes:
    get:
      summary: Get a list of supported countries  phone codes
      tags:
        - Countries
      parameters:
        - in: query
          name: region_id
          schema:
            type: string
          required: false
          description: Optional region ID to filter countries by a specific region
        - in: query
          name: page
          schema:
            type: integer
            example: 1
          required: false
          description: Optional page number for pagination (default is 1)
        - in: query
          name: page_size
          schema:
            type: integer
            example: 5
          required: false
          description: Optional number of results per page (default is 10)
      responses:
        '200':
          description: List of countries with phone codes fetched successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        iso_2:
                          type: string
                          example: ad
                        country_code:
                          type: string
                          example: '+91'
                        country_flag:
                          type: string
                          example: test
                  pagination:
                    type: object
                    properties:
                      page:
                        type: integer
                        example: 1
                      pageSize:
                        type: integer
                        example: 5
                      totalItems:
                        type: integer
                        example: 250
                      totalPages:
                        type: integer
                        example: 50
                      hasNextPage:
                        type: boolean
                        example: true
                      hasPrevPage:
                        type: boolean
                        example: false
                  message:
                    type: string
                    example: Phone code list fetched successfully.
        '404':
          description: No countries found
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items: {}
                  message:
                    type: string
                    example: Country not found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Internal server error
                  error:
                    type: string
                    example: Internal server error. Please try again later.
  /v1/auth/country:
    get:
      summary: Get a list of supported countries with region names
      tags:
        - Countries
      parameters:
        - in: query
          name: region_id
          schema:
            type: string
          required: false
          description: Optional region ID to filter countries by a specific region
      responses:
        '200':
          description: List of countries fetched successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        iso_2:
                          type: string
                          example: ae
                        name:
                          type: string
                          example: United Arab Emirates (Middle East)
                        display_name:
                          type: string
                          example: UAE (Middle East)
                  message:
                    type: string
                    example: Countries fetched successfully
        '404':
          description: No countries found
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items: {}
                  message:
                    type: string
                    example: Country not found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Internal server error
                  error:
                    type: string
                    example: Internal server error. Please try again later.
  /v1/auth/designation:
    get:
      summary: Get Designations List
      tags:
        - Designation
      responses:
        '200':
          description: Successfully fetched designations
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                          example: 01JZ2RGD8VQXZ7N2C9KJQ0VZHZ
                        name:
                          type: string
                          example: Manager
                        key:
                          type: string
                          example: manager
                  message:
                    type: string
                    example: Designation fetched successfully
        '404':
          description: No designations found
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Designation not found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Internal server error. Please try again later.
  /v1/auth/region:
    get:
      summary: Get Regions List
      tags:
        - Region
      responses:
        '200':
          description: Successfully fetched regions
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                          example: reg_01JZ2RG968V8N98E6FRTEW0AAT
                        name:
                          type: string
                          example: North America
                  message:
                    type: string
                    example: Region fetched successfully
        '404':
          description: No regions found
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items: {}
                  message:
                    type: string
                    example: Region not found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Internal server error. Please try again later.
  /v1/auth/session-storage:
    post:
      summary: Establish session using bearer token
      description: >-
        Validates the bearer token and establishes a session by calling the
        session service.
      tags:
        - Session Storage
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Session established successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Session established
                  data:
                    type: object
                    properties:
                      user:
                        type: object
                        properties:
                          actor_id:
                            type: string
                            example: '003'
                          actor_type:
                            type: string
                            example: cq_user
                          auth_identity_id:
                            type: string
                            example: authid_01JZN2M0V1F1DD267A11JPZNYF
                          app_metadata:
                            type: object
                            properties:
                              cq_user_id:
                                type: string
                                example: '003'
                          iat:
                            type: integer
                            example: 1752041913
                          exp:
                            type: integer
                            example: 1752128313
        '400':
          description: Missing or invalid Authorization header
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Authorization token must be provided in the Bearer format.
        '500':
          description: Internal server or request setup error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Internal server error. Please try again later.
        '502':
          description: No response from session service
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: No response from session service
  /v1/auth/signin:
    post:
      summary: Sign in user with email and password
      tags:
        - Users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                  example: <EMAIL>
                password:
                  type: string
                  example: Smart@123
      responses:
        '200':
          description: Login successful, returns JWT token
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
                    example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        '400':
          description: Missing email or password
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Email and password must be provided.
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Internal server error. Please try again later.
  /v1/auth/signup/email-resend:
    post:
      summary: Resend OTP to email address
      tags:
        - Signup - Email
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
              properties:
                email:
                  type: string
                  format: email
                  example: <EMAIL>
      responses:
        '200':
          description: OTP resent successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: OTP resent successfully.
        '400':
          description: Email not provided
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Email is required
        '404':
          description: No unverified OTP request found for the email
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Email OTP request does not exist
        '429':
          description: Resend request rate limited
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Please wait 5 seconds before requesting a new OTP.
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Internal server error. Please try again later.
  /v1/auth/signup/email-send:
    post:
      summary: Send or update email OTP for verification
      tags:
        - Signup - Email
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                  example: <EMAIL>
      responses:
        '200':
          description: OTP sent or updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Email verification code sent successfully
        '400':
          description: Bad request (e.g., email already registered or missing)
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Email is required
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Internal server error. Please try again later.
  /v1/auth/signup/email-verify:
    put:
      summary: Verify Email OTP
      tags:
        - Signup - Email
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - otp_code
              properties:
                email:
                  type: string
                  example: <EMAIL>
                otp_code:
                  type: string
                  example: 123456
      responses:
        '200':
          description: OTP verified successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  is_verified:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: email OTP verification was successful.
        '400':
          description: Invalid or expired OTP or missing inputs
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: OTP is invalid or expired.
        '404':
          description: No OTP record found for the given email
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: No OTP record found for the given email.
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Internal server error. Please try again later.
  /v1/auth/signup/mobile-resend:
    post:
      summary: Resend OTP to mobile number
      tags:
        - Signup - Mobile
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - mobile_number
              properties:
                mobile_number:
                  type: string
                  example: '+11234567890'
      responses:
        '200':
          description: OTP resent successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: OTP has been resent successfully.
        '400':
          description: Mobile number not provided
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Mobile number is required
        '404':
          description: No unverified OTP request found for the mobile number
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Mobile OTP request not found
        '429':
          description: Resend request rate limited
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Please wait 8 seconds before requesting a new OTP.
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Internal server error
  /v1/auth/signup/mobile-send:
    post:
      summary: Send OTP to mobile number
      tags:
        - Signup - Mobile
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - mobile_number
              properties:
                mobile_number:
                  type: string
                  example: '+11234567890'
      responses:
        '200':
          description: OTP sent successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Mobile verification OTP sent successfully.
        '400':
          description: Mobile number missing or already registered
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Mobile number already exists
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Internal server error
  /v1/auth/signup/mobile-verify:
    put:
      summary: Verify OTP for mobile number
      tags:
        - Signup - Mobile
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - mobile_number
                - otp_code
              properties:
                mobile_number:
                  type: string
                  example: '+11234567890'
                otp_code:
                  type: string
                  example: '123456'
      responses:
        '200':
          description: OTP verified successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  is_verified:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: OTP verified successfully.
        '400':
          description: OTP expired or invalid, or mobile number missing
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: OTP is invalid or expired
        '404':
          description: OTP record not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: No OTP record found for the given mobile number
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Internal server error
  /v1/auth/signup/reset-password/{token}:
    put:
      summary: Set or reset the password using token
      tags:
        - Signup - Reset Password
      parameters:
        - in: path
          name: token
          required: true
          schema:
            type: string
          description: JWT token containing the email for verification
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - password
              properties:
                password:
                  type: string
                  format: password
                  example: NewStrongPassword123!
      responses:
        '200':
          description: Password set successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                  message:
                    type: string
                    example: Password updated successfully
        '400':
          description: Invalid token or missing password
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Invalid token
        '404':
          description: Guest user not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: User not found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Internal server error. Please try again later.
  /v1/auth/signup:
    post:
      summary: Register a new guest user
      tags:
        - Users
      parameters:
        - in: query
          name: source
          required: true
          schema:
            type: string
          description: The source of the guest user (e.g., partner or directEndCustomer)
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - country_code
                - mobile_number
                - designation
                - region_id
                - registered_country_id
                - terms_and_conditions
              properties:
                first_name:
                  type: string
                last_name:
                  type: string
                designation:
                  type: string
                mobile_number:
                  type: string
                country_code:
                  type: string
                email:
                  type: string
                  format: email
                company_name:
                  type: string
                has_sales_reference:
                  type: boolean
                registered_country_id:
                  type: string
                profile_pic_file_name:
                  type: string
                redington_sales_reference:
                  type: string
                terms_and_conditions:
                  type: boolean
                region_id:
                  type: string
              example:
                first_name: Guest
                last_name: User
                designation: 01JZ2RGD8VQXZ7N2C9KJQ0VZHZ
                mobile_number: '78541236985'
                country_code: '+91'
                email: <EMAIL>
                company_name: Test Company
                has_sales_reference: false
                registered_country_id: in
                profile_pic_file_name: profile.jpg
                redington_sales_reference: RSR1234
                terms_and_conditions: true
                region_id: 01JZ2RGD8VQXZ7N2C9KJQ0VZHZ
      responses:
        '200':
          description: Guest user registered successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Guest user registered successfully
        '400':
          description: Invalid input, missing verification, or user already exists
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Email or mobile number not verified
        '404':
          description: Designation or country not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Designation not found
        '500':
          description: Server error during registration or email sending
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Internal server error
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
tags: []
