export function CustomerMasterModelMapper(dto: any) {
  return {
    Type: dto.type,
    ID: dto.id,
    CustId: dto.custId,
    OrgName: dto.orgName,
    FirstName: dto.firstName,
    LastName: dto.lastName,
    EmailId: dto.emailId,
    SAPCustId: dto.sapCustId,
    SAPCompany: dto.sapCompany,
    CountryId: dto.countryId,
    AddressLine1: dto.addressLine1,
    AddressLine2: dto.addressLine2,
    UserName: dto.userName,
    StoreId: dto.storeId,
    BrandId: dto.brandId,
    PartnerId: dto.partnerId,
    MPNID: dto.mpnId,
    DomainId: dto.domainId,
    CreditLimit: dto.creditLimit,
    IONO: dto.ioNo,
    Taxable: dto.taxable,
    YInvoice: dto.yInvoice,
    MInvoice: dto.mInvoice,
    MAZInvoice: dto.mazInvoice,
    MiddleName: dto.middleName,
    OrganizationRegistrationNumber: dto.organizationRegistrationNumber,
    CustomerVertical: dto.customerVertical,
    Segment: dto.segment,
    Sector: dto.sector,
    TinNumber: dto.tinNumber,
    PanNumber: dto.panNumber,
    NewTenant: dto.newTenant,
    City: dto.city,
    AssociatedPartnerId: dto.associatedPartnerId,
    PostalCode: dto.postalCode,
    PhoneNumber: dto.phoneNumber,
    VatId: dto.vatId,
    State: dto.state,
  };
}
