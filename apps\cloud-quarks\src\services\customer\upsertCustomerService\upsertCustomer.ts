import logger from "../../../utils/logger";
import { CreateCustomerStatusResponse } from "../../../types/responses/customResponse";
import { updateCustomerUsingSP } from "../../../modules/sqlRaw/customersRaw/updateCustomerRaw";
import { createCustomerStoredProc } from "../../../modules/sqlRaw/customersRaw/createCustomerRaw";
// import { updateCustomerRaw } from "../../../modules/sqlRaw/customersRaw/updateCustomerRaw";
// import { createCustomerRaw } from "../../../modules/sqlRaw/customersRaw/createCustomerRaw";

export async function UpsertCustomer(
  action: "CREATECUSTOMER" | "UPDATECUSTOMER",
  customerModel: any
): Promise<CreateCustomerStatusResponse> {
  logger.info(
    `Entered into UpsertCustomer method with action ${action} and customer model: ${JSON.stringify(customerModel)}`
  )

  let responseFromDal: any

  if (action.toUpperCase() === "CREATECUSTOMER") {
    logger.info(
      `Going to hit DAL CreateCustomer function with action: ${action} and customer model: ${JSON.stringify(
        customerModel
      )}`
    )
    responseFromDal = await createCustomerStoredProc(action, customerModel);

    logger.info(
      `Response from DAL in case of create customer: ${JSON.stringify(responseFromDal)}`
    )
  } else if (action.toUpperCase() === "UPDATECUSTOMER") {
    logger.info(
      `Going to hit DAL UpdateCustomer function with action: ${action} and customer model: ${JSON.stringify(
        customerModel
      )}`
    )
    responseFromDal = await updateCustomerUsingSP(action, customerModel)

    logger.info(
      `Response from DAL in case of update customer: ${JSON.stringify(responseFromDal)}`
    )
  }

  logger.info(`Going to deserialize string: ${JSON.stringify(responseFromDal)}`)

  const response: CreateCustomerStatusResponse = {
    status: responseFromDal?.Status ?? "",
    message: responseFromDal?.Message ?? "",
    UserName: responseFromDal?.UserName ?? "",
    Password: responseFromDal?.Password ?? "",
  }

  logger.info(`Going to return CreateCustomerStatusResponse as ${JSON.stringify(response)}`)

  return response
}
