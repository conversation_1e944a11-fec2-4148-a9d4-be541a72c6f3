import { z } from "zod";

/**
 * Get Renewal Promotion Query Schema
 * Matches .NET GetRenewalPromotion method parameters
 */
export const GetRenewalPromotionQuerySchema = z.object({
  subscriptionId: z.string().min(1, "Subscription ID is required"),
  billType: z.string().min(1, "Bill type is required"),
  term: z.string().min(1, "Term is required"),
  brandId: z.string().transform((val) => parseInt(val, 10)).refine((val) => !isNaN(val), "Brand ID must be a valid number"),
  materialno: z.string().min(1, "Material number is required"),
  quantity: z.string().transform((val) => parseInt(val, 10)).refine((val) => !isNaN(val) && val > 0, "Quantity must be a positive number"),
  segment: z.string().min(1, "Segment is required"),
  customerTermEndDate: z.string().transform((val) => new Date(val)).refine((val) => !isNaN(val.getTime()), "Customer term end date must be a valid date"),
  isOldSubscriptionModified: z.string().transform((val) => val.toLowerCase() === "true").refine((val) => typeof val === "boolean", "isOldSubscriptionModified must be a boolean")
});

export type GetRenewalPromotionQueryType = z.infer<typeof GetRenewalPromotionQuerySchema>;

/**
 * Validate Get Renewal Promotion Request
 * Matches .NET validation patterns
 */
export function validateGetRenewalPromotionRequest(query: any): {
  isValid: boolean;
  errors: string[];
  data?: GetRenewalPromotionQueryType;
} {
  try {
    const validatedData = GetRenewalPromotionQuerySchema.parse(query);
    return {
      isValid: true,
      errors: [],
      data: validatedData
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
      return {
        isValid: false,
        errors
      };
    }
    return {
      isValid: false,
      errors: ["Validation failed"]
    };
  }
}
