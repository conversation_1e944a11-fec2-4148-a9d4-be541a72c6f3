import { getSqlServerConnection } from "../../utils/sqlServerClient";
import logger from "../../utils/logger";
import sql from "mssql";
import { GoogleCustomerDetailsModel } from "../../types/responses/customResponse";

/**
 * Get Google customer details by customer ID
 * Matches .NET GoogleCustomerDAL.GetCustomerDetails method
 */
export async function getCustomerDetailsDAL(
  custId: string
): Promise<GoogleCustomerDetailsModel> {
  logger.info(
    `Entered into GoogleCustomerDAL.GetCustomerDetails with custId: ${custId}`
  );

  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();

    const procedure = "spGetGoogleCustomerDetails"; // Stored procedure name

    request.input("iMagentoCustId", sql.VarChar(20), custId); // Removed '@' and matched length

    logger.info(
      `Going to hit ${procedure} proc with parameters iMagentoCustId: ${custId}`
    );

    const result = await request.execute<GoogleCustomerDetailsModel>(procedure);
    const customerDetails = result.recordset?.[0];

    if (!customerDetails) {
      throw new Error(`No customer details found for custId: ${custId}`);
    }

    logger.info(`Got Customer Details as ${JSON.stringify(customerDetails)}`);
    return customerDetails;
  } catch (error: any) {
    logger.error(`Error in getCustomerDetailsDAL: ${error.message}`);
    throw error;
  }
}
