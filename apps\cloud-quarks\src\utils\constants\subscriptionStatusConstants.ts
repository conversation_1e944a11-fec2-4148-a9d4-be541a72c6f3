// Subscription Status Constants (matching .NET SubStatus class)
export const SubStatus = {
  UP: "up",
  DOWN: "down",
  CANC<PERSON>: "cancel",
  SUSPENDED: "suspended",
  ACTIVE: "active",
  DELETED: "deleted",
  SUSPEND: "suspend",
  AUTORENEWON: "autorenewon",
  AUTORENEWOFF: "autorenewoff",
  EXPIRED: "expired",
  MPNIDUPDATE: "mpnidupdate",
} as const;

// Type for subscription status values
export type SubStatusType = typeof SubStatus[keyof typeof SubStatus];

// Valid statuses for patch operations (matching .NET validation logic)
export const VALID_PATCH_STATUSES = [
  SubStatus.DELETED,
  SubStatus.SUSPENDED,
  SubStatus.ACTIVE,
  SubStatus.UP,
  SubStatus.DOWN,
  SubStatus.AUTORENEWON,
  SubStatus.AUTORENEWOFF,
  SubStatus.MPNIDUPDATE,
] as const;

// Type for valid patch status values
export type ValidPatchStatusType = typeof VALID_PATCH_STATUSES[number];

/**
 * Check if a status is valid for patch operations
 * Matches .NET validation: status == SubStatus.DELETED || status == SubStatus.SUSPENDED || ...
 */
export function isValidPatchStatus(status: string): boolean {
  return VALID_PATCH_STATUSES.includes(status as ValidPatchStatusType);
}
