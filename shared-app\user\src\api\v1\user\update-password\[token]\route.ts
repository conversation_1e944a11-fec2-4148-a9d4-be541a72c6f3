import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { verify } from 'jsonwebtoken';
import { Modules } from '@medusajs/framework/utils';
import { stringConstants } from '@org/utils';
import CQUserService from '../../../../../modules/cq-user/service';
import { CQUser_MODULE } from '../../../../../modules/cq-user';

/**
 * @openapi
 * /v1/user/update-password/{token}:
 *   put:
 *     summary: Update the password using token
 *     tags:
 *       - Users
 *     parameters:
 *       - in: path
 *         name: token
 *         required: true
 *         schema:
 *           type: string
 *         description: JWT token containing the email for verification
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - password
 *             properties:
 *               password:
 *                 type: string
 *                 format: password
 *                 example: EncodedPassword!
 *     responses:
 *       200:
 *         description: Password set successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                 message:
 *                   type: string
 *                   example: Password updated successfully
 *       400:
 *         description: Invalid token or missing password
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Invalid token
 *       404:
 *         description: Guest user not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: User not found
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error. Please try again later.
 */

export async function PUT(req: MedusaRequest, res: MedusaResponse) {
  const authModuleService: any = req.scope.resolve(Modules.AUTH);
  const { token } = req?.params;
  const { password } = req?.body as {
    email: string;
    password: string;
  };

  try {
    const decodedToken: any = verify(token, process.env.JWT_SECRET as string);

    const userEmail = decodedToken?.email;
    if (!userEmail) {
      return res.status(400).json({ message: 'Invalid token' });
    }
    const guestUserService: CQUserService = req.scope.resolve(CQUser_MODULE);
    const [users] = await guestUserService.listAndCountCqUsers({
      email: userEmail,
    });
    const guestUser = users.length ? users[0] : null;

    if (!guestUser) {
      res.status(404).json({
        message: stringConstants.NOT_FOUND('User'),
      });
    }

    const guestUserId = guestUser?.id;
    await authModuleService.updateProvider('emailpass', {
      entity_id: userEmail,
      password: password,
    });

    if (guestUser) {
      await guestUserService.updateCqUsers({
        id: guestUserId,
        is_password_set: true,
      });
    }

    return res.status(200).json({
      message: stringConstants.UPDATED_SUCCESSFULLY('Password'),
    });
  } catch (err: any) {
    if (err.name === "TokenExpiredError") {
      return res.status(401).json({ message: stringConstants.TOKEN_EXPIRED(), });
    }
    return res
      .status(500)
      .json({ message: stringConstants.INTERNAL_SERVER_ERROR() });
  }
}
