import { validateCiscoCustomerValidateDto } from "../ciscoCustomerValidateValidator";

describe("validateCiscoCustomerValidateDto", () => {
  it("should return no errors for valid DTO", () => {
    // Arrange
    const validDto = {
      custId: "cust123",
      storeId: "store123",
      partyName: "Example Corp",
      addressLine1: "123 Main St",
      addressLine2: "Suite 100",
      city: "San Francisco",
      state: "CA",
      postalCode: "94105",
      country: "United States",
      countryCode: "US",
      businessContactName: "John Doe",
      businessContactEmail: "<EMAIL>",
      businessContactNumber: "+1-555-123-4567"
    };

    // Act
    const result = validateCiscoCustomerValidateDto(validDto);

    // Assert
    expect(result.isValid).toBe(true);
    expect(result.errors).toEqual([]);
  });

  it("should return error when DTO is null", () => {
    // Arrange
    const nullDto = null;

    // Act
    const result = validateCiscoCustomerValidateDto(nullDto);

    // Assert
    expect(result.isValid).toBe(false);
    expect(result.errors).toEqual(["Invalid data structure"]);
  });

  it("should return error when DTO is undefined", () => {
    // Arrange
    const undefinedDto = undefined;

    // Act
    const result = validateCiscoCustomerValidateDto(undefinedDto);

    // Assert
    expect(result.isValid).toBe(false);
    expect(result.errors).toEqual(["Invalid data structure"]);
  });

  it("should return error when custId is empty", () => {
    // Arrange
    const dtoWithEmptyCustId = {
      custId: "",
      storeId: "store123",
      partyName: "Example Corp",
      addressLine1: "123 Main St",
      city: "San Francisco",
      state: "CA",
      postalCode: "94105",
      country: "United States",
      countryCode: "US",
      businessContactName: "John Doe",
      businessContactEmail: "<EMAIL>",
      businessContactNumber: "+1-555-123-4567"
    };

    // Act
    const result = validateCiscoCustomerValidateDto(dtoWithEmptyCustId);

    // Assert
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain("Country code cannot be null or empty.");
  });

  it("should return error when storeId is empty", () => {
    // Arrange
    const dtoWithEmptyStoreId = {
      custId: "cust123",
      storeId: "",
      partyName: "Example Corp",
      addressLine1: "123 Main St",
      city: "San Francisco",
      state: "CA",
      postalCode: "94105",
      country: "United States",
      countryCode: "US",
      businessContactName: "John Doe",
      businessContactEmail: "<EMAIL>",
      businessContactNumber: "+1-555-123-4567"
    };

    // Act
    const result = validateCiscoCustomerValidateDto(dtoWithEmptyStoreId);

    // Assert
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain("Store Id cannot be null or empty.");
  });

  it("should return error when businessContactEmail is invalid", () => {
    // Arrange
    const dtoWithInvalidEmail = {
      custId: "cust123",
      storeId: "store123",
      partyName: "Example Corp",
      addressLine1: "123 Main St",
      city: "San Francisco",
      state: "CA",
      postalCode: "94105",
      country: "United States",
      countryCode: "US",
      businessContactName: "John Doe",
      businessContactEmail: "invalid-email",
      businessContactNumber: "+1-555-123-4567"
    };

    // Act
    const result = validateCiscoCustomerValidateDto(dtoWithInvalidEmail);

    // Assert
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain("BusinessContactEmail is not a valid email address");
  });

  it("should allow addressLine2 to be optional", () => {
    // Arrange
    const dtoWithoutAddressLine2 = {
      custId: "cust123",
      storeId: "store123",
      partyName: "Example Corp",
      addressLine1: "123 Main St",
      city: "San Francisco",
      state: "CA",
      postalCode: "94105",
      country: "United States",
      countryCode: "US",
      businessContactName: "John Doe",
      businessContactEmail: "<EMAIL>",
      businessContactNumber: "+1-555-123-4567"
    };

    // Act
    const result = validateCiscoCustomerValidateDto(dtoWithoutAddressLine2);

    // Assert
    expect(result.isValid).toBe(true);
    expect(result.errors).toEqual([]);
  });

  it("should return multiple errors when multiple fields are invalid", () => {
    // Arrange
    const dtoWithMultipleErrors = {
      custId: "",
      storeId: "",
      partyName: "",
      addressLine1: "",
      city: "",
      state: "",
      postalCode: "",
      country: "",
      countryCode: "",
      businessContactName: "",
      businessContactEmail: "invalid-email",
      businessContactNumber: ""
    };

    // Act
    const result = validateCiscoCustomerValidateDto(dtoWithMultipleErrors);

    // Assert
    expect(result.isValid).toBe(false);
    expect(result.errors.length).toBeGreaterThan(1);
  });
});
