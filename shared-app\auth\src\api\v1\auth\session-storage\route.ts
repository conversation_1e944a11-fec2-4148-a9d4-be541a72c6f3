import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import axios from 'axios';
import jwt from 'jsonwebtoken';
import * as dotenv from 'dotenv';
import { stringConstants } from '@org/utils';
dotenv.config();

/**
 * @openapi
 * components:
 *   securitySchemes:
 *     bearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 */

/**
 * @openapi
 * /v1/auth/session-storage:
 *   post:
 *     summary: Establish session using bearer token
 *     description: Validates the bearer token and establishes a session by calling the session service.
 *     tags:
 *      - Session Storage
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Session established successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Session established
 *                 data:
 *                   type: object
 *                   properties:
 *                     user:
 *                       type: object
 *                       properties:
 *                         actor_id:
 *                           type: string
 *                           example: "003"
 *                         actor_type:
 *                           type: string
 *                           example: cq_user
 *                         auth_identity_id:
 *                           type: string
 *                           example: authid_01JZN2M0V1F1DD267A11JPZNYF
 *                         app_metadata:
 *                           type: object
 *                           properties:
 *                             cq_user_id:
 *                               type: string
 *                               example: "003"
 *                         iat:
 *                           type: integer
 *                           example: 1752041913
 *                         exp:
 *                           type: integer
 *                           example: 1752128313
 *       400:
 *         description: Missing or invalid Authorization header
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Authorization token must be provided in the Bearer format.
 *       502:
 *         description: No response from session service
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: No response from session service
 *       500:
 *         description: Internal server or request setup error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error. Please try again later.
 */

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(400).json({
        message: stringConstants.AUTHORIZATION_TOKEN_MISSING(),
      });
    }

    const token = authHeader.replace('Bearer ', '');

    try {
      const decoded = jwt.decode(token);
      if (!decoded) {
        return res
          .status(401)
          .json({ message: stringConstants.INVALID_TOKEN_FORMAT() });
      }
    } catch {
      return res
        .status(401)
        .json({ message: stringConstants.INVALID_JWT_TOKEN() });
    }

    let response;
    try {
      response = await axios.post(
        `${process.env.MEDUSA_DEFAULT_SESSION_URL}`,
        {},
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
          timeout: 10000,
        }
      );

      const actorId = response.data?.user?.actor_id || 'unknown_actor';
      const userId = response.data?.user?.app_metadata?.cq_user_id || actorId;

      const sessionData = {
        token: token,
        actor_id: actorId,
        user_id: userId,
        actor_type: 'cq_user',
        created_at: new Date().toISOString(),
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      };

      const encodedSession = encodeURIComponent(JSON.stringify(sessionData));

      const isProduction = process.env.NODE_ENV === 'development';
      const cookie = [
        `custom_auth_session=${encodedSession}`,
        'HttpOnly',
        ...(isProduction ? ['Secure'] : []),
        'SameSite=Lax',
        'Path=/',
        `Expires=${new Date(Date.now() + 24 * 60 * 60 * 1000).toUTCString()}`,
      ].join('; ');

      res.setHeader('Set-Cookie', cookie);

      if (req.session) {
        req.session.userId = userId;
        req.session.actorId = actorId;
        req.session.actorType = 'cq_user';
        req.session.authenticated = true;
      }

      return res.status(200).json({
        message: stringConstants.SESSION_ESTABLISHED_VERIFY(),
        data: {
          user: response.data?.user,
          session_created: true,
          expires_in: 24 * 60 * 60,
        },
        debug: {
          cookie_value: encodedSession,
          cookie_length: encodedSession.length,
          full_cookie_header: cookie,
        },
      });
    } catch (axiosError: any) {
      if (axiosError.response) {
        return res.status(axiosError.response.status).json({
          message:
            axiosError.response.data?.message ||
            stringConstants.SESSION_SERVICE_ERROR(),
          data: axiosError.response.data || null,
        });
      } else if (axiosError.request) {
        return res.status(502).json({
          message: stringConstants.SESSION_RESSPONE_ERROR(),
        });
      } else {
        return res.status(500).json({
          message: stringConstants.SESSION_SETUP_ERROR(),
        });
      }
    }
  } catch (err: any) {
    return res.status(500).json({
      message: stringConstants.INTERNAL_SERVER_ERROR(),
    });
  }
}
