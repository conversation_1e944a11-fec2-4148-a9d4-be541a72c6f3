import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import {
  ResponseStatus,
  PlaceTransitionRequest
} from "../../types/responses/customResponse";
import { validateSubscriptionTransition, addSubscriptionTransition } from "./transitionDAL";


export async function placeTransitionService(
  req: MedusaRequest,
  request: PlaceTransitionRequest
): Promise<ResponseStatus> {
  let response: ResponseStatus = { Status: false };

  // Validation SP
  // -Is Existing Transition request is in progress.
  // -Only one request can be taken that is either of upgrade, increase, decrease and terminate, suspend, resume.
  logger.info(`Entering into DAL ValidateSubscriptionTransition method with request-${JSON.stringify(request)}`);
  response = await validateSubscriptionTransition(req, request);
  logger.info(`Response from DAL ValidateSubscriptionTransition method with response-${JSON.stringify(response)}`);
  if (!response.Status) {
    logger.info(`Entered validationResponse is not success and returning with response ${JSON.stringify(response)}`);
    return response;
  }

  // If from Catalog item id not passed from Magento then we are using MaterialId
  if (!request.FromCatalogItemId) {
    request.FromCatalogItemId = request.FromMaterialId;
  }

  // Save PlaceTransition
  logger.info(`Entering into DAL AddSubscriptionTransition method with request-${JSON.stringify(request)}`);
  response = await addSubscriptionTransition(req, request);
  logger.info(`Response from DAL AddSubscriptionTransition method with response-${JSON.stringify(response)}`);

  logger.info(`Exiting from PlaceTransition with response ${JSON.stringify(response)}`);
  return response;
}
