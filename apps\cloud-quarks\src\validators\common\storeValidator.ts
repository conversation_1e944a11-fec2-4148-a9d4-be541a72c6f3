import logger from "../../utils/logger";
import { getSqlServerConnection } from "../../utils/sqlServerClient";
import sql from "mssql";

/**
 * Store Validator
 * Matches .NET StoreValidator functionality
 */
export async function validateStore(storeId: string): Promise<{ isValid: boolean; errors: string[] }> {
  logger.info(`Validating store with storeId: ${storeId}`);
  
  const errors: string[] = [];

  if (!storeId || storeId.trim() === '') {
    errors.push('StoreId is required');
    return { isValid: false, errors };
  }

  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();

    // Check if store exists (matching .NET store validation logic)
    request.input('StoreId', sql.VarChar(50), storeId);
    
    const result = await request.query(`
      SELECT COUNT(*) as StoreCount 
      FROM Stores 
      WHERE StoreId = @StoreId AND IsActive = 1
    `);

    const storeCount = result.recordset[0]?.StoreCount || 0;

    if (storeCount === 0) {
      errors.push(`Store with ID '${storeId}' does not exist or is inactive`);
    }

    logger.info(`Store validation completed for storeId: ${storeId}, isValid: ${errors.length === 0}`);
    
    return {
      isValid: errors.length === 0,
      errors
    };
  } catch (error: any) {
    logger.error(`Error validating store: ${error.message}`);
    errors.push('Store validation failed due to database error');
    return { isValid: false, errors };
  }
}
