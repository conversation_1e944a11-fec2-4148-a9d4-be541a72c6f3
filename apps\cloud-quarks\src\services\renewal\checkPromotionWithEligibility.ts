import logger from "../../utils/logger";
import {
  SubscriptionModel,
  SubscriptionPromotionDetailModel,
  PromotionEligibility,
  PromotionModel
} from "../../types/responses/customResponse";
import { checkPromotionEligibilityService } from "./checkPromotionEligibility";
import { comparableString } from "../../utils/constants/brandEnum";

/**
 * Check Promotion With Eligibility
 * Matches .NET CheckPromotionWithEligibility method
 */
export async function checkPromotionWithEligibilityService(
  subDetails: SubscriptionModel,
  billType: string,
  term: string,
  materialno: string,
  quantity: number,
  segment: string
): Promise<SubscriptionPromotionDetailModel> {
  logger.info(`Entered into method CheckPromotionWithEligibility subDetails: ${JSON.stringify(subDetails)}, billType:${billType}, term:${term},materialno:${materialno},quantity:${quantity}, segment:${segment}`);

  // Initialize subscription promotion detail model (matching .NET constructor)
  const subscriptionPromotionDetailModel: SubscriptionPromotionDetailModel = {
    promotionEligibilities: []
  };

  // Create promotion eligibility list (matching .NET List<PromotionEligibility> promotionEligibilities)
  const promotionEligibilities: PromotionEligibility[] = [];

  // Create promotion eligibility object (matching .NET PromotionEligibility promotionEligibility)
  const promotionEligibility: PromotionEligibility = {
    MaterialId: materialno,
    Term: term,
    BillType: billType,
    Segment: segment,
    Quantity: quantity
  };

  // Add to list (matching .NET promotionEligibilities.Add(promotionEligibility))
  promotionEligibilities.push(promotionEligibility);

  // Create promotion model (matching .NET CloudQuarks.Models.PromotionModel promotionModel)
  const promotionModel: PromotionModel = {
    StoreId: subDetails.StoreId,
    CustomerId: subDetails.CustomerId,
    PromotionEligibility: promotionEligibilities
  };

  logger.info(`Going to hit CheckPromotionEligibility with promotionModel : ${JSON.stringify(promotionModel)}`);

  // Call CheckPromotionEligibility (matching .NET _promotionFactory.CheckPromotionEligibility)
  subscriptionPromotionDetailModel.promotionEligibilities = await checkPromotionEligibilityService(promotionModel);

  logger.info(`Promotion eligibilities are ${JSON.stringify(subscriptionPromotionDetailModel.promotionEligibilities)} , Subscription end date is ${subDetails.EndDate}`);

  // Filter promotions by end date (matching .NET LINQ: x.EndDate >= subDetails.EndDate.AddDays(1))
  const subscriptionEndDatePlusOne = new Date(subDetails.EndDate);
  subscriptionEndDatePlusOne.setDate(subscriptionEndDatePlusOne.getDate() + 1);

  subscriptionPromotionDetailModel.promotionEligibilities = subscriptionPromotionDetailModel.promotionEligibilities.filter(
    x => x.EndDate && new Date(x.EndDate) >= subscriptionEndDatePlusOne
  );

  logger.info(`Filtered promotions are ${JSON.stringify(subscriptionPromotionDetailModel.promotionEligibilities)}`);

  // Process promotion values (matching .NET foreach loop)
  for (const promotion of subscriptionPromotionDetailModel.promotionEligibilities) {
    // Convert promotion value based on policy type (matching .NET logic)
    if (promotion.PolicyType && promotion.PromotionValue !== undefined) {
      promotion.PromotionValue = comparableString(promotion.PolicyType) === "PERCENTDISCOUNT"
        ? Math.round(promotion.PromotionValue * 100)
        : promotion.PromotionValue;
    }
  }

  logger.info(`Got promotion eligibilities: ${JSON.stringify(subscriptionPromotionDetailModel.promotionEligibilities)}`);

  return subscriptionPromotionDetailModel;
}