{"name": "@org/source", "version": "0.0.0", "license": "MIT", "scripts": {"docker:compose:admin": "docker-compose up admin", "docker:compose:auth": "docker-compose up authentication", "start": "node dist/main.js", "dev": "ts-node src/main.ts", "worker": "ts-node apps/bullmq-service/src/worker.ts", "serve:all": "npx nx run-many --target=serve --all", "serve:apps": "npx nx run-many --target=serve --projects=admin", "lint": "npx eslint apps libs --ext .ts,.tsx,.js,.jsx", "seed": "cross-env-shell \"echo APP_NAME=$APP_NAME && npx nx run $APP_NAME:seed\"", "generate:swagger": "ts-node apps/company/src/scripts/swagger-yml.ts", "migrate:multi-region": "ts-node libs/utils/src/migrate/migrate.ts"}, "private": true, "dependencies": {"@getbrevo/brevo": "^2.2.0", "@types/cors": "^2.8.18", "@types/js-yaml": "^4.0.9", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.12", "@types/string-template": "^1.0.6", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@types/yamljs": "^0.2.34", "aws-sdk": "^2.1692.0", "axios": "^1.6.0", "bullmq": "^5.44.4", "cors": "^2.8.5", "ejs": "^3.1.10", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-simple-import-sort": "^12.1.1", "exceljs": "^4.4.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "husky": "^9.1.7", "js-yaml": "^4.1.0", "multer": "^1.4.5-lts.2", "passport": "^0.7.0", "passport-linkedin-oauth2": "^2.0.0", "string-template": "^1.0.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "yamljs": "^0.3.0"}, "devDependencies": {"@eslint/js": "^9.8.0", "@nrwl/jest": "^15.0.0", "@nx/esbuild": "20.6.1", "@nx/eslint": "20.6.1", "@nx/eslint-plugin": "20.6.1", "@nx/jest": "20.6.1", "@nx/js": "20.6.1", "@nx/node": "20.6.1", "@nx/workspace": "20.6.1", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@swc/jest": "~0.2.36", "@types/ejs": "^3.1.5", "@types/express": "^4.17.21", "@types/express-session": "^1.18.1", "@types/handlebars": "^4.1.0", "@types/jest": "^29.5.12", "@types/node": "~18.16.9", "@types/nodemailer": "^6.4.17", "@types/passport": "^1.0.17", "@types/passport-linkedin-oauth2": "^1.5.6", "@types/pg": "^8.11.11", "cross-env": "^7.0.3", "esbuild": "^0.19.2", "eslint": "^9.8.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-security": "^3.0.1", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "jsonc-eslint-parser": "^2.1.0", "nx": "20.6.1", "prettier": "^2.6.2", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "tslib": "^2.3.0", "typescript": "~5.7.2", "typescript-eslint": "^8.19.0"}, "workspaces": ["apps/*", "libs/elasticsearch", "libs/helmet", "libs/rate_limit", "cognito", "libs/cognito", "libs/ses", "libs/util-excel"]}