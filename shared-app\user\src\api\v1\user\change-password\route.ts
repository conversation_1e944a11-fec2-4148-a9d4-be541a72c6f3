import {
	MedusaRequest,
	MedusaResponse,
	AuthenticatedMedusaRequest,
} from '@medusajs/framework/http';
import { verify } from 'jsonwebtoken';
import Scrypt from 'scrypt-kdf';
import { Modules } from '@medusajs/framework/utils';
import { stringConstants } from '@org/utils';
import { logger } from '@medusajs/framework';
import { z } from 'zod';
import CQUserService from '../../../../modules/cq-user/service';
import { CQUser_MODULE } from '../../../../modules/cq-user';
import { changePasswordSchema } from '../validators';

type UserPasswordSchemaType = z.infer<typeof changePasswordSchema>;

/**
 * @openapi
 * /v1/user/change-password:
 *   post:
 *     summary: Change password for the authenticated guest user
 *     tags:
 *       - Users
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - oldEncryptedPassword
 *               - newEncryptedPassword
 *             properties:
 *               oldPassword:
 *                 type: string
 *                 description: The current password of the user
 *                 example: OldPass123!
 *               newPassword:
 *                 type: string
 *                 description: >
 *                   The new password must be 8–12 characters long and must contain:
 *                   - At least one uppercase letter
 *                   - At least one number
 *                   - At least one special character
 *                 example: NewPass456!
 *                 minLength: 8
 *                 maxLength: 12
 *     responses:
 *       200:
 *         description: Password updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Password updated successfully
 *       400:
 *         description: Invalid old password or new password does not meet criteria
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Old password and new password must not be the same
 *       500:
 *         description: Server error while updating password
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error occurred
 */

export async function POST(
	req: AuthenticatedMedusaRequest,
	res: MedusaResponse
) {
	const { oldPassword, newPassword } = req.body as UserPasswordSchemaType;
	const userService: CQUserService = req.scope.resolve(CQUser_MODULE);
	const authModuleService: any = req.scope.resolve(Modules.AUTH);

	try {
		const [users] = await userService.listAndCountCqUsers({
			id: req.auth_context.actor_id,
		});
		const checkGuestUser = users.length ? users[0] : null;
		if (!checkGuestUser) {
			return res.status(401).json({
				message: stringConstants.UNAUTHORIZED(),
			});
		}
		if (oldPassword === newPassword) {
			return res
				.status(400)
				.json({ message: stringConstants.PASSWORD_NOT_TO_SAME() });
		}
		const { success } = await authModuleService.authenticate('emailpass', {
			body: {
				email: checkGuestUser.email,
				password: oldPassword,
			},
		});
		if (!success) {
			return res
				.status(400)
				.json({ message: stringConstants.INVALID_PASSWORD() });
		}
		await authModuleService.updateProvider('emailpass', {
			entity_id: checkGuestUser.email,
			password: newPassword,
		});

		res
			.status(200)
			.json({ message: stringConstants.UPDATED_SUCCESSFULLY('Password') });
	} catch (err: any) {
		logger.error(err);
		return res
			.status(500)
			.json({ message: stringConstants.INTERNAL_SERVER_ERROR() });
	}
}
