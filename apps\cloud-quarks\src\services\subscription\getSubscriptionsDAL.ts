import logger from "../../utils/logger";
import sql from "mssql";
import { getSqlServerConnection } from "../../utils/sqlServerClient";
import { SubscriptionResponseModel } from "../../types/responses/customResponse";

/**
 * Get Subscriptions from database
 * Matches .NET SubscriptionDAL.GetSubscriptions method
 */
export async function getSubscriptionsDAL(
  storeId: string,
  partnerId: string,
  customerId: string,
  brandId: string,
  subscriptionStatuses: string,
  expiryInDays: string
): Promise<SubscriptionResponseModel[]> {
  logger.info(`Entered into DAL GetSubscriptions method with storeId-${storeId}, partner-${partnerId}, customerId-${customerId}, brandId-${brandId}, subscriptionStatuses--${subscriptionStatuses}, expiryInDays--${expiryInDays}`);

  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();

    const procedure = "spGetSubscriptionsToPortal"; // Matches .NET Procedures.spGetSubscriptionsToPortal

    // Add input parameters (matching .NET DynamicParameters)
    request.input("iStoreId", sql.VarChar(50), storeId);
    request.input("iPartnerId", sql.VarChar(50), partnerId);
    request.input("iCustomerId", sql.VarChar(50), customerId);
    request.input("iBrandId", sql.VarChar(50), brandId);
    request.input("iSubscriptionStatuses", sql.VarChar(255), subscriptionStatuses);
    request.input("iExpiryInDays", sql.VarChar(50), expiryInDays);

    logger.info(`Going to hit DB with proc name : ${procedure} and parameters storeId-${storeId}, partnerId-${partnerId}, customerId-${customerId}, brandId-${brandId}, subscriptionStatuses-${subscriptionStatuses}, expiryInDays-${expiryInDays}`);

    const result = await request.execute<SubscriptionResponseModel>(procedure);
    const response = result.recordset || [];

    logger.info(`Proc ran successfully, response count: ${response.length}`);
    return response;

  } catch (error: any) {
    logger.error(`Error in getSubscriptionsDAL: ${error.message}`);
    throw error;
  }
}
