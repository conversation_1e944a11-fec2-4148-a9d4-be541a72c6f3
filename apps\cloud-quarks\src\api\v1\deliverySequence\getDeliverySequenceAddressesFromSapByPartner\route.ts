import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import { GetAddressFromSAP } from "../../../../services/deliverySequence/GetAddressFromSAPService";
import { z } from "zod";

const CustomerSapUniqueInfoValidator = z.object({
  customerCode: z.string().min(1, "Customer code cannot be blank or null"),
  companyCode: z.string().min(1, "Company code cannot be blank or null"),
  storeId: z.string().min(1, "Store-Id cannot be blank or null. Ex: AE-EN"),
});

/**
 * @openapi
 * /v1/deliverySequence/getDeliverySequenceAddressesFromSapByPartner:
 *   post:
 *     summary: Get delivery sequence addresses from SAP by business partner
 *     tags:
 *       - DeliverySequence
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - customerCode
 *               - companyCode
 *               - storeId
 *             properties:
 *               customerCode:
 *                 type: string
 *                 description: Unique customer code
 *               companyCode:
 *                 type: string
 *                 description: SAP company code
 *               storeId:
 *                 type: string
 *                 description: Store identifier
 *             example:
 *               customerCode: "123456"
 *               companyCode: "A1"
 *               storeId: "Store-789"
 *     responses:
 *       200:
 *         description: Successfully retrieved delivery sequence addresses
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 httpStatusCode:
 *                   type: integer
 *                   example: 200
 *                 content:
 *                   type: object
 *                   properties:
 *                     status:
 *                       type: string
 *                       example: "SUCCESS"
 *                     message:
 *                       type: string
 *                       example: "Delivery addresses retrieved"
 *                     customerParties:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           partnerType:
 *                             type: string
 *                             example: "AG"
 *                           partnerCode:
 *                             type: string
 *                             example: "P12345"
 *                           name:
 *                             type: string
 *                             example: "John Enterprises"
 *                           address1:
 *                             type: string
 *                             example: "123 Main Street"
 *                           address2:
 *                             type: string
 *                             example: "Suite 400"
 *                           address3:
 *                             type: string
 *                             example: ""
 *                           city:
 *                             type: string
 *                             example: "Mumbai"
 *                           state:
 *                             type: string
 *                             example: "MH"
 *                           pinCode:
 *                             type: string
 *                             example: "400001"
 *                           paymentTermsCode:
 *                             type: string
 *                             example: "PT30"
 *                           paymentType:
 *                             type: string
 *                             example: "Credit"
 *       400:
 *         description: Bad request. Input validation failed.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "FAIL"
 *                 message:
 *                   type: string
 *                   example: "customerCode is required"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "FAIL"
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 */


export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req);

  logger.info(
    `GetDeliverySequenceAddressesFromSapByPartner API called | correlationId: ${correlationId} | Body: ${JSON.stringify(
      req.body
    )}`
  );

  const validation = CustomerSapUniqueInfoValidator.safeParse(req.body);

  if (!validation.success) {
    const errors = validation.error.errors.map((e) => e.message);
    logger.info(
      `Validation errors | correlationId: ${correlationId} | Errors: ${errors.join(
        ", "
      )}`
    );
    return res.status(400).json(errors);
  }

  try {
    const dto = validation.data;
    const sapCustomerUniqueInfoModel = {
      CustomerCode: dto.customerCode,
      CompanyCode: dto.companyCode,
    };

    const storeId = dto.storeId;

    const result = await GetAddressFromSAP(req, sapCustomerUniqueInfoModel, storeId);
    logger.info(
      `Returning SAP delivery sequence address | correlationId: ${correlationId} | response: ${JSON.stringify(
        result
      )}`
    );
    return res.status(200).json(result);
  } catch (err) {
    logger.error(
      `Error in GetDeliverySequenceAddressesFromSapByPartner | correlationId: ${correlationId}`,
      err
    );
    return res.status(500).json({
      status: "FAIL",
      message: "Internal server error",
    });
  }
}
