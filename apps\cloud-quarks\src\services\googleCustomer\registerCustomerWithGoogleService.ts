import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import {
  GoogleAPIResponse,
  GoogleCreateCustomerRequest,
  GoogleCreateCustomerResponse,
  GoogleErrorResponse
} from "../../types/responses/customResponse";
import { getGoogleTokenService } from "../token/google/getGoogleTokenService";
import { createGoogleRequestHeaders } from "../../config/microsoftEndpoints";
import { GoogleEndpoints } from "../../config/googleEndpoints";
import { PostMethodWithRequestJsonBodyAndHeaders400Handle, EndPointResponseModel } from "../externalService/externalEndPointService";
import { getGoogleAccountIdFromStore } from "../googleOffer/getGooglePurchasableOfferService";

/**
 * Register Customer With Google Service
 * Matches .NET GoogleCustomerService.RegisterCustomerWithGoogle method
 */
export async function registerCustomerWithGoogleService(
  req: MedusaRequest,
  storeId: string,
  createCustomerRequest: GoogleCreateCustomerRequest
): Promise<GoogleAPIResponse> {
  logger.info(`Entered into RegisterCustomerWithGoogle service method with storeId ${storeId}, GoogleCreateCustomerRequest:${JSON.stringify(createCustomerRequest)}`);
  
  let response: GoogleAPIResponse;

  logger.info(`Going to call DAL method GetGoogleAccountId with StoreId ${storeId}`);
  const googleAccountId = await getGoogleAccountIdFromStore(storeId);
  logger.info(`Got googleAccountId ${googleAccountId}`);

  // Build the URL for CreateCustomer (matching .NET URL construction)
  const createCustomerUrl = GoogleEndpoints.createCustomerUrl(googleAccountId);
  logger.info(`CreateCustomer URL: ${createCustomerUrl}`);

  logger.info(`Going to hit GetGoogleToken method with storeId-${storeId}`);
  const token = await getGoogleTokenService(req, storeId);
  logger.info(`Got token from BL TokenService method.`);

  const headerList = createGoogleRequestHeaders(req, token, storeId);
  logger.info(`Prepared HeaderList for external call- ${JSON.stringify(headerList)}`);

  const requestBodyJson = JSON.stringify(createCustomerRequest);
  logger.info(`Prepared requestBody for external call- ${requestBodyJson}`);

  logger.info(`Going to hit ExternalEndPoint PostMethodWithRequestJsonBodyAndHeaders400Handle method with url-${createCustomerUrl}, RequestBody-${requestBodyJson}, headerlist-${JSON.stringify(headerList)}`);
  
  const configModule = req?.scope?.resolve("configModule");
  const vendorResponse: EndPointResponseModel = await PostMethodWithRequestJsonBodyAndHeaders400Handle(
    {
      url: createCustomerUrl,
      body: requestBodyJson,
      headers: headerList,
      isVendorHit: true,
      module: "RegisterCustomerWithGoogle"
    },
    configModule
  );
  
  logger.info(`Response from ExternalEndPoint PostMethodWithRequestJsonBodyAndHeaders400Handle is ${JSON.stringify(vendorResponse)}`);

  if (vendorResponse.isSuccessStatusCode) {
    logger.info(`Entered into IsSuccessStatusCode true section. Going to deserialize content ${vendorResponse?.content}`);
    
    try {
      const createCustomerResponse: GoogleCreateCustomerResponse = JSON.parse(vendorResponse.content);
      logger.info(`Deserialized object : ${JSON.stringify(createCustomerResponse)}`);

      // Extract customer ID from Name field (matching .NET: Name?.Split('/')?.Last())
      const customerId = createCustomerResponse?.Name?.split('/').pop();
      logger.info(`Customer Id fetched from response: ${customerId}`);

      createCustomerResponse.CustomerId = customerId;
      response = {
        Message: "Success",
        Data: createCustomerResponse,
        StatusCode: 200,
        IsError: false
      };
    } catch (parseError) {
      logger.error(`Error parsing create customer response: ${parseError}`);
      response = {
        Message: "Failed to parse create customer response",
        Data: null,
        StatusCode: 500,
        IsError: true
      };
    }
  } else {
    logger.info(`Entered into IsSuccessStatusCode false section.`);
    logger.info(`Setting status Failure`);

    logger.info(`Received errored vendorResponse and deserialising vendorResponse.ErrorMessage-${vendorResponse.errorMessage}`);

    try {
      const errorResponse: GoogleErrorResponse = JSON.parse(vendorResponse.errorMessage || "{}");
      logger.info(`DeserializeObject ${JSON.stringify(errorResponse)}`);
      
      response = {
        Message: getErrorMessage(errorResponse),
        Data: null,
        StatusCode: errorResponse.Error?.Code || vendorResponse.httpStatusCode || 500,
        IsError: true
      };
    } catch (parseError) {
      logger.error(`Error parsing error response: ${parseError}`);
      response = {
        Message: vendorResponse.errorMessage || "Unknown error occurred",
        Data: null,
        StatusCode: vendorResponse.httpStatusCode || 500,
        IsError: true
      };
    }
  }

  logger.info(`Going to return vendorResponse from BL method with vendorResponse model ${JSON.stringify(response)}.`);
  return response;
}

/**
 * Get Error Message from Google Error Response
 * Matches .NET GoogleErrorResponse.GetErrorMessage() method
 */
function getErrorMessage(errorResponse: GoogleErrorResponse): string {
  if (errorResponse?.Error?.Message) {
    return errorResponse.Error.Message;
  }
  
  if (errorResponse?.Error?.Details && errorResponse.Error.Details.length > 0) {
    const firstDetail = errorResponse.Error.Details[0];
    if (firstDetail?.ErrorMessages && firstDetail.ErrorMessages.length > 0) {
      return firstDetail.ErrorMessages[0].ErrorMessage || "Unknown error";
    }
  }
  
  return "Unknown error occurred";
}
