import { defineMiddlewares, validateAndTransformBody } from '@medusajs/framework/http';
// import { authenticate } from '@medusajs/medusa';
import { helmetMiddleware } from '@org/helmet';
import { countrySchema } from './v1/admin/country/validators';

export default defineMiddlewares({
  routes: [
    {
      matcher: '/*',
      middlewares: [helmetMiddleware],
    },
    {
      matcher: '/test',
      // middlewares: [authenticate('user', ['session'])],
    },
    {
      matcher: '/user',
      // middlewares: [authenticate('user', ['session'])],
    },
    {
      matcher: '/v1/admin/country',
      method: "POST",
      middlewares: [
        validateAndTransformBody(countrySchema)
      ]
    }
  ],
});
