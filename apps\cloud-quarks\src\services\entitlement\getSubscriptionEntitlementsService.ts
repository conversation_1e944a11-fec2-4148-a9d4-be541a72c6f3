import { getSqlServerConnection } from "../../utils/sqlServerClient";
import logger from "../../utils/logger";
import { EntitlementModel, SubscriptionEntitlement } from "../../types/responses/customResponse";

export async function getSubscriptionEntitlementsService(
  subscriptionId: string
): Promise<EntitlementModel | null> {
  logger.info(
    `Entered into GetSubscriptionEntitlements Service method with subscriptionId - ${subscriptionId}`
  );

  try {
    const pool = await getSqlServerConnection()
    const request = pool.request();
    const procName = "Usp_GetSubscriptionEntitlements";

    request.input("iSubscriptionId", subscriptionId);

    logger.info(
      `DB Request initiated for subscriptionId: ${subscriptionId} to SP: ${procName}`
    );

    const result = await request.execute(procName);
    const rows = result.recordset as SubscriptionEntitlement[];

    const entitlementModel: EntitlementModel = {
      totalCount: rows.length,
      entitlements: rows,
    };

    logger.info(
      `DB Request completed for subscriptionId: ${subscriptionId}, Response: ${JSON.stringify(entitlementModel)}`
    );

    return entitlementModel;
  } catch (error) {
    logger.error(
      `ERROR in getSubscriptionEntitlementsService | subscriptionId: ${subscriptionId}, Error: ${error}`
    );
    return null;
  }
}
