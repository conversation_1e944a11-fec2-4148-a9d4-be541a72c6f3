import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import { normalizeQueryParams } from "../../../../utils/mappers/getCustomerUsers";
import { validateCustomerAgreementQuery } from "../../../../validators/customer/customCustomerValidator";
import { SetCustomerAgreementSchema } from "../../../../validators/customer/setCustomerAgreementValidator";
import { setCustomerAgreementService } from "../../../../services/index";

/**
 * @openapi
 * /v1/customer/setCustomerAgreement:
 *   post:
 *     summary: Set customer agreement
 *     tags:
 *       - Customer
 *     parameters:
 *       - in: query
 *         name: storeId
 *         required: true
 *         schema:
 *           type: string
 *         description: Store ID
 *       - in: query
 *         name: brandId
 *         required: true
 *         schema:
 *           type: string
 *         description: Brand ID
 *       - in: query
 *         name: custId
 *         required: true
 *         schema:
 *           type: string
 *         description: Customer ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - primaryContact
 *               - dateAgreed
 *               - type
 *               - agreementLink
 *               - userId
 *             properties:
 *               primaryContact:
 *                 type: object
 *                 required:
 *                   - firstName
 *                   - lastName
 *                   - email
 *                 properties:
 *                   firstName:
 *                     type: string
 *                     example: "John"
 *                   lastName:
 *                     type: string
 *                     example: "Doe"
 *                   email:
 *                     type: string
 *                     example: "<EMAIL>"
 *                   phoneNumber:
 *                     type: string
 *                     example: "+1234567890"
 *                   middleName:
 *                     type: string
 *                     example: "Michael"
 *                   organizationRegistrationNumber:
 *                     type: string
 *                     example: "ORG123456"
 *               templateId:
 *                 type: string
 *                 description: "Optional - will be auto-populated from Microsoft API if not provided"
 *                 example: "template-123"
 *               dateAgreed:
 *                 type: string
 *                 format: date-time
 *                 example: "2025-07-09T10:59:55.895Z"
 *               type:
 *                 type: string
 *                 example: "MicrosoftCustomerAgreement"
 *               agreementLink:
 *                 type: string
 *                 example: "https://example.com/agreement"
 *               userId:
 *                 type: string
 *                 example: "user-456"
 *           example:
 *             primaryContact:
 *               firstName: "string"
 *               lastName: "string"
 *               email: "string"
 *               phoneNumber: "string"
 *               middleName: "string"
 *               organizationRegistrationNumber: "string"
 *             templateId: "string"
 *             dateAgreed: "2025-07-09T10:59:55.895Z"
 *             type: "string"
 *             agreementLink: "string"
 *             userId: "string"
 *     responses:
 *       200:
 *         description: Customer agreement set successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: string
 *                 statusCode:
 *                   type: number
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                 message:
 *                   type: string
 */

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const correlationId = getCorrelationId(req);
    const dto = req.body;

    logger.info(
      `Entered into SetCustomerAgreement controller | CorrelationId: ${correlationId}, DTO: ${JSON.stringify(dto)}`
    );

    // Extract and normalize query parameters
    const rawQuery = req.query as Record<string, string>;
    const keyValuePairs = normalizeQueryParams(rawQuery, ["storeId", "brandId", "custId"]);

    const { storeId, brandId, custId } = keyValuePairs;

    logger.info(
      `SetCustomerAgreement API called | CorrelationId: ${correlationId}, storeId: ${storeId}, brandId: ${brandId}, custId: ${custId}, setCustomerAgreementDto: ${JSON.stringify(dto)}`
    );

    // Validate DTO structure (camelCase input)
    const parseResult = SetCustomerAgreementSchema.safeParse(dto);
    logger.info(`Validations error count on setCustomerAgreementDto object is ${parseResult.success ? 0 : parseResult.error.errors.length}`);

    if (!parseResult.success) {
      const dtoValidationErrors = parseResult.error.errors.map((err: any) => err.message);
      logger.info(`Validation errors are ${JSON.stringify(dtoValidationErrors)}`);

      const validationErrorResponse = {
        errors: dtoValidationErrors,
        statusCode: 400,
      };

      logger.info(`Going to send bad request response ${JSON.stringify(validationErrorResponse)}`);
      return res.status(400).json(validationErrorResponse);
    }

    logger.info(`Assigning key value pair`);
    logger.info(`Request parameter values: ${JSON.stringify(keyValuePairs)}`);
    logger.info(`Assigned values storeId: ${storeId}, brandId: ${brandId}, custId: ${custId}`);

    // Validate query parameters
    const validationErrors = validateCustomerAgreementQuery(storeId, brandId, custId);
    logger.info(`Validation errors count = ${validationErrors.length}; Errors: ${JSON.stringify(validationErrors)}`);

    if (validationErrors.length > 0) {
      logger.info(`Going to return BadRequest with Validations error in setCustomerAgreement method ${JSON.stringify(validationErrors)}`);

      const errorResponse = {
        errors: validationErrors,
        statusCode: 400,
      };

      logger.info(`Going to send bad request response ${JSON.stringify(errorResponse)}`);
      return res.status(400).json(errorResponse);
    }

    logger.info(`Going to map setCustomerAgreementDto to model`);
    const setCustomerAgreementRequest = parseResult.data;

    logger.info(`Mapped setCustomerAgreementRequest: ${JSON.stringify(setCustomerAgreementRequest)}`);

    // Call the setCustomerAgreementService
    const response = await setCustomerAgreementService(req, storeId, brandId, custId, setCustomerAgreementRequest);
    logger.info(`Result from BL SetCustomerAgreement method is ${JSON.stringify(response)}`);

    const okResponse = response.setCustomerAgreement;
    const error = response.error;

    if (!okResponse) {
      return res.status(500).json(error);
    } else {
      return res.status(200).json(okResponse);
    }
  } catch (err) {
    logger.error("Set Customer Agreement error: ", err as Error);
    return res.status(500).json({
      status: false,
      message: "Internal server error",
    });
  }
}