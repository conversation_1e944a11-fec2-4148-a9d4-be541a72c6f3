import { loadEnv, defineConfig } from '@medusajs/framework/utils';

loadEnv(process.env.NODE_ENV || 'development', process.cwd());

type DbKey = 'india' | 'turkey' | 'master' | 'mea';

const VALID_REGIONS: readonly <PERSON>b<PERSON><PERSON>[] = [
  'india',
  'turkey',
  'master',
  'mea',
] as const;

const dbMap: Record<DbKey, string | undefined> = {
  india: process.env.DATABASE_URL_INDIA,
  turkey: process.env.DATABASE_URL_TURKEY,
  master: process.env.DATABASE_URL,
  mea: process.env.DATABASE_URL_MEA,
};

const getRegionFromArgs = (): DbKey => {
  const arg = process.argv.find((arg) => arg.startsWith('--region='));
  const value = arg?.split('=')[1];

  return (VALID_REGIONS.includes(value as <PERSON>b<PERSON><PERSON>) ? value : 'master') as Db<PERSON><PERSON>;
};

const getDb = (key: DbKey): string | undefined => dbMap[key];
const region = getRegionFromArgs();

// Define modules based on database type
const getModules = (databaseType: 'master') => {
  const baseModules = [
    {
      resolve: './src/modules/cq-user',
      options: {
        providers: [],
        definition: {
          isQueryable: true,
          migrations: true,
        },
      },
    },
    {
      resolve: './src/modules/custom-loader',
    },
  ];

  return baseModules;
};

const databaseType = (process.env.DATABASE_TYPE as 'master') || 'master';

module.exports = defineConfig({
  projectConfig: {
    databaseUrl: getDb(region),
    http: {
      storeCors:
        'http://localhost:3000,https://editor.swagger.io,http://localhost:3001',
      adminCors: 'http://localhost:8080,https://editor.swagger.io',
      authCors:
        'http://localhost:3000,http://localhost:8080,https://editor.swagger.io,http://localhost:3001',
      jwtSecret: process.env.JWT_SECRET || 'supersecret',
      cookieSecret: process.env.COOKIE_SECRET || 'supersecret',
    },
    sessionOptions: {
      name: process.env.SESSION_NAME || 'auth_cookie',
      resave: false,
      rolling: false,
      saveUninitialized: false,
      ttl: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
      cookie: {
        secure: true,
        httpOnly: true,
        sameSite: 'Strict',
        path: '/',
      },
    } as any,
  },
  modules: getModules(databaseType),
});
