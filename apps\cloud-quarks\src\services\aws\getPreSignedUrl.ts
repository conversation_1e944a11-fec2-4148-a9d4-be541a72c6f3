import { S3Client, GetObjectCommand, GetObjectCommandInput } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import logger from "../../utils/logger";
import { AWSCredentials } from "../../types/responses/customResponse";
// import https from "https";
// import { NodeHttpHandler } from "@aws-sdk/node-http-handler";

export async function getPreSignedUrlService(
  awsCred: AWSCredentials,
  keyName: string,
  preSignedUrlRetentionHours: number
): Promise<string> {
  logger.info("Entered into GetPreSignedUrl method.");

//   const httpsAgent = new https.Agent({
//   keepAlive: true,
//   ca,                    // trust your corp root CA
//   rejectUnauthorized: true,
// });
  
  // Create S3 client
  const s3Client = new S3Client({
    credentials: {
      accessKeyId: awsCred.AWS_ACCESS_KEY_ID,
      secretAccessKey: awsCred.AWS_SECRET_ACCESS_KEY,
    },
    region: awsCred.AWS_REGION,
    // requestHandler: new NodeHttpHandler({ httpsAgent }),
  });

  try {
    // Check if object exists
    const getObjectParams: GetObjectCommandInput = {
      Bucket: awsCred.AWS_BUCKETNAME,
      Key: keyName,
    };

    // Verify object exists by attempting to get its metadata
    const getObjectCommand = new GetObjectCommand(getObjectParams);

    try {
      // This will throw an error if object doesn't exist
      await s3Client.send(getObjectCommand);
    } catch (metadataError: any) {
      // Handle object not found
      if (metadataError.name === 'NoSuchKey' || metadataError.$metadata?.httpStatusCode === 404) {
        logger.info(`Object not found: ${keyName} in bucket: ${awsCred.AWS_BUCKETNAME}`);
        return ""; // Return empty string
      }
      // Re-throw other errors
      throw metadataError;
    }

    // Create pre-signed URL request
    const command = new GetObjectCommand({
      Bucket: awsCred.AWS_BUCKETNAME,
      Key: keyName,
    });

    // Calculate expiration time
    const expiresIn = preSignedUrlRetentionHours * 60 * 60; // Convert hours to seconds

    // Generate pre-signed URL
    const preSignedUrl = await getSignedUrl(s3Client, command, {
      expiresIn: expiresIn,
    });

    logger.info(`Pre-signed URL generated successfully for key: ${keyName}`);
    return preSignedUrl;

  } catch (error: any) {
    // Handle S3 exceptions
    logger.error(`Error occurred in GetPreSignedUrl method. Error: ${error.message}, stackTrace: ${error.stack}`);

    // Check for NotFound status
    if (error.name === 'NoSuchKey' || error.$metadata?.httpStatusCode === 404) {
      return ""; // Return empty string
    }

    // Re-throw other exceptions
    throw error;
  }
}