import AWS from 'aws-sdk';
import logger from "../../utils/logger";
import { AWSCredentials } from "../../types/responses/customResponse";
import { getAWSCredentials, validateAWSCredentials } from "../../config/awsCredentialsConfig";

/**
 * Get Pre-Signed URL for S3 Object
 * Matches .NET GetPreSignedUrl method exactly
 *
 * @param awsCred - AWS credentials object containing access keys, region, and bucket info
 * @param keyName - S3 object key/filename to generate pre-signed URL for
 * @param preSignedUrlRetentionHours - Number of hours the pre-signed URL should remain valid
 * @returns Promise<string> - Pre-signed URL or empty string if object not found
 */
export async function getPreSignedUrlService(
  awsCred: AWSCredentials,
  keyName: string,
  preSignedUrlRetentionHours: number
): Promise<string> {
  logger.info("Entered into GetPreSignedUrl method.");

  // Create S3 client (matching .NET AmazonS3Client constructor)
  const s3Client = new AWS.S3({
    accessKeyId: awsCred.AWS_ACCESS_KEY_ID,
    secretAccessKey: awsCred.AWS_SECRET_ACCESS_KEY,
    region: awsCred.AWS_REGION,
  });

  try {
    // Check if object exists first (matching .NET GetObjectMetadataAsync)
    await s3Client.headObject({
      Bucket: awsCred.AWS_BUCKETNAME,
      Key: keyName,
    }).promise();

    // Generate pre-signed URL (matching .NET GetPreSignedURLAsync)
    const params = {
      Bucket: awsCred.AWS_BUCKETNAME,
      Key: keyName,
      Expires: preSignedUrlRetentionHours * 3600, // Convert hours to seconds (matching .NET DateTime.UtcNow.AddHours)
    };

    const preSignedUrl = await s3Client.getSignedUrlPromise('getObject', params);

    logger.info(`Pre-signed URL generated successfully for key: ${keyName}`);
    return preSignedUrl;

  } catch (error: any) {
    // Handle S3 exceptions (matching .NET AmazonS3Exception handling)
    logger.error(`Error occurred in GetPreSignedUrl method. Error: ${error.message}, stackTrace: ${error.stack}`);

    // Check for NotFound status (matching .NET HttpStatusCode.NotFound)
    if (error.statusCode === 404) {
      return ""; // Return empty string (matching .NET string.Empty)
    }

    // Re-throw other exceptions (matching .NET throw)
    throw error;
  }
}

/**
 * Get Pre-Signed URL using credentials from configuration
 * Convenience method that retrieves AWS credentials automatically
 */
export async function getPreSignedUrlWithConfig(
  resource: string,
  keyName: string,
  preSignedUrlRetentionHours: number
): Promise<string> {
  logger.info(`Getting pre-signed URL with config for resource: ${resource}, key: ${keyName}`);

  // Get AWS credentials from configuration (matching .NET StaticConfig pattern)
  const awsCredentials = await getAWSCredentials(resource);

  if (!awsCredentials) {
    const errorMessage = `AWS credentials not found for resource: ${resource}`;
    logger.error(errorMessage);
    throw new Error(errorMessage);
  }

  // Validate credentials
  if (!validateAWSCredentials(awsCredentials)) {
    const errorMessage = `Invalid AWS credentials for resource: ${resource}`;
    logger.error(errorMessage);
    throw new Error(errorMessage);
  }

  // Call the main service with retrieved credentials
  return await getPreSignedUrlService(awsCredentials, keyName, preSignedUrlRetentionHours);
}