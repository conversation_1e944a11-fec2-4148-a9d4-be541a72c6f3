import { model } from "@medusajs/framework/utils"
import { Brand } from "./brand"
import { BrandCategory } from "./brand-category"

export const BrandBrandCategory = model.define("cq_brand_brand_category", {
    id: model.id().primaryKey(),
    brand: model.belongsTo(() => Brand, { mappedBy: 'brand' }),
    brand_category: model.belongsTo(() => BrandCategory, { mappedBy: 'brand_category' }),
    metadata: model.json(),
})
