import { defineMiddlewares } from "@medusajs/framework/http"
import cors from "cors"

export default defineMiddlewares({
  routes: [
    {
      matcher: "/v1/*",
      middlewares: [
        cors({
          origin: ["http://localhost:7001", "https://editor.swagger.io"],
          credentials: true,
          methods: ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"],
          allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With"],
        }),
      ],
    },
  ],
})
