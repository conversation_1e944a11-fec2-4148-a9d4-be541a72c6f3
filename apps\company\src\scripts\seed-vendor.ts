import { ExecArgs } from '@medusajs/framework/types';
import PartnerRegistrationModuleService from '../modules/company/service';
import { COMPANY_REGISTRATION_MODULE } from '../modules/company';
import { Modules } from '@medusajs/framework/utils';
import { masterDB } from '../utils/master-db';

export default async function seedVendor({ container }: ExecArgs) {
	const vendorService: PartnerRegistrationModuleService = container.resolve(
		COMPANY_REGISTRATION_MODULE
	);
	const regionService = container.resolve(Modules.REGION);

	const targetBrands = [
		{ vendor_name: 'Microsoft' },
		{ vendor_name: 'Google' },
	];

	const targetRegionNames = ['India', 'Turkey', 'MEA'];

	// Step 1: Get regions and map names to IDs
	// const allRegions = await regionService.listRegions(); // assumes list() gets all regions

	const { rows: allRegions } = await masterDB.query(
		'SELECT id, name FROM region'
	);

	const regionMap = new Map(
		allRegions.map((r: any) => [r.name.toLowerCase(), r.id])
	);

	const targetRegionIds = targetRegionNames
		.map((name) => regionMap.get(name.toLowerCase()))
		.filter(Boolean); // remove any undefined (not found regions)

	if (targetRegionIds.length !== targetRegionNames.length) {
		console.warn('Some region names were not found in the Region table.');
	}

	// Step 2: Filter out existing vendors
	const existingBrands = await vendorService.listVendors({});

	const existingBrandNames = new Set(
		existingBrands.map((b) => b.vendor_name.toLowerCase())
	);

	const brandsToCreate = targetBrands
		.filter((b) => {
			if (!b.vendor_name) {
				console.warn(`Invalid vendor skipped: ${JSON.stringify(b)}`);
				return false;
			}
			return !existingBrandNames.has(b.vendor_name.toLowerCase());
		})
		.map((b) => ({
			...b,
			metadata: {},
		}));

	let createdCount = 0;

	for (const brand of brandsToCreate) {
		const createdBrand = await vendorService.createVendors(brand);

		await Promise.all(
			targetRegionIds.map((region_id) =>
				vendorService.createRegionVendors({
					vendor_id: createdBrand.id,
					region_id,
					metadata: {},
				})
			)
		);

		createdCount++;
	}

	console.log(`Seeded ${createdCount} vendor(s) with region assignments.`);
}
