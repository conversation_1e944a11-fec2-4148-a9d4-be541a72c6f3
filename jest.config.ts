// default
// import type { Config } from 'jest';
// import { getJestProjectsAsync } from '@nx/jest';

// export default async (): Promise<Config> => ({
//   projects: await getJestProjectsAsync(),
// });

// manual
import { loadEnv } from '@medusajs/utils';
loadEnv('test', process.cwd());

module.exports = {
  transform: {
    '^.+\\.[jt]s$': ['@swc/jest'],
  },
  testEnvironment: 'node',
  moduleFileExtensions: ['js', 'ts', 'json'],
  testMatch: ['**/src/**/__tests__/**/*.unit.spec.[jt]s'],
};
