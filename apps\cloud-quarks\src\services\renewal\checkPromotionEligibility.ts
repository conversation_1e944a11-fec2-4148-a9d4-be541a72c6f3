import logger from "../../utils/logger";
import { getStoreDetails } from "../token/microsoft/getStoreDetailsService";
import { getMsToken } from "../token/microsoft/getTokenService";
import {
  PromotionModel,
  PromotionEligibility,
  PromotionEligibilityRequest,
  EligibilityResponse,
  PromotionEligibilityError
} from "../../types/responses/customResponse";
import { getPromotionsByMaterialId } from "./promotionDAL";
import { checkSkuAvailabilityService } from "./checkSkuAvailabilityService";
import { verifyPromotionEligibilityService } from "./verifyPromotionEligibility";
import { mapPromotionEligibility } from "./mapPromotionEligibilityHelper";

/**
 * Check Promotion Eligibility
 * Matches .NET CheckPromotionEligibility method
 */
export async function checkPromotionEligibilityService(
  request: PromotionModel
): Promise<PromotionEligibility[]> {
  logger.info(`Entered into CheckPromotionEligibility Service method with request ${JSON.stringify(request)}`);

  let model: PromotionEligibility[] = [];

  // Get store details (matching .NET TokenRequest tokenRequest = _storeFactory.GetStoreDetails)
  // Note: We need brandId for this, but it's not available in the PromotionModel
  // This should be passed as a separate parameter in a future refactor
  const tokenRequest = await getStoreDetails({ storeId: request.StoreId });

  // Get token from Microsoft (matching .NET BCommon.GetToken)
  const token = await getMsToken({
    brand: tokenRequest.brand || "",
    client_id: tokenRequest.clientid || "",
    client_secret: tokenRequest.clientsecret || "",
    grant_type: tokenRequest.granttype || "",
    markValue: tokenRequest.markvalue?.toString() || "0",
    redirect_uri: tokenRequest.redirecturi || "http://localhost",
    refresh_token: tokenRequest.token || "",
    resource: tokenRequest.resource || "",
    store_domain: tokenRequest.storedomain || "",
  });

  if (!token || !token.access_token) {
    throw new Error("Failed to generate access token");
  }

  const eligibilityRequest: PromotionEligibilityRequest[] = [];
  const eligibilityRequestWithErrors: PromotionEligibility[] = [];
  let index = 0;

  // Process each promotion eligibility item (matching .NET foreach loop)
  for (const item of request.PromotionEligibility) {
    const peRequest: PromotionEligibilityRequest = {};

    // Split product (matching .NET string[] product = item.MaterialId.Split(':'))
    const product = item.MaterialId?.split(':') || [];

    // Get promotions by material ID (matching .NET GetPromotionsByMaterialId call)
    const promotion = await getPromotionsByMaterialId(
      request.StoreId,
      item.MaterialId || "",
      item.Term || "",
      item.BillType || "",
      item.Segment || ""
    );

    logger.info(`promotion ${JSON.stringify(promotion)}`);

    if (promotion) {
      // Map promotion data to request and item (matching .NET mapping)
      peRequest.PromotionId = item.PromotionId = promotion.PromotionId;
      peRequest.Quantity = item.Quantity;
      peRequest.TermDuration = promotion.Term;
      peRequest.BillingCycle = promotion.BillType;
      peRequest.MaterialId = promotion.MaterialId;
      item.PromotionName = promotion.PromotionName;
      item.PromotionDescription = promotion.PromotionDescription;
      item.PolicyType = promotion.PolicyType;
      item.PromotionValue = promotion.PromotionValue;
      item.StartDate = promotion.StartDate;
      item.EndDate = promotion.EndDate;

      try {
        // Call API to get catalog item ID (matching .NET CheckSkuAvailability call)
        const catalogItemId = await checkSkuAvailabilityService(
          request.CustomerId,
          product[0] || "",
          product[1] || "",
          token.access_token,
          "",
          "",
          item.Segment || ""
        );

        peRequest.CatalogItemId = item.CatalogItemId = catalogItemId;
        logger.info(`CatalogItemId ${item.CatalogItemId}`);

        if (!item.CatalogItemId || item.CatalogItemId.trim() === "") {
          // Product not available (matching .NET string.IsNullOrEmpty check)
          item.Errors = [{ Description: "Product is not available at vendor" } as PromotionEligibilityError];
          item.IsEligible = false;
          eligibilityRequestWithErrors.push(item);
        } else {
          // Add to eligibility request (matching .NET else block)
          peRequest.Id = index;
          eligibilityRequest.push(peRequest);
          index++;
        }
      } catch (error: any) {
        // Handle SKU availability check error
        logger.error(`Error checking SKU availability: ${error.message}`);
        item.Errors = [{ Description: error.message || "Error checking product availability" } as PromotionEligibilityError];
        item.IsEligible = false;
        eligibilityRequestWithErrors.push(item);
      }
    } else {
      // Promotion not available (matching .NET else block)
      item.Errors = [{ Description: "Promotion for this product is not available" } as PromotionEligibilityError];
      item.IsEligible = false;
      eligibilityRequestWithErrors.push(item);
    }
  }

  // Verify promotion eligibility if there are valid requests (matching .NET VerifyPromotionEligibility call)
  let response: EligibilityResponse = { TotalCount: 0, Items: [] };
  if (eligibilityRequest && eligibilityRequest.length > 0) {
    response = await verifyPromotionEligibilityService(
      eligibilityRequest,
      request.CustomerId,
      token.access_token
    );
  }

  // Map promotion eligibility (matching .NET MapPromotionEligiblity call)
  model = mapPromotionEligibility(request, response, eligibilityRequestWithErrors);

  logger.info(`Returning model ${JSON.stringify(model)}`);
  return model;
}