import { z } from "zod";

// Get Provisional Status Data query parameters validation schema
export const GetProvisionalStatusDataQuerySchema = z.object({
  customerId: z.string().min(1, "CustomerId cannot be null or empty"),
  orderId: z.string().min(1, "OrderId cannot be null or empty"),
  token: z.string().min(1, "Token cannot be null or empty"),
});

// Type definitions for TypeScript
export type GetProvisionalStatusDataQueryType = z.infer<typeof GetProvisionalStatusDataQuerySchema>;

// Validation function for query parameters
export function validateGetProvisionalStatusDataQuery(
  customerId: string | undefined,
  orderId: string | undefined,
  token: string | undefined
): string[] {
  const errors: string[] = [];
  
  if (!customerId || customerId.trim() === "") {
    errors.push("The 'customerId' parameter is missing in the request.");
  }
  
  if (!orderId || orderId.trim() === "") {
    errors.push("The 'orderId' parameter is missing in the request.");
  }
  
  if (!token || token.trim() === "") {
    errors.push("The 'token' parameter is missing in the request.");
  }
  
  return errors;
}
