export function validateGetContractRequest(storeId: string, partnerId: string): string[] {
  const errors: string[] = []

  if (!storeId || storeId.trim() === "") {
    errors.push("The 'StoreId' URL parameter is missing in the request.")
  }

  if (!partnerId || partnerId.trim() === "") {
    errors.push("The 'PartnerId' URL parameter is missing in the request.")
  }

  return errors
}

export function validateGetLastContractInfoRequest(subscriptionId: string): string[] {
  const errors: string[] = []

  if (!subscriptionId || subscriptionId.trim() === "") {
    errors.push("The 'subscriptionId' URL parameter is missing in the request.")
  }

  return errors
}