openapi: 3.0.0
info:
  title: Medusa User API
  version: 1.0.0
  description: User API to manage user details.
servers:
  - url: http://localhost:8080
    description: Local development server
paths:
  /v1/user/access-management/manage-permission/:
    get:
      summary: Retrieve list of modules with associated pages and permissions
      tags:
        - Modules
      parameters:
        - name: page
          in: query
          required: false
          schema:
            type: integer
            default: 1
        - name: page_size
          in: query
          required: false
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: Module list retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Role Module fetched successfully
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                          example: 01JZ2EHJSZXZZ1AH946PCCJNR3
                        module:
                          type: string
                          example: Accounts
                        order_id:
                          type: integer
                          example: 2
                        persona_id:
                          type: string
                          example: 01JZ2EHJRX9W4B76RCBRH5985Z
                        persona:
                          type: object
                          properties:
                            id:
                              type: string
                              example: 01JZ2EHJRX9W4B76RCBRH5985Z
                        pages:
                          type: array
                          items:
                            type: object
                            properties:
                              id:
                                type: string
                                example: 01JZ79C7APGQPA3AG8ZWTZP2HR
                              page:
                                type: string
                                example: Company Registration
                              order_id:
                                type: integer
                                example: 1
                              module_id:
                                type: string
                                example: 01JZ2EHJSZXZZ1AH946PCCJNR3
                              permissions:
                                type: array
                                items:
                                  type: object
                                  properties:
                                    id:
                                      type: string
                                      example: 01JZ2EHJR7V21FXE8M5V7RPAZJ
                                    permission:
                                      type: string
                                      example: Edit
                  pagination:
                    type: object
                    properties:
                      page:
                        type: integer
                        example: 1
                      pageSize:
                        type: integer
                        example: 10
                      totalItems:
                        type: integer
                        example: 17
                      totalPages:
                        type: integer
                        example: 2
                      hasNextPage:
                        type: boolean
                        example: true
                      hasPrevPage:
                        type: boolean
                        example: false
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Internal server error occurred
  /v1/user/access-management/roles:
    get:
      summary: Retrieve list of roles with associated pages and permissions
      tags:
        - Roles
      parameters:
        - name: page
          in: query
          required: false
          schema:
            type: integer
            default: 1
        - name: page_size
          in: query
          required: false
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: Roles retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                        role:
                          type: string
                        persona:
                          type: object
                          properties:
                            id:
                              type: string
                            persona:
                              type: string
                        created_at:
                          type: string
                          format: date-time
                  pagination:
                    type: object
                    properties:
                      page:
                        type: integer
                      pageSize:
                        type: integer
                      totalItems:
                        type: integer
                      totalPages:
                        type: integer
                      hasNextPage:
                        type: boolean
                      hasPrevPage:
                        type: boolean
                example:
                  message: Roles fetched successfully
                  data:
                    - id: 01JZ79N8M6K6ZQ7KE1DCRHAPSB
                      role: Admin
                      persona:
                        id: 01JZ2EHJRX9W4B76RCBRH5985Z
                        persona: Partner
                      created_at: '2025-07-03T04:45:30.119Z'
                    - id: 01JZ7C2FCS4GQ2TWC08HNB5N5X
                      role: Admin
                      persona:
                        id: 01JZ2EHJRX9W4B76RCBRH5985Z
                        persona: Partner
                      created_at: '2025-07-03T05:27:40.185Z'
                  pagination:
                    page: 1
                    pageSize: 10
                    totalItems: 2
                    totalPages: 1
                    hasNextPage: false
                    hasPrevPage: false
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Internal server error occurred
    post:
      summary: Create a role with associated pages and permissions
      tags:
        - Roles
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                role:
                  type: string
                  example: Admin
                persona:
                  type: string
                  example: 01JZ2EHJRX9W4B76RCBRH5985Z
                modules:
                  type: array
                  items:
                    type: object
                    properties:
                      pages:
                        type: array
                        items:
                          type: object
                          properties:
                            page_id:
                              type: string
                              example: 01JZ79C7APGQPA3AG8ZWTZP2HR
                            permission_id:
                              type: array
                              items:
                                type: string
                              example:
                                - 01JZ2EHJR7V21FXE8M5V7RPAZJ
      responses:
        '200':
          description: Role created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      id:
                        type: string
                        example: 01JZ7C2FCS4GQ2TWC08HNB5N5X
                      role:
                        type: string
                        example: Admin
                      order_id:
                        type: integer
                        nullable: true
                        example: null
                      metadata:
                        type: object
                        nullable: true
                        example: null
                      persona_id:
                        type: string
                        example: 01JZ2EHJRX9W4B76RCBRH5985Z
                      created_at:
                        type: string
                        format: date-time
                        example: '2025-07-03T05:27:40.185Z'
                      updated_at:
                        type: string
                        format: date-time
                        example: '2025-07-03T05:27:40.185Z'
                      deleted_at:
                        type: string
                        nullable: true
                        example: null
                      pages:
                        type: array
                        items:
                          type: object
                        example: []
                example:
                  message: Roles created successfully
                  data:
                    id: 01JZ7C2FCS4GQ2TWC08HNB5N5X
                    role: Admin
                    order_id: null
                    metadata: null
                    persona_id: 01JZ2EHJRX9W4B76RCBRH5985Z
                    created_at: '2025-07-03T05:27:40.185Z'
                    updated_at: '2025-07-03T05:27:40.185Z'
                    deleted_at: null
                    pages: []
        '400':
          description: Bad request (e.g., permission not found or failed to create)
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: >-
                      Some permissions do not exist. Role and permissions not
                      created.
                  failedPermissions:
                    type: array
                    items:
                      type: string
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Internal server error occurred
    put:
      summary: Update a role and its associated pages and permissions
      tags:
        - Roles
      parameters:
        - name: role_id
          in: query
          required: true
          schema:
            type: string
            example: 01JZ7C2FCS4GQ2TWC08HNB5N5X
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                role:
                  type: string
                  example: Admin
                persona:
                  type: string
                  example: 01JZ2EHJRZAQ6Y9P6W6DTB74QS
                modules:
                  type: array
                  items:
                    type: object
                    properties:
                      pages:
                        type: array
                        items:
                          type: object
                          properties:
                            page_id:
                              type: string
                              example: 01JZ79C7APGQPA3AG8ZWTZP2HR
                            permission_id:
                              type: array
                              items:
                                type: string
                              example:
                                - 01JZ2EHJR7V21FXE8M5V7RPAZJ
      responses:
        '200':
          description: Role updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Roles updated successfully
                  data:
                    type: object
                    properties:
                      id:
                        type: string
                        example: 01JZ7C2FCS4GQ2TWC08HNB5N5X
                      role:
                        type: string
                        example: Admin
                      persona_id:
                        type: string
                        example: 01JZ2EHJRZAQ6Y9P6W6DTB74QS
                      persona:
                        type: object
                        properties:
                          id:
                            type: string
                            example: 01JZ2EHJRZAQ6Y9P6W6DTB74QS
                example:
                  message: Roles updated successfully
                  data:
                    id: 01JZ7C2FCS4GQ2TWC08HNB5N5X
                    role: Admin
                    persona_id: 01JZ2EHJRZAQ6Y9P6W6DTB74QS
                    persona:
                      id: 01JZ2EHJRZAQ6Y9P6W6DTB74QS
        '400':
          description: Bad request (e.g., missing role_id or permission failure)
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: role_id is required in query parameters
                  failedPermissions:
                    type: array
                    items:
                      type: string
        '404':
          description: Role not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Role not found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Internal server error occurred
  /v1/user/accounts/users/{userId}:
    get:
      summary: Get a single user by ID
      tags:
        - Accounts
      parameters:
        - in: path
          name: userId
          schema:
            type: string
          required: true
          description: The unique identifier of the user
      responses:
        '200':
          description: User fetched successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      id:
                        type: string
                      first_name:
                        type: string
                      last_name:
                        type: string
                      email:
                        type: string
                      mobile_number:
                        type: string
                      company_name:
                        type: string
                      terms_and_conditions:
                        type: boolean
                      profile_pic_file_name:
                        type: string
                      cq_designation:
                        type: object
                        properties:
                          id:
                            type: string
                          name:
                            type: string
                          key:
                            type: string
                          metadata:
                            type: object
                            nullable: true
                      country:
                        type: object
                        properties:
                          iso_2:
                            type: string
                          iso_3:
                            type: string
                          num_code:
                            type: string
                          name:
                            type: string
                          display_name:
                            type: string
                          region_id:
                            type: string
                          region:
                            type: object
                            properties:
                              id:
                                type: string
                          metadata:
                            type: object
                            nullable: true
              example:
                data:
                  id: '005'
                  first_name: Indhu
                  last_name: I
                  email: <EMAIL>
                  mobile_number: '9874865221'
                  company_name: yuv company
                  terms_and_conditions: true
                  profile_pic_file_name: profile459.jpg
                  cq_designation:
                    id: 01JZ2RGD8VQXZ7N2C9KJQ0VZHZ
                    name: CEO
                    key: ceo
                    metadata: {}
                  country:
                    iso_2: in
                    iso_3: ind
                    num_code: '356'
                    name: INDIA
                    display_name: India
                    region_id: reg_01JZ2RG968V8N98E6FRTEW0AAT
                    region:
                      id: reg_01JZ2RG968V8N98E6FRTEW0AAT
                    metadata: null
        '404':
          description: User not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: User not found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Internal server error
  /v1/user/accounts/users:
    get:
      summary: Get a list of users with advanced filters and optional Excel download
      tags:
        - Accounts
      parameters:
        - in: query
          name: source
          schema:
            type: string
          required: false
          description: Filter by user source (e.g., Direct End Customer)
        - in: query
          name: country_id
          schema:
            type: string
          required: false
          description: Filter by country ISO2 code
        - in: query
          name: company_type
          schema:
            type: string
            enum:
              - Guest User
              - Onboarded User
          required: false
          description: Filter by company type
        - in: query
          name: q
          schema:
            type: string
          required: false
          description: Search by first or last name (partial match)
        - in: query
          name: start_date
          schema:
            type: string
            format: date
          required: false
          description: Filter users created on or after this date (YYYY-MM-DD)
        - in: query
          name: end_date
          schema:
            type: string
            format: date
          required: false
          description: Filter users created on or before this date (YYYY-MM-DD)
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          required: false
          description: Page number for pagination
        - in: query
          name: page_size
          schema:
            type: integer
            default: 10
          required: false
          description: Number of items per page
        - in: query
          name: download
          schema:
            type: string
            enum:
              - excel
          required: false
          description: If 'excel', returns user data as a downloadable Excel file
      responses:
        '200':
          description: List of users fetched successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                          example: u123
                        first_name:
                          type: string
                          example: Indhira
                        last_name:
                          type: string
                          example: Doe
                        email:
                          type: string
                          example: <EMAIL>
                        company_type:
                          type: string
                          example: Direct End Customer
                        is_onboarded:
                          type: boolean
                          example: true
                        is_active:
                          type: boolean
                          example: true
                        country:
                          type: object
                          properties:
                            display_name:
                              type: string
                              example: Andorra
                  pagination:
                    type: object
                    properties:
                      page:
                        type: integer
                        example: 1
                      pageSize:
                        type: integer
                        example: 10
                      totalItems:
                        type: integer
                        example: 125
                      totalPages:
                        type: integer
                        example: 13
                      hasNextPage:
                        type: boolean
                        example: true
                      hasPrevPage:
                        type: boolean
                        example: false
        '404':
          description: No users found for the specified filters
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Users for the specified country not found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Internal server error
  /v1/user/change-password:
    post:
      summary: Change password for the authenticated guest user
      tags:
        - Users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - oldPassword
                - newPassword
              properties:
                oldPassword:
                  type: string
                  description: The current password of the user
                  example: OldPass123!
                newPassword:
                  type: string
                  description: >
                    The new password must be 8–12 characters long and must
                    contain: - At least one uppercase letter - At least one
                    number - At least one special character
                  example: NewPass456!
                  minLength: 8
                  maxLength: 12
      responses:
        '200':
          description: Password updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Password updated successfully
        '400':
          description: Invalid old password or new password does not meet criteria
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Old password and new password must not be the same
        '500':
          description: Server error while updating password
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Internal server error occurred
  /v1/user/forgot-password:
    post:
      summary: Request password reset link for user
      tags:
        - Forgot Password
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
              properties:
                email:
                  type: string
                  format: email
                  example: <EMAIL>
      responses:
        '200':
          description: Password reset link sent successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Password reset link has been sent to your email.
        '404':
          description: User not found with the provided email
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: User with this email not found
        '500':
          description: Error sending reset email or internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Error sending reset email
  /v1/user/update-profile:
    put:
      summary: Update an existing guest user
      tags:
        - Users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - id
                - first_name
                - last_name
                - designation
                - mobile_number
                - email
                - company_name
                - country_code
              properties:
                id:
                  type: string
                  example: 9
                first_name:
                  type: string
                  maxLength: 50
                  pattern: ^[A-Za-z0-9\s'-]+$
                  description: >-
                    First name may include letters, numbers, spaces, hyphens,
                    and apostrophes
                  example: John
                last_name:
                  type: string
                  maxLength: 50
                  pattern: ^[A-Za-z0-9\s'-]+$
                  description: >-
                    Last name may include letters, numbers, spaces, hyphens, and
                    apostrophes
                  example: Doe
                designation:
                  type: string
                  maxLength: 100
                  pattern: ^[A-Za-z0-9\s\-/]+$
                  description: >-
                    Designation may include letters, numbers, spaces, hyphens,
                    and slashes
                  example: 01JVMA9GDP69MRQ3VFZMR19C8W
                mobile_number:
                  type: string
                  maxLength: 15
                  pattern: >-
                    ^[+]?[0-9]{1,4}[-\s]?[(]?[0-9]{1,4}[)]?[-\s]?[0-9]+[-\s]?[0-9]+$
                  description: Must be a valid international format (e.g., +919876543210)
                  example: +91-9876543210
                email:
                  type: string
                  format: email
                  maxLength: 100
                  example: <EMAIL>
                company_name:
                  type: string
                  maxLength: 100
                  pattern: ^[A-Za-z0-9\s\-&]+$
                  description: >-
                    Company name may include letters, numbers, spaces, hyphens,
                    and ampersands
                  example: Acme & Sons
                country_code:
                  type: string
                  example: US
      responses:
        '200':
          description: Guest user updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Guest user updated successfully
        '400':
          description: Email or mobile number not verified
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Email or mobile number not verified
        '404':
          description: Guest user or related data not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Country not found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Internal server error occurred
  /v1/user/store:
    get:
      summary: Get a list of stores filtered by region
      tags:
        - Stores
      parameters:
        - in: query
          name: region_id
          schema:
            type: string
          required: true
          description: Region ID to filter stores by associated countries
      responses:
        '200':
          description: List of stores fetched successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                          example: '001'
                        name:
                          type: string
                          example: Test Store
              example:
                data:
                  - id: '001'
                    name: Test Store
        '404':
          description: Region not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Region not found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Internal server error
  /v1/user/update-password/{token}:
    put:
      summary: Update the password using token
      tags:
        - Update Password
      parameters:
        - in: path
          name: token
          required: true
          schema:
            type: string
          description: JWT token containing the email for verification
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - password
              properties:
                password:
                  type: string
                  format: password
                  example: NewStrongPassword123!
      responses:
        '200':
          description: Password set successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                  message:
                    type: string
                    example: Password updated successfully
        '400':
          description: Invalid token or missing password
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Invalid token
        '404':
          description: Guest user not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: User not found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Internal server error. Please try again later.
  /v1/user/user-preference-column-list:
    post:
      summary: Get user-specific column preferences for a table
      tags:
        - User Preferences
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - table_master_id
              properties:
                table_master_id:
                  type: string
                  example: tbl_abc123
                  description: The ID of the table for which to fetch columns
      responses:
        '200':
          description: Successfully fetched user column preferences
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: User column preference fetched successfully
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        column_master_id:
                          type: string
                          example: col_456
                        table_master_id:
                          type: string
                          example: tbl_abc123
                        column_name:
                          type: string
                          example: Customer Name
                        table_name:
                          type: string
                          example: customers
                        column_slug:
                          type: string
                          example: customer_name
                        column_type:
                          type: string
                          example: string
                        sort_order:
                          type: integer
                          example: 1
                        is_selected:
                          type: boolean
                          example: true
        '404':
          description: Table or Columns not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Table master not found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Internal server error
  /v1/user/user-preference-column:
    post:
      summary: Update user-specific column preferences for a table
      tags:
        - User Preferences
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - table_master_id
                - column_details
              properties:
                table_master_id:
                  type: string
                  example: tbl_abc123
                  description: >-
                    The ID of the table whose column preferences are being
                    updated
                column_details:
                  type: array
                  description: Array of column preference objects
                  items:
                    type: object
                    required:
                      - column_master_id
                      - sort_order
                    properties:
                      column_master_id:
                        type: string
                        example: col_xyz789
                        description: The ID of the column
                      sort_order:
                        type: integer
                        example: 2
                        description: The order in which the column should appear
      responses:
        '200':
          description: User column preferences updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: User column preferences updated successfully.
        '404':
          description: Table or columns not found or invalid
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: >-
                      Some column_master_ids are not associated with the
                      specified table_master_id.
                  invalid_column_ids:
                    type: array
                    items:
                      type: string
                    example:
                      - col_invalid_1
                      - col_invalid_2
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Internal server error while updating preferences.
components: {}
tags: []
