import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import { getDocuSignAccessTokenService } from "../../../../services/index";

/**
 * @openapi
 * /v1/authentication/getDocuSignAccessToken:
 *   get:
 *     summary: Get DocuSign access token using JWT authentication
 *     tags:
 *       - Authentication
 *     responses:
 *       200:
 *         description: DocuSign access token retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 access_token:
 *                   type: string
 *                   example: eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
 *                 token_type:
 *                   type: string
 *                   example: Bearer
 *                 expires_in:
 *                   type: number
 *                   example: 3600
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Internal server error
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const correlationId = getCorrelationId(req);

    logger.info(
      `Get DocuSign Access Token API called | CorrelationId: ${correlationId}`
    );

    // Call DocuSign token service
    const tokenResponse = await getDocuSignAccessTokenService(req);

    if (tokenResponse.success && tokenResponse.data) {
      logger.info(
        `Successfully retrieved DocuSign token | CorrelationId: ${correlationId}`
      );
      return res.status(200).json({
        status: "success",
        access_token: tokenResponse.data.access_token,
        token_type: tokenResponse.data.token_type,
        expires_in: tokenResponse.data.expires_in,
      });
    } else {
      logger.error(
        `Failed to retrieve DocuSign token | CorrelationId: ${correlationId} | Error: ${tokenResponse.error}`
      );
      return res.status(500).json({
        status: false,
        message: tokenResponse.error || "Failed to retrieve DocuSign access token",
      });
    }
  } catch (err) {
    logger.error("Get DocuSign Access Token error: ", err as Error);
    return res.status(500).json({
      status: false,
      message: "Internal server error",
    });
  }
}