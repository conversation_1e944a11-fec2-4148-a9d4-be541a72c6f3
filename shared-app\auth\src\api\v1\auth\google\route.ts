import {
    MedusaRequest,
    MedusaResponse,
} from '@medusajs/framework';

const { COGNITO_DOMAIN, COGNITO_CLIENT_ID, REDIRECT_URI } = process.env;

// /**
//  * @openapi
//  * /cognito/google:
//  *   get:
//  *     summary: Redirects to Google authentication via AWS Cognito
//  *     tags:
//  *       - Auth
//  *     description: Redirects the user to the Google login page using AWS Cognito's hosted UI.
//  *     responses:
//  *       302:
//  *         description: Redirects to Google login via Cognito
//  *       404:
//  *         description: Missing required environment variables
//  *         content:
//  *           application/json:
//  *             schema:
//  *               type: object
//  *               properties:
//  *                 message:
//  *                   type: string
//  *                   example: Missing required environment variables.
//  *       500:
//  *         description: Server error during authentication initiation
//  *         content:
//  *           application/json:
//  *             schema:
//  *               type: object
//  *               properties:
//  *                 message:
//  *                   type: string
//  *                   example: An error occurred while starting authentication.
//  */

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
    if (!COGNITO_DOMAIN || !COGNITO_CLIENT_ID || !REDIRECT_URI) {
        res.status(404).json({ message: "Missing required environment variables." });
        return;
    };
    try {
        const scopes = encodeURIComponent('openid email profile');
        const loginUrl = `${COGNITO_DOMAIN}/oauth2/authorize?identity_provider=Google&response_type=CODE&client_id=${COGNITO_CLIENT_ID}&redirect_uri=${encodeURIComponent(REDIRECT_URI)}&scope=${scopes}`;
        res.redirect(loginUrl);
    } catch (error) {
        res.status(500).json({ message: "An error occurred while starting authentication." });
    }
}