import { MedusaRequest, MedusaResponse } from '@medusajs/framework'
import { getCustomerValidationStatusService } from '../../../../services/index'
import logger from "../../../../utils/logger";

/**
 * @openapi
 * /v1/customer/getCustomerValidationStatus:
 *   get:
 *     summary: Get Customer Validation Status
 *     tags:
 *       - Customer
 *     parameters:
 *       - in: query
 *         name: customerId
 *         schema:
 *           type: string
 *         required: true
 *         description: Customer ID
 *       - in: query
 *         name: storeId
 *         schema:
 *           type: string
 *         required: true
 *         description: Store ID
 *     responses:
 *       200:
 *         description: Successfully fetched validation status
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Customer is valid
 *                 data:
 *                   type: object
 *                   example:
 *                     customerId: "cust-123"
 *                     isValid: true
 *       400:
 *         description: Bad request (Missing or invalid input)
 *       500:
 *         description: Internal server error
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
    try {
        logger.info(`Get Customer Validation Status API is Hit`);
        const { customerId, storeId } = req.query

        const result = await getCustomerValidationStatusService(
            req,
            customerId as string,
            storeId as string
        )

        return res.status(result.status ? 200 : 400).json(result)
    } catch (err) {
        logger.error("Get Customer Validation Status error: ", err as Error);
        return res.status(500).json({
            status: false,
            message: "Internal server error"
        })
    }
}
