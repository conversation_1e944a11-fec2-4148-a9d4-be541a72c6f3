export type ExportedRequest = {
	id: string;
	companyId: string;
	country: string;
	panNo: string;
	companyName: string;
	businessId: string;
	directorName: string;
	directorPhone: string;
	salesName: string;
	salesPhone: string;
	userEmail: string;
	userFirstName: string;
	userLastName: string;
};

export type ExportedUserRequest = {
	id: string;
	created_at: string;
	first_name: string;
	last_name: string;
	company_type: string;
	email: string;
	store: string;
	company_name: string;
	role: string;
	country: string;
	user_type: string;
	status: string;
};

export type ExportedCompanyRegistrationRequest = {
	id: string;
	first_name: string;
	last_name: string;
	company_type: string;
	email: string;
	store: string;
	company_name: string;
	redington_account_id: number;
	role: string;
	country: string;
	requested_by_admin: boolean;
	status: string;
};

export type ExportedCompanyOnboardingRequest = {
	id: string;
	first_name: string;
	last_name: string;
	company_type: string;
	email: string;
	store: string;
	company_name: string;
	redington_account_id: number;
	role: string;
	country: string;
	requested_by_admin: boolean;
	requested_source: string;
	status: string;
};

export type SourceType = {
	partner: 'Partner';
	directEndCustomer: 'Direct End Customer';
};

export const allowedSources = ['partner', 'directEndCustomer'] as const;
export type SourceKey = keyof SourceType;
