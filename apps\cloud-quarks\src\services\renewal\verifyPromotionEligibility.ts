import logger from "../../utils/logger";
import axios from "axios";
import { MicrosoftEndpoints } from "../../config/microsoftEndpoints";
import {
  PromotionEligibilityRequest,
  EligibilityResponse,
  ResponseError
} from "../../types/responses/customResponse";

/**
 * Verify Promotion Eligibility
 * Matches .NET VerifyPromotionEligibility method
 */
export async function verifyPromotionEligibilityService(
  eligibilityRequest: PromotionEligibilityRequest[],
  customerId: string,
  token: string,
  orderId?: string,
  partnerId?: string,
  storeId?: string
): Promise<EligibilityResponse> {
  logger.info(`Entered into VerifyPromotionEligibility Service method with eligibilityRequest ${JSON.stringify(eligibilityRequest)},customerId ${customerId},orderId ${orderId},partnerId ${partnerId},storeId ${storeId}`);

  let eligibilityResponse: EligibilityResponse;
  const requestModel = { items: eligibilityRequest };

  // Get endpoint URL (matching .NET endpoint construction)
  const endPoint = MicrosoftEndpoints.verifyPromotionEligibilityUrl(customerId);

  try {
    // Prepare headers (matching .NET HttpClient.DefaultRequestHeaders.Authorization)
    const headers = {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
      Accept: "application/json"
    };

    // Log trace request (matching .NET logging)
    logger.info(`Request initiated to VerifyPromotionEligibility ${endPoint},requestModel ${JSON.stringify(requestModel)}`);

    // Send request (matching .NET client.PostAsync)
    const response = await axios.post(endPoint, requestModel, {
      headers,
      validateStatus: () => true // Allow all status codes to handle errors manually
    });

    // Log trace response (matching .NET logging)
    logger.info(`Response from VerifyPromotionEligibility ${endPoint},response ${JSON.stringify(response.data)}`);

    if (response.status >= 200 && response.status < 300) {
      // Parse the response body (matching .NET response.IsSuccessStatusCode)
      eligibilityResponse = response.data as EligibilityResponse;
    } else {
      // Handle error response (matching .NET else block)
      const error = response.data as ResponseError;
      logger.info(`Throwing exception ${error.description}`);
      throw new Error(error.description || `HTTP ${response.status}: ${response.statusText}`);
    }

    logger.info(`Returning response ${JSON.stringify(eligibilityResponse)}`);
    return eligibilityResponse;

  } catch (error: any) {
    logger.error(`Error in verifyPromotionEligibilityService: ${error.message}`);

    // If it's an axios error, try to extract the error details
    if (axios.isAxiosError(error) && error.response?.data) {
      const errorData = error.response.data as ResponseError;
      throw new Error(errorData.description || error.message);
    }

    throw error;
  }
}