import {
  MedusaRequest,
  MedusaResponse,
  AuthenticatedMedusaRequest,
} from '@medusajs/framework/http';
import CQUserService from '../../../../modules/cq-user/service';
import { CQUser_MODULE } from '../../../../modules/cq-user';
import { generateJwtToken, Modules } from '@medusajs/framework/utils';
import createGuestUserWorkflow from '../../../../workflows/create-guest-user';
import { MyNotificationProviderService } from '@org/ses';
import { logger } from '@medusajs/framework';
import { IRegionModuleService } from '@medusajs/framework/types';
import { GuestUserSchema } from './validators';
import { z } from 'zod';
import {
  stringConstants,
  sendTransactionalEmail,
  renderResetPasswordEmail,
} from '@org/utils';
import { SourceKey, allowedSources } from '@org/excel';

type GuestUserSchemaType = z.infer<typeof GuestUserSchema>;

/**
 * @openapi
 * /v1/user/signup:
 *   post:
 *     summary: Register a new guest user
 *     tags:
 *       - Signup
 *     parameters:
 *       - in: query
 *         name: source
 *         required: true
 *         schema:
 *           type: string
 *         description: The source of the guest user (e.g., partner or directEndCustomer)
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - country_code
 *               - mobile_number
 *               - designation
 *               - region_id
 *               - registered_country_id
 *               - terms_and_conditions
 *             properties:
 *               first_name:
 *                 type: string
 *               last_name:
 *                 type: string
 *               designation:
 *                 type: string
 *               mobile_number:
 *                 type: string
 *               country_code:
 *                 type: string
 *               email:
 *                 type: string
 *                 format: email
 *               company_name:
 *                 type: string
 *               has_sales_reference:
 *                 type: boolean
 *               registered_country_id:
 *                 type: string
 *               profile_pic_file_name:
 *                 type: string
 *               redington_sales_reference:
 *                 type: string
 *               terms_and_conditions:
 *                 type: boolean
 *               region_id:
 *                 type: string
 *             example:
 *               first_name: "Guest"
 *               last_name: "User"
 *               designation: "01JZ2RGD8VQXZ7N2C9KJQ0VZHZ"
 *               mobile_number: "78541236985"
 *               country_code: "+91"
 *               email: "<EMAIL>"
 *               company_name: "Test Company"
 *               has_sales_reference: false
 *               registered_country_id: "in"
 *               profile_pic_file_name: "profile.jpg"
 *               redington_sales_reference: "RSR1234"
 *               terms_and_conditions: true
 *               region_id: "01JZ2RGD8VQXZ7N2C9KJQ0VZHZ"
 *     responses:
 *       200:
 *         description: Guest user registered successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Guest user registered successfully
 *       400:
 *         description: Invalid input, missing verification, or user already exists
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Email or mobile number not verified
 *       404:
 *         description: Designation or country not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Designation not found
 *       500:
 *         description: Server error during registration or email sending
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error
 */

export async function POST(
  req: AuthenticatedMedusaRequest<GuestUserSchemaType>,
  res: MedusaResponse
) {
  const rawSource = req.query.source;

  // Extract and validate source key
  const source = rawSource
    ? String(Array.isArray(rawSource) ? rawSource[0] : rawSource)
    : undefined;

  if (!source || !allowedSources.includes(source as SourceKey)) {
    return res.status(400).json({
      message: `Invalid source. Must be one of: ${allowedSources.join(', ')}`,
    });
  }

  const guestUserService: CQUserService = req.scope.resolve('cq_user');
  const regionModuleService: IRegionModuleService = req.scope.resolve(
    Modules.REGION
  );

  const {
    email: userEmail,
    mobile_number: userMobileNumber,
    designation: userDesignation,
    registered_country_id: userCountryCode,
    has_sales_reference,
  } = req.body;

  if (!has_sales_reference) {
    req.body.redington_sales_reference = undefined;
  }

  try {
    // 1. Check if user already exists
    const [users] = await guestUserService.listAndCountCqUsers({
      email: userEmail.toLowerCase().trim(),
    });
    if (users.length > 0) {
      return res.status(400).json({
        message: stringConstants.ALREADY_EXISTS('User'),
      });
    }

    // 2. Check OTP verifications
    const [emailVerified, mobileVerified] = await Promise.all([
      guestUserService.listAndCountEmailOtpVerifications({
        email: userEmail.toLowerCase().trim(),
        is_verified: true,
      }),
      guestUserService.listAndCountEmailOtpVerifications({
        mobile_number: userMobileNumber,
        is_verified: true,
      }),
    ]);

    if (!emailVerified?.length || !mobileVerified?.length) {
      return res.status(400).json({
        message: 'Email or mobile number not verified',
      });
    }

    // 3. Validate designation and country
    const [designationCheck, countryCheck] = await Promise.allSettled([
      guestUserService.retrieveDesignation(userDesignation),
      regionModuleService.retrieveCountry(userCountryCode),
    ]);

    if (designationCheck.status === 'rejected') {
      return res.status(404).json({
        message: stringConstants.NOT_FOUND('Designation'),
      });
    }
    if (countryCheck.status === 'rejected') {
      return res.status(404).json({
        message: stringConstants.NOT_FOUND('Country'),
      });
    }

    // 4. Create guest user with source key
    const { result } = await createGuestUserWorkflow(req.scope).run({
      input: {
        guestUser: { ...req.body, source },
        authIdentityId: req.auth_context?.auth_identity_id,
        country_id: userCountryCode,
      },
    });

    // 5. Mark email/mobile as registered
    await guestUserService.updateEmailOtpVerifications([
      {
        selector: { email: userEmail.toLowerCase().trim() },
        data: { is_registered: true },
      },
      {
        selector: { mobile_number: userMobileNumber },
        data: { is_registered: true },
      },
    ]);

    // 6. Map persona
    const personas = (await guestUserService.listCqPersonas?.({})) || [];
    const matchedPersona = personas.find(
      (p) => p.key?.toLowerCase() === source
    );
    if (matchedPersona) {
      await guestUserService.createCqUserPersonas({
        user_id: result.id,
        persona_id: matchedPersona.id,
      });
    } else {
      logger.warn(`Persona with key "${source}" not found.`);
    }

    // 7. Send welcome email
    sendEmailInBackground(
      userEmail.toLowerCase().trim(),
      result.first_name,
      result.last_name
    );

    return res.status(200).json({
      message: stringConstants.REGISTERED_SUCCESSFULLY('Guest user'),
    });
  } catch (error: any) {
    return res.status(500).json({
      message: stringConstants.INTERNAL_SERVER_ERROR('Server'),
    });
  }
}

async function sendEmailInBackground(
  email: string,
  firstName: string,
  lastName: string
) {
  try {
    const token = generateJwtToken(
      { email },
      {
        secret: process.env.JWT_SECRET as string,
        expiresIn: '1h',
      }
    );
    const resetLink = `${process.env.FRONTEND_URL}/signup/set-password/${token}/?name=${firstName} ${lastName}`;
    const htmlBody = await renderResetPasswordEmail(
      `${firstName} ${lastName}`,
      resetLink
    );
    await sendTransactionalEmail({
      toEmail: email,
      subjectTemplate: 'Welcome to Cloudquarks! Set Your Password',
      htmlContentTemplate: htmlBody,
      senderEmail: '<EMAIL>',
      senderName: 'Redington',
    });
  } catch (err) {
    logger.warn(`Failed to send welcome email to ${email}:`);
  }
}
