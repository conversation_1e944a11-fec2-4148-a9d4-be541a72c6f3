import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import { checkSubscriptionStatusService } from "./checkSubscriptionStatusService";
import { registerSubscriptionStatusService } from "./registerSubscriptionStatusService";
import { updateSubscriptionStatusDAL } from "./updateSubscriptionStatusDAL";

/**
 * Update Subscription Iteration with retry logic
 * Matches .NET updateSubscriptionIteration method
 */
export async function updateSubscriptionIterationService(
  req: MedusaRequest,
  customerId: string,
  subscriptionId: string,
  token: string,
  retryCount: number,
  totalRetry: number
): Promise<string> {
  logger.info(`Entered into updateSubscriptionIteration Service method with CustomerId ${customerId},SubscriptionId ${subscriptionId},retryCount ${retryCount}, totalRetry ${totalRetry}`);

  try {
    // Convert subscriptionId to lowercase (matching .NET: SubscriptionId = SubscriptionId?.ToLower())
    const lowerSubscriptionId = subscriptionId?.toLowerCase();

    // Check subscription status (matching .NET: CheckSubscriptionStatus(CustomerId, SubscriptionId, token))
    const response = await checkSubscriptionStatusService(req, customerId, lowerSubscriptionId, token);

    if (response !== null) {
      // Check if status is NOTREGISTERED (matching .NET: response.Status.ToUpper() == "NOTREGISTERED")
      if (response.Status?.toUpperCase() === "NOTREGISTERED") {
        if (retryCount <= totalRetry) {
          // Log trace request (matching .NET logging)
          logger.info(`Request with retryCount:${retryCount} to RegisterSubscriptionStatus(CustomerId:${customerId},SubscriptionId:${lowerSubscriptionId} and going to call RegisterSubscriptionStatus and updateSubscriptionIteration`);

          // Register subscription status (matching .NET: RegisterSubscriptionStatus(CustomerId, SubscriptionId, token))
          await registerSubscriptionStatusService(req, customerId, lowerSubscriptionId, token);

          // Recursive call with incremented retry count (matching .NET: updateSubscriptionIteration(CustomerId, SubscriptionId, token, ++retryCount, totalRetry))
          return await updateSubscriptionIterationService(req, customerId, lowerSubscriptionId, token, retryCount + 1, totalRetry);
        } else {
          // Return error message when retry limit exceeded (matching .NET error message)
          logger.info(`Returning error - We are experiencing delays from vendor in Subscription Registration. Kindly try after after some time`);
          return "We are experiencing delays from vendor in Subscription Registration. Kindly try after after some time";
        }
      } else {
        // Subscription is registered, update status in database (matching .NET: UpdateSubscriptionStatus(SubscriptionId))
        await updateSubscriptionStatusDAL(lowerSubscriptionId);
        
        logger.info(`Returning REGISTERED`);
        return "REGISTERED";
      }
    } else {
      // Response is null, retry if within limit (matching .NET null response handling)
      if (retryCount <= totalRetry) {
        return await updateSubscriptionIterationService(req, customerId, lowerSubscriptionId, token, retryCount + 1, totalRetry);
      } else {
        // Return error message if retry limit exceeded (matching .NET error message)
        logger.info(`Returning error - Unable to find the subscription on Microsoft`);
        return "Unable to find the subscription on Microsoft";
      }
    }
  } catch (error: any) {
    logger.error(`Error in updateSubscriptionIterationService: ${error.message}`);
    
    // Retry on error if within limit, otherwise return error message
    if (retryCount <= totalRetry) {
      logger.info(`Error occurred, retrying with retryCount: ${retryCount + 1}`);
      return await updateSubscriptionIterationService(req, customerId, subscriptionId, token, retryCount + 1, totalRetry);
    } else {
      logger.info(`Returning error - Unable to find the subscription on Microsoft`);
      return "Unable to find the subscription on Microsoft";
    }
  }
}
