import logger from "../../utils/logger";
import {
  PromotionModel,
  PromotionEligibility,
  EligibilityResponse,
  PromotionEligibilityError
} from "../../types/responses/customResponse";

/**
 * Map Promotion Eligibility
 * Matches .NET MapPromotionEligiblity method
 */
export function mapPromotionEligibility(
  request: PromotionModel,
  response: EligibilityResponse,
  eligibilityRequestWithErrors: PromotionEligibility[]
): PromotionEligibility[] {
  logger.info(`Entered into MapPromotionEligiblity method`);

  const model: PromotionEligibility[] = [];

  // Add items with errors first (matching .NET logic)
  model.push(...eligibilityRequestWithErrors);

  // Process successful response items
  if (response && response.Items) {
    for (const responseItem of response.Items) {
      // Find corresponding request item by Id
      const requestItem = request.PromotionEligibility.find(
        (item, index) => index === responseItem.Id
      );

      if (requestItem && responseItem.Eligibilities && responseItem.Eligibilities.length > 0) {
        const eligibility = responseItem.Eligibilities[0]; // Take first eligibility

        // Map the response to the request item (matching .NET mapping)
        requestItem.IsEligible = eligibility.IsEligible;
        
        // Map errors if any
        if (eligibility.Errors && eligibility.Errors.length > 0) {
          requestItem.Errors = eligibility.Errors.map(error => ({
            MinimumRequiredSeats: error.MinimumRequiredSeats || 0,
            MaximumRequiredSeats: error.MaximumRequiredSeats || 0,
            AvailableSeats: error.AvailableSeats || 0,
            Type: error.Type || "",
            Description: error.Description || ""
          } as PromotionEligibilityError));
        } else {
          requestItem.Errors = [];
        }

        model.push(requestItem);
      }
    }
  }

  logger.info(`Returning mapped model with ${model.length} items`);
  return model;
}
