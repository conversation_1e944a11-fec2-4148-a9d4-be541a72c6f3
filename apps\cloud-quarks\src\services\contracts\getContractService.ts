import logger from "../../utils/logger";
import { getSqlServerConnection } from "../../utils/sqlServerClient";

/**
 * Transform database response to match .NET response format exactly
 * Converts date formats and ensures Pascal case keys
 */
function transformContractResponse(dbResponse: any[]): GetContractResponse[] {
  return dbResponse.map(item => ({
    ContractId: item.ContractId || "",
    OrderId: item.OrderId || "",
    SubscriptionId: item.SubscriptionId || "",
    MaterialId: item.MaterialId || "",
    MaterialNo: item.MaterialNo || "",
    MaterialDesc: item.MaterialDesc || "",
    Qty: item.Qty || 0,
    MaterialType: item.MaterialType || "",
    PlanType: item.PlanType || "",
    StartDate: formatDateToDDMMYYYY(item.StartDate),
    EndDate: formatDateToDDMMYYYY(item.EndDate),
    PartnerCompany: item.PartnerCompany || "",
    CustomerCompany: item.CustomerCompany || "",
    ContractValue: item.ContractValue || 0,
    Status: item.Status || "",
    Brand: item.Brand || "",
    ProvType: item.ProvType || "",
    CreateDate: formatDateToDDMMYYYY(item.CreateDate),
    ListPrice: item.ListPrice || 0,
    UnitPrice: item.UnitPrice || 0,
    StatusReason: item.StatusReason || "",
    Segment: item.Segment || "",
    Term: item.Term || "",
    PaymentTermsCode: item.PaymentTermsCode || "",
    PaymentTerms: item.PaymentTerms || ""
  }));
}

/**
 * Format date to DD/MM/YYYY format to match .NET response
 */
function formatDateToDDMMYYYY(dateValue: any): string {
  if (!dateValue) return "";

  try {
    const date = new Date(dateValue);
    if (isNaN(date.getTime())) return "";

    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
  } catch (error) {
    logger.error(`Error formatting date: ${dateValue}`, error);
    return "";
  }
}

interface GetContractResponse {
  ContractId: string;
  OrderId: string;
  SubscriptionId: string;
  MaterialId: string;
  MaterialNo: string;
  MaterialDesc: string;
  Qty: number;
  MaterialType: string;
  PlanType: string;
  StartDate: string; // Format: "29/07/2024" (DD/MM/YYYY)
  EndDate: string; // Format: "28/07/2025" (DD/MM/YYYY)
  PartnerCompany: string;
  CustomerCompany: string;
  ContractValue: number;
  Status: string;
  Brand: string;
  ProvType: string;
  CreateDate: string; // Format: "28/07/2025" (DD/MM/YYYY)
  ListPrice: number;
  UnitPrice: number;
  StatusReason: string;
  Segment: string;
  Term: string;
  PaymentTermsCode: string;
  PaymentTerms: string;
}

export async function getContractService(
  storeId: string,
  partnerId: string,
  expiryInDays: string
): Promise<GetContractResponse[]> {
  try {
    logger.info(
      `Entered into GetContract method and going to hit DAL with storeId-${storeId}, partnerId-${partnerId}, expiryInDays-${expiryInDays}`
    );

    const response = await GetContract(storeId, partnerId, expiryInDays);

    logger.info(`Response from DAL GetContract: ${JSON.stringify(response)}`);

    return response;
  } catch (error) {
    logger.error(`GetContract Error :`);
    throw error;
  }
}

export async function GetContract(
  storeId: string,
  partnerId: string,
  expiryInDays: string
): Promise<GetContractResponse[]> {
  logger.info(
    `Entered into Contract GetContract method with storeId-${storeId}, partnerId-${partnerId}, expiryInDays-${expiryInDays}`
  );
  const pool = await getSqlServerConnection();
  const request = pool.request();

  request.input("iStoreId", storeId);
  request.input("iPartnerId", partnerId);
  request.input("iExpiryInDays", expiryInDays);

  logger.info(
    `Going to hit DB with proc name: spGetContractsToPortal and parameters: storeId=${storeId}, partnerId=${partnerId}, expiryInDays=${expiryInDays}`
  );

  const result = await request.execute<GetContractResponse>(
    "spGetContractsToPortal"
  );
  const dbResponse = result.recordset || [];

  logger.info(
    `Raw response from spGetContractsToPortal: ${JSON.stringify(dbResponse)}`
  );

  // Transform response to match .NET format exactly
  const transformedResponse = transformContractResponse(dbResponse);

  logger.info(
    `Transformed response matching .NET format: ${JSON.stringify(transformedResponse)}`
  );

  return transformedResponse;
}
