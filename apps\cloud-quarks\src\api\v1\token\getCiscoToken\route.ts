import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import { getCiscoTokenService } from "../../../../services/index";

/**
 * @openapi
 * /v1/token/getCiscoToken:
 *   get:
 *     summary: Retrieve Cisco Access Token
 *     operationId: getCiscoToken
 *     description: |
 *       Returns a Cisco access token for a given storeId. This token is typically used to interact with Cisco's APIs.
 *     tags:
 *       - Token
 *     parameters:
 *       - in: query
 *         name: storeId
 *         schema:
 *           type: string
 *         required: true
 *         description: The ID of the store requesting the Cisco token.
 *     responses:
 *       200:
 *         description: Cisco token retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 access_token:
 *                   type: string
 *                 token_type:
 *                   type: string
 *                 expires_in:
 *                   type: number
 *                 scope:
 *                   type: string
 *       400:
 *         description: Missing or invalid storeId
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: storeId cannot be null or empty
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 error:
 *                   type: string
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { storeId } = req.query as { storeId?: string };
    const correlationId = getCorrelationId(req);

    logger.info(
      `GetCiscoToken API called | CorrelationId: ${correlationId}, storeId: ${storeId}`
    );

    // Validate storeId
    if (!storeId || storeId.trim() === "") {
      logger.info(
        `Invalid request - Missing or empty storeId | CorrelationId: ${correlationId}`
      );
      return res
        .status(400)
        .json({ message: "storeId cannot be null or empty" });
    }

    // Call service
    const token = await getCiscoTokenService(req, storeId);

    console.log("token---------->", token);

    logger.info(
      `Successfully retrieved Cisco token | CorrelationId: ${correlationId}`
    );

    return res.status(200).json(token);
  } catch (error: any) {
    logger.error(
      `Error in GetCiscoToken API: ${error.message} | Stack: ${error.stack}`
    );

    return res.status(500).json({
      message: "Internal server error",
      error: error.message,
    });
  }
}
