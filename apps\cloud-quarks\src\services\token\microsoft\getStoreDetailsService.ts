import { SqlServerService } from "../../../modules/sqlRaw/getStoreDetailsRaw/getStoreDetailsRaw";
import logger from "../../../utils/logger";

type GetStoreDetailsParams = {
  storeId: string;
  brandId?: string;
  provisionType?: string;
  credentialType?: string | null;
};

export const getStoreDetails = async (params: GetStoreDetailsParams) => {
  logger.info("Get Store Details Service Called", params);
  const sqlService = new SqlServerService();
  return await sqlService.getStoreDetails(params);
};
