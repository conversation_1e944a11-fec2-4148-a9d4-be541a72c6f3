import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import {
  GoogleCreateCustomerDto,
  GoogleAPIResponse
} from "../../../../types/responses/customResponse";
import {
  validateGoogleCreateCustomerDto,
  mapDtoToRequest
} from "../../../../validators/google/googleCreateCustomerValidator";
import { registerCustomerWithGoogleService } from "../../../../services/googleCustomer/registerCustomerWithGoogleService";
import { Messages } from "../../../../services/googleCustomer/verifyDomainExistsService";

/**
 * @openapi
 * /v1/googleCustomer/registerCustomerWithGoogle/store/{storeId}:
 *   post:
 *     summary: Register Customer With Google
 *     description: Registers a new customer directly with Google Cloud Identity API. This endpoint creates a customer in Google's system and returns the customer details including the generated customer ID.
 *     tags:
 *       - GoogleCustomer
 *     parameters:
 *       - in: path
 *         name: storeId
 *         required: true
 *         schema:
 *           type: string
 *         description: Store ID where the customer is being registered
 *         example: "STORE001"
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - brandIds
 *             properties:
 *               brandIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of brand IDs associated with the customer
 *                 example: ["BRAND001", "BRAND002"]
 *               orgDisplayName:
 *                 type: string
 *                 description: Organization display name
 *                 example: "Acme Corporation"
 *               orgPostalAddress:
 *                 type: object
 *                 properties:
 *                   revision:
 *                     type: integer
 *                     description: Address revision number
 *                     example: 1
 *                   regionCode:
 *                     type: string
 *                     description: Region code (e.g., country code)
 *                     example: "US"
 *                   languageCode:
 *                     type: string
 *                     description: Language code for the address
 *                     example: "en"
 *                   postalCode:
 *                     type: string
 *                     description: Postal/ZIP code
 *                     example: "12345"
 *                   sortingCode:
 *                     type: string
 *                     description: Sorting code for mail delivery
 *                     example: "ABC123"
 *                   administrativeArea:
 *                     type: string
 *                     description: State or administrative area
 *                     example: "California"
 *                   locality:
 *                     type: string
 *                     description: City or locality
 *                     example: "San Francisco"
 *                   sublocality:
 *                     type: string
 *                     description: Sublocality or district
 *                     example: "Downtown"
 *                   addressLines:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: Array of address lines
 *                     example: ["123 Main St", "Suite 100"]
 *                   recipients:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: Array of recipients
 *                     example: ["John Doe"]
 *                   organization:
 *                     type: string
 *                     description: Organization name for the address
 *                     example: "Acme Corporation"
 *               primaryContactInfo:
 *                 type: object
 *                 properties:
 *                   firstName:
 *                     type: string
 *                     description: Primary contact first name
 *                     example: "John"
 *                   lastName:
 *                     type: string
 *                     description: Primary contact last name
 *                     example: "Doe"
 *                   displayName:
 *                     type: string
 *                     description: Primary contact display name
 *                     example: "John Doe"
 *                   email:
 *                     type: string
 *                     format: email
 *                     description: Primary contact email address
 *                     example: "<EMAIL>"
 *                   title:
 *                     type: string
 *                     description: Primary contact job title
 *                     example: "IT Administrator"
 *                   phone:
 *                     type: string
 *                     description: Primary contact phone number
 *                     example: "******-123-4567"
 *               alternateEmail:
 *                 type: string
 *                 format: email
 *                 description: Alternate email address
 *                 example: "<EMAIL>"
 *               domain:
 *                 type: string
 *                 description: Domain name for the customer
 *                 example: "acme.com"
 *               languageCode:
 *                 type: string
 *                 description: Preferred language code
 *                 example: "en"
 *               cloudIdentityInfo:
 *                 type: object
 *                 properties:
 *                   customerType:
 *                     type: string
 *                     description: Type of customer (e.g., business, education)
 *                     example: "business"
 *                   primaryDomain:
 *                     type: string
 *                     description: Primary domain for cloud identity
 *                     example: "acme.com"
 *                   isDomainVerified:
 *                     type: string
 *                     description: Domain verification status
 *                     example: "true"
 *                   alternateEmail:
 *                     type: string
 *                     format: email
 *                     description: Alternate email for cloud identity
 *                     example: "<EMAIL>"
 *                   phoneNumber:
 *                     type: string
 *                     description: Phone number for cloud identity
 *                     example: "******-987-6543"
 *                   languageCode:
 *                     type: string
 *                     description: Language code for cloud identity
 *                     example: "en"
 *                   adminConsoleUri:
 *                     type: string
 *                     description: Admin console URI
 *                     example: "https://admin.google.com"
 *                   eduData:
 *                     type: object
 *                     properties:
 *                       instituteType:
 *                         type: string
 *                         description: Type of educational institution
 *                         example: "university"
 *                       instituteSize:
 *                         type: string
 *                         description: Size of the educational institution
 *                         example: "large"
 *                       website:
 *                         type: string
 *                         description: Institution website URL
 *                         example: "https://www.acme-university.edu"
 *               channelPartnerId:
 *                 type: string
 *                 description: Channel partner ID
 *                 example: "CP123456"
 *               correlationId:
 *                 type: string
 *                 description: Correlation ID for request tracking
 *                 example: "corr-123-456-789"
 *               googleCustomerId:
 *                 type: string
 *                 description: Existing Google customer ID (optional)
 *                 example: "google-customer-123"
 *               cloudIdentityId:
 *                 type: string
 *                 description: Existing cloud identity ID (optional)
 *                 example: "cloud-identity-456"
 *     responses:
 *       200:
 *         description: Customer registered successfully with Google
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Message:
 *                   type: string
 *                   example: "Success"
 *                 Data:
 *                   type: object
 *                   properties:
 *                     Name:
 *                       type: string
 *                       example: "customers/C12345678"
 *                     CustomerId:
 *                       type: string
 *                       example: "C12345678"
 *                 StatusCode:
 *                   type: integer
 *                   example: 200
 *                 IsError:
 *                   type: boolean
 *                   example: false
 *       400:
 *         description: Bad Request - Validation errors
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Message:
 *                   type: string
 *                   example: "BrandId cannot be null or empty, Store with ID 'invalid' does not exist"
 *                 Data:
 *                   type: object
 *                   nullable: true
 *                   example: null
 *                 StatusCode:
 *                   type: integer
 *                   example: 400
 *                 IsError:
 *                   type: boolean
 *                   example: true
 *       422:
 *         description: Unprocessable Entity - Google API error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Message:
 *                   type: string
 *                   example: "Domain already exists in Google"
 *                 Data:
 *                   type: object
 *                   nullable: true
 *                   example: null
 *                 StatusCode:
 *                   type: integer
 *                   example: 422
 *                 IsError:
 *                   type: boolean
 *                   example: true
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Message:
 *                   type: string
 *                   example: "An error occurred: Internal server error"
 *                 Data:
 *                   type: object
 *                   nullable: true
 *                   example: null
 *                 StatusCode:
 *                   type: integer
 *                   example: 500
 *                 IsError:
 *                   type: boolean
 *                   example: true
 */
export async function POST(
  req: MedusaRequest<GoogleCreateCustomerDto>,
  res: MedusaResponse
): Promise<void> {
  // Extract route parameters
  const { storeId } = req.params;
  const googleCreateCustomerDto = req.body;

  logger.info(`Entered into RegisterCustomerWithGoogle API with storeid ${storeId}, GoogleCreateCustomerDto - ${JSON.stringify(googleCreateCustomerDto)}`);

  let response: GoogleAPIResponse = {
    Message: "Success",
    Data: null,
    StatusCode: 200,
    IsError: false
  };

  // Map storeId to DTO (matching .NET parameter mapping)
  googleCreateCustomerDto.storeId = storeId;

  try {
    logger.info(`Going to validate ${JSON.stringify(googleCreateCustomerDto)}`);
    const validationResult = await validateGoogleCreateCustomerDto(googleCreateCustomerDto);

    logger.info(`Validations error count on GoogleCreateCustomerDto object is ${validationResult.errors.length}`);
    if (!validationResult.isValid) {
      logger.info(`validation errors are ${JSON.stringify(validationResult.errors)}`);

      response.Message = validationResult.errors.join(', ');
      response.StatusCode = 400; // HttpStatusCode.BadRequest
      response.IsError = true;

      logger.info(`Going to send bad request response ${JSON.stringify(response)}`);
      res.status(400).json(response);
      return;
    }

    logger.info(`Going to map Create Customer request. Dto is ${JSON.stringify(googleCreateCustomerDto)}`);
    const createCustomerRequest = mapDtoToRequest(googleCreateCustomerDto);
    logger.info(`Mapped dto is :${JSON.stringify(createCustomerRequest)}`);

    logger.info(`Going to call service method RegisterCustomerWithGoogle with parameter storeId ${storeId},createCustomerRequest ${JSON.stringify(createCustomerRequest)} `);

    response = await registerCustomerWithGoogleService(
      req,
      storeId,
      createCustomerRequest
    );

    logger.info(`Response from service ${JSON.stringify(response)} `);
  } catch (exp: any) {
    logger.error(`Error in RegisterCustomerWithGoogle API method. Message : ${exp.message}, StackTrace: ${exp.stack} `);
    response.Message = Messages.ExceptionMessage(exp.message);
    response.IsError = true;
    response.StatusCode = 422; // HttpStatusCode.UnprocessableEntity
  }

  logger.info(`Returning response from RegisterCustomerWithGoogle API - ${JSON.stringify(response)}`);

  // Return ContentResult equivalent (matching .NET response structure)
  res.status(response.StatusCode).json(response);
}