import { z } from "zod";
import logger from "../../utils/logger";

// Zod schema for GetNCEProductUpgrades query parameters
export const GetNCEProductUpgradesQuerySchema = z.object({
  customerId: z.string().min(1, "CustomerId cannot be blank or null"),
  subscriptionId: z.string().min(1, "SubscriptionId cannot be blank or null"),
  storeId: z.string().min(1, "StoreId cannot be blank or null"),
  eligibilityType: z.string().optional().default("immediate"),
});

export type GetNCEProductUpgradesQueryType = z.infer<typeof GetNCEProductUpgradesQuerySchema>;

/**
 * Validate GetNCEProductUpgrades request parameters
 * Matches .NET ValidationGetNCEProductUpgradesRequest method
 */
export function validateGetNCEProductUpgradesRequest(queryParams: any): string[] {
  logger.info(`Entered into ValidationGGetNCEProductUpgradesRequest method with customerId-${queryParams.customerId}, subscriptionId-${queryParams.subscriptionId}, storeId-${queryParams.storeId}`);
  
  const validationErrors: string[] = [];

  // Validate customerId (matching .NET validation logic)
  logger.info(`Validating query parameter customerId-${queryParams.customerId}`);
  if (!queryParams.customerId || queryParams.customerId.trim() === '') {
    logger.info(`Received validation error for query parameter customerId -${queryParams.customerId}, customerId can't be null or empty`);
    validationErrors.push("CustomerId can't be null or empty.");
  }
  logger.info(`Validated query parameter customerId`);

  // Validate subscriptionId (matching .NET validation logic)
  logger.info(`Validating query parameter subscriptionId-${queryParams.subscriptionId}`);
  if (!queryParams.subscriptionId || queryParams.subscriptionId.trim() === '') {
    logger.info(`Received validation error for query parameter subscriptionId -${queryParams.subscriptionId}, subscriptionId can't be null or empty`);
    validationErrors.push("SubscriptionId can't be null or empty");
  }
  logger.info(`Validated query parameter subscriptionId`);

  // Validate storeId (matching .NET validation logic)
  logger.info(`Validating query parameter storeId-${queryParams.storeId}`);
  if (!queryParams.storeId || queryParams.storeId.trim() === '') {
    logger.info(`Received validation error for query parameter storeId -${queryParams.storeId}, storeId can't be null or empty`);
    validationErrors.push("StoreId can't be null or empty.");
  }
  logger.info(`Validated query parameter storeId`);

  // Also run Zod validation for additional checks
  try {
    GetNCEProductUpgradesQuerySchema.parse(queryParams);
    logger.info(`Zod validation passed for GetNCEProductUpgrades request`);
  } catch (error: any) {
    if (error.errors) {
      error.errors.forEach((err: any) => {
        const fieldName = err.path.join('.');
        // Only add if not already added by custom validation
        const errorMessage = `${fieldName}: ${err.message}`;
        if (!validationErrors.some(existing => existing.includes(fieldName))) {
          validationErrors.push(errorMessage);
        }
      });
    }
    logger.error(`Zod validation failed: ${JSON.stringify(error.errors)}`);
  }

  logger.info(`Going to return Validation error list ${JSON.stringify(validationErrors)}`);
  return validationErrors;
}
