import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { ContainerRegistrationKeys, Modules } from '@medusajs/framework/utils';
import { stringConstants } from '@org/utils';

/**
 * @openapi
 * /v1/user/store:
 *   get:
 *     summary: Get a list of stores filtered by region
 *     tags:
 *       - Stores
 *     parameters:
 *       - in: query
 *         name: region_id
 *         schema:
 *           type: string
 *         required: true
 *         description: Region ID to filter stores by associated countries
 *     responses:
 *       200:
 *         description: List of stores fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         example: "001"
 *                       name:
 *                         type: string
 *                         example: "Test Store"
 *             example:
 *               data:
 *                 - id: "001"
 *                   name: "Test Store"
 *       404:
 *         description: Region not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Region not found
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const regionService = req.scope.resolve(Modules.REGION);
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

    const regionId = req.query.region_id as string;

    let region;
    try {
      region = await regionService.retrieveRegion(regionId, {
        relations: ['countries'],
      });
    } catch (error) {
      return res.status(404).json({
        message: stringConstants.NOT_FOUND('Region'),
      });
    }

    const iso2Codes = region?.countries?.map((country) => country.iso_2) ?? [];

    const { data } = await query.graph({
      entity: 'cq_store',
      fields: ['id', 'name'],
      filters: {
        region_country_id: iso2Codes,
      },
    });

    return res.status(200).json({
      data,
    });
  } catch (err) {
    return res
      .status(500)
      .json({ message: stringConstants.INTERNAL_SERVRER_ERROR });
  }
}
