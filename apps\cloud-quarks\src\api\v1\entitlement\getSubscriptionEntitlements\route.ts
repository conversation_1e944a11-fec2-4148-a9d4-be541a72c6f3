import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import { getSubscriptionEntitlementsService } from "../../../../services/entitlement/getSubscriptionEntitlementsService";

/**
 * @openapi
 * /v1/entitlement/getSubscriptionEntitlements:
 *   get:
 *     summary: Get Subscription Entitlements
 *     description: Retrieves entitlements for a given subscription.
 *     tags:
 *       - Entitlement
 *     parameters:
 *       - in: query
 *         name: subscriptionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Subscription ID
 *         example: "sub-123456"
 *     responses:
 *       200:
 *         description: Successfully retrieved subscription entitlements
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 totalCount:
 *                   type: integer
 *                   example: 2
 *                 entitlements:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       subscriptionId:
 *                         type: string
 *                         example: "sub-123456"
 *                       customerId:
 *                         type: string
 *                         example: "cust-789"
 *                       entitlementId:
 *                         type: string
 *                         example: "ent-001"
 *                       friendlyName:
 *                         type: string
 *                         example: "Microsoft 365 Business Premium"
 *                       status:
 *                         type: string
 *                         example: "Active"
 *       204:
 *         description: No entitlements found
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal Server Error
 *                 isError:
 *                   type: boolean
 *                   example: true
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const { subscriptionId } = req.query as Record<string, string>;
  const correlationId = getCorrelationId(req);

  logger.info(
    `Entered GET SubscriptionEntitlements API | CorrelationId: ${correlationId} | subscriptionId - ${subscriptionId}`
  );

  try {
    const result = await getSubscriptionEntitlementsService(subscriptionId);

    if (!result || result.totalCount === 0) {
      return res.status(204).send(); // No content
    }

    return res.status(200).json(result);
  } catch (error) {
    logger.error(
      `ERROR in GET SubscriptionEntitlements API | subscriptionId: ${subscriptionId} | Error: ${error}`
    );
    return res
      .status(500)
      .json({ message: "Internal Server Error", isError: true });
  }
}
