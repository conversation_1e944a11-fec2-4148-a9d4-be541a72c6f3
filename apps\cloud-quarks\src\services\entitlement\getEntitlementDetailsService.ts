import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import { Entitlement } from "../../types/responses/customResponse";
import {
  BrandEnum,
  SubStatus,
  comparableString,
} from "../../utils/constants/brandEnum";
import { getOrderDetail } from "./entitlementDAL";
import { getTokenFromOrderRow } from "./tokenService";
import { getProvisionalStatusService } from "./getProvisionalStatusService";
import { getOrdersDetailFromVendorByID } from "./orderDetailsService";
import { getSubscriptionById } from "./subscriptionService";
import { getEntitlements } from "./entitlementsService";

export async function getEntitlementDetailsService(
  req: MedusaRequest,
  orderLineKey: number
): Promise<Entitlement | null> {
  logger.info(
    `Entered into GetEntitlementDetails API with EntitlementRequest - orderLineKey: ${orderLineKey}`
  );

  try {
    //Get the orders whose status is SubscriptionProvisioningStatus false from Azure RI, Subscription status and perpetual
    const orderRow = await getOrderDetail(orderLineKey);

    if (!orderRow) {
      logger.error(`No order found for orderLineKey: ${orderLineKey}`);
      return null;
    }

    logger.info(
      `GetOrderDetail call in progress for provisioningStatus | Request: orderLineKey - ${orderLineKey}`
    );

    let entitlement: Entitlement | null = null;

    try {
      console.log("orderRow------------>", orderRow);
      // Get token from order row data
      const token = await getTokenFromOrderRow(orderRow);

      console.log("token------>", token);

      logger.info(`Token received: ${JSON.stringify(token)}`);

      let subsProvStatus = orderRow.SubsProvStatus;
      const brandKey = orderRow.BrandKey;

      if (!subsProvStatus) {
        logger.info(
          `SubsProvStatus is false, checking GetProvisionalStatus...`
        );

        const status = await getProvisionalStatusService(
          req,
          orderRow.CustomerId,
          orderRow.VendorOrderId,
          token
        );

        if (status === "success") {
          logger.info(`GetProvisionalStatus returned 'success'`);
          subsProvStatus = true;
        } else {
          logger.info(
            `GetProvisionalStatus returned '${status}', not marking as provisioned`
          );
        }
      }

      //If status is success then call getentitlement endpoint and order details endpoint
      if (subsProvStatus) {
        console.log("subsProvStatus--------->", subsProvStatus);
        console.log("brandKey--------->", brandKey);
        console.log("token--------->", token);
        // For Azure Plan entitlement is always null, so skipping calling it for Azure Plan
        if (
          brandKey !== BrandEnum.AzurePlan &&
          brandKey !== BrandEnum.NCE &&
          brandKey !== BrandEnum.MarketPlace &&
          brandKey !== BrandEnum.Perpetual &&
          brandKey !== BrandEnum.Subscription
        ) {
          console.log("brandKey------BrandEnum------>", BrandEnum);
          entitlement = await getEntitlements(
            req,
            orderRow.CustomerId,
            orderRow.VendorOrderId,
            token,
            orderRow.BrandName
          );
        } else {
          entitlement = {} as Entitlement;
        }

        // Handle subscription ID
        if (!orderRow.SubscriptionId) {
          if (brandKey === BrandEnum.AzurePlan) {
            const order = await getOrdersDetailFromVendorByID(
              req,
              orderRow.CustomerId,
              orderRow.VendorOrderId,
              token
            );

            if (order && order.LineItems && order.LineItems.length > 0) {
              entitlement.subscriptionId = order.LineItems[0]?.SubscriptionId;
            }
          }
        } else {
          entitlement.subscriptionId = orderRow.SubscriptionId;
        }

        const isNCE = brandKey === BrandEnum.NCE;
        const isMarketPlace = brandKey === BrandEnum.MarketPlace;
        const isSoftwareSubscription = brandKey === BrandEnum.Subscription;

        // To get end date for NCE brand
        if (isNCE || isMarketPlace || isSoftwareSubscription) {
          if (entitlement.subscriptionId) {
            const subscriptionResponse = await getSubscriptionById(
              req,
              orderRow.CustomerId,
              entitlement.subscriptionId,
              token
            );

            if (subscriptionResponse) {
              // Always assign status if it's a Marketplace subscription
              if (isMarketPlace) {
                entitlement.subscriptionStatus = subscriptionResponse.Status;
              }

              const isActiveMarketplace =
                isMarketPlace &&
                comparableString(subscriptionResponse.Status) ===
                  comparableString(SubStatus.ACTIVE);

              // Set dates only if ACTIVE status for Marketplace, or always for Software/NCE
              if (isSoftwareSubscription || isNCE || isActiveMarketplace) {
                entitlement.expiryDate = subscriptionResponse.CommitmentEndDate;
                entitlement.hasAddOn =
                  subscriptionResponse.HasPurchasableAddons;
                entitlement.startDate = subscriptionResponse.EffectiveStartDate;
              }
            }
          }
        }
      } else {
        entitlement = null;
      }
    } catch (innerError) {
      logger.error(
        `ERROR in GetProvisionalStatus OR getEntitlements | ErrorDetail: ${
          innerError instanceof Error ? innerError.message : innerError
        }`
      );
      entitlement = null;
    }

    logger.info(
      `GetOrderDetail call Success for provisioningStatus: ${JSON.stringify(
        entitlement
      )}`
    );
    return entitlement;
  } catch (outerError) {
    logger.error(
      `ERROR in GetOrderDetail OR getEntitlements | ErrorDetail: ${
        outerError instanceof Error ? outerError.message : outerError
      }`
    );
    return null;
  }
}
