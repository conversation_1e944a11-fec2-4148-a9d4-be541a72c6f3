import { model } from "@medusajs/framework/utils";
import { CqCompanyOnboarding } from "./company-onboarding";
import { CqCompanyRegistration } from "./company-registration";

export const AuthorizedSignatory = model.define("cq_authorized_signatory", {
	id: model.id().primaryKey(),
	company_onboarding: model.belongsTo(() => CqCompanyOnboarding).nullable(),
	company_registration: model.belongsTo(() => CqCompanyRegistration).nullable(),
	first_name: model.text(),
	last_name: model.text(),
	email: model.text(),
	country_code: model.text(),
	mobile_number: model.text(),
});
