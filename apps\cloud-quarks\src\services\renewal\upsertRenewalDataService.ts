import logger from "../../utils/logger";
import sql from "mssql";
import {
  ConstantValue,
  RenewalMasterModel,
  StatusResponse,
} from "../../validators/renewal/customRenewalValidator";
import { getSqlServerConnection } from "../../utils/sqlServerClient";
import { ComparableString } from "../../validators/customer/customCustomerValidator";

export async function upsertRenewalDataService(
  renewalModel: RenewalMasterModel
): Promise<StatusResponse> {
  logger.info(
    `Entered into BL UpsertRenewalData method with renewal model: ${JSON.stringify(
      renewalModel
    )}`
  );

  let response: StatusResponse = {
    Status: "",
    Message: "",
  };

  try {
    logger.info(
      `Entered into UPDATE section. Going to hit ValidateRenewalRequest with subscriptionId: ${renewalModel.SubscriptionId}`
    );
    response = await validateRenewalRequest(renewalModel.SubscriptionId);

    if (
      ComparableString(response.Status) ===
      ComparableString(ConstantValue.PASS)
    ) {
      response = await upsertRenewalDataDAL(renewalModel);
    }

    logger.info(`Update response from DAL is ${JSON.stringify(response)}`);
  } catch (error) {
    logger.error(`Exception in UpsertRenewalData method`, {
      message: (error as Error).message,
      stack: (error as Error).stack,
    });
  }

  return response;
}

export async function validateRenewalRequest(
  subscriptionId: string
): Promise<StatusResponse> {
  logger.info(
    `Entered into DAL validateRenewalRequest method with subscriptionId - ${subscriptionId}`
  );

  const response: StatusResponse = { Status: "", Message: "" };

  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();

    request.input("iSubscription", subscriptionId);
    request.output("oRetVal", sql.Bit);
    request.output("oRetMessage", sql.VarChar(200));

    logger.info(
      `Going to hit DB with proc name: spValidateSubscriptionRenewal and parameters: subscriptionId=${subscriptionId}`
    );

    await request.execute("spValidateSubscriptionRenewal");

    const statusBool = request.parameters.oRetVal.value;
    const message = request.parameters.oRetMessage.value;

    response.Status = statusBool ? ConstantValue.PASS : ConstantValue.FAIL;
    response.Message = message;

    logger.info(`Return value is ${response.Status}`);
    logger.info(`Return message is ${response.Message}`);
  } catch (error) {
    logger.error(`Error in validateRenewalRequest method`, {
      message: (error as Error).message,
      stack: (error as Error).stack,
    });

    response.Status = ConstantValue.FAIL;
    response.Message = (error as Error).message;
  }

  return response;
}


export async function upsertRenewalDataDAL(
  renewalMasterModel: RenewalMasterModel
): Promise<StatusResponse> {
  logger.info(
    `Entered into upsertRenewalDataService method with renewalMasterModel: ${JSON.stringify(renewalMasterModel)}`
  );

  try {
    logger.info(
      `Calling DAL upsertRenewalData with renewalMasterModel: ${JSON.stringify(renewalMasterModel)}`
    );

    const responseFromDal = await upsertRenewalData(renewalMasterModel);

    logger.info(
      `Response from DAL in upsertRenewalData: ${JSON.stringify(responseFromDal)}`
    );

    const response: StatusResponse = {
      Status: responseFromDal?.Status ?? "",
      Message: responseFromDal?.Message ?? "",
    };

    logger.info(`Returning upsertRenewalDataService result: ${JSON.stringify(response)}`);
    return response;
  } catch (error) {
    logger.error("Error in upsertRenewalDataService", {
      error: (error as Error).message,
      stack: (error as Error).stack,
    });

    return {
      Status: "FAIL",
      Message: (error as Error).message,
    };
  }
}



export async function upsertRenewalData(
  renewal: RenewalMasterModel
): Promise<{ Status: string; Message: string }> {
  logger.info(
    `Entered into DAL upsertRenewalData with model: ${JSON.stringify(renewal)}`
  );

  const pool = await getSqlServerConnection();
  const request = pool.request();

  try {
    request.input("BrandId", sql.VarChar(50), renewal.BrandId);
    request.input(
      "Status",
      sql.VarChar(20),
      renewal.AutoRenewStatus ? "AutoRenewOn" : "AutoRenewOff"
    );
    request.input("MaterialId", sql.VarChar(50), renewal.MaterialNo);
    request.input("BillingCycle", sql.VarChar(50), renewal.PlanType);
    request.input("TermDuration", sql.VarChar(50), renewal.TermDuration);
    request.input("PromotionId", sql.VarChar(50), renewal.PromotionId);
    request.input("Quantity", sql.Int, renewal.Quantity);
    request.input("CustomTermEndDate", sql.VarChar(50), renewal.CustomTermEndDate);
    request.input("SubscriptionId", sql.VarChar(50), renewal.SubscriptionId);
    request.input("DiscountPercentage", sql.Float, renewal.DiscountPercentage);
    request.input("Tax", sql.VarChar(50), ""); // Static empty string like in C#
    request.input("PromotionRate", sql.Float, renewal.PromotionRate);
    request.input("IsOldSubscriptionModified", sql.Bit, renewal.IsOldSubscriptionModified);
    request.input("TaxPercentage", sql.Float, renewal.TaxPercentage);
    request.input("CustomTermType", sql.VarChar(50), renewal.CustomTermType);
    request.input("CustomTermPrice", sql.Float, renewal.CustomTermPrice);
    request.input("FirstScheduleEndDate", sql.VarChar(50), renewal.FirstScheduleEndDate);
    request.input("QuantityType", sql.VarChar(50), renewal.QuantityType);

    request.output("RetVal", sql.VarChar(20));
    request.output("Message", sql.VarChar(200));

    logger.info(
      `Calling stored procedure spUpsertRenewalData with parameters: ${JSON.stringify(
        renewal
      )}`
    );

    await request.execute("spUpsertRenewalData");

    const status = request.parameters.RetVal.value;
    const message = request.parameters.Message.value;

    logger.info(`Return value: ${status}, Message: ${message}`);

    return {
      Status: status,
      Message: message,
    };
  } catch (error) {
    logger.error(
      `Error in upsertRenewalData with message: ${(error as Error).message}`
    );
    return {
      Status: "FAIL",
      Message: (error as Error).message,
    };
  }
}
