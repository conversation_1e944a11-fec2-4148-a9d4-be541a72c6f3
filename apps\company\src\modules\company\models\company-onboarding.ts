import { model } from '@medusajs/framework/utils';
import { UserBrandPreferences } from './user-brand-preferences';
import { AuthorizedSignatory } from './authorized-signatory';

export const CqCompanyOnboarding = model.define('cq_company_onboarding', {
	id: model.id().primaryKey(),
	redington_account_id: model.text(),
	user_id: model.text().nullable(),
	region_id: model.text().nullable(),
	country: model.text(),
	gst_number: model.text(),
	company_name: model.text(),
	address1: model.text(),
	address2: model.text(),
	country_of_business: model.text(),
	state: model.text(),
	city: model.text(),
	postal_code: model.text(),
	website: model.text(),
	camunda_process_id: model.text().nullable(),
	director_first_name: model.text(),
	director_last_name: model.text(),
	director_email: model.text(),
	director_country_code: model.text(),
	director_mobile_number: model.text(),
	sales_first_name: model.text(),
	sales_last_name: model.text(),
	sales_email: model.text(),
	sales_country_code: model.text(),
	sales_mobile_number: model.text(),
	accounts_first_name: model.text(),
	accounts_last_name: model.text(),
	accounts_email: model.text(),
	accounts_country_code: model.text(),
	accounts_mobile_number: model.text(),
	authorized_signatories: model.hasMany(() => AuthorizedSignatory, {
		mappedBy: 'company_onboarding',
	}),
	user_brand_preference: model.hasMany(() => UserBrandPreferences, {
		mappedBy: 'company_onboarding',
	}),
	requested_by_admin: model.boolean(),
	requested_source: model.text().nullable(),
	status_id: model.text(),
	// To do store mapping
	store_id: model.text().nullable(), // One company can have multiple stores, so this is a one-to-many relationship
	reason: model.text().nullable(),
	metadata: model.json(),
	created_by: model.text().nullable(),
	updated_by: model.text().nullable(),
	deleted_by: model.text().nullable(),
});
