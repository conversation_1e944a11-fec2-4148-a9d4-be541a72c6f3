import { MedusaRequest, MedusaResponse } from '@medusajs/framework';

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { CustomerCode, CompanyCode } = req.body as {
      CustomerCode: string;
      CompanyCode: string;
    };

    if (!CustomerCode || !CompanyCode) {
      return res.status(400).json({
        success: false,
        message: 'CustomerCode and CompanyCode are required.',
      });
    }

    const response = await fetch(process.env.SAP_URL!, {
      method: 'POST',
      headers: {
        client_id: process.env.SAP_CLIENT_ID!,
        client_secret: process.env.SAP_CLIENT_SECRET!,
        business_partner: process.env.SAP_BUSINESS_PARTNER!,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ CustomerCode, CompanyCode }),
    });

    const data = await response.json();
    console.log('***Response from SAP:***', data);

    return res.status(200).json({
      success: true,
      data,
    });
  } catch (error) {
    console.error('Error fetching customer validation:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch customer validation.',
    });
  }
}
