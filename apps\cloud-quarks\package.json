{"name": "microsoft", "version": "0.0.1", "description": "A starter for Medusa projects.", "author": "Medusa (https://medusajs.com)", "license": "MIT", "type": "commonjs", "keywords": ["sqlite", "postgres", "typescript", "ecommerce", "headless", "medusa"], "scripts": {"build": "medusa build", "seed": "medusa exec ./src/scripts/seed.ts", "start": "medusa start", "dev": "medusa develop", "test:integration:http": "TEST_TYPE=integration:http NODE_OPTIONS=--experimental-vm-modules jest --silent=false --runInBand --forceExit", "test:integration:modules": "TEST_TYPE=integration:modules NODE_OPTIONS=--experimental-vm-modules jest --silent --runInBand --forceExit", "test:unit": "TEST_TYPE=unit NODE_OPTIONS=--experimental-vm-modules jest --silent --runInBand --forceExit", "generate:swagger": "ts-node src/scripts/swagger-yml.ts", "swagger:serve": "ts-node src/scripts/swagger-server.ts"}, "dependencies": {"@aws-sdk/client-s3": "^3.856.0", "@aws-sdk/s3-request-presigner": "^3.856.0", "@medusajs/admin-sdk": "2.8.3", "@medusajs/cli": "2.8.3", "@medusajs/framework": "2.8.3", "@medusajs/medusa": "2.8.3", "@mikro-orm/core": "6.4.3", "@mikro-orm/knex": "6.4.3", "@mikro-orm/migrations": "6.4.3", "@mikro-orm/postgresql": "6.4.3", "awilix": "^8.0.1", "axios": "^1.10.0", "cors": "^2.8.5", "js-yaml": "^4.1.0", "jsonwebtoken": "^9.0.2", "mssql": "^11.0.1", "pg": "^8.13.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "winston": "^3.17.0"}, "devDependencies": {"@medusajs/test-utils": "2.8.3", "@mikro-orm/cli": "6.4.3", "@swc/core": "1.5.7", "@swc/jest": "^0.2.36", "@types/express": "^5.0.3", "@types/jest": "^29.5.13", "@types/mssql": "^9.1.7", "@types/node": "^20.0.0", "@types/react": "^18.3.2", "@types/react-dom": "^18.2.25", "jest": "^29.7.0", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "ts-node": "^10.9.2", "typescript": "^5.6.2", "vite": "^5.2.11", "yalc": "^1.0.0-pre.53"}, "engines": {"node": ">=20"}}