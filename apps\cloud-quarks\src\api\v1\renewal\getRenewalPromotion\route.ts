import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
// Removed separate query validation - using comprehensive validation service instead
import { validateRenewalPromotionRequest } from "../../../../services/renewal/validateRenewalPromotionService";
import { getRenewalPromotionService } from "../../../../services/renewal/getRenewalPromotionService";

/**
 * @openapi
 * /v1/renewal/getRenewalPromotion:
 *   get:
 *     summary: Get renewal promotion details
 *     tags:
 *       - Renewal
 *     parameters:
 *       - in: query
 *         name: subscriptionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Subscription ID
 *         example: "sub-123456"
 *       - in: query
 *         name: billType
 *         required: true
 *         schema:
 *           type: string
 *         description: Billing type
 *         example: "Monthly"
 *       - in: query
 *         name: term
 *         required: true
 *         schema:
 *           type: string
 *         description: Term duration
 *         example: "P1M"
 *       - in: query
 *         name: brandId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Brand ID
 *         example: 1001
 *       - in: query
 *         name: materialno
 *         required: true
 *         schema:
 *           type: string
 *         description: Material number
 *         example: "MAT123"
 *       - in: query
 *         name: quantity
 *         required: true
 *         schema:
 *           type: integer
 *         description: Quantity
 *         example: 5
 *       - in: query
 *         name: segment
 *         required: true
 *         schema:
 *           type: string
 *         description: Segment
 *         example: "Commercial"
 *       - in: query
 *         name: customerTermEndDate
 *         required: true
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Customer term end date
 *         example: "2024-12-31T23:59:59Z"
 *       - in: query
 *         name: isOldSubscriptionModified
 *         required: true
 *         schema:
 *           type: boolean
 *         description: Whether old subscription is modified
 *         example: false
 *     responses:
 *       200:
 *         description: Renewal promotion details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: string
 *       500:
 *         description: Internal server error
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req);

  try {
    logger.info(
      `Entered into GetRenewalPromotion method with subscriptionid :${req.query.subscriptionId}, billtype:${req.query.billType}, term:${req.query.term},brandid:${req.query.brandId},materialno:${req.query.materialno},quantity:${req.query.quantity},segment:${req.query.segment}, customerTermEndDate:${req.query.customerTermEndDate}, isOldSubscriptionModified:${req.query.isOldSubscriptionModified} | CorrelationId: ${correlationId}`
    );

    try {
      // Extract and convert query parameters (matching .NET method parameters)
      const subscriptionId = req.query.subscriptionId as string;
      const billType = req.query.billType as string;
      const term = req.query.term as string;
      const brandId = parseInt(req.query.brandId as string, 10);
      const materialno = req.query.materialno as string;
      const quantity = parseInt(req.query.quantity as string, 10);
      const segment = req.query.segment as string;
      const customerTermEndDate = new Date(req.query.customerTermEndDate as string);
      const isOldSubscriptionModified = (req.query.isOldSubscriptionModified as string)?.toLowerCase() === 'true';

      // Validate renewal promotion request (matching .NET ValidateRenewalPromotionRequest)
      const validationMessages = await validateRenewalPromotionRequest(
        subscriptionId,
        materialno,
        billType,
        term,
        quantity,
        isOldSubscriptionModified
      );

      logger.info(`Validation messages count are ${validationMessages.length} | CorrelationId: ${correlationId}`);

      if (validationMessages.length === 0) {
        // Call the renewal promotion service (matching .NET _renewalFactory.GetRenewalPromotion)
        const subscriptionPromotionDetailModel = await getRenewalPromotionService(
          req,
          subscriptionId,
          billType,
          term,
          brandId,
          materialno,
          quantity,
          segment,
          customerTermEndDate,
          isOldSubscriptionModified
        );

        logger.info(`subscriptionPromotionDetailModel from BL layer : ${JSON.stringify(subscriptionPromotionDetailModel)} | CorrelationId: ${correlationId}`);

        // Map to DTO (matching .NET _mapper.Map<SubscriptionPromotionDetailModel, RenewalPromotionDetailDto>)
        // Note: In this case, we're returning the promotionEligibilities directly as in .NET
        const renewalPromotionDetailDto = subscriptionPromotionDetailModel;
        logger.info(`dto prepared is ${JSON.stringify(renewalPromotionDetailDto)} | CorrelationId: ${correlationId}`);

        logger.info(`Returning from controller: ${JSON.stringify(renewalPromotionDetailDto.promotionEligibilities)} | CorrelationId: ${correlationId}`);
        return res.status(200).json(renewalPromotionDetailDto.promotionEligibilities);
      }

      if (validationMessages.length > 0) {
        logger.info(`Returning bad request from controller with validation messages: ${JSON.stringify(validationMessages)} | CorrelationId: ${correlationId}`);
        return res.status(400).json(validationMessages);
      }
    } catch (error: any) {
      logger.error(
        `Exception is GetRenewalPromotion controller method. Message: ${error.message}, StackTrace:${error.stack} | CorrelationId: ${correlationId}`
      );
      return res.status(500).json({ error: "Internal server error" });
    }

    // Default return (matching .NET return Ok())
    return res.status(200).json({});

  } catch (error: any) {
    const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";

    logger.error(
      `Unexpected error in GetRenewalPromotion API | CorrelationId: ${correlationId} | Message: ${errorMessage} | StackTrace: ${error instanceof Error ? error.stack : 'No stack trace'}`
    );

    return res.status(500).json({ error: "Internal server error" });
  }
}