import { loadEnv, defineConfig } from '@medusajs/framework/utils';

loadEnv(process.env.NODE_ENV || 'development', process.cwd());

// Define modules based on database type
const getModules = (databaseType: 'master' | 'india' | 'mea' | 'turkey') => {
	const baseModules = [];

	// Company module resolve for approval from admin
	baseModules.push({
		resolve: './../../company/src/modules/company',
	});

	if (databaseType === 'master') {
		baseModules.push(
			{
				resolve: './../../../shared-app/user/src/modules/cq-user',
				options: {
					providers: [],
					definition: {
						isQueryable: true,
						migrations: true,
					},
				},
			},
			{
				resolve: './src/modules/master',
			},
			  {
      resolve: './src/modules/custom-loader',
    },
		);
	}

	return baseModules;
};

const databaseType =
	(process.env.DATABASE_TYPE as 'master' | 'india' | 'mea' | 'turkey') ||
	'master';

module.exports = defineConfig({
	projectConfig: {
		databaseUrl: process.env.DATABASE_URL,
		http: {
			storeCors:
				'http://localhost:3000,https://editor.swagger.io,http://localhost:3001',
			adminCors: 'http://localhost:8080,https://editor.swagger.io',
			authCors:
				'http://localhost:3000,http://localhost:8080,https://editor.swagger.io,http://localhost:3001',
			jwtSecret: process.env.JWT_SECRET || 'supersecret',
			cookieSecret: process.env.COOKIE_SECRET || 'supersecret',
		},
		sessionOptions: {
			name: process.env.SESSION_NAME || 'auth_cookie',
			resave: false,
			rolling: false,
			saveUninitialized: false,
			ttl: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
			cookie: {
				secure: true,
				httpOnly: true,
				sameSite: 'Strict',
				path: '/',
			},
		} as any,
	},
	modules: getModules(databaseType),
});
