import { MicrosoftEndpoints } from "../../../config/microsoftEndpoints";
import logger from "../../../utils/logger";

type MsTokenInput = {
  brand: string;
  client_id: string;
  client_secret: string;
  grant_type: string;
  markValue: string;
  redirect_uri: string;
  refresh_token: string;
  resource: string;
  store_domain: string;
};

export async function getMsToken(input: MsTokenInput): Promise<any> {
  logger.info("getMsToken Service called");
  const body = new URLSearchParams({
    brand: input.brand,
    client_id: input.client_id,
    client_secret: input.client_secret,
    grant_type: input.grant_type,
    markValue: input.markValue,
    redirect_uri: input.redirect_uri,
    refresh_token: input.refresh_token,
    resource: input.resource,
    store_domain: input.store_domain,
  }).toString();

  console.log("body---getMsToken---->", body);
  const completeUrlWithParams = MicrosoftEndpoints.getMsTokenUrl(input.store_domain)
  console.log("completeUrlWithParams---getMsToken---->", completeUrlWithParams);

  const response = await fetch(completeUrlWithParams, {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    body,
  });

  const data = await response.json();

  if (!response.ok) {
    throw new Error(`Failed to fetch Microsoft token: ${JSON.stringify(data)}`);
  }

  return data;
}

// export async function getMsToken(): Promise<any> {
//   const body = new URLSearchParams({
//     brand: process.env.MS_BRAND || "",
//     client_id: process.env.MS_CLIENT_ID || "",
//     client_secret: process.env.MS_CLIENT_SECRET || "",
//     grant_type: process.env.MS_GRANT_TYPE || "",
//     markValue: process.env.MS_MARK_VALUE || "",
//     redirect_uri: process.env.MS_REDIRECT_URI || "",
//     refresh_token: process.env.MS_REFRESH_TOKEN || "",
//     resource: process.env.MS_RESOURCE || "",
//     store_domain: process.env.MS_STORE_DOMAIN || "",
//   }).toString()

//   const response = await fetch(process.env.MS_OAUTH_TOKEN!, {
//     method: "POST",
//     headers: {
//       "Content-Type": "application/x-www-form-urlencoded",
//     },
//     body,
//   })

//   const data = await response.json()

//   if (!response.ok) {
//     throw new Error(`Failed to fetch Microsoft token: ${JSON.stringify(data)}`)
//   }

//   return data
// }
