import { z } from "zod";
import logger from "../../utils/logger";

/**
 * Renewal Request MS DTO (matching .NET RenewalRequestMSDto)
 */
export interface RenewalRequestMSDto {
  skey: number;
  brandId: string;
  storeId: string;
}

/**
 * Renewal Request MS DTO Validation Schema
 * Matches .NET RenewalRequestMSValidator
 */
export const renewalRequestMSDtoSchema = z.object({
  Skey: z.number({
    required_error: "Skey cannot be null",
    invalid_type_error: "Skey must be a number"
  }).min(1, "Skey cannot be empty"),
  
  BrandId: z.string({
    required_error: "BrandId cannot be null"
  }).min(1, "BrandId cannot be empty"),
  
  StoreId: z.string({
    required_error: "StoreId cannot be null"
  }).min(1, "StoreId cannot be empty")
});

export type RenewalRequestMSDtoType = z.infer<typeof renewalRequestMSDtoSchema>;

/**
 * Validate Renewal Request MS DTO
 * Matches .NET RenewalRequestMSValidator functionality
 */
export function validateRenewalRequestMSDto(dto: any): string[] {
  logger.info(`Validating renewal request MS DTO: ${JSON.stringify(dto)}`);
  
  const errors: string[] = [];

  // Check if dto is null or undefined
  if (!dto) {
    errors.push("Invalid data structure");
    return errors;
  }

  try {
    renewalRequestMSDtoSchema.parse(dto);
    logger.info(`Validation passed for renewal request MS DTO`);
  } catch (error: any) {
    if (error.errors) {
      error.errors.forEach((err: any) => {
        errors.push(err.message);
      });
    }
    logger.info(`Validation errors: ${JSON.stringify(errors)}`);
  }

  return errors;
}
