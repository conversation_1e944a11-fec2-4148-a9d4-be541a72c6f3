import { LoaderOptions } from '@medusajs/framework/types';
import { initializeAWSCredentials } from '../../../services/aws/getAWSCredentials';
import logger from '../../../utils/logger';

/**
 * AWS Credentials Loader
 * Initializes AWS credentials from database during application startup
 * This loader runs automatically when the Medusa application starts
 */
export default async function awsCredentialsLoader({
  container,
}: LoaderOptions) {
  try {
    logger.info('AWS Credentials Loader: Starting AWS credentials initialization...');
    
    // Initialize AWS credentials from database
    await initializeAWSCredentials();
    
    logger.info('AWS Credentials Loader: AWS credentials loaded successfully from database');
  } catch (error) {
    // Log error but don't throw to prevent application startup failure
    // AWS credentials can still be loaded later or fall back to environment variables
    logger.error('AWS Credentials Loader: Failed to load AWS credentials from database:', error);
    logger.warn('AWS Credentials Loader: Application will continue with environment variable fallback');
  }
}
