import { z } from "zod";

// Azure Operation Details query parameters validation schema
export const AzureOperationDetailsQuerySchema = z.object({
  storeId: z.string().min(1, "StoreId can't be null or empty"),
  brandId: z.string().min(1, "BrandId can't be null or empty"),
  operationId: z.string().min(1, "OperationId can't be null or empty"),
});

// Type definition for TypeScript
export type AzureOperationDetailsQueryType = z.infer<typeof AzureOperationDetailsQuerySchema>;

// Validation function matching .NET ValidateGetAzureOperationDetails method
export function validateGetAzureOperationDetails(
  storeId: string, 
  brandId: string, 
  operationId: string
): string[] {
  const validationErrors: string[] = [];

  // StoreId validation (matching .NET logic)
  if (!storeId || storeId.trim() === "") {
    validationErrors.push("StoreId can't be null or empty");
  }
  // Note: Store existence check (_storeService.IsStoreExists) would need to be implemented separately

  // BrandId validation (matching .NET logic)
  if (!brandId || brandId.trim() === "") {
    validationErrors.push("BrandId can't be null or empty");
  }

  // OperationId validation (matching .NET logic)
  if (!operationId || operationId.trim() === "") {
    validationErrors.push("OperationId can't be null or empty");
  }

  return validationErrors;
}

// Additional validation for store existence (to be implemented when store service is available)
export async function validateStoreExists(storeId: string): Promise<boolean> {
  // TODO: Implement store existence check
  // This would call _storeService.IsStoreExists(storeId) equivalent
  // For now, return true to avoid blocking
  return true;
}
