import { asClass, AwilixContainer } from 'awilix';
import { CQUser_MODULE } from '@org/guest-user';
import GuestUserService from '@org/guest-user-service';

type LoaderContext = {
  container: AwilixContainer;
};

export default async function ({ container }: LoaderContext): Promise<void> {
  // Register the shared-app service under the exact same token
  container.register({
    [CQUser_MODULE]: asClass(GuestUserService).singleton(),
  });

  console.log('🔌 GuestUserService bound in Admin container');
}