import logger from "../../utils/logger";
import { OrderDetail } from "../../types/responses/customResponse";
import { MicrosoftEndpoints } from "../../config/microsoftEndpoints";
import { getMethod } from "../externalService/externalEndPointService";

export async function getOrdersDetailFromVendorByID(
  req: any,
  customerId: string,
  orderId: string,
  token: string
): Promise<OrderDetail> {
  logger.info(
    `Entered into GetOrdersDetailFromVendorByID Service method with customerId - ${customerId}, orderId - ${orderId}`
  );

  try {
    console.log("orderRow.CustomerId--------->", customerId);
    console.log("orderRow.VendorOrderId--------->", orderId);
    console.log("token--------->", token);
    const completeUrlWithParams =
      MicrosoftEndpoints.getOrdersDetailFromVendorByIdUrl(customerId, orderId);

    const headerList = {
      Authorization: `Bearer ${token}`,
      Accept: "application/json",
    };

    const configModule = req.scope.resolve("configModule");

    const response = await getMethod(
      {
        url: completeUrlWithParams,
        headers: headerList,
        isVendorHit: true,
        module: "GetOrdersDetailFromVendorByID",
      },
      configModule
    );

    console.log("response----------->", response);

    logger.info(
      `Response from GetOrdersDetailFromVendorByID Response - ${JSON.stringify(
        response
      )}`
    );

    if (response.isSuccessStatusCode && response.content) {
      logger.info(`Entered into IsSuccessStatusCode = true`);

      const order = JSON.parse(response.content) as OrderDetail;
      logger.info(`Deserialized Object order - ${JSON.stringify(order)}`);

      if (!order || !order.Id) {
        logger.warn(`Throwing error - order is null or missing ID`);
        throw new Error("Error while fetching order from vendor");
      }

      logger.info(`Returning Order object - ${JSON.stringify(order)}`);
      return order;
    } else {
      logger.warn(
        `GetOrdersDetailFromVendorByID failed: HTTP ${response.httpStatusCode}, Error: ${response.errorMessage}`
      );
      throw new Error(
        response.errorMessage || "Error while fetching order from vendor"
      );
    }
  } catch (error) {
    logger.error(`Error in getOrdersDetailFromVendorByID: ${error}`);
    throw error;
  }
}
