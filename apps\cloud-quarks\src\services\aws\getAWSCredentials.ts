import logger from "../../utils/logger";
import { getSqlServerConnection } from "../../utils/sqlServerClient";
import { AWSCredentials } from "../../types/responses/customResponse";

// Global variable to store AWS credentials list
let AWSCredentialList: AWSCredentials[] = [];

export async function getAWSCredentials(): Promise<void> {
  try {
    logger.info("Entered into GetAWSCredentials method");

    // Get SQL connection 
    const pool = await getSqlServerConnection();
    const request = pool.request();

    // Execute stored procedure
    const procedure = "Usp_GetAWSCredentials";

    logger.info(`Going to execute stored procedure: ${procedure}`);

    const result = await request.execute<AWSCredentials>(procedure);
    console.log("result-------------->",result);

    // Populate global list
    AWSCredentialList = result.recordset || [];

    logger.info(`AWS credentials loaded successfully. Count: ${AWSCredentialList.length}`);
    logger.info(`AWS credentials: ${JSON.stringify(AWSCredentialList)}`);

  } catch (error: any) {
    // Handle exception 
    logger.error(`Error in GetAWSCredentials method: ${error.message}`, error);
    throw error;
  }
}

/**
 * Get AWS Credentials List
 * Returns the cached AWS credentials list
 */
export function getAWSCredentialsList(): AWSCredentials[] {
  return AWSCredentialList;
}

/**
 * Find AWS Credentials by Resource
 * Matches .NET StaticConfig.AWSCredentialList.Find(x => x.RESOURCE == resource)
 */
export function findAWSCredentialsByResource(resource: string): AWSCredentials | undefined {
  logger.info(`Finding AWS credentials for resource: ${resource}`);

  const credentials = AWSCredentialList.find(x => x.RESOURCE === resource);

  if (credentials) {
    logger.info(`AWS credentials found for resource: ${resource}`);
  } else {
    logger.warn(`No AWS credentials found for resource: ${resource}`);
  }

  return credentials;
}

export async function initializeAWSCredentials(): Promise<void> {
  logger.info("Initializing AWS credentials from database");
  await getAWSCredentials();
}

/**
 * Refresh AWS Credentials
 * Reloads AWS credentials from database
 * Useful for updating credentials without restarting the application
 */
export async function refreshAWSCredentials(): Promise<void> {
  logger.info("Refreshing AWS credentials from database");
  await getAWSCredentials();
}

/**
 * Check if AWS Credentials are loaded
 * Returns true if credentials have been loaded from database
 */
export function areAWSCredentialsLoaded(): boolean {
  return AWSCredentialList.length > 0;
}