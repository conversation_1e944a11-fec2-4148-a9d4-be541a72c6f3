import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { ContainerRegistrationKeys } from '@medusajs/framework/utils';
import { stringConstants } from '@org/utils';
import {
  generateExcelBuffer,
  ExcelColumn,
  ExportedUserRequest,
} from '@org/excel';
import countryCqUserLink from '../../../../../links/user-country';
import regionCountryCqStoreLink from '../../../../../links/region-store';
import { formatPagination } from '@org/utils';

/**
 * @openapi
 * /v1/user/accounts/users:
 *   get:
 *     summary: Get a list of users with advanced filters and optional Excel download
 *     tags:
 *       - Accounts
 *     parameters:
 *       - in: query
 *         name: source
 *         schema:
 *           type: string
 *         required: false
 *         description: Filter by user source (e.g., Direct End Customer)
 *       - in: query
 *         name: country_id
 *         schema:
 *           type: string
 *         required: false
 *         description: Filter by country ISO2 code
 *       - in: query
 *         name: company_type
 *         schema:
 *           type: string
 *           enum: [Guest User, Onboarded User]
 *         required: false
 *         description: Filter by company type
 *       - in: query
 *         name: q
 *         schema:
 *           type: string
 *         required: false
 *         description: Search by first or last name (partial match)
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         required: false
 *         description: Filter users created on or after this date (YYYY-MM-DD)
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         required: false
 *         description: Filter users created on or before this date (YYYY-MM-DD)
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         required: false
 *         description: Page number for pagination
 *       - in: query
 *         name: page_size
 *         schema:
 *           type: integer
 *           default: 10
 *         required: false
 *         description: Number of items per page
 *       - in: query
 *         name: download
 *         schema:
 *           type: string
 *           enum: [excel]
 *         required: false
 *         description: If 'excel', returns user data as a downloadable Excel file
 *     responses:
 *       200:
 *         description: List of users fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         example: "u123"
 *                       first_name:
 *                         type: string
 *                         example: "Indhira"
 *                       last_name:
 *                         type: string
 *                         example: "Doe"
 *                       email:
 *                         type: string
 *                         example: "<EMAIL>"
 *                       company_type:
 *                         type: string
 *                         example: "Direct End Customer"
 *                       is_onboarded:
 *                         type: boolean
 *                         example: true
 *                       is_active:
 *                         type: boolean
 *                         example: true
 *                       country:
 *                         type: object
 *                         properties:
 *                           display_name:
 *                             type: string
 *                             example: "Andorra"
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                       example: 1
 *                     pageSize:
 *                       type: integer
 *                       example: 10
 *                     totalItems:
 *                       type: integer
 *                       example: 125
 *                     totalPages:
 *                       type: integer
 *                       example: 13
 *                     hasNextPage:
 *                       type: boolean
 *                       example: true
 *                     hasPrevPage:
 *                       type: boolean
 *                       example: false
 *       404:
 *         description: No users found for the specified filters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Users for the specified country not found
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    let page = parseInt(req.query.page as string, 10);
    let pageSize = parseInt(req.query.page_size as string, 10);

    if (isNaN(page) || page < 1) page = 1;
    if (isNaN(pageSize) || pageSize < 1) pageSize = 10;

    const skip = (page - 1) * pageSize;
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
    const isDownload = req.query.download === 'excel';
    const filters: Record<string, any> = {};

    const { start_date, end_date } = req.query;
    if (start_date || end_date) {
      filters.created_at = {};
      if (start_date) {
        filters.created_at.$gte = new Date(start_date as string);
      }
      if (end_date) {
        filters.created_at.$lte = new Date(end_date as string);
      }
    }

    const keyword = req.query.q;
    if (keyword) {
      filters.$or = [
        { first_name: { $like: `%${keyword}%` } },
        { last_name: { $like: `%${keyword}%` } },
      ];
    }

    const companyTypeRaw = req.query.company_type as string;
    const companyType = Array.isArray(companyTypeRaw)
      ? (companyTypeRaw[0] || '').toString().toLowerCase()
      : (companyTypeRaw || '').toString().toLowerCase();

    if (companyType === 'guest user') {
      filters.is_onboarded = { $eq: false };
    } else if (companyType === 'onboarded user') {
      filters.is_onboarded = { $eq: true };
    }

    const sourceParam = (req.query.source as string) ?? '';
    if (sourceParam) {
      filters.source = { $eq: sourceParam };
    }

    let data, metadata;

    const fetchUsersByCountries = async (
      isoCodes: any[],
      skip: number,
      pageSize: number
    ) => {
      const { data } = await query.graph({
        entity: countryCqUserLink.entryPoint,
        fields: ['cq_user_id'],
        filters: {
          country_iso_2: isoCodes,
        },
        pagination: {
          skip,
          take: pageSize,
        },
      });

      return data;
    };

    const fetchCountriesByStore = async (storeId: any) => {
      const { data } = await query.graph({
        entity: regionCountryCqStoreLink.entryPoint,
        fields: ['country_iso_2'],
        filters: {
          cq_store_id: storeId,
        },
      });

      return data.map((entry) => entry.country_iso_2);
    };

    const { country_id, store_id } = req.query;
    let isoCodes = [];

    const hasCountry = !!country_id;
    const hasStore = false;

    if (hasCountry || hasStore) {
      if (hasCountry && hasStore) {
        const storeCountries = await fetchCountriesByStore(store_id);
        if (!storeCountries.length) {
          return res.status(404).json({
            message: stringConstants.NOT_FOUND(
              'Countries linked to the specified store'
            ),
          });
        }

        if (storeCountries.includes(country_id)) {
          isoCodes = [country_id];
        } else {
          return res.status(404).json({
            message: stringConstants.NOT_FOUND(
              'No users found for the given country and store combination'
            ),
          });
        }
      } else if (hasStore) {
        const storeCountries = await fetchCountriesByStore(store_id);
        if (!storeCountries.length) {
          return res.status(404).json({
            message: stringConstants.NOT_FOUND(
              'Users linked to the specified store'
            ),
          });
        }
        isoCodes = storeCountries;
      } else if (hasCountry) {
        isoCodes = [country_id];
      }

      const linkedUsers = await fetchUsersByCountries(isoCodes, skip, pageSize);

      if (!linkedUsers?.length) {
        return res.status(404).json({
          message: stringConstants.NOT_FOUND(
            'No users found for the given filters'
          ),
        });
      }

      filters.id = linkedUsers.map((user) => user.cq_user_id);
    }

    const result = await query.graph({
      entity: 'cq_user',
      fields: [
        '*',
        'country.*',
        'cq_designation.*',
        'country.region.*',
        // 'country.cq_stores.*',
      ],
      filters,
      pagination: {
        skip,
        take: pageSize,
      },
    });

    data = result.data.map((user: any) => {
      const hasStoreFilter = !!req.query.store_id;

      let filteredStores = [];

      if (user.country?.cq_stores?.length) {
        if (hasStoreFilter) {
          filteredStores = user.country.cq_stores.filter(
            (store: any) => store.id === req.query.store_id
          );
        } else {
          filteredStores = user.country.cq_stores.filter(
            (store: any) => store.default === true
          );
        }
      }

      return {
        ...user,
        country: user.country
          ? {
              ...user.country,
              // cq_stores: filteredStores,
            }
          : null,
      };
    });

    metadata = result.metadata;

    if (!data || data.length === 0) {
      return res.status(404).json({
        message: stringConstants.NOT_FOUND('User'),
      });
    }

    if (isDownload) {
      const columns: ExcelColumn<ExportedUserRequest>[] = [
        { header: 'ID', key: 'id', width: 10 },
        { header: 'Account Creation Date', key: 'created_at', width: 25 },
        { header: 'First Name', key: 'first_name', width: 15 },
        { header: 'Last Name', key: 'last_name', width: 15 },
        { header: 'Company Type', key: 'company_type', width: 25 },
        { header: 'Email', key: 'email', width: 20 },
        { header: 'Store', key: 'store', width: 25 },
        { header: 'Company Name', key: 'company_name', width: 20 },
        { header: 'Role', key: 'role', width: 25 },
        { header: 'Country', key: 'country', width: 25 },
        { header: 'User Type', key: 'user_type', width: 15 },
        { header: 'Status', key: 'status', width: 15 },
      ];

      const transformedData = data.map((user) => ({
        id: user.id,
        created_at: new Date(user.created_at).toLocaleDateString('en-GB'),
        first_name: user.first_name ?? '',
        last_name: user.last_name ?? '',
        company_type: user.source ?? '',
        email: user.email ?? '',
        store: user.country?.cq_stores?.[0]?.name ?? '',
        company_name: user.company_name ?? '',
        role: '',
        country: user.country?.display_name ?? '',
        user_type: user.is_onboarded ? 'Onboarded User' : 'Guest User',
        status: user.is_active ? 'Active' : 'Inactive',
      }));

      const buffer = await generateExcelBuffer(
        transformedData,
        columns,
        'User'
      );

      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      );
      res.setHeader('Content-Disposition', 'attachment; filename="user.xlsx"');
      return res.send(buffer);
    }

    const totalItems = metadata?.count ?? 0;

    return res.status(200).json({
      data: data,
      pagination: formatPagination({ page, pageSize, totalItems }),
    });
  } catch (err) {
    return res.status(500).json({
      message: stringConstants.INTERNAL_SERVER_ERROR(),
    });
  }
}
