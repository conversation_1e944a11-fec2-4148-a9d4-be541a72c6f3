import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import { GoogleAPIResponse, Operation, GoogleErrorResponse } from "../../types/responses/customResponse";
import { getGoogleTokenService } from "../token/google/getGoogleTokenService";
import { createGoogleRequestHeaders } from "../../config/microsoftEndpoints";
import { GoogleEndpoints } from "../../config/googleEndpoints";
import { PostMethodWithRequestJsonBodyAndHeaders400Handle } from "../externalService/externalEndPointService";
import { getGoogleAccountIdFromStore } from "../googleOffer/getGooglePurchasableOfferService";


export async function provisionCustomerCloudIdentityService(
  storeId: string,
  googleCustomerId: string,
  req?: MedusaRequest
): Promise<GoogleAPIResponse> {
  logger.info(`Entered into ProvisionCustomerCloudIdentity service method with storeId: ${storeId}, googleCustomerId: ${googleCustomerId}`);

  try {
    // Get Google Account ID (matching .NET: _googlePartnerManagementDAL.GetGoogleRedingtonAccountId(storeId))
    logger.info(`Going to call DAL method GetGoogleRedingtonAccountId with StoreId ${storeId}`);
    const googleAccountId = await getGoogleAccountIdFromStore(storeId);
    logger.info(`Got googleAccountId ${googleAccountId}`);

    // Build the provision cloud identity URL (matching .NET URL construction)
    const provisionCloudIdentityUrl = GoogleEndpoints.provisionCustomerCloudIdentityUrl(googleAccountId, googleCustomerId);
    logger.info(`Provision cloud identity URL: ${provisionCloudIdentityUrl}`);

    // Get Google token (matching .NET: _tokenService.GetGoogleToken(storeId))
    logger.info(`Going to hit GetGoogleToken method with storeId-${storeId}`);
    const token = await getGoogleTokenService(req, storeId);
    logger.info(`Got token from BL TokenService method.`);

    // Create Google request headers (matching .NET: _externalEndpointRequestHeaders.CreateGoogleRequestHeaders(token, storeId))
    const headerList = req
      ? createGoogleRequestHeaders(req, token, storeId)
      : {
          Authorization: `Bearer ${token}`,
          "x-region": storeId,
        };
    logger.info(`Prepared HeaderList for Google call: ${JSON.stringify(headerList)}`);

    logger.info(`Going to hit ExternalEndPoint PostMethodWithRequestJsonBodyAndHeaders400Handle method with url-${provisionCloudIdentityUrl}, RequestBody-null, headerlist-${JSON.stringify(headerList)}`);
    
    const configModule = req?.scope?.resolve("configModule");
    
    // Call Google API (matching .NET: PostMethodWithRequestJsonBodyAndHeaders400Handle with null body)
    const response = await PostMethodWithRequestJsonBodyAndHeaders400Handle(
      {
        url: provisionCloudIdentityUrl,
        body: null,
        headers: headerList,
        isVendorHit: true,
        module: "ProvisionCustomerCloudIdentity",
      },
      configModule
    );
    
    logger.info(`Response from ExternalEndPoint PostMethodWithRequestJsonBodyAndHeaders400Handle is ${JSON.stringify(response)}`);

    if (response.isSuccessStatusCode) {
      // Success response (matching .NET: deserialize into Operation)
      logger.info(`Received success response and deserializing response.Content-${response.content}`);
      
      const provisionCloudIdentityResponse: Operation = JSON.parse(response.content);
      
      // Extract operation ID from Name (matching .NET logic)
      logger.info(`Going to split provisionCloudIdentityResponse.name to fetch operationId`);
      if (provisionCloudIdentityResponse.Name) {
        const splittedArray = provisionCloudIdentityResponse.Name.split('/');
        provisionCloudIdentityResponse.OperationId = splittedArray[splittedArray.length - 1];
        logger.info(`Fetched operationId- ${provisionCloudIdentityResponse.OperationId}`);
      }

      const apiResponse: GoogleAPIResponse = {
        Message: "Success",
        Data: provisionCloudIdentityResponse,
        StatusCode: 200,
        IsError: false
      };

      logger.info(`Going to return ProvisionCustomerCloudIdentity API response- ${JSON.stringify(apiResponse)}`);
      return apiResponse;
    } else {
      // Error response (matching .NET error handling)
      logger.info(`Received errored response and deserialising response.Content-${response.errorMessage}`);
      
      const errorResponse: GoogleErrorResponse = JSON.parse(response.errorMessage || response.content || "{}");
      
      const apiResponse: GoogleAPIResponse = {
        Message: errorResponse.Error?.Message || "Unknown error occurred",
        StatusCode: errorResponse.Error?.Code || response.httpStatusCode,
        IsError: true
      };

      logger.info(`Going to return ProvisionCustomerCloudIdentity API response- ${JSON.stringify(apiResponse)}`);
      return apiResponse;
    }

  } catch (error: any) {
    logger.error(`Error in ProvisionCustomerCloudIdentity service method. Message: ${error.message}, StackTrace: ${error.stack}`);
    
    return {
      Message: `An error occurred: ${error.message}`,
      StatusCode: 422, // UnprocessableEntity
      IsError: true
    };
  }
}
