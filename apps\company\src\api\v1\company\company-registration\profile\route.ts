import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { ContainerRegistrationKeys } from '@medusajs/framework/utils';
import { stringConstants } from '@org/utils';
import { masterDB } from '../../../../../utils/master-db';
import { logger } from '@medusajs/framework';

/**
 * @openapi
 * /v1/company/company-registration/profile:
 *   get:
 *     summary: Retrieve partner registration profile
 *     tags:
 *       - Company Registration
 *     parameters:
 *       - in: query
 *         name: region_id
 *         required: true
 *         schema:
 *           type: string
 *         description: The region for which to retrieve the profile
 *     responses:
 *       200:
 *         description: Partner registration profile retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   description: The partner registration profile details
 *                 message:
 *                   type: string
 *                   example: Partner registration profile retrieved successfully
 *             example:
 *               data:
 *                 questions:
 *                   - question: "What type of partner are you?"
 *                     id: "Q01"
 *                     answer_type_id: "AT01"
 *                     answers:
 *                       - answer: "ISV"
 *                         id: "A01"
 *                       - answer: "SI"
 *                         id: "A02"
 *                       - answer: "MSP"
 *                         id: "A03"
 *                       - answer: "VAR"
 *                         id: "A04"
 *                       - answer: "Reseller"
 *                         id: "A05"
 *                     answer_type:
 *                       type: "single choice drop down"
 *                       id: "AT01"
 *                   - question: "How you came to know redington cloud?"
 *                     id: "Q02"
 *                     answer_type_id: "AT01"
 *                     answers:
 *                       - answer: "Social Media"
 *                         id: "A06"
 *                       - answer: "Webinar"
 *                         id: "A07"
 *                       - answer: "Sales Rep"
 *                         id: "A08"
 *                       - answer: "Email Campaign"
 *                         id: "A09"
 *                       - answer: "Other"
 *                         id: "A10"
 *                     answer_type:
 *                       type: "single choice drop down"
 *                       id: "AT01"
 *                   - question: "Your core business."
 *                     id: "Q03"
 *                     answer_type_id: "AT01"
 *                     answers:
 *                       - answer: "Software"
 *                         id: "A11"
 *                       - answer: "Hardware"
 *                         id: "A12"
 *                       - answer: "Services"
 *                         id: "A13"
 *                       - answer: "Consulting"
 *                         id: "A14"
 *                     answer_type:
 *                       type: "single choice drop down"
 *                       id: "AT01"
 *                   - question: "Focused customer vertical."
 *                     id: "Q04"
 *                     answer_type_id: "AT02"
 *                     answers:
 *                       - answer: "Healthcare"
 *                         id: "A15"
 *                       - answer: "Finance"
 *                         id: "A16"
 *                       - answer: "Education"
 *                         id: "A17"
 *                       - answer: "Retail"
 *                         id: "A18"
 *                       - answer: "Manufacturing"
 *                         id: "A19"
 *                     answer_type:
 *                       type: "multiple choice"
 *                       id: "AT02"
 *                   - question: "Select your interested workloads to work with redington"
 *                     id: "Q05"
 *                     answer_type_id: "AT03"
 *                     answers:
 *                       - answer: "Data & AI"
 *                         id: "A20"
 *                       - answer: "Security"
 *                         id: "A21"
 *                       - answer: "DevOps"
 *                         id: "A22"
 *                       - answer: "Cloud Infrastructure"
 *                         id: "A23"
 *                       - answer: "IoT"
 *                         id: "A24"
 *                     answer_type:
 *                       type: "multiple choice dropdown"
 *                       id: "AT03"
 *                   - question: "Year of incorporation:"
 *                     id: "Q06"
 *                     answer_type_id: "AT04"
 *                     answers: []
 *                     answer_type:
 *                       type: "input"
 *                       id: "AT04"
 *                   - question: "Banker name:"
 *                     id: "Q07"
 *                     answer_type_id: "AT04"
 *                     answers: []
 *                     answer_type:
 *                       type: "input"
 *                       id: "AT04"
 *                   - question: "Whether availing CC/OD"
 *                     id: "Q08"
 *                     answer_type_id: "AT05"
 *                     answers:
 *                       - answer: "Yes"
 *                         id: "A25"
 *                       - answer: "No"
 *                         id: "A26"
 *                     answer_type:
 *                       type: "single choice"
 *                       id: "AT05"
 *               message: Partner registration profile retrieved successfully
 *       400:
 *         description: Bad request - Region is missing or invalid
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Region is required and must be valid
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error. Please try again later.
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
	const regionId = req.query.region_id as string;

	try {
		const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

		const matchedRegion = await masterDB.query(
			`SELECT id FROM region WHERE id = $1`,
			[regionId]
		);

		if (matchedRegion.rowCount === 0) {
			return res.status(404).json({
				message: stringConstants.NOT_FOUND('Region'),
			});
		}

		const region = matchedRegion.rows[0];

		const { data: questions } = await query.graph({
			entity: 'cq_question',
			fields: ['question', 'id', 'answers.answer', 'answer_type.type'],
			filters: {
				region_id: region.id,
			},
			pagination: {
				order: {
					order_id: 'ASC',
				},
			},
		});

		return res.status(200).json({ questions });
	} catch (err: any) {
		logger.error('Error in company registration profile route:', err);
		return res.status(500).json({
			message: stringConstants.INTERNAL_SERVER_ERROR(),
		});
	}
}
