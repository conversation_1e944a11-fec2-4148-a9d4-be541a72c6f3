import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import { getStoreDetails } from "../token/microsoft/getStoreDetailsService";
import { getMsToken } from "../token/microsoft/getTokenService";
import {
  MicrosoftApiResponseWithResponseType
} from "../../types/responses/customResponse";
import {
  UnBilledAzureDataDownloadModelType
} from "../../validators/azure/unBilledAzureDataDownloadValidator";
import {
  getSubscriptionCountByIdAndPeriod,
  getAzureUnBilledUsageData,
  getS3FileDownLoadStatusAndKey,
  saveAzureUnbilledUsageQueue
} from "./azureDAL";
import { getPreSignedUrl } from "../aws/s3Handler";
import {
  AzureUnbilledUsageDataDownload,
  Messages,
  getAzureThresholdLimit,
  getAzureRetentionHours
} from "../../utils/constants/azureConstants";
import { isComparableStringEqual } from "../../validators/customer/customCustomerValidator";
import { getPreSignedUrlService } from "../aws/getPreSignedUrl";

export async function retrieveAzureUsageDataOrCreateQueueService(
  req: MedusaRequest,
  requestModel: UnBilledAzureDataDownloadModelType
): Promise<MicrosoftApiResponseWithResponseType> {
  logger.info(`Entered into RetrieveAzureUsageDataOrCreateQueue service method with unBilledAzureDataDownloadModel-${JSON.stringify(requestModel)}`);

  const apiResponse: MicrosoftApiResponseWithResponseType = {
    message: "Success",
    statusCode: 200,
    isError: false,
  };

  try {
    // Get configuration module for accessing settings
    const configModule = req.scope.resolve("configModule");

    // Get threshold limit from configuration
    const thresholdLimit = getAzureThresholdLimit();
    logger.info(`ThresholdLimit-${thresholdLimit}`);

    // Get subscription count by ID and period
    const subscriptionCount = await getSubscriptionCountByIdAndPeriod(
      requestModel.SubscriptionId,
      requestModel.Period
    );
    logger.info(`subscriptionCount-${subscriptionCount}`);

    if (thresholdLimit >= subscriptionCount) {
      logger.info(`Entered into section when threshold limit is more than Subscription row count`);

      // Get data directly from DB when under threshold
      const dataFromDB = await getAzureUnBilledUsageData(
        requestModel.CustomerId,
        requestModel.SubscriptionId,
        requestModel.Period
      );

      apiResponse.data = dataFromDB;
      apiResponse.message = Messages.AzureUnbilledUsageDownloadRequestReady;
      apiResponse.type = AzureUnbilledUsageDataDownload.THRESHOLD_DATA_DOWNLOADED;
    } else {
      logger.info(`Entered into section when data is more than threshold limit`);

      // Check if download request already exists
      const { status, s3FilePath } = await getS3FileDownLoadStatusAndKey(
        requestModel.SubscriptionId,
        requestModel.Period
      );
      logger.info(`Download request fetched with status ${status}, s3FilePath- ${s3FilePath}`);

      if (!status && !s3FilePath) {
        logger.info(`Entered into section when the download request does not exist.`);

        // Create new queue entry
        await saveAzureUnbilledUsageQueue(
          requestModel.StoreId,
          requestModel.SubscriptionId,
          requestModel.CustomerId,
          requestModel.Period,
          AzureUnbilledUsageDataDownload.STATUS_NOT_PROCESSED
        );

        apiResponse.statusCode = 202; // Accepted
        apiResponse.message = Messages.AzureUnbilledUsageDownloadRequestAccepted;
        apiResponse.type = AzureUnbilledUsageDataDownload.QUEUE_CREATED;
      } else if (
        isComparableStringEqual(status, AzureUnbilledUsageDataDownload.STATUS_NOT_PROCESSED) ||
        isComparableStringEqual(status, AzureUnbilledUsageDataDownload.STATUS_IN_PROGRESS)
      ) {
        logger.info(`Entered into section when the download request is in progress.`);

        apiResponse.statusCode = 202; // Accepted
        apiResponse.message = Messages.AzureUnbilledUsageDownloadRequestInProgress;
        apiResponse.type = AzureUnbilledUsageDataDownload.QUEUE_PROCESSING;
      } else if (
        isComparableStringEqual(status, AzureUnbilledUsageDataDownload.STATUS_COMPLETED) &&
        s3FilePath
      ) {
        logger.info(`Entered into section when the download request is completed.`);

        if (!requestModel.AWSCredentials) {
          logger.error(`AWS credentials not available for generating pre-signed URL`);
          apiResponse.statusCode = 500;
          apiResponse.message = "AWS credentials not configured";
          apiResponse.isError = true;
          return apiResponse;
        }

        // Get retention hours from configuration
        const retentionHours = getAzureRetentionHours();

        // Generate pre-signed URL
        const preSignedUrl = await getPreSignedUrlService(
          requestModel.AWSCredentials,
          s3FilePath,
          retentionHours
        );
        logger.info(`Generated presigned url - ${preSignedUrl}`);

        if (!preSignedUrl) {
          logger.info(`Entered into section when the download request exists but S3 file not found.`);

          // Re-create queue entry if S3 file not found
          await saveAzureUnbilledUsageQueue(
            requestModel.StoreId,
            requestModel.SubscriptionId,
            requestModel.CustomerId,
            requestModel.Period,
            AzureUnbilledUsageDataDownload.STATUS_NOT_PROCESSED
          );

          apiResponse.statusCode = 202; // Accepted
          apiResponse.message = Messages.AzureUnbilledUsageDownloadRequestAccepted;
          apiResponse.type = AzureUnbilledUsageDataDownload.QUEUE_CREATED;
        } else {
          apiResponse.data = preSignedUrl;
          apiResponse.statusCode = 201; // Created
          apiResponse.message = Messages.AzureUnbilledUsageDownloadRequestReady;
          apiResponse.type = AzureUnbilledUsageDataDownload.S3_URL_GENERATED;
        }
      } else {
        logger.info(`Entered into else condition, no condition matched`);
      }
    }

    logger.info(`Going to send response ${JSON.stringify(apiResponse)}`);
    return apiResponse;
  } catch (error) {
    logger.error(`RetrieveAzureUsageDataOrCreateQueue Service Error:`, error);

    const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";

    return {
      message: `Exception: ${errorMessage}`,
      statusCode: 422, // UnprocessableEntity
      isError: true,
    };
  }
}

