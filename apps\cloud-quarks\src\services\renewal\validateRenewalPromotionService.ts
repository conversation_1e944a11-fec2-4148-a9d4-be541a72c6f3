import logger from "../../utils/logger";
import { SubscriptionModel } from "../../types/responses/customResponse";
import { getSubscriptionDetailById } from "../linkedAccount/getSubscriptionService";
import { comparableString } from "../../utils/constants/brandEnum";

/**
 * Validate Renewal Promotion Request
 * Matches .NET ValidateRenewalPromotionRequest method exactly
 * This handles both basic validation AND database lookup validation
 */
export async function validateRenewalPromotionRequest(
  subscriptionId: string,
  materialNo: string,
  billType: string,
  term: string,
  quantity: number,
  isOldSubscriptionModified: boolean
): Promise<string[]> {
  const validationMessages: string[] = [];

  // Basic validation (matching .NET: if( string.IsNullOrEmpty(subscriptionId) ))
  if (!subscriptionId || subscriptionId.trim() === "") {
    validationMessages.push("Subscription id is required");
  } else if (subscriptionId && subscriptionId.trim() !== "") {
    // Database validation (matching .NET: else if( !string.IsNullOrEmpty(subscriptionId) ))

    // Get subscription details (matching .NET: SubscriptionModel subscriptionDetails = await _subscriptionService.GetSubscriptionDetailById(subscriptionId))
    const subscriptionDetails = await getSubscriptionDetailById(subscriptionId);
    logger.info(`Subscription details are ${JSON.stringify(subscriptionDetails)}`);

    // Check if subscription exists (matching .NET: var isSubscriptionExists = subscriptionDetails == null ? false : true)
    const isSubscriptionExists = subscriptionDetails ? true : false;

    if (!isSubscriptionExists) {
      logger.info(`subscription do not exists`);
      validationMessages.push("Subscription id do not exists in records");
    } else if (
      isOldSubscriptionModified === true &&
      subscriptionDetails &&
      comparableString(materialNo) === comparableString(subscriptionDetails.MaterialId) &&
      comparableString(billType) === comparableString(subscriptionDetails.PlanType) &&
      comparableString(term) === comparableString(subscriptionDetails.Duration) &&
      quantity === subscriptionDetails.Quantity
    ) {
      // Complex validation (matching .NET complex condition exactly)
      logger.info(`Section`);
      validationMessages.push(
        `Field isOldSubscriptionModified=${isOldSubscriptionModified} does not fit as materialid, billType, term, quantity are same as in DB`
      );
    }
  }

  logger.info(`Validation messages are ${JSON.stringify(validationMessages)}`);
  return validationMessages;
}
