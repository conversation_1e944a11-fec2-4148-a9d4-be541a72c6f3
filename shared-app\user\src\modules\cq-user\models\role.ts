import { model } from '@medusajs/framework/utils';
import Page from './page';
import RolePage from './role-page';
import CQUser from './cq_user';
import Persona from './persona';

const Role = model.define('cq_role', {
  id: model.id().primary<PERSON>ey(),
  role: model.text(),
  order_id: model.number().nullable(),
  metadata: model.json().nullable(),
  created_by: model.belongsTo(() => CQUser, { mappedBy: undefined }).nullable(),
  updated_by: model.belongsTo(() => CQUser, { mappedBy: undefined }).nullable(),
  deleted_by: model.belongsTo(() => CQUser, { mappedBy: undefined }).nullable(),
  pages: model.manyToMany(() => Page, {
    pivotEntity: () => RolePage,
    mappedBy: 'roles',
  }),
  persona: model.belongsTo(() => Persona, { mappedBy: 'roles' }),
});

export default Role;

// Role type table
