import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import { z } from "zod";

/**
 * @openapi
 * /v1/subscription/autoRenewSubscription:
 *   post:
 *     summary: Toggle Auto-Renewal for a Subscription
 *     tags:
 *       - Subscription
 *     parameters:
 *       - in: query
 *         name: subscriptionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Subscription ID to update
 *       - in: query
 *         name: storeId
 *         required: true
 *         schema:
 *           type: string
 *         description: Store ID
 *       - in: query
 *         name: autoRenew
 *         required: true
 *         schema:
 *           type: boolean
 *         description: Set to true to enable auto-renew, false to disable
 *     responses:
 *       200:
 *         description: Auto-renew status successfully updated
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                   example: "Pass"
 *                 Message:
 *                   type: string
 *                   example: AutoRenew status for SubscriptionId abc123 set to Yes
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                   example: "Fail"
 *                 Message:
 *                   type: string
 *                   example: Internal server error
 */

const querySchema = z.object({
  subscriptionId: z.string().min(1, "subscriptionId is required"),
  storeId: z.string().min(1, "storeId is required"),
  autoRenew: z
    .union([z.literal("true"), z.literal("false")])
    .transform((val) => val === "true"),
});

type StatusResponse = {
  Status: string;
  Message: string;
};

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req);

  try {
    const { subscriptionId, storeId, autoRenew } = querySchema.parse(req.query);

    logger.info(
      `Entered into AutoRenewSubscription API with subscriptionId-${subscriptionId}, storeId-${storeId}, autoRenew-${autoRenew}`,
      { correlationId }
    );

    const response: StatusResponse = {
      Status: "Pass",
      Message: `AutoRenew status for SubscriptionId ${subscriptionId} set to ${autoRenew ? "Yes" : "False"}`,
    };

    return res.status(200).json(response);
  } catch (error: any) {
    // Handle both Zod validation errors and unexpected errors
    const errorMessage =
      error?.issues?.[0]?.message || error?.message || "Internal server error";

    logger.error(`Error in AutoRenewSubscription`, {
      errorMessage,
      stack: error?.stack,
      correlationId,
    });

    const response: StatusResponse = {
      Status: "Fail",
      Message: errorMessage,
    };

    return res.status(500).json(response);
  }
}