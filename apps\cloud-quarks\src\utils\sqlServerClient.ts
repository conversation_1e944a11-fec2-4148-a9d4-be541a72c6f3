import sql from 'mssql';
import * as dotenv from "dotenv";
dotenv.config();

if (!process.env.SQL_SERVER || !process.env.SQL_USER || !process.env.SQL_PASS || !process.env.SQL_DATABASE) {
  throw new Error("Missing SQL environment variables");
}

// SQL_USER=CMP_Admin
// SQL_PASS=CMP@dmin#01
// SQL_SERVER=172.20.36.141
// SQL_DATABASE=MarketPlaceQA

const sqlConfig: sql.config = {
  user: 'CMP_Admin',
  password: 'CMP@dmin#01',
  server: '172.20.36.141',
  database: 'MarketPlaceQA',
  options: {
    encrypt: true, // Try false if local
    trustServerCertificate: true,
  },
  port: 1433,
};

export const getSqlServerConnection = async () => {
  try {
    console.log("Attempting SQL connection...");
    const pool = await sql.connect(sqlConfig);
    console.log("Connected to SQL Server");
    return pool;
  } catch (err) {
    console.error("SQL Server connection error:", err);
    throw err;
  }
};
