import logger from "../../utils/logger";
import { getSubscriptionDetailById } from "./getSubscriptionService";

export async function checkIfSubscriptionExists(subscriptionId: string): Promise<boolean> {
  const subscription = await getSubscriptionDetailById(subscriptionId);
  const exists = !!subscription;
  logger.info(`checkIfSubscriptionExists | subscriptionId=${subscriptionId} | exists=${exists}`);
  return exists;
}
