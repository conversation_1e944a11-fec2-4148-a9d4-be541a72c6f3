import logger from "../../utils/logger";
import {
  SubscriptionPromotionDetailModel
} from "../../types/responses/customResponse";
import { getSubscriptionDetailById } from "../linkedAccount/getSubscriptionService";
import { checkLastPromotionDetailsService } from "./checkLastPromotionDetailsService";
import { checkPromotionWithEligibilityService } from "./checkPromotionWithEligibility";
import { comparableString } from "../../utils/constants/brandEnum";

/**
 * Get Renewal Promotion
 * Matches .NET GetRenewalPromotion method
 */
export async function getRenewalPromotionService(
  req: any,
  subscriptionId: string,
  billType: string,
  term: string,
  brandId: number,
  materialno: string,
  quantity: number,
  segment: string,
  customerTermEndDate: Date,
  isOldSubscriptionModified: boolean
): Promise<SubscriptionPromotionDetailModel> {
  let subscriptionPromotionDetailModel: SubscriptionPromotionDetailModel = {
    promotionEligibilities: []
  };

  // Get subscription details (matching .NET: SubscriptionModel subDetails = await _subscriptionFactory.GetSubscriptionDetailById(subscriptionId))
  const subDetails = await getSubscriptionDetailById(subscriptionId);

  if (!subDetails) {
    throw new Error(`Subscription not found for ID: ${subscriptionId}`);
  }

  // Product upgrade means there is a change in any of these- Material Id, BillType, Term
  let isProductUpgrade = false;
  if (isOldSubscriptionModified) {
    // Matching .NET: materialno.ComparableString() != subDetails.MaterialId.ComparableString() || billType.ComparableString() != subDetails.PlanType.ComparableString() || term.ComparableString() != subDetails.Duration.ComparableString()
    isProductUpgrade =
      comparableString(materialno) !== comparableString(subDetails.MaterialId) ||
      comparableString(billType) !== comparableString(subDetails.PlanType) ||
      comparableString(term) !== comparableString(subDetails.Duration);

    logger.info(`isOldSubscriptionModified: ${isOldSubscriptionModified}, isProductUpgrade:${isProductUpgrade}`);
  }

  if (!isOldSubscriptionModified || !isProductUpgrade) {
    // Matching .NET: CheckLastPromotionDetails call
    logger.info(`Going to hit CheckLastPromotionDetails method with subscriptionid:${subscriptionId} and subDetails as ${JSON.stringify(subDetails)}`);
    subscriptionPromotionDetailModel = await checkLastPromotionDetailsService(
      req,
      subscriptionId,
      subDetails,
      billType,
      term,
      materialno,
      quantity,
      segment,
      brandId // Pass brandId parameter as in .NET
    );
  } else if (isOldSubscriptionModified && isProductUpgrade) {
    // Matching .NET: CheckPromotionWithEligibility call
    logger.info(`Entered in to section when isOldSubscriptionModified - ${isOldSubscriptionModified},isProductUpgrade - ${isProductUpgrade}`);
    subscriptionPromotionDetailModel = await checkPromotionWithEligibilityService(
      subDetails,
      billType,
      term,
      materialno,
      quantity,
      segment
    );
  }

  return subscriptionPromotionDetailModel;
}