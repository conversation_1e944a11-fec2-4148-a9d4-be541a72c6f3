import { z } from "zod";

// PrimaryContact object validation (camelCase to match request body)
export const PrimaryContactSchema = z.object({
  firstName: z.string().min(1, "firstName cannot be null or empty"),
  lastName: z.string().min(1, "lastName cannot be null or empty"),
  email: z.string().min(1, "email cannot be null or empty"),
  phoneNumber: z.string().optional(),
  middleName: z.string().optional(),
  organizationRegistrationNumber: z.string().optional(),
});

// PascalCase version for Microsoft API (after transformation)
export const PrimaryContactPascalSchema = z.object({
  FirstName: z.string().min(1, "FirstName cannot be null or empty"),
  LastName: z.string().min(1, "LastName cannot be null or empty"),
  Email: z.string().min(1, "Email cannot be null or empty"),
  PhoneNumber: z.string().optional(),
  MiddleName: z.string().optional(),
  OrganizationRegistrationNumber: z.string().optional(),
});

// Main SetCustomerAgreement DTO validation schema (camelCase to match request body)
export const SetCustomerAgreementSchema = z.object({
  primaryContact: PrimaryContactSchema,
  templateId: z.string().optional(), // Optional since it will be populated from Microsoft API
  dateAgreed: z.string().min(1, "dateAgreed cannot be null or empty"),
  type: z.string().min(1, "type cannot be null or empty"),
  agreementLink: z.string().min(1, "agreementLink cannot be null or empty"),
  userId: z.string().min(1, "userId cannot be null or empty"),
});

// PascalCase version for Microsoft API (after transformation)
export const SetCustomerAgreementPascalSchema = z.object({
  PrimaryContact: PrimaryContactPascalSchema,
  TemplateId: z.string().optional(),
  DateAgreed: z.string().min(1, "DateAgreed cannot be null or empty"),
  Type: z.string().min(1, "Type cannot be null or empty"),
  AgreementLink: z.string().min(1, "AgreementLink cannot be null or empty"),
  UserId: z.string().min(1, "UserId cannot be null or empty"),
});

// Type definitions for TypeScript (camelCase - for request body)
export type PrimaryContactType = z.infer<typeof PrimaryContactSchema>;
export type SetCustomerAgreementType = z.infer<typeof SetCustomerAgreementSchema>;

// Type definitions for Microsoft API (PascalCase - for external API)
export type PrimaryContactPascalType = z.infer<typeof PrimaryContactPascalSchema>;
export type SetCustomerAgreementPascalType = z.infer<typeof SetCustomerAgreementPascalSchema>;
