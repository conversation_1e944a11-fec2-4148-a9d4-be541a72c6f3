import logger from "../../utils/logger";
import sql from "mssql";
import { getSqlServerConnection } from "../../utils/sqlServerClient";
import { ValidateGetNCEProductUpgradesResponse } from "../../types/responses/customResponse";

/**
 * Validate Get NCE Product Upgrades Request DAL
 * Matches .NET TransitionDal.ValidateGetNCEProductUpgradesRequest method
 */
export async function validateGetNCEProductUpgradesRequestDAL(
  customerId: string,
  subscriptionId: string,
  storeId: string
): Promise<ValidateGetNCEProductUpgradesResponse | null> {
  logger.info(`Entered into DAL ValidateGetNCEProductUpgradesRequest method with customerId-${customerId},subscriptionId-${subscriptionId} and storeId-${storeId}`);

  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();

    const procedure = "spValidateCustomerSubscription"; // Matches .NET Procedures.spValidateCustomerSubscription

    // Add parameters (matching .NET: parameters.Add("@iSubscriptionId", subscriptionId); etc.)
    request.input("@iSubscriptionId", sql.VarChar(255), subscriptionId);
    request.input("@iCustId", sql.VarChar(255), customerId);
    request.input("@iStoreId", sql.VarChar(255), storeId);

    logger.info(`Going to hit DB with proc name : ${procedure} and parameters @iSubscriptionId: ${subscriptionId}, @iCustId: ${customerId}, @iStoreId: ${storeId}`);

    // Execute stored procedure (matching .NET: connection.QueryAsync<ValidateGetNCEProductUpgradesResponse>)
    const result = await request.execute<ValidateGetNCEProductUpgradesResponse>(procedure);
    console.log("result---->",result);
    const response = result.recordset?.[0] || null;

    logger.info(`Proc ran successfully, response ${JSON.stringify(response)}`);
    return response;

  } catch (error: any) {
    logger.error(`Error in validateGetNCEProductUpgradesRequestDAL: ${error.message}`);
    throw error;
  }
}
