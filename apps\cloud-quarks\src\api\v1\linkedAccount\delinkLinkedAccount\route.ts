import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import { validateLinkedAccountId } from "../../../../validators/linkedAccount/linkedAccountValidator";
import { delinkLinkedAccountService } from "../../../../services/linkedAccount/delinkLinkedAccountService";

/**
 * @openapi
 * /v1/linkedAccount/delinkLinkedAccount:
 *   put:
 *     summary: Delink a linked account by subscription ID
 *     description: Removes the association of a linked account using the provided subscription ID.
 *     tags:
 *       - LinkedAccount
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - linkedAccountId
 *             properties:
 *               linkedAccountId:
 *                 type: string
 *                 description: The subscription ID or linked account identifier to be delinked
 *                 example: "SUB123456"
 *     responses:
 *       200:
 *         description: Successfully delinked the account
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                   example: PASS
 *                 Message:
 *                   type: string
 *                   example: Linked account delinked successfully
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: FAIL
 *                 message:
 *                   type: array
 *                   items:
 *                     type: string
 *                   example: ["Linked account id cannot be null or empty"]
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: FAIL
 *                 message:
 *                   type: string
 *                   example: Internal server error
 */

export async function PUT(req: MedusaRequest, res: MedusaResponse) {
  try {
    const correlationId = getCorrelationId(req);
    const { linkedAccountId } = req.body as { linkedAccountId?: string };

    logger.info(
      `DelinkLinkedAccount API called | CorrelationId: ${correlationId}, linkedAccountId=${linkedAccountId}`
    );

    const validationMessages = await validateLinkedAccountId(linkedAccountId);

    if (validationMessages.length > 0) {
      logger.info(
        `Validation failed | CorrelationId: ${correlationId} | Errors: ${JSON.stringify(
          validationMessages
        )}`
      );
      return res.status(400).json({
        status: "FAIL",
        message: validationMessages,
      });
    }

    const response = await delinkLinkedAccountService(linkedAccountId!);

    logger.info(
      `DelinkLinkedAccount service completed | CorrelationId: ${correlationId}, Response: ${JSON.stringify(response)}`
    );

    return res.status(200).json(response);
  } catch (error) {
    logger.error("DelinkLinkedAccount API error", error as Error);
    return res.status(500).json({
      status: "FAIL",
      message: "Internal server error",
    });
  }
}
