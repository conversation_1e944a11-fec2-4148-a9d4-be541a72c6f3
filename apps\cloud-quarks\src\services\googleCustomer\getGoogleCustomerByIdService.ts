import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import { GoogleAPIResponse, GoogleCustomerDetailModel, GoogleErrorResponse } from "../../types/responses/customResponse";
import { getGoogleTokenService } from "../token/google/getGoogleTokenService";
import { createGoogleRequestHeaders } from "../../config/microsoftEndpoints";
import { GoogleEndpoints } from "../../config/googleEndpoints";
import { getMethod } from "../externalService/externalEndPointService";
import { getGoogleAccountIdFromStore } from "../googleOffer/getGooglePurchasableOfferService";

/**
 * Get Google Customer By ID
 * Matches .NET GetGoogleCustomerById service method
 */
export async function getGoogleCustomerByIdService(
  storeId: string,
  googleCustomerId: string,
  req?: MedusaRequest
): Promise<GoogleAPIResponse> {
  logger.info(`Entered into GetGoogleCustomerById service method with storeId: ${storeId}, googleCustomerId: ${googleCustomerId}`);

  let apiResponse: GoogleAPIResponse;

  try {
    // Get Google Account ID (matching .NET: _googlePartnerManagementDAL.GetGoogleRedingtonAccountId(storeId))
    logger.info(`Going to call DAL method GetGoogleRedingtonAccountId with StoreId ${storeId}`);
    const googleAccountId = await getGoogleAccountIdFromStore(storeId);
    logger.info(`Got googleAccountId ${googleAccountId}`);

    // Build the URL for GetGoogleCustomerById (matching .NET URL construction)
    const getGoogleCustomerByIdUrl = GoogleEndpoints.getGoogleCustomerByIdUrl(googleAccountId, googleCustomerId);
    logger.info(`GetGoogleCustomerById URL: ${getGoogleCustomerByIdUrl}`);

    // Get Google token (matching .NET: _tokenService.GetGoogleToken(storeId))
    logger.info(`Going to hit GetGoogleToken method with storeId-${storeId}`);
    const token = await getGoogleTokenService(req, storeId);
    logger.info(`Got token from BL TokenService method.`);

    // Create Google request headers (matching .NET: _externalEndpointRequestHeaders.CreateGoogleRequestHeaders(token, storeId))
    const headerList = req 
      ? createGoogleRequestHeaders(req, token, storeId)
      : {
          Authorization: `Bearer ${token}`,
          "x-region": storeId,
        };
    
    logger.info(`Going to hit ExternalEndPoint GetMethod method with url-${getGoogleCustomerByIdUrl} and headerlist-${JSON.stringify(headerList)}`);
    
    const configModule = req?.scope?.resolve("configModule");

    console.log("getGoogleCustomerByIdUrl----------->", getGoogleCustomerByIdUrl);
    console.log("headerList----------->", headerList);
    
    // Call Google API (matching .NET: GetMethod)
    const response = await getMethod(
      {
        url: getGoogleCustomerByIdUrl,
        headers: headerList,
        isVendorHit: true,
        module: "GetGoogleCustomerById",
      },
      configModule
    );

    logger.info(`Response from ExternalEndPoint GetMethod is ${JSON.stringify(response)}`);

    if (response.isSuccessStatusCode) {
      logger.info(`Received success response and deserializing response.Content-${response.content}`);
      
      // Deserialize response to GoogleCustomerDetailModel (matching .NET deserialization)
      const customerResponse: GoogleCustomerDetailModel = JSON.parse(response.content);
      customerResponse.googleCustomerId = googleCustomerId;
      
      apiResponse = {
        Message: "Success",
        Data: customerResponse,
        StatusCode: 200,
        IsError: false
      };
    } else {
      logger.info(`Received errored response and deserialising response.Content-${response.content}`);
      
      // Handle error response (matching .NET error handling)
      const errorResponse: GoogleErrorResponse = JSON.parse(response.content || response.errorMessage || "{}");
      
      apiResponse = {
        Message: errorResponse.Error?.Message || "Unknown error occurred",
        StatusCode: errorResponse.Error?.Code || 500,
        IsError: true
      };
    }

  } catch (error: any) {
    logger.error(`Error in GetGoogleCustomerById service: ${error.message}`);
    
    apiResponse = {
      Message: `An error occurred: ${error.message}`,
      StatusCode: 500,
      IsError: true
    };
  }

  logger.info(`Going to return GetGoogleCustomerById API response- ${JSON.stringify(apiResponse)}`);
  return apiResponse;
}
