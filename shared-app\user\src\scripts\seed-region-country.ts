import { Client } from 'pg';

const client = new Client({
  connectionString: process.env.DATABASE_URL,
});

export async function syncRegionCountry() {
  // Data to sync
  const insertData: [string, string, string, string, string, number | null, any | null][] = [
    ['bh', 'bhr', '048', 'BAHRAIN', 'Bahrain', null, null],
    ['ci', 'civ', '384', "COTE D'IVOIRE", "Cote D'Ivoire", null, null],
    ['eg', 'egy', '818', 'EGYPT', 'Egypt', null, null],
    ['et', 'eth', '231', 'ETHIOPIA', 'Ethiopia', null, null],
    ['gh', 'gha', '288', 'GHANA', 'Ghana', null, null],
    ['in', 'ind', '356', 'INDIA', 'India', null, null],
    ['iq', 'irq', '368', 'IRAQ', 'Iraq', null, null],
    ['jo', 'jor', '400', 'J<PERSON><PERSON><PERSON>', 'Jordan', null, null],
    ['ke', 'ken', '404', 'KENYA', 'Kenya', null, null],
    ['kw', 'kwt', '414', 'KUWAIT', 'Kuwait', null, null],
    ['lb', 'lbn', '422', 'LEBANON', 'Lebanon', null, null],
    ['mu', 'mus', '480', 'MAURITIUS', 'Mauritius', null, null],
    ['ng', 'nga', '566', 'NIGERIA', 'Nigeria', null, null],
    ['om', 'omn', '512', 'OMAN', 'Oman', null, null],
    ['qa', 'qat', '634', 'QATAR', 'Qatar', null, null],
    ['rw', 'rwa', '646', 'RWANDA', 'Rwanda', null, null],
    ['sa', 'sau', '682', 'SAUDI ARABIA', 'Saudi Arabia', null, null],
    ['za', 'zaf', '710', 'SOUTH AFRICA', 'South Africa', null, null],
    ['tz', 'tza', '834', 'TANZANIA, UNITED REPUBLIC OF', 'Tanzania, United Republic of', null, null],
    ['tr', 'tur', '792', 'TURKEY', 'Turkey', null, null],
    ['ug', 'uga', '800', 'UGANDA', 'Uganda', null, null],
    ['ae', 'are', '784', 'UNITED ARAB EMIRATES', 'United Arab Emirates', null, null],
  ];

  const insertQuery = `
    INSERT INTO region_country (iso_2, iso_3, num_code, name, display_name, region_id, metadata)
    VALUES ($1, $2, $3, $4, $5, $6, $7)
  `;

  try {
    await client.connect();

    // Fetch existing iso_2 values
    const existingRes = await client.query(`SELECT iso_2 FROM region_country`);
    const existingIso2 = new Set(existingRes.rows.map(row => row.iso_2));

    const incomingIso2 = new Set(insertData.map(row => row[0]));

    // Identify records to delete (in DB but not in insertData)
    const toDelete = [...existingIso2].filter(iso2 => !incomingIso2.has(iso2));

    // Identify records to insert (in insertData but not in DB)
    const toInsert = insertData.filter(row => !existingIso2.has(row[0]));

    // Delete outdated records
    if (toDelete.length > 0) {
      const deleteQuery = `
        DELETE FROM region_country
        WHERE iso_2 = ANY($1)
      `;
      await client.query(deleteQuery, [toDelete]);
      console.log(`Deleted ${toDelete.length} outdated records.`);
    }

    // Insert new records
    for (const record of toInsert) {
      await client.query(insertQuery, record);
    }
    console.log(`Inserted ${toInsert.length} new records.`);
  } catch (err: unknown) {
    if (err instanceof Error) {
      console.error('Error executing queries:', err.stack);
    } else {
      console.error('Error executing queries:', err);
    }
  } finally {
    await client.end();
  }
}
