import logger from "../../utils/logger";
import axios from "axios";
import { MicrosoftEndpoints } from "../../config/microsoftEndpoints";
import {
  SkuResponse,
  ResponseError
} from "../../types/responses/customResponse";

/**
 * Check SKU Availability
 * Matches .NET CheckSkuAvailability method
 */
export async function checkSkuAvailabilityService(
  customerId: string,
  productId: string,
  skuId: string,
  token: string,
  orderId?: string,
  mSRequestId?: string,
  segment?: string
): Promise<string> {
  logger.info(`Entered into CheckSkuAvailability Service method with customerId ${customerId}, ProductId ${productId}, SkuId ${skuId}, orderId ${orderId}, mSRequestId ${mSRequestId}, segment ${segment}`);

  // Get availability URL (matching .NET endpoint construction)
  const getAvailabilityURL = MicrosoftEndpoints.getAvailabilityUrl(customerId, productId, skuId);

  try {
    let catalog_id = "";

    logger.info(`Request for CheckSkuAvailability, API ${getAvailabilityURL}`);

    // Prepare headers (matching .NET HttpClient setup)
    const headers: Record<string, string> = {
      Authorization: `Bearer ${token}`,
      Accept: "application/json"
    };

    // Add MS-RequestId header if provided (matching .NET client.DefaultRequestHeaders.Add)
    if (mSRequestId) {
      headers["MS-RequestId"] = mSRequestId;
    }

    // Send request (matching .NET client.GetAsync)
    const response = await axios.get(getAvailabilityURL, {
      headers,
      validateStatus: () => true // Allow all status codes to handle errors manually
    });

    // Log response (matching .NET logging)
    logger.info(`Response from CheckSkuAvailability, response ${JSON.stringify(response.data)}`);

    if (response.status >= 200 && response.status < 300) {
      // Parse the response body (matching .NET response.IsSuccessStatusCode)
      const sku = response.data as SkuResponse;
      
      // Filter by segment if provided (matching .NET LINQ filtering)
      const filteredItems = segment 
        ? sku.items.filter(x => x.segment.toLowerCase() === segment.toLowerCase())
        : sku.items;

      if (sku && filteredItems.length > 0) {
        // Check product count if available (matching .NET logic)
        catalog_id = filteredItems[0].catalogItemId;
      } else {
        logger.info(`Entered into section where Item is not found, returning empty response`);
        return "";
      }
    } else {
      // Handle error response (matching .NET else block)
      logger.info(`Entered into a section where there is an exception`);
      const error = response.data as ResponseError;
      logger.info(`Exception: ${error.description}`);
      throw new Error(error.description || `HTTP ${response.status}: ${response.statusText}`);
    }

    logger.info(`returning catalog_id: ${catalog_id}`);
    return catalog_id;

  } catch (error: any) {
    logger.error(`Error in checkSkuAvailabilityService: ${error.message}`);
    
    // If it's an axios error, try to extract the error details
    if (axios.isAxiosError(error) && error.response?.data) {
      const errorData = error.response.data as ResponseError;
      throw new Error(errorData.description || error.message);
    }
    
    throw error;
  }
}
