import { model } from "@medusajs/framework/utils"
import CQUser from './cq_user';
import ActivityType from "./cq_activity_types" 

const UserActivityLog = model.define("cq_user_activity_logs", {
  id: model.id().primaryKey(),
  user: model.belongsTo(() => CQUser, { mappedBy: undefined }),
  activity_type: model.belongsTo(() => ActivityType, { mappedBy: undefined }),
  status: model.enum(["success", "error"]).default("success"),
  error_code: model.text().nullable(),
  error_message: model.text().nullable(),
  ip_address: model.text().nullable(),
  device_info: model.text().nullable(),
  location: model.text().nullable(),
  user_agent: model.text().nullable(),
  metadata: model.json().nullable(),

})

export default UserActivityLog