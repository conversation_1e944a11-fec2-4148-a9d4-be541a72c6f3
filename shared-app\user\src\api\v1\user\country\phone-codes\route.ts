import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from '@medusajs/framework';
import { ContainerRegistrationKeys } from '@medusajs/framework/utils';
import { formatPagination, stringConstants } from '@org/utils';

/**
 * @openapi
 * /v1/user/country/phone-codes:
 *   get:
 *     summary: Get a list of supported countries  phone codes
 *     tags:
 *       - Countries
 *     parameters:
 *       - in: query
 *         name: region_id
 *         schema:
 *           type: string
 *         required: false
 *         description: Optional region ID to filter countries by a specific region
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           example: 1
 *         required: false
 *         description: Optional page number for pagination (default is 1)
 *       - in: query
 *         name: page_size
 *         schema:
 *           type: integer
 *           example: 5
 *         required: false
 *         description: Optional number of results per page (default is 10)
 *     responses:
 *       200:
 *         description: List of countries with phone codes fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       iso_2:
 *                         type: string
 *                         example: "ad"
 *                       country_code:
 *                         type: string
 *                         example: "+91"
 *                       country_flag:
 *                         type: string
 *                         example: "test"
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                       example: 1
 *                     pageSize:
 *                       type: integer
 *                       example: 5
 *                     totalItems:
 *                       type: integer
 *                       example: 250
 *                     totalPages:
 *                       type: integer
 *                       example: 50
 *                     hasNextPage:
 *                       type: boolean
 *                       example: true
 *                     hasPrevPage:
 *                       type: boolean
 *                       example: false
 *                 message:
 *                   type: string
 *                   example: Phone code list fetched successfully.
 *       404:
 *         description: No countries found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items: {}
 *                 message:
 *                   type: string
 *                   example: Country not found
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error
 *                 error:
 *                   type: string
 *                   example: Internal server error. Please try again later.
 */

export async function GET(
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
): Promise<void> {
  try {
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

    let page = parseInt(req.query.page as string, 10);
    let pageSize = parseInt(req.query.page_size as string, 10);

    if (isNaN(page) || page < 1) page = 1;
    if (isNaN(pageSize) || pageSize < 1) pageSize = 10;

    const skip = (page - 1) * pageSize;

    const filters: Record<string, any> = {};

    const { region_id } = req.query;
    if (region_id) {
      filters.region_id = region_id;
    }

    const { data, metadata } = await query.graph({
      entity: 'country',
      fields: [
        'iso_2',
        'cq_country_extension.country_code',
        'cq_country_extension.country_flag',
        'cq_country_extension.is_custom',
      ],
      filters,
      pagination: {
        skip,
        take: pageSize,
        order: {
          iso_2: 'ASC',
        },
      },
    });

    const formattedData = data
      .filter((item) => item.cq_country_extension?.is_custom === true)
      .map((item: any) => ({
        iso_2: item.iso_2,
        country_code: item.cq_country_extension?.country_code ?? null,
        country_flag: item.cq_country_extension?.country_flag ?? null,
      }));

    const totalItems = metadata?.count ?? 0;

    res.status(200).json({
      success: true,
      data: formattedData,
      pagination: formatPagination({ page, pageSize, totalItems }),
      message: stringConstants.LIST_SUCCESS('Phone code'),
    });
  } catch (error) {
    console.error('Error fetching phone code:', error);
    res.status(500).json({
      success: false,
      message: stringConstants.INTERNAL_SERVER_ERROR(),
    });
  }
}
