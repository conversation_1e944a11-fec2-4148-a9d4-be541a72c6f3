import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { stringConstants } from '@org/utils';
import axios from 'axios';
import * as dotenv from 'dotenv';
dotenv.config();

/**
 * @openapi
 * /v1/auth/logout:
 *   delete:
 *     summary: Log out the current user by destroying the session and clearing cookies
 *     tags:
 *       - Users
 *     responses:
 *       200:
 *         description: Logout successful, session destroyed and cookies cleared
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Logout successful.
 *       500:
 *         description: Internal server error or session destruction failure
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Failed to logout due to an internal error.
 */

export async function DELETE(req: MedusaRequest, res: MedusaResponse) {
  try {
    if (req.session) {
      req.session.destroy((err: any) => {
        if (err) {
          console.error('Session destruction error:', err);
          return res
            .status(500)
            .json({ message: stringConstants.LOGOUT_ERROR() });
        }
      });
    }

    res.clearCookie('auth_cookie', {
      path: '/',
      httpOnly: true,
      secure: false,
      sameSite: 'lax',
    });

    res.clearCookie('custom_auth_session', {
      path: '/',
      httpOnly: true,
      secure: false,
      sameSite: 'lax',
    });

    return res
      .status(200)
      .json({ message: stringConstants.LOGOUT_SUCCESSFUL() });
  } catch (err: any) {
    if (axios.isAxiosError(err)) {
      const status = err.response?.status || 500;
      const message =
        err.response?.data?.message ||
        (status === 401
          ? stringConstants.INVALID_CREDENTIALS()
          : stringConstants.AUTHENTICATION_FAILED());

      return res.status(status).json({ message });
    }

    console.error('Unexpected Error:', err);
    return res
      .status(500)
      .json({ message: stringConstants.INTERNAL_SERVER_ERROR() });
  }
}
