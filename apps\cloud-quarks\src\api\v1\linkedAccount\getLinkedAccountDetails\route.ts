import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import { validateLinkedAccountId } from "../../../../validators/linkedAccount/linkedAccountValidator";
import { getLinkedAccountDetailsService } from "../../../../services/linkedAccount/getLinkedAccountDetailsService";

/**
 * @openapi
 * /v1/linkedAccount/getLinkedAccountDetails:
 *   get:
 *     summary: Get linked account details by subscription ID
 *     description: Retrieves linked account details using the provided subscription ID (linkedAccountId).
 *     tags:
 *       - LinkedAccount
 *     parameters:
 *       - in: query
 *         name: linkedAccountId
 *         required: true
 *         schema:
 *           type: string
 *         description: Linked account ID (usually the subscription ID)
 *         example: "SUB123456"
 *     responses:
 *       200:
 *         description: Successfully retrieved the linked account details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: PASS
 *                 message:
 *                   type: object
 *                   properties:
 *                     PartnerName:
 *                       type: string
 *                     SapCompany:
 *                       type: string
 *                     EmailId:
 *                       type: string
 *                     StoreID:
 *                       type: string
 *                     CustName:
 *                       type: string
 *                     SubscriptionId:
 *                       type: string
 *                     CustEmail:
 *                       type: string
 *                     SapCustId:
 *                       type: string
 *                     PartnerId:
 *                       type: string
 *                     CustId:
 *                       type: string
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: FAIL
 *                 message:
 *                   type: array
 *                   items:
 *                     type: string
 *                   example: ["Linked account id cannot be null or empty"]
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: FAIL
 *                 message:
 *                   type: string
 *                   example: Internal server error
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const correlationId = getCorrelationId(req);
    const { linkedAccountId } = req.query as { linkedAccountId?: string };

    logger.info(
      `GetLinkedAccountDetails API called | CorrelationId: ${correlationId}, linkedAccountId=${linkedAccountId}`
    );

    const validationMessages = await validateLinkedAccountId(linkedAccountId);

    if (validationMessages.length > 0) {
      logger.info(
        `Validation failed | CorrelationId: ${correlationId} | Errors: ${JSON.stringify(
          validationMessages
        )}`
      );
      return res.status(400).json({
        status: "FAIL",
        message: validationMessages,
      });
    }

    const linkedAccountDetails = await getLinkedAccountDetailsService(linkedAccountId!);

    logger.info(
      `Success | CorrelationId: ${correlationId} | Response: ${JSON.stringify(linkedAccountDetails)}`
    );

    return res.status(200).json({
      status: "PASS",
      message: linkedAccountDetails,
    });
  } catch (error) {
    logger.error("GetLinkedAccountDetails API error", error as Error);
    return res.status(500).json({
      status: "FAIL",
      message: "Internal server error",
    });
  }
}
