import {
  createWorkflow,
  createStep,
  StepResponse,
  WorkflowResponse,
} from '@medusajs/framework/workflows-sdk';
import { setAuthAppMetadataStep } from '@medusajs/medusa/core-flows';
import CQUserService from '../../../user/src/modules/cq-user/service';
import { CognitoIdentityServiceProvider } from 'aws-sdk';
import crypto from 'crypto';
import { GuestUserSchema } from '../api/v1/user/signup/validators';
import { ContainerRegistrationKeys, Modules } from '@medusajs/framework/utils';

import { z } from 'zod';
import { stringConstants } from '@org/utils';
import axios from 'axios';
import { logger } from '@medusajs/framework';
type GuestUserSchemaType = z.infer<typeof GuestUserSchema>;

type CreateGuestUserWorkflowInput = {
  guestUser: GuestUserSchemaType;
  authIdentityId: string;
  country_id: string;
};

const createCognitoUserStep = createStep(
  'create-cognito-user',
  async (input: CreateGuestUserWorkflowInput, { container }) => {
    const logger = container.resolve('logger');
    const clientId = process.env.COGNITO_CLIENT_ID!;
    const clientSecret = process.env.COGNITO_CLIENT_SECRET!;

    const cognito = new CognitoIdentityServiceProvider({
      region: process.env.S3_REGION!,
    });

    const computeSecretHash = (username: string): string => {
      const message = username + clientId;
      return crypto
        .createHmac('sha256', clientSecret)
        .update(message)
        .digest('base64');
    };

    const secretHash = computeSecretHash(input.guestUser.email);

    const params = {
      ClientId: clientId,
      Username: input.guestUser.email,
      Password: 'Test@123',
      UserAttributes: [
        { Name: 'custom:first_name', Value: input.guestUser.first_name },
        { Name: 'email', Value: input.guestUser.email },
        { Name: 'custom:last_name', Value: input.guestUser.last_name },
        { Name: 'custom:mobile_number', Value: input.guestUser.mobile_number },
        { Name: 'custom:company_name', Value: input.guestUser.company_name },
        { Name: 'custom:country_code', Value: input.guestUser.country_code },
        { Name: 'custom:role', Value: 'guest_user' },
      ],
      SecretHash: secretHash,
    };

    try {
      const signUpResponse = await cognito.signUp(params).promise();
      const cognitoUserId = signUpResponse.UserSub;

      return new StepResponse({
        success: true,
        message: stringConstants.REGISTERED_SUCCESSFULLY('Cognito User'),
        cognitoUserId: cognitoUserId,
      });
    } catch (error: any) {
      logger.error(error);
      throw new Error('Cognito user registration failed');
    }
  }
);

const createGuestUserStep = createStep(
  'create-guest-user-step',
  async (
    {
      guestUser: guestUserData,
    }: Pick<CreateGuestUserWorkflowInput, 'guestUser'>,
    { container }
  ) => {
    const guestUserModuleService: CQUserService = container.resolve('cq_user');
    const query = container.resolve(ContainerRegistrationKeys.QUERY);

    const { data: users } = await query.graph({
      entity: 'cq_user',
      fields: ['id'],
      filters: {},
      pagination: {
        skip: 0,
        take: 1,
        order: {
          created_at: 'DESC',
        },
      },
    });

    let newId = '001';
    if (users && users.length > 0) {
      const maxIdNum = parseInt(users[0].id, 10);
      if (!isNaN(maxIdNum)) {
        newId = String(maxIdNum + 1).padStart(3, '0');
      }
    }

    if (guestUserData.profile_pic_file_name) {
      try {
        const s3Response = await axios.post(`${process.env.S3_URL}`, {
          folder_name: 'cq-profile-picture',
          image_name: guestUserData.profile_pic_file_name,
        });

        guestUserData.profile_pic_file_name = s3Response.data?.link
          .split('/')
          .pop();
      } catch (error: any) {
        throw new Error('Failed to upload profile picture');
      }
    }

    const guestUser = await guestUserModuleService.createCqUsers({
      ...guestUserData,
      cq_designation: guestUserData.designation,
      id: newId,
    });

    return new StepResponse(guestUser);
  }
);

const createUserCountryLinkStep = createStep(
  'create-user-country-link-step',
  async (
    {
      user,
      countryId,
    }: {
      user: any;
      countryId: string;
    },
    { container }
  ) => {
    const logger = container.resolve('logger');
    const link = container.resolve(ContainerRegistrationKeys.LINK);

    try {
      await link.create({
        [Modules.REGION]: { country_iso_2: countryId },
        cq_user: { cq_user_id: user.id },
      });

      return new StepResponse({
        success: true,
        message: 'User-country link created successfully',
      });
    } catch (error: any) {
      logger.error('Error creating user-country link:', error);
      throw new Error('Failed to create user-country link');
    }
  }
);

const createGuestUserWorkflow = createWorkflow(
  'create-guest-user',
  function (input: CreateGuestUserWorkflowInput) {
    const guestUser = createGuestUserStep({
      guestUser: input.guestUser,
    });

    createUserCountryLinkStep({
      user: guestUser,
      countryId: input.country_id,
    });

    // createCognitoUserStep(input);

    // setAuthAppMetadataStep({
    //   authIdentityId: input.authIdentityId,
    //   actorType: 'guest_user',
    //   value: guestUser.id,
    // });

    return new WorkflowResponse(guestUser);
  }
);

export default createGuestUserWorkflow;
