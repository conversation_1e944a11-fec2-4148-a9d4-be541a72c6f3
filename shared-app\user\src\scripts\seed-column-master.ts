import { ExecArgs } from '@medusajs/framework/types';
import { CQUser_MODULE } from '../modules/cq-user';
import CQUserService from '../modules/cq-user/service';

export default async function seedColumnMaster({ container }: ExecArgs) {
  const cqUserService: CQUserService = container.resolve(CQUser_MODULE);

  const tableColumnMap = {
    Users: [
      'ID',
      'Company Name',
      'User Type',
      'Status',
      'Account Creation Date',
      'Email ID',
      'Roles',
      'Action',
      'First Name',
      'Last Name',
      'Country',
      'Requested Source',
    ],
    'Company Registration': [
      'Registration ID',
      'Country',
      'First Name',
      'Last Name',
      'Requested Source',
      'User Email ID',
      'Company Name',
      'Redington Account ID',
      'Requested By',
      'Requested Store',
      'Status',
      'Action',
      'State',
    ],
    'User Roles': ['ID', 'Role Name', 'Persona', 'Created Date', 'Action'],
  };

  // Slug & type map
  const slugTypeMap: any = {
    ID: { slug: 'id', type: 'text' },
    'First Name': { slug: 'first_name', type: 'text' },
    'Last Name': { slug: 'last_name', type: 'text' },
    'Email ID': { slug: 'email', type: 'text' },
    'Company Name': { slug: 'company_name', type: 'text' },
    Country: { slug: 'country', type: 'object' },
    Roles: { slug: 'cq_roles', type: 'array' },
    'User Type': { slug: 'is_onboarded', type: 'boolean' },
    Status: { slug: 'status', type: 'object' },
    'Account Creation Date': { slug: 'account_creation_date', type: 'text' },
    'Company Type': { slug: 'company_type', type: 'text' },
    'Registration ID': { slug: 'registration_id', type: 'text' },
    'Role Name': { slug: 'role', type: 'text' },
    'Persona': { slug: 'persona', type: 'object' },
    'Created Date': { slug: 'created_at', type: 'timestamp' },
    'Action': { slug: 'action', type: 'text' },
  };

  const tableMasters = (await cqUserService.listCqPages?.({})) || [];

  const tableMap = new Map(
    tableMasters.map((t) => [t.page.toLowerCase(), t.id])
  );

  const columnsToCreate: any = [];

  for (const [tableName, columnNames] of Object.entries(tableColumnMap)) {
    const tableId = tableMap.get(tableName.toLowerCase());

    if (!tableId) {
      console.warn(`Table not found: ${tableName}`);
      continue;
    }

    columnNames.forEach((columnName, index) => {
      const def = slugTypeMap[columnName] || {
        slug: columnName.toLowerCase().replace(/\s+/g, '_'),
        type: 'text',
      };

      columnsToCreate.push({
        column_name: columnName,
        column_slug: def.slug,
        column_type: def.type,
        sort_order: index + 1,
        table_master_id: tableId,
        metadata: {},
      });
    });
  }

  const existingColumns = (await cqUserService.listCqColumnMasters?.({})) || [];

  const existingKeys = new Set(
    existingColumns.map(
      (c) => `${c.table_master_id}-${c.column_slug?.toLowerCase()}`
    )
  );

  const newColumns = columnsToCreate.filter((col: any) => {
    const key = `${col.table_master_id}-${col.column_slug.toLowerCase()}`;
    return !existingKeys.has(key);
  });

  if (newColumns.length > 0) {
    await Promise.all(
      newColumns.map((col: any) => cqUserService.createCqColumnMasters(col))
    );
  }

  console.log(`Seeded ${newColumns.length} column(s).`);
}
