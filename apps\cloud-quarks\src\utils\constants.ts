export const Urls = {
  MS_BASE_URL_MULESOFT: `http://172.20.36.236:8600`,
  GET_TOKEN: `http://172.20.36.115:8503/api/gettoken/{0}/oauth2/token`,
  TEST_URL_TO_GET_TOKEN: `https://login.microsoftonline.com/rilsbox.onmicrosoft.com/oauth2/token`,
  MS_BASE_URL: `https://api.partnercenter.microsoft.com`,
  GGOGLE_BASE_URL: `https://cloudchannel.googleapis.com`,
  MS_OAUTH_GET_TOKEN: `https://login.microsoftonline.com/{0}/oauth2/token`,
  MS_CREATE_CUSTOMER_PATH: `/v1/customers`,
  MS_GET_CUSTOMER_VALIDATION_URL: `/v1/customers/{0}/validationStatus?type=account`,
  MS_GET_CUSTOMER_USERS_URL: `/v1/customers/{0}/users`,
  MS_GET_CUSTOMER_BILLING_INFO_URL: `/v1/customers/{0}/profiles/billing`,
  MS_GET_CUSTOMER_AGREEMENT_URL: `/v1/customers/{0}/agreements?agreementType=MicrosoftCustomerAgreement`,
  MS_GET_CUSTOMER_BY_ID_URL: `/v1/customers/{0}`,
  MS_GET_CUSTOMERS_URL: `/v1/customers?filter={0}`,
  MS_CHECK_DOMAIN_URL: `/v1/domains/{0}`,
  MS_GET_DIRECT_SIGN_STATUS_URL: `/v1/customers/{0}/directSignedMicrosoftCustomerAgreementStatus`,
  MS_CHECK_MPNID_STATUS_URL: `/v1/profiles/mpn?mpnId={0}`,
  MS_GET_MIC_AGREEMENTS_URL: `/v1/agreements?agreementType=MicrosoftCustomerAgreement`,
  MS_POST_AGREEMENTS_URL: `/v1/customers/{0}/agreements`,
  MS_EXPORT_AZURE_BILLED_USAGE_URL: `/v1.0/reports/partners/billing/usage/billed/export`,
  MS_EXPORT_AZURE_UNBILLED_USAGE_URL: `/v1/invoices/unbilledusage`,
  CS_OAUTH_GET_TOKEN: `/sys-moderncommercep4-api/as/token.oauth2`,
  CS_BASE_URL: `apx.cisco.com`,
  CS_CUSTOMER_BASE_URL: `api.cisco.com`,
  CS_PLACE_ORDER: `/sys-moderncommercep4-api/{0}/subscription`,
  CS_CHECK_STATUS: `/sys-moderncommercep4-api/{0}/subscription/status?csbSubscriptionId={1}`,
  CS_PROVISIONING_VALIDATE: `/sys-moderncommercep4-api/{0}/provisioning/validate`,
  CS_GET_CUSTOMER_NAMES: `/sys-moderncommercep4-api/{0}/search/getCustomerNames`,
  CS_VALIDATE_CUSTOMER: `/sys-moderncommercep4-api/{0}/validation/customer`,
  CS_SUBSCRIPTION_UPDATE: `/sys-moderncommercep4-api/{0}/subscription`,
  MS_GET_ENTITLEMENTS_URL_PATH_1: `/v1/customers/{0}/entitlements`,
  MS_GET_ENTITLEMENTS_URL_PATH_2: `?entitlementtype={1}&showExpiry=true`,
  MS_GET_PROVISIONAL_STATUS_URL_PATH_1: `/v1/customers/{0}/orders`,
  MS_GET_PROVISIONAL_STATUS_URL_PATH_2: `/{1}/provisioningstatus`,
  MS_GET_ORDER_DETAIL_FROM_VENDORS_BY_ID_URL_PATH_1: `/v1/customers/{0}`,
  MS_GET_ORDER_DETAIL_FROM_VENDORS_BY_ID_URL_PATH_2: `/orders/{1}`,
  MS_TERMINATE_ORDER_URL_PATH_1: `/v1/customers/{0}`,
  MS_TERMINATE_ORDER_URL_PATH_2: `/orders/{1}`,
  GET_SUBSCRIPTION_BY_ID_URL_PATH_1: `/v1/customers/{0}`,
  GET_SUBSCRIPTION_BY_ID_URL_PATH_2: `/subscriptions/{1}`,
  GET_AZURE_PLAN_ENTITLEMENT_URL_PATH_1: `/v1/customers/{0}/subscriptions`,
  GET_AZURE_PLAN_ENTITLEMENT_URL_PATH_2: `/{1}/azureentitlements`,
  GET_GOOGLE_PURCHASABLE_OFFERS: `/v1/accounts/{0}/customers/{1}:listPurchasableOffers?createEntitlementPurchase.sku=products/{2}/skus/{3}&pageSize=500`,
  GET_GOOGLE_ENTITLEMENT_BY_ID: `/v1/accounts/{0}/customers/{1}/entitlements/{2}`,
  GET_GOOGLE_CUSTOMER_BY_ID: `/v1/google/{0}/customers/{1}`,
  GET_CHANNEL_PARTNER_LINK_STATE: `/v1/google/{0}/channelPartnerLinks/{1}?view=full`,
  MS_CHECK_SUBSCRIPTION_STATUS_URL: `/v1/customers/{0}/subscriptions/{1}/registrationstatus`,
  MS_REGISTER_SUBSCRIPTION_URL: `/v1/customers/{0}/subscriptions/{1}/registrations`,
  MS_GET_SUBSCRIPTION_URL: `/v1/customers/{0}/subscriptions/{1}`,
  MS_PATCH_SUBSCRIPTION_URL: `/v1/customers/{0}/subscriptions/{1}`,
  MS_GET_NCE_TRANSITION_ELIGIBILITY_URL: `/v1/customers/{0}/subscriptions/{1}/transitionEligibilities`,
  MS_VERIFY_PROMOTION_ELIGIBILITY_URL: `/v1/customers/{0}/promotionEligibilities`,
  MS_GET_AVAILABILITY_URL: `/v1/customers/{0}/products/{1}/skus/{2}/availabilities`,
  GOOGLE_VERIFY_DOMAIN_EXISTS: `/v1/google/checkCloudIdentityAccountsExist/{0}`,
  GOOGLE_CREATE_CUSTOMER: `/v1/google/{0}/customers`,
  GOOGLE_PROVISION_CUSTOMER_CLOUD_IDENTITY: `/v1/google/{0}/customers/{1}/provisionCloudIdentity`,
  GET_INDIRECT_RESELLER_URL: `/v1/relationships?relationship_type=IsIndirectCloudSolutionProviderOf`,
  GRAPH_BASE_URL: `https://graph.microsoft.com`,
  EXPORT_RECONCILIATION_DATA_URL: `/v1.0/reports/partners/billing/reconciliation/billed/export`,
  MS_GET_AZURE_OPERATION_DETAILS_URL: `/v1.0/reports/partners/billing/operations/{0}`
}
