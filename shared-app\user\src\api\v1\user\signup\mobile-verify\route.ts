import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import CQUserService from '../../../../../modules/cq-user/service';
import { CQUser_MODULE } from '../../../../../modules/cq-user';
import { stringConstants } from '@org/utils';

/**
 * @openapi
 * /v1/user/signup/mobile-verify:
 *   put:
 *     summary: Verify OTP for mobile number
 *     tags:
 *       - Signup - Mobile
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - mobile_number
 *               - otp_code
 *             properties:
 *               mobile_number:
 *                 type: string
 *                 example: "+11234567890"
 *               otp_code:
 *                 type: string
 *                 example: "123456"
 *     responses:
 *       200:
 *         description: OTP verified successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 is_verified:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: OTP verified successfully.
 *       400:
 *         description: OTP expired or invalid, or mobile number missing
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: OTP is invalid or expired
 *       404:
 *         description: OTP record not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: No OTP record found for the given mobile number
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error
 */

export async function PUT(req: MedusaRequest, res: MedusaResponse) {
  const emailOtpService: CQUserService = req.scope.resolve(CQUser_MODULE);

  try {
    const { mobile_number, otp_code } = req.body as {
      mobile_number: string;
      otp_code: string;
    };

    if (!mobile_number || !otp_code) {
      return res.status(400).json({ message: stringConstants.MOBILE_VERIFY() });
    }

    const otpRecords = await emailOtpService.listEmailOtpVerifications({
      mobile_number,
    });

    if (!otpRecords || otpRecords.length === 0) {
      return res.status(404).json({
        message: stringConstants.OTP_RECORD_NOT_FOUND('mobile number'),
      });
    }

    const latestOtp = otpRecords[otpRecords.length - 1];
    const now = new Date();

    if (new Date(latestOtp.expires_at) < now) {
      return res.status(400).json({ message: stringConstants.OTP_EXPIRED() });
    }

    if (latestOtp.otp_code !== otp_code) {
      return res.status(400).json({ message: stringConstants.OTP_INVALID() });
    }

    const data = await emailOtpService.updateEmailOtpVerifications({
      id: latestOtp.id,
      is_verified: true,
    });

    return res.status(200).json({
      is_verified: data.is_verified,
      message: stringConstants.OTP_VERIFIED(),
    });
  } catch (err: any) {
    return res.status(500).json({});
  }
}
