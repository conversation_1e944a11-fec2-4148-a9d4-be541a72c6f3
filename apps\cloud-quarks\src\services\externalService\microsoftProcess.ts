import { getMsToken } from "../../services/index";
import logger from "../../utils/logger";
import { MicrosoftCreateCustomerRequest } from "../../config/microsoftEndpoints";
import { ComparableString } from "../../validators/customer/customCustomerValidator";
import { CreateCustomerStatusResponse } from "../../types/responses/customResponse";
import { UpsertCustomer } from "../customer/upsertCustomerService/upsertCustomer";
import axios from "axios";
import { Urls } from "../../utils/constants";

export async function MicrosoftProcess(
  customerModel: any,
  microsoftRequest: MicrosoftCreateCustomerRequest,
  store: any
): Promise<CreateCustomerStatusResponse> {
  logger.info(
    `Entered into MicrosoftProcess method with customer model: ${JSON.stringify(
      customerModel
    )}, microsoft request body: ${JSON.stringify(
      microsoftRequest
    )}, store: ${JSON.stringify(store)}`
  );

  const response: CreateCustomerStatusResponse = {
    status: "FAIL",
    message: "Unhandled",
  };

  try {

    const token = await getMsToken({
      brand: store.brand || "",
      client_id: store.clientid || "",
      client_secret: store.clientsecret || "",
      grant_type: store.granttype || "",
      markValue: store.markvalue?.toString() || "",
      redirect_uri: store.redirecturi || "",
      refresh_token: store.token || "",
      resource: store.resource || "",
      store_domain: store.storedomain || "",
    });

    console.log("token------->",token)

    logger.info(
      `Going to hit CreateCustomerAtMicrosoft method with token: ${token} and microsoft create request: ${JSON.stringify(
        microsoftRequest
      )}`
    );

    const { status: msStatus, message: msMessage } =
      await CreateCustomerAtMicrosoft(microsoftRequest, token.access_token);

    logger.info(
      `Got response from Microsoft: ${JSON.stringify({ msStatus, msMessage })}`
    );

    const normalizedStatus = ComparableString(msStatus);

    if (normalizedStatus === "CREATED") {
      const msCustomer: {
        Id?: string;
        UserCredentials?: {
          UserName?: string;
          Password?: string;
        };
      } = JSON.parse(msMessage);

      customerModel.MPNID = msCustomer?.Id;
      customerModel.SAPCustId = msCustomer?.UserCredentials?.UserName;
      customerModel.SAPCompany = msCustomer?.UserCredentials?.Password;

      logger.info(`Customer create model: ${JSON.stringify(customerModel)}`);
      logger.info(
        `Calling UpsertCustomer('UPDATECUSTOMER') with updated model`
      );

      const updateRes = await UpsertCustomer("UPDATECUSTOMER", customerModel);
      logger.info(`Response from UpsertCustomer: ${JSON.stringify(updateRes)}`);

      response.status = updateRes?.status ?? false;
      response.message = msCustomer?.Id ?? "";
      response.UserName = customerModel.SAPCustId;
      response.Password = customerModel.SAPCompany;
    } else {
      logger.info(`Status from Microsoft was ${normalizedStatus}`);

      response.status = normalizedStatus === "CREATED" ? "PASS" : "FAIL";
      response.message = msMessage;
    }
  } catch (error) {
    logger.error("Error in MicrosoftProcess: ", error as Error);
    response.status = 'FAIL';
    response.message = "Exception in MicrosoftProcess";
  }

  logger.info(
    `Returning response from MicrosoftProcess: ${JSON.stringify(response)}`
  );
  return response;
}

async function CreateCustomerAtMicrosoft(
  custRequest: MicrosoftCreateCustomerRequest,
  token: string
): Promise<{ status: string; message: string }> {
  try {
    const baseUrl = Urls.MS_BASE_URL;
    const endpointPath = Urls.MS_CREATE_CUSTOMER_PATH;
    const endPoint = `${baseUrl}${endpointPath}`;

    logger.info(`Complete Microsoft endpoint is: ${endPoint}`);

    const headers = {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    };

    const response = await axios.post(endPoint, custRequest, { headers });

    logger.info(
      `Got Status from Microsoft: ${
        response.status
      }, Full response: ${JSON.stringify(response.data)}`
    );

    return {
      status: String(response.status),
      message: JSON.stringify(response.data),
    };
  } catch (err: any) {
    logger.error("Error while calling Microsoft API", err);
    return {
      status: "500",
      message: "Error while calling Microsoft API",
    };
  }
}
