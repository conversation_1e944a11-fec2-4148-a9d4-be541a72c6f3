{"namespaces": ["public"], "name": "public", "tables": [{"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "type": {"name": "type", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_answer_type", "schema": "public", "indexes": [{"keyName": "IDX_cq_answer_type_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_answer_type_deleted_at\" ON \"cq_answer_type\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_answer_type_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "redington_account_id": {"name": "redington_account_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "user_id": {"name": "user_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "region_country_id": {"name": "region_country_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "country": {"name": "country", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "gst_number": {"name": "gst_number", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "company_name": {"name": "company_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "address1": {"name": "address1", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "address2": {"name": "address2", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "country_of_business": {"name": "country_of_business", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "state": {"name": "state", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "city": {"name": "city", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "postal_code": {"name": "postal_code", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "website": {"name": "website", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "camunda_process_id": {"name": "camunda_process_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "director_first_name": {"name": "director_first_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "director_last_name": {"name": "director_last_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "director_email": {"name": "director_email", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "director_country_code": {"name": "director_country_code", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "director_mobile_number": {"name": "director_mobile_number", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "sales_first_name": {"name": "sales_first_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "sales_last_name": {"name": "sales_last_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "sales_email": {"name": "sales_email", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "sales_country_code": {"name": "sales_country_code", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "sales_mobile_number": {"name": "sales_mobile_number", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "accounts_first_name": {"name": "accounts_first_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "accounts_last_name": {"name": "accounts_last_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "accounts_email": {"name": "accounts_email", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "accounts_country_code": {"name": "accounts_country_code", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "accounts_mobile_number": {"name": "accounts_mobile_number", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "requested_by_admin": {"name": "requested_by_admin", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "boolean"}, "requested_source": {"name": "requested_source", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "status_id": {"name": "status_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "store_id": {"name": "store_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "reason": {"name": "reason", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "metadata": {"name": "metadata", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "json"}, "created_by": {"name": "created_by", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "updated_by": {"name": "updated_by", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "deleted_by": {"name": "deleted_by", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_company_onboarding", "schema": "public", "indexes": [{"keyName": "IDX_cq_company_onboarding_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_company_onboarding_deleted_at\" ON \"cq_company_onboarding\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_company_onboarding_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "user_id": {"name": "user_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "region_country_id": {"name": "region_country_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "gst_number": {"name": "gst_number", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "pan_number": {"name": "pan_number", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "part_of_sez": {"name": "part_of_sez", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "cin_number": {"name": "cin_number", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "company_name": {"name": "company_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "address_1": {"name": "address_1", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "address_2": {"name": "address_2", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "country_of_business": {"name": "country_of_business", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "postal_code": {"name": "postal_code", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "address_name": {"name": "address_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "state": {"name": "state", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "city": {"name": "city", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "country_code": {"name": "country_code", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "contact_number": {"name": "contact_number", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "legal_status_of_company": {"name": "legal_status_of_company", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "website": {"name": "website", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "social_media_account": {"name": "social_media_account", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "number_of_offices_in_region": {"name": "number_of_offices_in_region", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "number_of_warehouses_in_region": {"name": "number_of_warehouses_in_region", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "number_of_employees": {"name": "number_of_employees", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "number_of_sales_staff": {"name": "number_of_sales_staff", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "number_of_technical_staff": {"name": "number_of_technical_staff", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "twitter_account_url": {"name": "twitter_account_url", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "linkedin_account_url": {"name": "linkedin_account_url", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "facebook_account_url": {"name": "facebook_account_url", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "instagram_account_url": {"name": "instagram_account_url", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "director_first_name": {"name": "director_first_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "director_last_name": {"name": "director_last_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "director_email": {"name": "director_email", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "director_country_code": {"name": "director_country_code", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "director_mobile_number": {"name": "director_mobile_number", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "sales_first_name": {"name": "sales_first_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "sales_last_name": {"name": "sales_last_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "sales_email": {"name": "sales_email", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "sales_country_code": {"name": "sales_country_code", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "sales_mobile_number": {"name": "sales_mobile_number", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "accounts_first_name": {"name": "accounts_first_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "accounts_last_name": {"name": "accounts_last_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "accounts_email": {"name": "accounts_email", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "accounts_country_code": {"name": "accounts_country_code", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "accounts_mobile_number": {"name": "accounts_mobile_number", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "requested_by_admin": {"name": "requested_by_admin", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "boolean"}, "requested_source": {"name": "requested_source", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "status_id": {"name": "status_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "store_id": {"name": "store_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "reason": {"name": "reason", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "redington_account_id": {"name": "redington_account_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "metadata": {"name": "metadata", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "json"}, "created_by": {"name": "created_by", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "updated_by": {"name": "updated_by", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "deleted_by": {"name": "deleted_by", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_company_registration", "schema": "public", "indexes": [{"keyName": "IDX_cq_company_registration_gst_number_unique", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE UNIQUE INDEX IF NOT EXISTS \"IDX_cq_company_registration_gst_number_unique\" ON \"cq_company_registration\" (gst_number) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_company_registration_pan_number_unique", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE UNIQUE INDEX IF NOT EXISTS \"IDX_cq_company_registration_pan_number_unique\" ON \"cq_company_registration\" (pan_number) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_company_registration_cin_number_unique", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE UNIQUE INDEX IF NOT EXISTS \"IDX_cq_company_registration_cin_number_unique\" ON \"cq_company_registration\" (cin_number) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_company_registration_redington_account_id_unique", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE UNIQUE INDEX IF NOT EXISTS \"IDX_cq_company_registration_redington_account_id_unique\" ON \"cq_company_registration\" (redington_account_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_company_registration_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_company_registration_deleted_at\" ON \"cq_company_registration\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_company_registration_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "document_id": {"name": "document_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "company_registration_id": {"name": "company_registration_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "document_uploaded_link": {"name": "document_uploaded_link", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "description": {"name": "description", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "created_by": {"name": "created_by", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "updated_by": {"name": "updated_by", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "deleted_by": {"name": "deleted_by", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_company_document", "schema": "public", "indexes": [{"keyName": "IDX_cq_company_document_company_registration_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_company_document_company_registration_id\" ON \"cq_company_document\" (company_registration_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_company_document_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_company_document_deleted_at\" ON \"cq_company_document\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_company_document_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"cq_company_document_company_registration_id_foreign": {"constraintName": "cq_company_document_company_registration_id_foreign", "columnNames": ["company_registration_id"], "localTableName": "public.cq_company_document", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_company_registration", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "company_onboarding_id": {"name": "company_onboarding_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "company_registration_id": {"name": "company_registration_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "first_name": {"name": "first_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "last_name": {"name": "last_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "email": {"name": "email", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "country_code": {"name": "country_code", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "mobile_number": {"name": "mobile_number", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_authorized_signatory", "schema": "public", "indexes": [{"keyName": "IDX_cq_authorized_signatory_company_onboarding_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_authorized_signatory_company_onboarding_id\" ON \"cq_authorized_signatory\" (company_onboarding_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_authorized_signatory_company_registration_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_authorized_signatory_company_registration_id\" ON \"cq_authorized_signatory\" (company_registration_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_authorized_signatory_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_authorized_signatory_deleted_at\" ON \"cq_authorized_signatory\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_authorized_signatory_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"cq_authorized_signatory_company_onboarding_id_foreign": {"constraintName": "cq_authorized_signatory_company_onboarding_id_foreign", "columnNames": ["company_onboarding_id"], "localTableName": "public.cq_authorized_signatory", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_company_onboarding", "deleteRule": "set null", "updateRule": "cascade"}, "cq_authorized_signatory_company_registration_id_foreign": {"constraintName": "cq_authorized_signatory_company_registration_id_foreign", "columnNames": ["company_registration_id"], "localTableName": "public.cq_authorized_signatory", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_company_registration", "deleteRule": "set null", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "region_country_id": {"name": "region_country_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "company_registration_id": {"name": "company_registration_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_cr_office_country", "schema": "public", "indexes": [{"keyName": "IDX_cq_cr_office_country_company_registration_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_cr_office_country_company_registration_id\" ON \"cq_cr_office_country\" (company_registration_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_cr_office_country_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_cr_office_country_deleted_at\" ON \"cq_cr_office_country\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_cr_office_country_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"cq_cr_office_country_company_registration_id_foreign": {"constraintName": "cq_cr_office_country_company_registration_id_foreign", "columnNames": ["company_registration_id"], "localTableName": "public.cq_cr_office_country", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_company_registration", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "question": {"name": "question", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "order_id": {"name": "order_id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": false, "nullable": false, "mappedType": "integer"}, "region_id": {"name": "region_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "answer_type_id": {"name": "answer_type_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_question", "schema": "public", "indexes": [{"keyName": "IDX_cq_question_answer_type_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_question_answer_type_id\" ON \"cq_question\" (answer_type_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_question_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_question_deleted_at\" ON \"cq_question\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_question_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"cq_question_answer_type_id_foreign": {"constraintName": "cq_question_answer_type_id_foreign", "columnNames": ["answer_type_id"], "localTableName": "public.cq_question", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_answer_type", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "answer": {"name": "answer", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "questions_id": {"name": "questions_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "order_id": {"name": "order_id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": false, "nullable": false, "mappedType": "integer"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_answer", "schema": "public", "indexes": [{"keyName": "IDX_cq_answer_questions_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_answer_questions_id\" ON \"cq_answer\" (questions_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_answer_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_answer_deleted_at\" ON \"cq_answer\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_answer_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"cq_answer_questions_id_foreign": {"constraintName": "cq_answer_questions_id_foreign", "columnNames": ["questions_id"], "localTableName": "public.cq_answer", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_question", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "questions_id": {"name": "questions_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "answer_id": {"name": "answer_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "company_registration_id": {"name": "company_registration_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_profile_info", "schema": "public", "indexes": [{"keyName": "IDX_cq_profile_info_questions_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_profile_info_questions_id\" ON \"cq_profile_info\" (questions_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_profile_info_answer_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_profile_info_answer_id\" ON \"cq_profile_info\" (answer_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_profile_info_company_registration_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_profile_info_company_registration_id\" ON \"cq_profile_info\" (company_registration_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_profile_info_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_profile_info_deleted_at\" ON \"cq_profile_info\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_profile_info_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"cq_profile_info_questions_id_foreign": {"constraintName": "cq_profile_info_questions_id_foreign", "columnNames": ["questions_id"], "localTableName": "public.cq_profile_info", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_question", "updateRule": "cascade"}, "cq_profile_info_answer_id_foreign": {"constraintName": "cq_profile_info_answer_id_foreign", "columnNames": ["answer_id"], "localTableName": "public.cq_profile_info", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_answer", "updateRule": "cascade"}, "cq_profile_info_company_registration_id_foreign": {"constraintName": "cq_profile_info_company_registration_id_foreign", "columnNames": ["company_registration_id"], "localTableName": "public.cq_profile_info", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_company_registration", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "vendor_name": {"name": "vendor_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "metadata": {"name": "metadata", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "json"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_vendor", "schema": "public", "indexes": [{"keyName": "IDX_cq_vendor_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_vendor_deleted_at\" ON \"cq_vendor\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_vendor_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "region_id": {"name": "region_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "vendor_id_id": {"name": "vendor_id_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "metadata": {"name": "metadata", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "json"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_region_vendor", "schema": "public", "indexes": [{"keyName": "IDX_cq_region_vendor_vendor_id_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_region_vendor_vendor_id_id\" ON \"cq_region_vendor\" (vendor_id_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_region_vendor_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_region_vendor_deleted_at\" ON \"cq_region_vendor\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_region_vendor_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"cq_region_vendor_vendor_id_id_foreign": {"constraintName": "cq_region_vendor_vendor_id_id_foreign", "columnNames": ["vendor_id_id"], "localTableName": "public.cq_region_vendor", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_vendor", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "brand_name": {"name": "brand_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "vendor_id": {"name": "vendor_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "metadata": {"name": "metadata", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "json"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_brand", "schema": "public", "indexes": [{"keyName": "IDX_cq_brand_vendor_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_brand_vendor_id\" ON \"cq_brand\" (vendor_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_brand_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_brand_deleted_at\" ON \"cq_brand\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_brand_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"cq_brand_vendor_id_foreign": {"constraintName": "cq_brand_vendor_id_foreign", "columnNames": ["vendor_id"], "localTableName": "public.cq_brand", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_vendor", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "region_id": {"name": "region_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "brand_id_id": {"name": "brand_id_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "metadata": {"name": "metadata", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "json"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_region_brand", "schema": "public", "indexes": [{"keyName": "IDX_cq_region_brand_brand_id_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_region_brand_brand_id_id\" ON \"cq_region_brand\" (brand_id_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_region_brand_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_region_brand_deleted_at\" ON \"cq_region_brand\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_region_brand_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"cq_region_brand_brand_id_id_foreign": {"constraintName": "cq_region_brand_brand_id_id_foreign", "columnNames": ["brand_id_id"], "localTableName": "public.cq_region_brand", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_brand", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "category_name": {"name": "category_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "brand_id": {"name": "brand_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "metadata": {"name": "metadata", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "json"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_brand_category", "schema": "public", "indexes": [{"keyName": "IDX_cq_brand_category_brand_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_brand_category_brand_id\" ON \"cq_brand_category\" (brand_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_brand_category_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_brand_category_deleted_at\" ON \"cq_brand_category\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_brand_category_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"cq_brand_category_brand_id_foreign": {"constraintName": "cq_brand_category_brand_id_foreign", "columnNames": ["brand_id"], "localTableName": "public.cq_brand_category", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_brand", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "vendor_id": {"name": "vendor_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "brand_id": {"name": "brand_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "brand_category_id": {"name": "brand_category_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "company_registration_id": {"name": "company_registration_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "company_onboarding_id": {"name": "company_onboarding_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_user_brand_preferences", "schema": "public", "indexes": [{"keyName": "IDX_cq_user_brand_preferences_vendor_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_user_brand_preferences_vendor_id\" ON \"cq_user_brand_preferences\" (vendor_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_user_brand_preferences_brand_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_user_brand_preferences_brand_id\" ON \"cq_user_brand_preferences\" (brand_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_user_brand_preferences_brand_category_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_user_brand_preferences_brand_category_id\" ON \"cq_user_brand_preferences\" (brand_category_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_user_brand_preferences_company_registration_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_user_brand_preferences_company_registration_id\" ON \"cq_user_brand_preferences\" (company_registration_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_user_brand_preferences_company_onboarding_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_user_brand_preferences_company_onboarding_id\" ON \"cq_user_brand_preferences\" (company_onboarding_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_user_brand_preferences_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_user_brand_preferences_deleted_at\" ON \"cq_user_brand_preferences\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_user_brand_preferences_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"cq_user_brand_preferences_vendor_id_foreign": {"constraintName": "cq_user_brand_preferences_vendor_id_foreign", "columnNames": ["vendor_id"], "localTableName": "public.cq_user_brand_preferences", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_vendor", "updateRule": "cascade"}, "cq_user_brand_preferences_brand_id_foreign": {"constraintName": "cq_user_brand_preferences_brand_id_foreign", "columnNames": ["brand_id"], "localTableName": "public.cq_user_brand_preferences", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_brand", "updateRule": "cascade"}, "cq_user_brand_preferences_brand_category_id_foreign": {"constraintName": "cq_user_brand_preferences_brand_category_id_foreign", "columnNames": ["brand_category_id"], "localTableName": "public.cq_user_brand_preferences", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_brand_category", "updateRule": "cascade"}, "cq_user_brand_preferences_company_registration_id_foreign": {"constraintName": "cq_user_brand_preferences_company_registration_id_foreign", "columnNames": ["company_registration_id"], "localTableName": "public.cq_user_brand_preferences", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_company_registration", "deleteRule": "set null", "updateRule": "cascade"}, "cq_user_brand_preferences_company_onboarding_id_foreign": {"constraintName": "cq_user_brand_preferences_company_onboarding_id_foreign", "columnNames": ["company_onboarding_id"], "localTableName": "public.cq_user_brand_preferences", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_company_onboarding", "deleteRule": "set null", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "region_id": {"name": "region_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "brand_category_id_id": {"name": "brand_category_id_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "metadata": {"name": "metadata", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "json"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_region_brand_category", "schema": "public", "indexes": [{"keyName": "IDX_cq_region_brand_category_brand_category_id_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_region_brand_category_brand_category_id_id\" ON \"cq_region_brand_category\" (brand_category_id_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_region_brand_category_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_region_brand_category_deleted_at\" ON \"cq_region_brand_category\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_region_brand_category_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"cq_region_brand_category_brand_category_id_id_foreign": {"constraintName": "cq_region_brand_category_brand_category_id_id_foreign", "columnNames": ["brand_category_id_id"], "localTableName": "public.cq_region_brand_category", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_brand_category", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "brand_id": {"name": "brand_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "brand_category_id": {"name": "brand_category_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "metadata": {"name": "metadata", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "json"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_brand_brand_category", "schema": "public", "indexes": [{"keyName": "IDX_cq_brand_brand_category_brand_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_brand_brand_category_brand_id\" ON \"cq_brand_brand_category\" (brand_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_brand_brand_category_brand_category_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_brand_brand_category_brand_category_id\" ON \"cq_brand_brand_category\" (brand_category_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_brand_brand_category_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_brand_brand_category_deleted_at\" ON \"cq_brand_brand_category\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_brand_brand_category_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"cq_brand_brand_category_brand_id_foreign": {"constraintName": "cq_brand_brand_category_brand_id_foreign", "columnNames": ["brand_id"], "localTableName": "public.cq_brand_brand_category", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_brand", "updateRule": "cascade"}, "cq_brand_brand_category_brand_category_id_foreign": {"constraintName": "cq_brand_brand_category_brand_category_id_foreign", "columnNames": ["brand_category_id"], "localTableName": "public.cq_brand_brand_category", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_brand_category", "updateRule": "cascade"}}, "nativeEnums": {}}], "nativeEnums": {}}