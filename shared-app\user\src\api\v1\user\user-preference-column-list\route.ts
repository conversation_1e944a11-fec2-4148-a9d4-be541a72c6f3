import {
  MedusaRequest,
  MedusaResponse,
  AuthenticatedMedusaRequest,
} from '@medusajs/framework/http';
import { ContainerRegistrationKeys } from '@medusajs/framework/utils';
import { logger } from '@medusajs/framework';
import { z } from 'zod';
import { formatPagination, stringConstants } from '@org/utils';

/**
 * @openapi
 * /v1/user/user-preference-column-list:
 *   post:
 *     summary: Get user-specific column preferences for a table
 *     tags:
 *       - User Preferences
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - table_master_id
 *             properties:
 *               table_master_id:
 *                 type: string
 *                 example: "tbl_abc123"
 *                 description: The ID of the table for which to fetch columns
 *     responses:
 *       200:
 *         description: Successfully fetched user column preferences
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: User column preference fetched successfully
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       column_master_id:
 *                         type: string
 *                         example: "col_456"
 *                       table_master_id:
 *                         type: string
 *                         example: "tbl_abc123"
 *                       column_name:
 *                         type: string
 *                         example: "Customer Name"
 *                       table_name:
 *                         type: string
 *                         example: "customers"
 *                       column_slug:
 *                         type: string
 *                         example: "customer_name"
 *                       column_type:
 *                         type: string
 *                         example: "string"
 *                       sort_order:
 *                         type: integer
 *                         example: 1
 *                       is_selected:
 *                         type: boolean
 *                         example: true
 *       404:
 *         description: Table or Columns not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Table master not found
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error
 */

export async function POST(
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) {
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
  const { actor_id: user_id } = req.auth_context;
  const { table_master_id } = req.body as any;

  const { data: tableData } = await query.graph({
    entity: 'cq_page',
    fields: ['*', 'columns.*'],
    filters: {
      id: table_master_id,
    },
  });

  if (!tableData || tableData.length === 0) {
    return res.status(404).json({
      success: false,
      message: stringConstants.NOT_FOUND('Table master'),
    });
  }

  const { data: userPreferences } = await query.graph({
    entity: 'cq_user_column_preference',
    fields: ['column_master_id', 'sort_order'],
    filters: {
      table_master_id,
      user_id,
    },
  });

  const preferenceMap = new Map(
    (userPreferences ?? []).map((pref) => [
      pref.column_master_id,
      pref.sort_order,
    ])
  );

  const selectedColumns = new Set(preferenceMap.keys());

  const transformed = tableData.flatMap((table) =>
    (table.columns ?? [])
      .filter((column) => column !== null)
      .map((column) => ({
        column_master_id: column.id,
        table_master_id: column.table_master_id,
        column_name: column.column_name,
        table_name: table.page,
        column_slug: column.column_slug,
        column_type: column.column_type,
        sort_order: column.sort_order,
        preferred_order: preferenceMap.get(column.id) ?? 0,
        is_selected: selectedColumns.has(column.id),
      }))
  );

  if (!transformed || transformed.length === 0) {
    return res.status(404).json({
      message: stringConstants.NOT_FOUND('Columns'),
      success: false,
    });
  }

  try {
    return res.status(200).json({
      success: true,
      message: stringConstants.FETCHED_SUCCESSFULLY('User column preference'),
      data: transformed,
    });
  } catch (err: any) {
    logger.error(err);
    return res
      .status(500)
      .json({ message: stringConstants.INTERNAL_SERVER_ERROR() });
  }
}
