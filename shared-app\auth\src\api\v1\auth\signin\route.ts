import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { stringConstants } from '@org/utils';
import axios from 'axios';
import * as dotenv from 'dotenv';
dotenv.config();

/**
 * @openapi
 * /v1/auth/signin:
 *   post:
 *     summary: Sign in user with email and password
 *     tags:
 *       - Users
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 example: <EMAIL>
 *               password:
 *                 type: string
 *                 example: Smart@123
 *     responses:
 *       200:
 *         description: Login successful, returns JWT token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 token:
 *                   type: string
 *                   example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 *       400:
 *         description: Missing email or password
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Email and password must be provided.
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error. Please try again later.
 */

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { email, password } = req.body as { email: string; password: string };

    if (!email || !password) {
      return res
        .status(400)
        .json({ message: stringConstants.EMAIL_PASSWORD_VERIFY() });
    }

    const response = await axios.post(
      `${process.env.MEDUSA_DEFAULT_LOGIN_URL}`,
      { email, password },
      { headers: { 'Content-Type': 'application/json' } }
    );

    const { token } = response.data;

    return res.status(200).json({ token });
  } catch (err: any) {
    if (axios.isAxiosError(err)) {
      const status = err.response?.status || 500;
      const message =
        err.response?.data?.message ||
        (status === 401
          ? stringConstants.INVALID_CREDENTIALS()
          : stringConstants.AUTHENTICATION_FAILED());

      return res.status(status).json({ message });
    }

    console.error('Unexpected Error:', err);
    return res.status(500).json({ message: stringConstants.INTERNAL_SERVER_ERROR() });
  }
}

