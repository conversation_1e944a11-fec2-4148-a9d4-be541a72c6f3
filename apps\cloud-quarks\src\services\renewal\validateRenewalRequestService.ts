import logger from "../../utils/logger";
import sql from "mssql";
import { getSqlServerConnection } from "../../utils/sqlServerClient";


export interface StatusResponse {
  Status: string;
  Message: string;
}


export async function validateRenewalRequestService(
  subscriptionId: string
): Promise<StatusResponse> {
  logger.info(
    `Entered into BL validateRenewalRequestService with subscriptionId: ${subscriptionId}`
  );

  try {
    const response = await validateRenewalRequestDAL(subscriptionId);
    logger.info(
      `Response from DAL validateRenewalRequest: ${JSON.stringify(response)}`
    );
    return response;
  } catch (error: any) {
  logger.error(
    `Exception in validateRenewalRequestService | subscriptionId: ${subscriptionId} | Error: ${error.message}`
  );
  return {
    Status: "FAIL",
    Message: error.message,
  };
}
}
export async function validateRenewalRequestDAL(
  subscriptionId: string
): Promise<StatusResponse> {
  logger.info(
    `Entered into DAL validateRenewalRequest with subscriptionId: ${subscriptionId}`
  );

  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();

    request.input("iSubscription", subscriptionId);
    request.output("oRetVal", sql.Bit);
    request.output("oRetMessage", sql.VarChar(200));

    logger.info(
      `Calling stored procedure spValidateSubscriptionRenewal with iSubscription=${subscriptionId}`
    );

    await request.execute("spValidateSubscriptionRenewal");

    const status: boolean = request.parameters.oRetVal.value;
    const message: string = request.parameters.oRetMessage.value;

    logger.info(
      `Stored procedure output - oRetVal: ${status}, oRetMessage: ${message}`
    );

    return {
      Status: status ? "PASS" : "FAIL",
      Message: message,
    };
  } catch (error: any) {
  logger.error(
    `Error in validateRenewalRequestDAL | subscriptionId: ${subscriptionId} | Error: ${error.message}`
  );

  return {
    Status: "FAIL",
    Message: error.message,
  };
}
}