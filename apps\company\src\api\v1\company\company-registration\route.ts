import {
	MedusaRequest,
	MedusaResponse,
	AuthenticatedMedusaRequest,
} from '@medusajs/framework/http';
import { COMPANY_REGISTRATION_MODULE } from '../../../../modules/company';
import { formatPagination, stringConstants } from '@org/utils';
import { RegistrationSchema } from './validators';
import { z } from 'zod';
import {
	createCompanyRegistrationWorkflow,
	getCompanyRegistrationWorkflow,
	getPendingStatusIdWorkflow,
} from '../../../../workflows/company-registration';
import {
	generateExcelBuffer,
	ExcelColumn,
	ExportedCompanyRegistrationRequest,
	SourceKey,
	allowedSources,
} from '@org/excel';
import { ContainerRegistrationKeys } from '@medusajs/framework/utils';
import axios from 'axios';

type CompanyRegistrationSchemaType = z.infer<typeof RegistrationSchema>;

/**
 * @openapi
 * /v1/company/company-registration:
 *   post:
 *     summary: Register a new company
 *     description: |
 *       Registers a new company with business details, contact info, authorized signatories (max 3), and brand preferences. Validates CIN, required documents, and profile questions before creating the full registration.
 *     tags:
 *       - Company Registration
 *     parameters:
 *       - in: query
 *         name: source
 *         required: true
 *         schema:
 *           type: string
 *         description: The source of the guest user (e.g., partner or directEndCustomer)
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - gst_number
 *               - pan_number
 *               - cin_number
 *               - company_name
 *               - address_1
 *               - country_of_business
 *               - state
 *               - city
 *               - postal_code
 *               - legal_status_of_company
 *               - country_code
 *               - contact_number
 *             properties:
 *               gst_number:
 *                 type: string
 *                 description: GST registration number of the company
 *                 example: "27**********1Z9"
 *               pan_number:
 *                 type: string
 *                 description: PAN number of the company
 *                 example: "**********"
 *               cin_number:
 *                 type: string
 *                 description: Corporate Identity Number (mandatory)
 *                 example: "U12345MH2020PTC123456"
 *               company_name:
 *                 type: string
 *                 description: Legal name of the company
 *                 example: "Tech Innovators Pvt. Ltd."
 *               address_1:
 *                 type: string
 *                 description: Primary address line
 *                 example: "Plot No. 21, Tech Park"
 *               address_2:
 *                 type: string
 *                 description: Secondary address line (optional)
 *                 example: "Phase II, Hinjewadi"
 *               country_of_business:
 *                 type: string
 *                 description: Country where the company operates
 *                 example: "India"
 *               state:
 *                 type: string
 *                 description: State/Province of operation
 *                 example: "Maharashtra"
 *               city:
 *                 type: string
 *                 description: City of operation
 *                 example: "Pune"
 *               postal_code:
 *                 type: string
 *                 description: Postal/ZIP code
 *                 example: "411057"
 *               legal_status_of_company:
 *                 type: string
 *                 description: Legal structure of the company
 *                 example: "Private Limited"
 *               country_code:
 *                 type: string
 *                 description: Country calling code for contact number
 *                 example: "+91"
 *               contact_number:
 *                 type: string
 *                 description: Primary contact number
 *                 example: "9876543210"
 *               website:
 *                 type: string
 *                 format: uri
 *                 description: Company website URL
 *                 example: "https://www.techinnovators.in"
 *               number_of_offices_in_region:
 *                 type: string
 *                 description: Number of offices in the current region
 *                 example: "5"
 *               other_countries_offices:
 *                 type: array
 *                 description: List of countries where the company has offices
 *                 items:
 *                   type: string
 *                 example: ["US", "UK"]
 *               number_of_warehouses_in_region:
 *                 type: integer
 *                 description: Number of warehouses in the current region
 *                 example: 3
 *               number_of_employees:
 *                 type: integer
 *                 description: Total number of employees
 *                 example: 150
 *               number_of_sales_staff:
 *                 type: integer
 *                 description: Number of sales staff
 *                 example: 40
 *               number_of_technical_staff:
 *                 type: integer
 *                 description: Number of technical staff
 *                 example: 30
 *               social_media:
 *                 type: object
 *                 properties:
 *                   twitter:
 *                     type: string
 *                     format: uri
 *                     example: "https://twitter.com/techinnovators"
 *                   facebook:
 *                     type: string
 *                     format: uri
 *                     example: "https://facebook.com/techinnovators"
 *                   linkedin:
 *                     type: string
 *                     format: uri
 *                     example: "https://linkedin.com/company/techinnovators"
 *                   instagram:
 *                     type: string
 *                     format: uri
 *                     example: "https://instagram.com/techinnovators"
 *               business_info:
 *                 type: object
 *                 required:
 *                   - director_owner
 *                   - sales
 *                   - accounts_operations
 *                 properties:
 *                   director_owner:
 *                     type: object
 *                     required:
 *                       - first_name
 *                       - last_name
 *                       - email
 *                       - contact_info
 *                     properties:
 *                       first_name:
 *                         type: string
 *                         example: "Rajesh"
 *                       last_name:
 *                         type: string
 *                         example: "Kumar"
 *                       email:
 *                         type: string
 *                         format: email
 *                         example: "<EMAIL>"
 *                       contact_info:
 *                         type: object
 *                         required:
 *                           - mobile_number
 *                           - country_code
 *                         properties:
 *                           mobile_number:
 *                             type: string
 *                             example: "9876500001"
 *                           country_code:
 *                             type: string
 *                             example: "+91"
 *                   sales:
 *                     type: object
 *                     required:
 *                       - first_name
 *                       - last_name
 *                       - email
 *                       - contact_info
 *                     properties:
 *                       first_name:
 *                         type: string
 *                         example: "Anjali"
 *                       last_name:
 *                         type: string
 *                         example: "Sharma"
 *                       email:
 *                         type: string
 *                         format: email
 *                         example: "<EMAIL>"
 *                       contact_info:
 *                         type: object
 *                         required:
 *                           - mobile_number
 *                           - country_code
 *                         properties:
 *                           mobile_number:
 *                             type: string
 *                             example: "**********"
 *                           country_code:
 *                             type: string
 *                             example: "+91"
 *                   accounts_operations:
 *                     type: object
 *                     required:
 *                       - first_name
 *                       - last_name
 *                       - email
 *                       - contact_info
 *                     properties:
 *                       first_name:
 *                         type: string
 *                         example: "Sandeep"
 *                       last_name:
 *                         type: string
 *                         example: "Verma"
 *                       email:
 *                         type: string
 *                         format: email
 *                         example: "<EMAIL>"
 *                       contact_info:
 *                         type: object
 *                         required:
 *                           - mobile_number
 *                           - country_code
 *                         properties:
 *                           mobile_number:
 *                             type: string
 *                             example: "9876500003"
 *                           country_code:
 *                             type: string
 *                             example: "+91"
 *                   authorized_signatories:
 *                     type: array
 *                     maxItems: 3
 *                     description: List of authorized signatories (max 3 allowed)
 *                     items:
 *                       type: object
 *                       required:
 *                         - first_name
 *                         - last_name
 *                         - email
 *                         - contact_info
 *                       properties:
 *                         first_name:
 *                           type: string
 *                           example: "Meera"
 *                         last_name:
 *                           type: string
 *                           example: "Nair"
 *                         email:
 *                           type: string
 *                           format: email
 *                           example: "<EMAIL>"
 *                         contact_info:
 *                           type: object
 *                           required:
 *                             - mobile_number
 *                             - country_code
 *                           properties:
 *                             mobile_number:
 *                               type: string
 *                               example: "9876500004"
 *                             country_code:
 *                               type: string
 *                               example: "+91"
 *               company_document:
 *                 type: object
 *                 description: Company related document URLs
 *                 properties:
 *                   document_id:
 *                       type: string
 *                       example: "01JWAVMZKK971TPRGFKY1Z5X9J"
 *                   document_uploaded_link:
 *                       type: string
 *                       example: "dummy.pdf"
 *                   description:
 *                       type: string
 *                       example: "Company Registration Document"
 *               profile_info:
 *                 type: array
 *                 description: List of profile question answers
 *                 items:
 *                   type: object
 *                   required:
 *                     - question_id
 *                     - answer_id
 *                   properties:
 *                     question_id:
 *                       type: string
 *                       example: "01JWAVMZKK971TPRGFKY1Z5X9J"
 *                     answer_id:
 *                       type: string
 *                       example: "01JWAVMZKXJXCSB0FS0GJ8MM7D"
 *                     terms_and_conditions:
 *                       type: boolean
 *                       example: true
 *               user_brand_preferences:
 *                 type: array
 *                 description: User's brand preferences linked to vendors
 *                 items:
 *                   type: object
 *                   required:
 *                     - vendor_id
 *                     - brand_id
 *                     - brand_category_id
 *                   properties:
 *                     vendor_id:
 *                       type: string
 *                       example: "01JWAKM1T5Q80BG7Y1CH4VY52S"
 *                     brand_id:
 *                       type: string
 *                       example: "01JWAKM1V6RDCM06R00SAXRYNX"
 *                     brand_category_id:
 *                       type: string
 *                       example: "01JWAKM1WZ672GVZ571DC0QJ46"
 *     responses:
 *       200:
 *         description: Company registration successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Company Registration request has been submitted successfully.
 *       400:
 *         description: Bad request - invalid input
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Invalid input data or CIN number missing or authorized signatories limit exceeded
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error. Please try again later.
 */

/**
 * @route POST /company/v1/company-registration
 * @group Company - Handles company registration
 * @param {CompanyRegistrationSchemaType.model} body.body.required - Company registration data
 * @returns {object} 200 - Registration successful
 * @returns {Error} 500 - Internal server error
 */
export async function POST(
	req: AuthenticatedMedusaRequest<CompanyRegistrationSchemaType>,
	res: MedusaResponse
) {
	const rawSource = req.query.source;

	const source = rawSource
		? String(Array.isArray(rawSource) ? rawSource[0] : rawSource)
		: undefined;

	if (!source || !allowedSources.includes(source as SourceKey)) {
		return res.status(400).json({
			message: `Invalid source. Must be one of: ${allowedSources.join(', ')}`,
		});
	}

	const companyRegistrationService: any = req.scope.resolve(
		COMPANY_REGISTRATION_MODULE
	);

	try {
		const {
			authorized_signatories,
			company_document,
			profile_info,
			user_brand_preferences,
			...registrationData
		} = req.body;

		// const { actor_id: user_id } = req.auth_context;

		if (!registrationData.cin_number) {
			return res.status(400).json({
				message: 'CIN number is required.',
			});
		}

		if (
			authorized_signatories &&
			Array.isArray(authorized_signatories) &&
			authorized_signatories.length > 3
		) {
			return res.status(400).json({
				message: 'A maximum of 3 authorized signatories are allowed.',
			});
		}
		const { result: pendingStatusId } = await getPendingStatusIdWorkflow(
			req.scope
		).run();

		const finalRegistrationData = {
			...registrationData,
			// user_id: user_id,
			status_id: pendingStatusId,
			requested_source: source,
			// region_id: region_id,
			metadata: {},
		};
		if (Array.isArray(profile_info) && profile_info.length > 0) {
			for (const item of profile_info) {
				const { questions_id, answer_id } = item;

				if (!questions_id || !answer_id) {
					return res.status(400).json({
						message:
							'Each profile_info item must include questions_id and answer_id',
					});
				}
				const data = await companyRegistrationService.listCqQuestions(
					{ id: questions_id },
					{ relations: ['answer_type'] }
				);
				if (!data.length || !data[0].answer_type?.type) {
					return res.status(400).json({
						message: `No answer_type found for questions_id: ${questions_id}`,
					});
				}

				const answerType = data[0].answer_type.type;
				if (
					answerType === 'multiple choice' ||
					answerType === 'multiple choice dropdown'
				) {
					if (!Array.isArray(answer_id) || answer_id.length === 0) {
						return res.status(400).json({
							message: `For the given question, the answer_id must be an array of IDs..`,
						});
					}
				}
			}
		}
		const { result: companyRegistration } =
			await createCompanyRegistrationWorkflow(req.scope).run({
				input: {
					companyDetails: {
						...finalRegistrationData,
						authorized_signatories,
						company_document,
						profile_info,
						user_brand_preferences,
					},
				},
			});
		const companyRegistrationId = companyRegistration.id;

		const otherCountries = registrationData.other_countries_offices;

		if (Array.isArray(otherCountries) && otherCountries.length > 0) {
			for (const region_country_id of otherCountries) {
				if (!region_country_id || typeof region_country_id !== 'string') {
					return res.status(400).json({
						message:
							'Each item in other_countries_offices must be a valid country ID string.',
					});
				}

				await companyRegistrationService.createCROfficeCountries({
					region_country_id,
					company_registration_id: companyRegistrationId,
				});
			}
		}

		if (company_document && Array.isArray(company_document)) {
			for (const document of company_document) {
				const response = await axios.post(`${process.env.S3_URL}`, {
					folder_name: 'cq-document',
					image_name: document.document_uploaded_link,
				});

				document.document_uploaded_link = response.data.link.split('/').pop();
				await companyRegistrationService.createCompanyDocuments({
					...document,
					company_registration_id: companyRegistrationId,
					// user_id: user_id,
				});
			}
		}

		if (authorized_signatories && Array.isArray(authorized_signatories)) {
			for (const signatory of authorized_signatories) {
				await companyRegistrationService.createAuthorizedSignatories({
					...signatory,
					company_registration_id: companyRegistrationId,
					company_id: null,
				});
			}
		}

		if (Array.isArray(profile_info) && profile_info.length > 0) {
			const validProfiles: any[] = [];

			for (const item of profile_info) {
				const { questions_id, answer_id } = item;

				if (!questions_id || !answer_id) {
					return res.status(400).json({
						message:
							'Each profile_info item must include questions_id and answer_id',
					});
				}
				const data = await companyRegistrationService.listCqQuestions(
					{ id: questions_id },
					{ relations: ['answer_type'] }
				);
				if (!data.length || !data[0].answer_type?.type) {
					return res.status(400).json({
						message: `No answer_type found for questions_id: ${questions_id}`,
					});
				}
				const answerType = data[0].answer_type.type;

				if (
					answerType === 'multiple choice' ||
					answerType === 'multiple choice dropdown'
				) {
					if (!Array.isArray(answer_id) || answer_id.length === 0) {
						return res.status(400).json({
							message: `For the given question, the answer_id must be an array of IDs..`,
						});
					}
				}
				validProfiles.push({
					questions_id,
					answer_id,
					company_registration_id: companyRegistrationId,
				});
			}

			await companyRegistrationService.createProfileInfos(validProfiles);
		}

		if (
			Array.isArray(user_brand_preferences) &&
			user_brand_preferences.length > 0
		) {
			for (const preference of user_brand_preferences) {
				if (
					!preference.vendor_id ||
					!preference.brand_id ||
					!preference.brand_category_id
				) {
					return res.status(400).json({
						message: 'Brand preference missing required fields',
					});
				}

				await companyRegistrationService.createUserBrandPreferences({
					...preference,
					company_registration_id: companyRegistrationId,
				});
			}
		}

		return res.status(200).json({
			message: stringConstants.REGISTRATION_SUCCESS(),
		});
	} catch (err: any) {
		console.error('Error during partner registration:', {
			message: err?.message,
			stack: err?.stack,
		});

		return res.status(500).json({
			message: stringConstants.INTERNAL_SERVER_ERROR(),
		});
	}
}

// Future Use : Partner portal GET API to fetch company registrations
/**
 * @openapi
 * /v1/company/company-registration/:
 *   get:
 *     summary: Get all company registrations
 *     description: |
 *        Retrieves a paginated list of Registred companies along with associated user details.
 *        Supports filtering via pagination parameters and optional Excel export via a query flag.
 *     tags:
 *       - Company Registration
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: The page number to retrieve (default is 1)
 *       - in: query
 *         name: page_size
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: The number of records per page (default is 10)
 *       - in: query
 *         name: download
 *         schema:
 *           type: string
 *           enum: [excel]
 *         description: Set to `excel` to download the result as an Excel file
 *     responses:
 *       200:
 *         description: A list of registered companies with user details.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       user_id:
 *                         type: string
 *                       company_name:
 *                         type: string
 *                       cin_number:
 *                         type: string
 *                       gst_number:
 *                         type: string
 *                       pan_number:
 *                         type: string
 *                       state:
 *                         type: string
 *                       city:
 *                         type: string
 *                       country_of_business:
 *                         type: string
 *                       postal_code:
 *                         type: string
 *                       contact_number:
 *                         type: string
 *                       requested_source:
 *                         type: string
 *                         enum: [partner, directEndCustomer]
 *                       created_at:
 *                         type: string
 *                         format: date-time
 *                       updated_at:
 *                         type: string
 *                         format: date-time
 *                       user:
 *                         type: object
 *                         description: Associated user details
 *                         properties:
 *                           id:
 *                             type: string
 *                           first_name:
 *                             type: string
 *                           last_name:
 *                             type: string
 *                           email:
 *                             type: string
 *                           role:
 *                             type: string
 *       401:
 *         description: Unauthorized - Missing or invalid bearer token
 *       500:
 *         description: Internal server error
 */

/**
 * @route GET /company/v1/company-registration
 * @group Company - Handles company registration
 * @param {string} page.query - Page number for pagination
 * @param {string} page_size.query - Number of items per page
 * @param {string} download.query - Set to 'excel' for Excel export
 * @returns {object} 200 - Successfully fetched company registrations
 * @returns {Error} 500 - Internal server error
 * @security BearerAuth
 * @description Fetches a list of all company registrations with associated user details.
 */

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
	try {
		const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

		// 🔢 Parse pagination parameters
		let page = parseInt(req.query.page as string, 10);
		let pageSize = parseInt(req.query.page_size as string, 10);
		const isDownload = req.query.download === 'excel';
		if (isNaN(page) || page < 1) page = 1;
		if (isNaN(pageSize) || pageSize < 1) pageSize = 10;

		const skip = (page - 1) * pageSize;

		// 🧾 Fetch company registrations (all for now, pagination can be optimized at query level)
		const { data: companyRegistrations, metadata } = await query.graph({
			entity: 'cq_company_registration',
			fields: ['*'],
			pagination: {
				skip,
				take: pageSize,
			},
		});

		// 👤 Fetch related users
		const { result: users } = await getCompanyRegistrationWorkflow(
			req.scope
		).run({});

		// 🗺️ Map user data by user_id
		const userMap: Record<string, any> = {};
		for (const user of users) {
			userMap[user.id] = user;
		}

		// 🔗 Merge user data into company registrations
		const mergedResult = companyRegistrations.map((reg: any) => ({
			...reg,
			// user: userMap[reg.user_id] || null,
		}));

		// 📦 Excel Export
		if (isDownload) {
			const columns: ExcelColumn<ExportedCompanyRegistrationRequest>[] = [
				{ header: 'Registration ID', key: 'id', width: 10 },
				{ header: 'Country', key: 'country', width: 25 },
				{ header: 'First Name', key: 'first_name', width: 15 },
				{ header: 'Last Name', key: 'last_name', width: 15 },
				{ header: 'Company Type', key: 'company_type', width: 25 },
				{ header: 'Email', key: 'email', width: 20 },
				{ header: 'Company Name', key: 'company_name', width: 20 },
				{
					header: 'Redington Account ID',
					key: 'redington_account_id',
					width: 20,
				},
				{ header: 'Store', key: 'store', width: 25 },
				{ header: 'Role', key: 'role', width: 25 },
				{ header: 'Requested By', key: 'requested_by_admin', width: 15 },
				{ header: 'Status', key: 'status', width: 15 },
			];

			const transformedData: ExportedCompanyRegistrationRequest[] =
				mergedResult.map((user: any) => ({
					id: user.id,
					first_name: user.first_name ?? '',
					last_name: user.last_name ?? '',
					company_type: user.source ?? '',
					email: user.email ?? '',
					store: user.country?.cq_stores?.[0]?.name ?? '',
					company_name: user.company_name ?? '',
					redington_account_id: user.redington_account_id ?? '',
					role: user.role ?? '', // Replace this with actual value if available
					country: user.country?.display_name ?? '',
					requested_by_admin: user.requested_by_admin ?? false, // Should be boolean
					status: user.is_active ? 'Active' : 'Inactive',
				}));

			const buffer = await generateExcelBuffer(
				transformedData,
				columns,
				'User'
			);

			res.setHeader(
				'Content-Type',
				'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
			);
			res.setHeader('Content-Disposition', 'attachment; filename="user.xlsx"');
			return res.send(buffer);
		}

		const totalItems = metadata?.count ?? 0;
		return res.status(200).json({
			data: mergedResult,
			pagination: formatPagination({ page, pageSize, totalItems }),
		});
	} catch (err: any) {
		console.error('Error fetching company registration:', {
			message: err?.message,
			stack: err?.stack,
		});

		return res.status(500).json({
			message: err.message || 'Internal server error',
		});
	}
};
