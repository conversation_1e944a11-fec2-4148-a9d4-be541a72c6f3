import axios from "axios";
import logger from "../../utils/logger";
import { Entitlement, ResponseError } from "../../types/responses/customResponse";
import { MicrosoftEndpoints } from "../../config/microsoftEndpoints";
import { getMethod } from "../externalService/externalEndPointService";

export async function getEntitlements(
  req: any,
  customerId: string,
  orderId: string,
  token: string,
  brandName: string
): Promise<Entitlement> {
  logger.info(`Entered into getEntitlements Service method with customerId - ${customerId}, orderId - ${orderId}, brandName - ${brandName}`);

  try {
    const completeUrlWithParams = MicrosoftEndpoints.getEntitlementsUrl(customerId,brandName)
      console.log("completeUrlWithParams---getMsToken---->", completeUrlWithParams);

    logger.info(
      `Request initiated to getEntitlements - ${completeUrlWithParams}`
    );

    const headerList = {
      Authorization: `Bearer ${token}`,
      Accept: "application/json",
    };

    const configModule = req.scope.resolve("configModule");

    const response = await getMethod(
      {
        url: completeUrlWithParams,
        headers: headerList,
        isVendorHit: true,
        module: "GetEntitlements",
      },
      configModule
    );

    console.log("response----------->", response);

    logger.info(`Response from getEntitlements ${JSON.stringify(response)}`);

    if (response.isSuccessStatusCode && response.content) {
      const entitlement = JSON.parse(response.content) as Entitlement;
      logger.info(`Returning entitlement ${JSON.stringify(entitlement)}`);
      return entitlement;
    } else {
      logger.warn(`GetEntitlements failed: HTTP ${response.httpStatusCode}, Error: ${response.errorMessage}`);
      throw new Error(response.errorMessage || "Failed to fetch entitlement");
    }
  } catch (error) {
    logger.error(`Error in getEntitlements: ${error}`);
    // Return empty entitlement on error instead of throwing
    logger.info(`Returning empty entitlement due to error`);
    return {} as Entitlement;
  }
}