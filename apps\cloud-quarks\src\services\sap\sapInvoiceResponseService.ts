import logger from "../../utils/logger";
import { getSqlServerConnection } from "../../utils/sqlServerClient";
import { GetSAPInvoiceDocumentModel, SapPreInvoiceResponseModel, S3Credentials } from "../../types/responses/customResponse";

import { getS3ObjectAsync } from "../../config/getS3Object";
import sql from "mssql";

/**
 * Check if invoice exists in database
 * Matches .NET CheckInvoiceExistsOrNotA  sync method
 */
export async function checkInvoiceExistsOrNotService(invoiceNo: string): Promise<boolean> {
  logger.info(`Entered into BL CheckInvoiceExistsOrNotAsync method, preInvoiceNo: ${invoiceNo}`);

  logger.info(`Going to hit DAL CheckInvoiceExistsOrNotAsync function with preInvoiceNo: ${invoiceNo}`);
  const isInvoiceExists = await checkInvoiceExistsOrNotDAL(invoiceNo);
  logger.info(`Response from DAL in case of CheckInvoiceExistsOrNotAsync: ${isInvoiceExists}`);

  logger.info(`Going to return response as ${isInvoiceExists}`);
  return isInvoiceExists;
}

/**
 * Get SAP Invoice Document
 * Matches .NET GetS3ObjectAsync method with Polly retry policy
 */
export async function getSAPInvoiceDocumentService(
  getSAPInvoiceDocumentModel: GetSAPInvoiceDocumentModel,
  s3SAPInvoiceCredentials: S3Credentials
): Promise<string> {
  logger.info(`Entered into BL GetSAPInvoiceDocumentAsync method, getSAPInvoiceDocument model: ${JSON.stringify(getSAPInvoiceDocumentModel)}`);

  logger.info(`Going to hit DAL GetSAPInvoiceDocumentAsync function with getSAPInvoiceDocument model: ${JSON.stringify(getSAPInvoiceDocumentModel)}`);
  const responseFromDal = await getSAPInvoiceDocumentDAL(getSAPInvoiceDocumentModel);
  logger.info(`Response from DAL in case of PostVendorInvoiceResponseAsync: ${responseFromDal}`);

  logger.info(`Going to hit BL GetS3ObjectAsync function with fileName: ${responseFromDal}`);

  // Use the new AWS SDK v3 with built-in retry logic
  const responseFromAWSService = await getS3ObjectAsync(s3SAPInvoiceCredentials, responseFromDal);

  logger.info(`Going to return response as ${responseFromAWSService}`);
  return responseFromAWSService;
}

/**
 * DAL method to check if invoice exists
 * Matches .NET CheckInvoiceExistsOrNotAsync DAL method
 */
async function checkInvoiceExistsOrNotDAL(invoiceNo: string): Promise<boolean> {
  logger.info(`Entered into DAL CheckInvoiceExistsOrNotAsync method, preInvoiceNo: ${invoiceNo}`);
  
  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();
    
    const query = "SELECT 1 WHERE EXISTS (SELECT 1 FROM [dbo].[tblSapPreInvoiceResponse] WHERE InvoiceNo = @invoiceNo)";
    
    request.input("invoiceNo", sql.VarChar(50), invoiceNo);
    
    logger.info(`Going to DB with query: ${query} and invoiceNo: ${invoiceNo}`);
    
    const result = await request.query(query);
    const exists = result.recordset.length > 0;

    logger.info(`Return value is ${exists}`);
    return exists;
  } catch (error) {
    logger.error(`Error in checkInvoiceExistsOrNotDAL: ${error}`);
    throw error;
  }
}

/**
 * DAL method to get SAP Invoice Document
 * Matches .NET GetSAPInvoiceDocumentAsync DAL method
 */
async function getSAPInvoiceDocumentDAL(getSAPInvoiceDocumentModel: GetSAPInvoiceDocumentModel): Promise<string> {
  logger.info(`Entered into DAL IsInvoiceNumberAlreadyExistsInVendorInvoiceResponseAsync method, invoiceNo: ${getSAPInvoiceDocumentModel.InvoiceNo}`);
  
  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();
    
    const query = "SELECT top 1 InvoiceDoc FROM [dbo].[tblSapPreInvoiceResponse] WHERE InvoiceNo = @invoiceNo";
    
    request.input("invoiceNo", sql.VarChar(50), getSAPInvoiceDocumentModel.InvoiceNo);
    
    logger.info(`Going to DB with query: ${query} and invoiceNo: ${getSAPInvoiceDocumentModel.InvoiceNo}`);
    
    const result = await request.query<SapPreInvoiceResponseModel>(query);
    
    if (result.recordset.length === 0) {
      throw new Error(`No invoice document found for InvoiceNo: ${getSAPInvoiceDocumentModel.InvoiceNo}`);
    }
    
    const invoiceDoc = result.recordset[0].InvoiceDoc;
    logger.info(`Return value is ${invoiceDoc}`);
    
    return invoiceDoc;
  } catch (error) {
    logger.error(`Error in getSAPInvoiceDocumentDAL: ${error}`);
    throw error;
  }
}
