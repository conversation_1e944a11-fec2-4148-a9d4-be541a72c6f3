import { z } from "zod";

export const AddUpdateCiscoResellerValidator = z.object({
  storeId: z.string().min(1, "storeId cannot be null or empty"),
  partnerId: z.string().min(1, "partnerId cannot be null or empty"),
  partyName: z.string().min(1, "partyName cannot be null or empty"),
  addressLine1: z.string().min(1, "addressLine1 cannot be null or empty"),
  addressLine2: z.string().min(1, "addressLine2 cannot be null or empty"),
  city: z.string().min(1, "city cannot be null or empty"),
  state: z.string().min(1, "state cannot be null or empty"),
  postalCode: z.string().min(1, "postalCode cannot be null or empty"),
  country: z.string().min(1, "country cannot be null or empty"),
  businessContactName: z
    .string()
    .min(1, "businessContactName cannot be null or empty"),
  businessContactEmail: z
    .string()
    .min(1, "businessContactEmail cannot be null or empty")
    .email("Invalid email"),
  businessContactNumber: z
    .string()
    .min(1, "businessContactNumber cannot be null or empty"),
  accountNumber: z.string().min(1, "accountNumber cannot be null or empty"),
  isActive: z.boolean().optional(),
});


export const CiscoConstants = {
  Ato: "ATO",
  Support: "SUPPORT",
  Product: "PRODUCT",
  CustomerCredentialType: "CUSTOMER",
  ValidationStatusError: "E",
  ValidationStatusGeo: "GEO_VALID",
  ValidationStatusGeoInvalid: "GEO_INVALID",
  ValidationStatusNameInvalid: "NAME_INVALID",
  StatusPass: "PASS",
  StatusFail: "FAIL",
  DbActionInsert: "INSERT",
  DbActionUpdate: "UPDATE",
  BrandName_Cisco: "Cisco",
}

export type ResellerDto = {
  StoreId: string
  PartnerId: string
  PartyName: string
  AddressLine1: string
  AddressLine2: string
  City: string
  State: string
  PostalCode: string
  Country: string
  BusinessContactName: string
  BusinessContactEmail: string
  BusinessContactNumber: string
  AccountNumber: string
  IsActive?: boolean
}

