import { MedusaRequest } from "@medusajs/framework";
import { Urls } from "../utils/constants";
import logger from "../utils/logger";
import { v4 as uuidv4 } from "uuid";

export const MicrosoftEndpoints = {
  getCustomerValidationStatusUrl: (customerId: string) => {
    const base = Urls.MS_BASE_URL || "";
    const path = Urls.MS_GET_CUSTOMER_VALIDATION_URL || "";
    return `${base}${path.replace("{0}", customerId)}`;
  },
  getMsTokenUrl: (storeDomain: string) => {
    const raw = Urls.MS_OAUTH_GET_TOKEN || "";
    return raw.replace("{0}", storeDomain);
  },
  getCustomerUsersUrl: (tenantId: string) => {
    const base = Urls.MS_BASE_URL || "";
    const path = Urls.MS_GET_CUSTOMER_USERS_URL || "";
    return `${base}${path.replace("{0}", tenantId)}`;
  },
  getCustomerBillingInfoUrl: (tenantId: string) => {
    const base = Urls.MS_BASE_URL || "";
    const path = Urls.MS_GET_CUSTOMER_BILLING_INFO_URL || "";
    return `${base}${path.replace("{0}", tenantId)}`;
  },
  getCustomerAgreementUrl: (custId: string) => {
    const base = Urls.MS_BASE_URL || "";
    const path = Urls.MS_GET_CUSTOMER_AGREEMENT_URL || "";
    return `${base}${path.replace("{0}", custId)}`;
  },
  getCustomerByIdUrl: (tenantId: string) => {
    const base = Urls.MS_BASE_URL || "";
    const path = Urls.MS_GET_CUSTOMER_BY_ID_URL || "";
    return `${base}${path.replace("{0}", tenantId)}`;
  },
  getCustomersUrl: (filter: any) => {
    const base = Urls.MS_BASE_URL || "";
    const path = Urls.MS_GET_CUSTOMERS_URL || "";
    return `${base}${path.replace("{0}", filter)}`;
  },
  checkDomainUrl: (domainId: string) => {
    const base = Urls.MS_BASE_URL || "";
    const path = Urls.MS_CHECK_DOMAIN_URL || "";
    return `${base}${path.replace("{0}", domainId)}`;
  },
  getDurectSignStatusOfCustAgreementUrl: (custTenantId: string) => {
    const base = Urls.MS_BASE_URL || "";
    const path = Urls.MS_GET_DIRECT_SIGN_STATUS_URL || "";
    return `${base}${path.replace("{0}", custTenantId)}`;
  },
  checkMPNIDStatusUrl: (mpnId: string) => {
    const base = Urls.MS_BASE_URL || "";
    const path = Urls.MS_CHECK_MPNID_STATUS_URL || "";
    return `${base}${path.replace("{0}", mpnId)}`;
  },
  getMicAgreementsUrl: () => {
    const base = Urls.MS_BASE_URL || "";
    const path = Urls.MS_GET_MIC_AGREEMENTS_URL || "";
    return `${base}${path}`;
  },
  postAgreementsUrl: (custId: string) => {
    const base = Urls.MS_BASE_URL || "";
    const path = Urls.MS_POST_AGREEMENTS_URL || "";
    return `${base}${path.replace("{0}", custId)}`;
  },
  getMicrosoftBaseUrl: () => {
    return Urls.MS_BASE_URL || "";
  },
  exportBilledUsageUrl: (invoiceId: string, attributeSet: string) => {
    const base = Urls.MS_BASE_URL || "";
    // TODO: Add the correct Microsoft endpoint path for billed usage export
    return `${base}/v1/invoices/${invoiceId}/billedusage?attributeSet=${attributeSet}`;
  },
  getAzureOperationDetailsUrl: (operationId: string) => {
    const base = Urls.GRAPH_BASE_URL || "";
    const path = Urls.MS_GET_AZURE_OPERATION_DETAILS_URL || "";
    return `${base}${path.replace("{0}", operationId)}`;
  },
  getEntitlementsUrl: (customerId: string, brand: string) => {
    const base = Urls.MS_BASE_URL || "";
    const path1 = Urls.MS_GET_ENTITLEMENTS_URL_PATH_1 || "";
    const path2 = Urls.MS_GET_ENTITLEMENTS_URL_PATH_2 || "";
    const entitlementType =
      brand === "Azure RI" ? "reservedinstance" : "software";
    return `${base}${path1.replace("{0}", customerId)}${path2.replace(
      "{1}",
      entitlementType
    )}`;
  },
  getProvisionalStatusURL: (customerId: string, orderId: string) => {
    const base = Urls.MS_BASE_URL || "";
    const path1 = Urls.MS_GET_PROVISIONAL_STATUS_URL_PATH_1 || "";
    const path2 = Urls.MS_GET_PROVISIONAL_STATUS_URL_PATH_2 || "";
    return `${base}${path1.replace("{0}", customerId)}${path2.replace(
      "{1}",
      orderId
    )}`;
  },
  getOrdersDetailFromVendorByIdUrl: (customerId: string, orderId: string) => {
    const base = Urls.MS_BASE_URL || "";
    const path1 = Urls.MS_GET_ORDER_DETAIL_FROM_VENDORS_BY_ID_URL_PATH_1 || "";
    const path2 = Urls.MS_GET_ORDER_DETAIL_FROM_VENDORS_BY_ID_URL_PATH_2 || "";
    return `${base}${path1.replace("{0}", customerId)}${path2.replace(
      "{1}",
      orderId
    )}`;
  },
  getSubscriptionByIdUrl: (customerId: string, subscriptionId: string) => {
    const base = Urls.MS_BASE_URL || "";
    const path1 = Urls.GET_SUBSCRIPTION_BY_ID_URL_PATH_1 || "";
    const path2 = Urls.GET_SUBSCRIPTION_BY_ID_URL_PATH_2 || "";
    return `${base}${path1.replace("{0}", customerId)}${path2.replace(
      "{1}",
      subscriptionId
    )}`;
  },
  terminateOrderUrl: (customerId: string, orderId: string) => {
    const base = Urls.MS_BASE_URL || "";
    const path1 = Urls.MS_TERMINATE_ORDER_URL_PATH_1 || "";
    const path2 = Urls.MS_TERMINATE_ORDER_URL_PATH_2 || "";
    return `${base}${path1.replace("{0}", customerId)}${path2.replace(
      "{1}",
      orderId
    )}`;
  },
  checkSubscriptionStatusUrl: (customerId: string, subscriptionId: string) => {
    const base = Urls.MS_BASE_URL || "";
    const path = Urls.MS_CHECK_SUBSCRIPTION_STATUS_URL || "";
    return `${base}${path.replace("{0}", customerId).replace("{1}", subscriptionId)}`;
  },
  registerSubscriptionUrl: (customerId: string, subscriptionId: string) => {
    const base = Urls.MS_BASE_URL || "";
    const path = Urls.MS_REGISTER_SUBSCRIPTION_URL || "";
    return `${base}${path.replace("{0}", customerId).replace("{1}", subscriptionId)}`;
  },
  getSubscriptionUrl: (customerId: string, subscriptionId: string) => {
    const base = Urls.MS_BASE_URL || "";
    const path = Urls.MS_GET_SUBSCRIPTION_URL || "";
    return `${base}${path.replace("{0}", customerId).replace("{1}", subscriptionId)}`;
  },
  patchSubscriptionUrl: (customerId: string, subscriptionId: string) => {
    const base = Urls.MS_BASE_URL || "";
    const path = Urls.MS_PATCH_SUBSCRIPTION_URL || "";
    return `${base}${path.replace("{0}", customerId).replace("{1}", subscriptionId)}`;
  },
  getNCETransitionEligibilityUrl: (customerId: string, subscriptionId: string, eligibilityType?: string) => {
    const base = Urls.MS_BASE_URL || "";
    const path = Urls.MS_GET_NCE_TRANSITION_ELIGIBILITY_URL || "";
    const baseUrl = `${base}${path.replace("{0}", customerId).replace("{1}", subscriptionId)}`;
    return eligibilityType ? `${baseUrl}?eligibilityType=${eligibilityType}` : baseUrl;
  },
  getAzurePlanEntitlementUrl: (customerId: string, subscriptionId: string) => {
    const base = Urls.MS_BASE_URL || "";
    const path1 = Urls.GET_AZURE_PLAN_ENTITLEMENT_URL_PATH_1 || "";
    const path2 = Urls.GET_AZURE_PLAN_ENTITLEMENT_URL_PATH_2 || "";
    return `${base}${path1.replace("{0}", customerId)}${path2.replace(
      "{1}",
      subscriptionId
    )}`;
  },
  getIndirectResellerByIdUrl: () => {
    const base = Urls.MS_BASE_URL || "";
    const path = Urls.GET_INDIRECT_RESELLER_URL || "";
    return `${base}${path}`;
  },
  exportReconciliationDatauUrl: () => {
    const base = Urls.GRAPH_BASE_URL || "";
    const path = Urls.EXPORT_RECONCILIATION_DATA_URL || "";
    return `${base}${path}`;
  },
  verifyPromotionEligibilityUrl: (customerId: string) => {
    const base = Urls.MS_BASE_URL || "";
    const path = Urls.MS_VERIFY_PROMOTION_ELIGIBILITY_URL || "";
    return `${base}${path.replace("{0}", customerId)}`;
  },
  getAvailabilityUrl: (customerId: string, productId: string, skuId: string) => {
    const base = Urls.MS_BASE_URL || "";
    const path = Urls.MS_GET_AVAILABILITY_URL || "";
    return `${base}${path.replace("{0}", customerId).replace("{1}", productId).replace("{2}", skuId)}`;
  },
};

// Types
export type DefaultAddress = {
  FirstName: string;
  LastName: string;
  AddressLine1: string;
  City: string;
  State: string;
  PostalCode: string;
  Country: string;
  PhoneNumber: string;
};

export type BillingProfileRequest = {
  Language: string;
  CompanyName: string;
  Email: string;
  Culture: string;
  DefaultAddress: DefaultAddress;
};

export type CompanyProfileRequest = {
  Domain: string;
  OrganizationRegistrationNumber: string;
};

export type MicrosoftCreateCustomerRequest = {
  CompanyProfile: CompanyProfileRequest;
  BillingProfile: BillingProfileRequest;
  AssociatedPartnerId: string;
};

export function getMicrosoftRequestHeader(
  token: string,
  requestId: string,
  correlationId: string
): Record<string, string> {
  return {
    Authorization: `Bearer ${token}`,
    "MS-RequestId": requestId,
    "MS-CorrelationId": correlationId,
  };
}

export function prepareHeaderGetCustomerValidationStatus(
  token: string
): Record<string, string> {
  console.info("Preparing HeaderList for external call");
  return {
    Authorization: `Bearer ${token}`,
  };
}

export function prepareCustomerValidationHeaders(
  token: string
): Record<string, string> {
  logger.info("Preparing HeaderList for external call");
  return {
    Authorization: `Bearer ${token}`,
  };
}

export const SerilogConfig = {
  MinimumLevel: {
    Default: "Information",
    Override: {
      Microsoft: "Error",
      System: "Error",
    },
  },
  CorrelationIdHeader: "CorrelationId",
  WriteTo: [{}],
};

export function getCorrelationId(req: MedusaRequest): string {
  const headerKey = SerilogConfig.CorrelationIdHeader.toLowerCase();
  return (req.headers[headerKey] || "").toString();
}

export function createMicrosoftRequestHeaders(
  token: string,
  storeId: string,
  requestId?: string,
  correlationId?: string,
  xRegion?: string,
  reqHeaders?: Record<string, string | string[]>
): Record<string, string> {
  const headers: Record<string, string> = {};

  const contextRegion = reqHeaders?.["x-region"]?.toString() || "";
  const contextCorrelationId = reqHeaders?.["correlation-id"]?.toString() || "";
  const contextRequestId = reqHeaders?.["requestid"]?.toString() || "";

  headers["Authorization"] = `Bearer ${token}`;
  headers["MS-RequestId"] = requestId || contextRequestId || uuidv4();
  headers["MS-CorrelationId"] =
    correlationId || contextCorrelationId || uuidv4();
  headers["x-region"] = xRegion || contextRegion || storeId;

  return headers;
}

export const CiscoDetails = {
  BrandNameInDB: "Cisco",
  ProvisionTypeInDB: "AUTO-CISCO",
  ThresholdLimitForATO: 1,
  TncAcceptance: "Y",
  BillingModel: "Prepaid Term",
  GetAllCustomerPageSize: 100,
};

export const GoogleDetails = {
  GoogleAccount: "C00use8gv",
  BrandNameInDB: "Google Cloud Platform",
  ProvisionTypeInDB: "AUTO-GOOGLE",
  LinkStates: {
    Invited: "INVITED",
    Revoked: "REVOKED",
    Active: "ACTIVE",
    Suspended: "SUSPENDED",
    Unspecified: "CHANNEL_PARTNER_LINK_STATE_UNSPECIFIED",
  },
};

export function createGoogleRequestHeaders(
  req: MedusaRequest,
  token: string,
  storeId: string
): Record<string, string> {
  const regionHeader = req.headers["x-region"];
  const region = Array.isArray(regionHeader)
    ? regionHeader[0]
    : regionHeader || storeId;

  return {
    Authorization: `Bearer ${token}`,
    "x-region": region,
  };
}

export const MICROSOFT_CONSTANTS = {
  RETRY_PATCH_SUBSCRIPTION_COUNT: 3,
  RETRY_PATCH_SUBSCRIPTION_TIMESPAN: "00:00:10", // format: hh:mm:ss
};