import { checkIfSubscriptionExists } from "../../services/linkedAccount/checkSubscriptionService";

export async function validateLinkedAccountId(linkedAccountId?: string): Promise<string[]> {
  const messages: string[] = [];

  if (!linkedAccountId) {
    messages.push("Linked account id cannot be null or empty");
  } else {
    const exists = await checkIfSubscriptionExists(linkedAccountId);
    if (!exists) {
      messages.push("Subscription does not exist");
    }
  }

  return messages;
}
