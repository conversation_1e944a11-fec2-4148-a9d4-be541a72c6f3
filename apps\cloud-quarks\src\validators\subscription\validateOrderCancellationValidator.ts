import { z } from "zod";
import logger from "../../utils/logger";

// Zod schema for ValidateOrderCancellation query parameters
export const ValidateOrderCancellationQuerySchema = z.object({
  subscriptionId: z.string().min(1, "SubscriptionId cannot be blank or null"),
});

export type ValidateOrderCancellationQueryType = z.infer<typeof ValidateOrderCancellationQuerySchema>;

/**
 * Validate ValidateOrderCancellation request parameters (matching .NET validation patterns)
 */
export function validateOrderCancellationRequest(queryParams: any): string[] {
  logger.info(`Entered into validateOrderCancellationRequest method with queryParams: ${JSON.stringify(queryParams)}`);
  
  const validationErrors: string[] = [];

  try {
    ValidateOrderCancellationQuerySchema.parse(queryParams);
    logger.info(`Zod validation passed for ValidateOrderCancellation request`);
  } catch (error: any) {
    if (error.errors) {
      error.errors.forEach((err: any) => {
        const fieldName = err.path.join('.');
        validationErrors.push(`${fieldName}: ${err.message}`);
      });
    }
    logger.error(`Zod validation failed: ${JSON.stringify(error.errors)}`);
  }

  // Additional custom validations (matching .NET patterns)
  if (!queryParams.subscriptionId || queryParams.subscriptionId.trim() === '') {
    validationErrors.push("The 'subscriptionId' query parameter is missing or empty in the request.");
  }

  logger.info(`Validation completed. Errors found: ${validationErrors.length}`);
  return validationErrors;
}
