import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { stringConstants } from '@org/utils';
import { COMPANY_REGISTRATION_MODULE } from '../../../../../modules/company';
import PartnerRegistrationModuleService from '../../../../../modules/company/service';

/**
 * @openapi
 * /v1/company/company-registration/brand-category:
 *   post:
 *     summary: Get Brand Categories by Region and Brand
 *     tags:
 *       - Company Registration
 *     parameters:
 *       - in: query
 *         name: region_id
 *         required: true
 *         schema:
 *           type: string
 *         description: Region ID to filter brand categories
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - brand_id
 *             properties:
 *               brand_id:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example:
 *                   - "01JWV02JR1HHKSGMMB63BGDC6Q"
 *                   - "01JWV02JRG65352NRRXDMKHMAP"
 *     responses:
 *       200:
 *         description: Successfully fetched brand categories
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       brand:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           name:
 *                             type: string
 *                       brand_category:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             id:
 *                               type: string
 *                             name:
 *                               type: string
 *                 message:
 *                   type: string
 *                   example: Brand Category fetched successfully
 *             example:
 *               data:
 *                 - brand:
 *                     id: 01JWV02JR1HHKSGMMB63BGDC6Q
 *                     name: Toyota
 *                   brand_category:
 *                     - id: 01JWV02JT80S6BFPEZX6WAJ591
 *                       name: Luxury
 *                     - id: 01JWV02JTRNXEZK09E3RCMFPG1
 *                       name: Electric
 *                 - brand:
 *                     id: 01JWV02JRG65352NRRXDMKHMAP
 *                     name: Honda
 *                   brand_category:
 *                     - id: 01JWV02JWD2M4QTD9DD6MQQ2D1
 *                       name: Luxury
 *                     - id: 01JWV02JWPSB5FQ6RWNMS93C2E
 *                       name: Electric
 *               message: Brand Category fetched successfully
 *       400:
 *         description: Missing required parameters
 *       404:
 *         description: Brand categories not found for the given region
 *       500:
 *         description: Internal server error
 */

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const brandCategoryService: PartnerRegistrationModuleService =
      req.scope.resolve(COMPANY_REGISTRATION_MODULE);

    const { region_id } = req.query as {
      region_id?: string;
    };

    const { brand_id } = req.body as {
      brand_id?: string[];
    };

    if (!region_id || !Array.isArray(brand_id)) {
      return res.status(400).json({
        message: stringConstants.PARAMETER_MISSING('region_id and brand_id'),
      });
    }

    // Fetch all brand category IDs linked to the region
    const regionBrandCategories =
      await brandCategoryService.listRegionBrandCategories({ region_id });
    const regionCategoryIds = regionBrandCategories.map(
      (rb) => rb.brand_category_id
    );

    if (!regionCategoryIds.length) {
      return res.status(404).json({
        data: [],
        message: stringConstants.NOT_FOUND('Brand Category'),
      });
    }

    const brand = await brandCategoryService.listBrands({ id: brand_id });
    const brandCategories = await brandCategoryService.listBrandCategories({
      brand_id,
    });

    const categoryMap = new Map();
    brandCategories.forEach((category) => {
      const brandId = category.brand_id;
      if (!categoryMap.has(brandId)) {
        categoryMap.set(brandId, []);
      }
      categoryMap.get(brandId).push({
        id: category.id,
        name: category.category_name,
      });
    });

    const nestedBrands = brand.map((b) => ({
      brand: {
        id: b.id,
        name: b.brand_name,
      },
      brand_category: categoryMap.get(b.id) || [],
    }));

    return res.status(200).json({
      data: nestedBrands,
      message: 'Brand Category fetched successfully',
    });
  } catch (err) {
    return res.status(500).json({
      message: stringConstants.INTERNAL_SERVER_ERROR(),
    });
  }
}
