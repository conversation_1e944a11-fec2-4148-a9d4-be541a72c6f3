import axios from "axios";
import logger from "../../utils/logger";
import {
  GetSubscriptionResponse,
  ResponseError,
} from "../../types/responses/customResponse";
import { MicrosoftEndpoints } from "../../config/microsoftEndpoints";
import { getMethod } from "../externalService/externalEndPointService";


export async function getSubscriptionById(
  req: any,
  customerId: string,
  subscriptionId: string,
  token: string
): Promise<GetSubscriptionResponse> {
  logger.info(
    `Entered into GetSubscriptionById Service method with customerId ${customerId}, subscriptionId ${subscriptionId}`
  );

  try {
    console.log("orderRow.CustomerId--------->", customerId);
    console.log("orderRow.subscriptionId--------->", subscriptionId);
    console.log("token--------->", token);
    const completeUrlWithParams = MicrosoftEndpoints.getSubscriptionByIdUrl(
      customerId,
      subscriptionId
    );
    logger.info(`Request to GetSubscriptionById ${completeUrlWithParams}`);

    const headerList = {
      Authorization: `Bearer ${token}`,
      Accept: "application/json",
    };

    const configModule = req.scope.resolve("configModule");

    const response = await getMethod(
      {
        url: completeUrlWithParams,
        headers: headerList,
        isVendorHit: true,
        module: "GetSubscriptionById",
      },
      configModule
    );

    console.log("response----------->", response);

    logger.info(`Response from GetSubscriptionById Response - ${JSON.stringify(response)}`);

    if (response.isSuccessStatusCode && response.content) {
      const subscriptionResponse = JSON.parse(response.content) as GetSubscriptionResponse;
      logger.info(`Returning response ${JSON.stringify(subscriptionResponse)}`);
      return subscriptionResponse;
    } else {
      logger.warn(`GetSubscriptionById failed: HTTP ${response.httpStatusCode}, Error: ${response.errorMessage}`);
      throw new Error(response.errorMessage || "Error while fetching subscription from vendor");
    }
  } catch (error) {
    logger.error(`Error in getSubscriptionById: ${error}`);
    throw error;
  }
}