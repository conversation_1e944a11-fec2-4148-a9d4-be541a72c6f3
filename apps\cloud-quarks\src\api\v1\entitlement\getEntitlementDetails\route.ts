import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import {
  EntitlementRequestSchema,
  validateEntitlementRequest
} from "../../../../validators/entitlement/getEntitlementDetailsValidator";
import { getEntitlementDetailsService } from "../../../../services/index";

/**
 * @openapi
 * /v1/entitlement/getEntitlementDetails:
 *   post:
 *     summary: Get entitlement details for an order line item
 *     tags:
 *       - Entitlement
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - orderLineKey
 *             properties:
 *               orderLineKey:
 *                 type: integer
 *                 minimum: 1
 *                 description: The ID of the order line item
 *             example:
 *               orderLineKey: 12345
 *     responses:
 *       200:
 *         description: Entitlement details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 includedEntitlements:
 *                   type: array
 *                   items:
 *                     type: object
 *                 referenceOrder:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     alternateId:
 *                       type: string
 *                 productId:
 *                   type: string
 *                 quantity:
 *                   type: integer
 *                 quantityDetails:
 *                   type: array
 *                   items:
 *                     type: object
 *                 entitledArtifacts:
 *                   type: array
 *                   items:
 *                     type: object
 *                 skuId:
 *                   type: string
 *                 entitlementType:
 *                   type: string
 *                 fulfillmentState:
 *                   type: string
 *                 expiryDate:
 *                   type: string
 *                   format: date-time
 *                 subscriptionId:
 *                   type: string
 *                 hasAddOn:
 *                   type: boolean
 *                 subscriptionStatus:
 *                   type: string
 *                 startDate:
 *                   type: string
 *                   format: date-time
 *       204:
 *         description: No entitlement found for the given order line key
 *       400:
 *         description: Bad request. Validation failed.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 isError:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                 statusCode:
 *                   type: integer
 *                   example: 400
 *       422:
 *         description: Service error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 isError:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                 statusCode:
 *                   type: integer
 *                   example: 422
 */

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req);

  try {
    const entitlementRequest = req.body;

    logger.info(
      `Entered into GetEntitlementDetails API with EntitlementRequest | CorrelationId: ${correlationId} | Request: ${JSON.stringify(entitlementRequest)}`
    );

    // Validate request body structure
    const parseResult = EntitlementRequestSchema.safeParse(entitlementRequest);
    if (!parseResult.success) {
      const validationErrors = parseResult.error.errors.map((err: any) => err.message);
      const errorMessage = validationErrors.join(', ');

      const apiResponse = {
        isError: true,
        message: errorMessage,
        statusCode: 400,
      };

      logger.info(`Going to send bad request response | CorrelationId: ${correlationId} | Response: ${JSON.stringify(apiResponse)}`);
      return res.status(400).json(apiResponse);
    }

    const validatedRequest = parseResult.data;

    // Additional validation
    const validationErrors = validateEntitlementRequest(validatedRequest.orderLineKey);
    if (validationErrors.length > 0) {
      const errorMessage = validationErrors.join(', ');

      const apiResponse = {
        isError: true,
        message: errorMessage,
        statusCode: 400,
      };

      logger.info(`Going to send bad request response | CorrelationId: ${correlationId} | Response: ${JSON.stringify(apiResponse)}`);
      return res.status(400).json(apiResponse);
    }


    console.log("validatedRequest.orderLineKey---------->",validatedRequest, req)

    // Call service to get entitlement details
    const entitlement = await getEntitlementDetailsService(req, validatedRequest.orderLineKey);

    if (entitlement === null) {
      logger.info(
        `No entitlement found for orderLineKey: ${validatedRequest.orderLineKey} | CorrelationId: ${correlationId}`
      );
      return res.status(204).send(); // No Content
    }

    logger.info(
      `Returning response from GetEntitlementDetails API | CorrelationId: ${correlationId} | Entitlement: ${JSON.stringify(entitlement)}`
    );

    // Return the entitlement object (matching .NET behavior)
    return res.status(200).json(entitlement);
  } catch (exp) {
    const errorMessage = exp instanceof Error ? exp.message : "Unknown error occurred";

    logger.error(
      `ERROR in GetEntitlementDetails | CorrelationId: ${correlationId} | ErrorDetail: ${errorMessage} | StackTrace: ${exp instanceof Error ? exp.stack : 'No stack trace'}`
    );

    const apiResponse = {
      isError: true,
      message: `Exception: ${errorMessage}`,
      statusCode: 422, // UnprocessableEntity
    };

    logger.error(
      `Returning error response from GetEntitlementDetails API | CorrelationId: ${correlationId} | ApiResponse: ${JSON.stringify(apiResponse)}`
    );

    return res.status(422).json(apiResponse);
  }
}