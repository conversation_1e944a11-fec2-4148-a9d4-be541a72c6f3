import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import { getSqlServerConnection } from "../../utils/sqlServerClient";
import sql from "mssql";
import {
  ResponseStatus,
  PlaceTransitionRequest,
  GetCustomerTenantIdAndMPNIdResponse
} from "../../types/responses/customResponse";

/**
 * Get Customer Tenant ID and MPN ID
 * Matches .NET GetCustomerTenantIdAndMPNId method
 */
export async function getCustomerTenantIdAndMPNId(
  req: MedusaRequest,
  storeId: string,
  partnerId: string,
  customerId: string
): Promise<GetCustomerTenantIdAndMPNIdResponse | null> {
  logger.info(`Entered into DAL GetCustomerTenantIdAndMPNId method with storeId-${storeId},partnerId-${partnerId} and customerId-${customerId}`);

  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();

    const procedure = "spGetCustomerTenantIdAndMPNId";
    
    request.input("StoreId", sql.VarChar(50), storeId);
    request.input("PartnerId", sql.VarChar(50), partnerId);
    request.input("CustId", sql.VarChar(50), customerId);

    logger.info(`Going to hit DB with proc name : ${procedure} and parameters StoreId: ${storeId}, PartnerId: ${partnerId}, CustId: ${customerId}`);
    
    const result = await request.execute<GetCustomerTenantIdAndMPNIdResponse>(procedure);
    const response = result.recordset?.[0] || null;
    
    logger.info(`Proc ran successfully, response ${JSON.stringify(response)}`);
    return response;
  } catch (error: any) {
    logger.error(`Error in getCustomerTenantIdAndMPNId: ${error.message}`);
    throw error;
  }
}

export async function validateSubscriptionTransition(
  req: MedusaRequest,
  request: PlaceTransitionRequest
): Promise<ResponseStatus> {
  logger.info(`Entered into DAL ValidateSubscriptionTransition method with request-${JSON.stringify(request)}`);
  
  const response: ResponseStatus = { Status: false };

  try {
    const pool = await getSqlServerConnection();
    const sqlRequest = pool.request();

    const procedure = "spValidateSubscriptionTransition";
    
    sqlRequest.input("iFromSubscription", sql.VarChar(100), request.FromSubscriptionId);
    sqlRequest.input("iToSubscription", sql.VarChar(100), request.ToSubscriptionId);
    sqlRequest.output("oRetVal", sql.Bit);
    sqlRequest.output("oRetMessage", sql.VarChar(200));

    logger.info(`Going to hit DB with proc name : ${procedure} and parameters iFromSubscription: ${request.FromSubscriptionId}, iToSubscription: ${request.ToSubscriptionId}`);
    
    await sqlRequest.execute(procedure);

    response.Status = sqlRequest.parameters.oRetVal.value;
    logger.info(`Return value is ${response.Status}`);

    response.Message = sqlRequest.parameters.oRetMessage.value;
    logger.info(`Return message is ${response.Message}`);

    return response;
  } catch (error: any) {
    logger.error(`Error in validateSubscriptionTransition: ${error.message}`);
    response.Status = false;
    response.Message = `Database error: ${error.message}`;
    return response;
  }
}

export async function addSubscriptionTransition(
  req: MedusaRequest,
  request: PlaceTransitionRequest
): Promise<ResponseStatus> {
  logger.info(`Entered into DAL AddSubscriptionTransition method with request-${JSON.stringify(request)}`);
  
  const response: ResponseStatus = { Status: false };

  try {
    const pool = await getSqlServerConnection();
    const sqlRequest = pool.request();

    const procedure = "spAddOrderTransition";

    // Add input parameters
    sqlRequest.input("BrandId", sql.VarChar(50), request.BrandId);
    sqlRequest.input("StoreId", sql.VarChar(50), request.StoreId);
    sqlRequest.input("PartnerId", sql.VarChar(50), request.PartnerId);
    sqlRequest.input("CustomerId", sql.VarChar(50), request.CustomerId);
    sqlRequest.input("FromSubscriptionId", sql.VarChar(100), request.FromSubscriptionId);
    sqlRequest.input("FromCatalogItemId", sql.VarChar(100), request.FromCatalogItemId);
    sqlRequest.input("Quantity", sql.Int, request.Quantity);
    sqlRequest.input("ToSubscriptionId", sql.VarChar(100), request.ToSubscriptionId);
    sqlRequest.input("ToCatalogItemId", sql.VarChar(100), request.ToCatalogItemId);
    sqlRequest.input("PromotionId", sql.VarChar(100), request.PromotionId);
    sqlRequest.input("Term", sql.VarChar(50), request.Term);
    sqlRequest.input("BillFreq", sql.VarChar(50), request.BillFreq);
    sqlRequest.input("UpdateType", sql.VarChar(50), request.UpdateType);
    sqlRequest.input("SubUpdateType", sql.VarChar(50), request.SubUpdateType);
    sqlRequest.input("UnitPrice", sql.Decimal(18, 2), request.UnitPrice);
    sqlRequest.input("TaxRate", sql.Decimal(18, 2), request.TaxRate);
    sqlRequest.input("PromotionRate", sql.Decimal(18, 2), request.PromotionRate);
    sqlRequest.input("Segment", sql.VarChar(50), request.Segment);

    // Add output parameters
    sqlRequest.output("RetVal", sql.Bit);
    sqlRequest.output("Message", sql.VarChar(200));

    logger.info(`Going to hit DB with proc name : ${procedure} and parameters ${JSON.stringify(request)}`);
    
    await sqlRequest.execute(procedure);

    response.Status = sqlRequest.parameters.RetVal.value;
    logger.info(`Return value is ${response.Status}`);

    response.Message = sqlRequest.parameters.Message.value;
    logger.info(`Return message is ${response.Message}`);

    return response;
  } catch (error: any) {
    logger.error(`Error in addSubscriptionTransition: ${error.message}`);
    response.Status = false;
    response.Message = `Database error: ${error.message}`;
    return response;
  }
}
