import { getSqlServerConnection } from "../../utils/sqlServerClient"
import logger from "../../utils/logger"


export interface StoreBusinessPartnerMapModel {
  Store: string
  BusinessPartner: string
}


export async function getAllBusinessPartnerStoreMapping(): Promise<
  StoreBusinessPartnerMapModel[]
> {
  logger.info(`Fetching store-business partner mappings from DB`)
  const query = "SELECT Store, BusinessPartner FROM tbllnkstorebusinesspartner"
  const pool = await getSqlServerConnection()
  const result = await pool.request().query(query)
  return result.recordset
}
