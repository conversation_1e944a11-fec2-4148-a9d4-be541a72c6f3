import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import {
  CiscoValidateCustomerRequest,
  CiscoCustomerModel,
  CiscoStatusResponseType,
  CiscoValidateCustomerResponse,
  CiscoValidateCustomerPartyResponse
} from "../../types/responses/customResponse";
import { getCiscoTokenService } from "../token/cisco/getCiscoTokenService";
import { PostMethodWithRequestJsonBodyAndHeaders400Handle } from "../externalService/externalEndPointService";
import { CiscoConstants } from "../../validators/reseller/addUpdateCiscoResellerValidator";
import { Urls } from "../../utils/constants";
import { saveCiscoCustomerDAL } from "./saveCiscoCustomerDAL";

// Helper function to normalize string comparison (matching .NET ComparableString extension)
function comparableString(str?: string): string {
  return (str || "").trim().toUpperCase();
}

/**
 * Validate and Save Cisco Customer
 * Matches .NET CustomerFactory.ValidateAndSaveCiscoCustomer method
 */
export async function validateAndSaveCiscoCustomerService(
  ciscoValidateCustomerRequest: CiscoValidateCustomerRequest,
  ciscoValidateDALModel: CiscoCustomerModel,
  req?: MedusaRequest
): Promise<CiscoStatusResponseType> {
  logger.info(`Going to ValidateAndSaveCiscoCustomer method with parameter CiscoValidateCustomerRequestModel ${JSON.stringify(ciscoValidateCustomerRequest)} and CiscoCustomerModelForDal ${JSON.stringify(ciscoValidateDALModel)}`);

  try {
    // Generate Cisco token (matching .NET: _tokenService.GetCiscoToken)
    logger.info(`Going to generate token using tokenService.GetCiscoToken method using storeId: ${ciscoValidateCustomerRequest.storeId}, CredentialType: ${CiscoConstants.CustomerCredentialType}`);
    const token = await getCiscoTokenService(req!, ciscoValidateCustomerRequest.storeId!, CiscoConstants.CustomerCredentialType);
    logger.info(`Token generated successfully`);

    // Get URL configurations (matching .NET configuration fetching)
    logger.info(`Going to fetch the cisco validate customer URL from app config`);
    const validateCustomerUrl = Urls.CS_VALIDATE_CUSTOMER;
    logger.info(`Cisco validate customer URL from app config :${validateCustomerUrl}`);

    logger.info(`Going to fetch ciscoCustomerBaseUrl from AppSetting`);
    const ciscoCustomerBaseUrl = Urls.CS_CUSTOMER_BASE_URL;
    logger.info(`Fetched ciscoCustomerBaseUrl : ${ciscoCustomerBaseUrl}`);

    logger.info(`Going to fetch mulesoftBaseUrl from AppSetting`);
    const mulesoftBaseUrl = process.env.MS_BASE_URL || "";
    logger.info(`Fetched mulesoftBaseUrl : ${mulesoftBaseUrl}`);

    // Create authorization headers (matching .NET header creation)
    logger.info(`Creating Authorization header`);
    const headerList: Record<string, string> = {
      "Authorization": `Bearer ${token}`
    };

    // Serialize request to JSON (matching .NET: JsonConvert.SerializeObject)
    logger.info(`Going to Serialize Object ${JSON.stringify(ciscoValidateCustomerRequest)}`);
    const payloadInJson = JSON.stringify(ciscoValidateCustomerRequest);
    logger.info(`Payload json created: ${payloadInJson}`);

    // Build validation endpoint URL (matching .NET URL construction)
    const url = `${mulesoftBaseUrl}${validateCustomerUrl.replace("{0}", ciscoCustomerBaseUrl)}`;
    logger.info(`Going to call Cisco endpoint using PostMethodWithRequestJsonBodyAndHeaders400Handle. URL: ${url}, body: ${payloadInJson}, header:${JSON.stringify(headerList)}`);

    const configModule = req?.scope?.resolve("configModule");

    // Call Cisco validation API (matching .NET: PostMethodWithRequestJsonBodyAndHeaders400Handle)
    const response = await PostMethodWithRequestJsonBodyAndHeaders400Handle(
      {
        url: url,
        body: payloadInJson,
        headers: headerList,
        isVendorHit: true,
        module: "ValidateAndSaveCiscoCustomer",
      },
      configModule
    );

    logger.info(`Response recieved from cisco validate ${JSON.stringify(response)}`);

    const statusResponse: CiscoStatusResponseType = {};

    if (response.isSuccessStatusCode) {
      logger.info(`Entered into IsSuccessStatusCode true section. Going to deserialize string ${response?.content}`);
      
      // Deserialize successful response (matching .NET: JsonConvert.DeserializeObject<CiscoValidateCustomerResponse>)
      const validationResponseFromCisco: CiscoValidateCustomerResponse = JSON.parse(response.content);
      logger.info(`Deserialized object : ${JSON.stringify(validationResponseFromCisco)}`);

      logger.info(`Fetching first element of party list from response : ${JSON.stringify(validationResponseFromCisco)}`);
      const partyFromResponse = validationResponseFromCisco.party?.[0];
      logger.info(`First available party from response : ${JSON.stringify(partyFromResponse)}`);

      // Validation logic (matching .NET validation checks)
      if (comparableString(validationResponseFromCisco.status) === comparableString(CiscoConstants.ValidationStatusError)) {
        logger.info(`Entered into section where cisco thrown a validation error. Going to return fail status`);
        statusResponse.Status = CiscoConstants.StatusFail;
        statusResponse.Message = validationResponseFromCisco.message;
        logger.info(`Status response. ${JSON.stringify(statusResponse)}`);
      } else if (partyFromResponse && 
                 ((comparableString(partyFromResponse.geoValidation?.status) === comparableString(CiscoConstants.ValidationStatusError) ||
                   comparableString(partyFromResponse.geoValidation?.status) === comparableString(CiscoConstants.ValidationStatusGeoInvalid)) &&
                  partyFromResponse.geoValidation?.message?.trim())) {
        logger.info(`Entered into Geo invalid section. Going to return fail status`);
        statusResponse.Status = CiscoConstants.StatusFail;
        statusResponse.Message = partyFromResponse.geoValidation.message;
        logger.info(`Status response. ${JSON.stringify(statusResponse)}`);
      } else if (partyFromResponse &&
                 ((comparableString(partyFromResponse.nameValidation?.status) === comparableString(CiscoConstants.ValidationStatusError) ||
                   comparableString(partyFromResponse.nameValidation?.status) === comparableString(CiscoConstants.ValidationStatusNameInvalid)) &&
                  partyFromResponse.nameValidation?.message?.trim())) {
        logger.info(`Entered into Name invalid section. Going to return fail status`);
        statusResponse.Status = CiscoConstants.StatusFail;
        statusResponse.Message = partyFromResponse.nameValidation.message;
        logger.info(`Status response. ${JSON.stringify(statusResponse)}`);
      } else {
        logger.info(`Entered into no validation error section. Going to call DAL to save the end customer with model : ${JSON.stringify(ciscoValidateDALModel)}`);
        
        // Save customer to database (matching .NET: _customerDal.SaveCiscoCustomer)
        const dalResponse = await saveCiscoCustomerDAL(ciscoValidateDALModel);
        logger.info(`Response from DAL ${JSON.stringify(dalResponse)}`);
        
        return dalResponse;
      }
    } else {
      logger.info(`Entered into IsSuccessStatusCode false section. `);
      logger.info(`Setting status Failure`);
      
      // Handle error response (matching .NET error handling)
      statusResponse.Status = CiscoConstants.StatusFail;
      statusResponse.Message = response.errorMessage || "Unknown error occurred";
      logger.info(`Status response. ${JSON.stringify(statusResponse)}`);
    }

    logger.info(`Going to return response. ${JSON.stringify(statusResponse)}`);
    return statusResponse;

  } catch (error: any) {
    logger.error(`Error in ValidateAndSaveCiscoCustomer service: ${error.message}`);
    
    // Return error response
    return {
      Status: CiscoConstants.StatusFail,
      Message: `An error occurred: ${error.message}`,
      ActionType: ""
    };
  }
}
