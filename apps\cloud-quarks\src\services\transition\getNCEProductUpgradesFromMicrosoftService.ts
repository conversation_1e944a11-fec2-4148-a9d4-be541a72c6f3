import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import { getMethod } from "../externalService/externalEndPointService";
import { MicrosoftEndpoints } from "../../config/microsoftEndpoints";
import { EndPointResponseModel } from "../externalService/externalEndPointService";
import { getStoreDetails } from "../token/microsoft/getStoreDetailsService";
import { getMsToken } from "../token/microsoft/getTokenService";

/**
 * Prepare Token
 * Matches .NET PrepareToken method
 */
async function prepareToken(storeId: string): Promise<string> {
  logger.info(`Entered PrepareToken Method and Going to hit BL GetStoreDetails method with storeId-${storeId}`);
  
  // Get store details (matching .NET: TokenRequest tokenRequest = _storeFactory.GetStoreDetails(storeId))
  const storeData = await getStoreDetails({ storeId });
  logger.info(`Response from BL GetStoreDetails is ${JSON.stringify(storeData)}`);

  // Generate token (matching .NET: string token = _tokenGenerator.GetToken(tokenRequest, _urlOptions.GetToken))
  logger.info(`Going to hit BL TokenGenerator method with tokenRequest-${JSON.stringify(storeData)}`);
  const token = await getMsToken({
    brand: storeData.brand || "",
    client_id: storeData.clientid || "",
    client_secret: storeData.clientsecret || "",
    grant_type: storeData.granttype || "",
    markValue: storeData.markvalue?.toString() || "0",
    redirect_uri: storeData.redirecturi || "http://localhost",
    refresh_token: storeData.token || "",
    resource: storeData.resource || "",
    store_domain: storeData.storedomain || "",
  });

  if (!token || !token.access_token) {
    logger.error(`Token generation failed`);
    throw new Error("Failed to generate access token");
  }

  logger.info(`Got token from BL TokenGenerator method and returning from PrepareToken Method`);
  return token.access_token;
}

/**
 * Prepare Header Authorization with Bearer Token
 * Matches .NET PrepareHeaderAuthorizationwithBearerToken method
 */
function prepareHeaderAuthorizationWithBearerToken(token: string): Record<string, string> {
  logger.info(`Preparing HeaderList for external call`);
  const headerList: Record<string, string> = {
    Authorization: `Bearer ${token}`,
    Accept: "application/json",
  };
  return headerList;
}

/**
 * Get NCE Product Upgrades From Microsoft
 * Matches .NET GetNCEProductUpgradesFromMicrosoft method
 */
export async function getNCEProductUpgradesFromMicrosoftService(
  req: MedusaRequest,
  customerId: string,
  subscriptionId: string,
  storeId: string,
  eligibilityType: string
): Promise<EndPointResponseModel> {
  logger.info(`Entered into BL GetNCEProductUpgradesFromMicrosoft method with customerId-${customerId}, subscriptionId-${subscriptionId}, storeId-${storeId}, eligibilityType- ${eligibilityType}`);

  try {
    // Get Token (matching .NET: string token = PrepareToken(storeId))
    logger.info(`Going to hit BL PrepareToken method with storeId-${storeId}`);
    const token = await prepareToken(storeId);
    logger.info(`Response Recieved from BL PrepareToken`);

    // Get Microsoft Url (matching .NET URL construction)
    logger.info(`Getting GetNCETransitionEligibility Url From microsoftEndpoints with customerId-${customerId}, subscriptionId-${subscriptionId}`);
    const url = MicrosoftEndpoints.getNCETransitionEligibilityUrl(customerId, subscriptionId, eligibilityType);
    logger.info(`Got GetNCETransitionEligibility Url From microsoftEndpoints : ${url}`);

    // Prepare headers (matching .NET: PrepareHeaderAuthorizationwithBearerToken(token))
    logger.info(`Entering PrepareHeaderAuthorizationwithBearerToken with token`);
    const headerList = prepareHeaderAuthorizationWithBearerToken(token);
    logger.info(`Got Header list from PrepareHeaderAuthorizationwithBearerToken`);

    const configModule = req.scope.resolve("configModule");

    // Make the GET request (matching .NET: await _externalEndPoint.GetMethod(url, headerList))
    logger.info(`Going to hit ExternalEndPoint GetMethod method with url-${url} and headerlist-${JSON.stringify(headerList)}`);
    const response = await getMethod(
      {
        url,
        headers: headerList,
        isVendorHit: true,
        module: "GetNCEProductUpgradesFromMicrosoft",
      },
      configModule
    );
    
    logger.info(`Response from ExternalEndPoint GetMethod and returning from GetNCEProductUpgradesFromMicrosoft with response ${JSON.stringify(response)}`);

    return response;

  } catch (error: any) {
    logger.error(`Error in getNCEProductUpgradesFromMicrosoftService: ${error.message}`);
    throw error;
  }
}
