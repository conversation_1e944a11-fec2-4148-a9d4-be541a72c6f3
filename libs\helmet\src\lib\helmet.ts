import {
  MedusaRequest,
  MedusaResponse,
  MedusaNextFunction,
} from '@medusajs/framework/http';
import helmet from 'helmet';

export const helmetMiddleware = (
  req: MedusaRequest,
  res: MedusaResponse,
  next: MedusaNextFunction
) => {
  return helmet({
    frameguard: {
      action: 'deny',
    },
    contentSecurityPolicy: {
      useDefaults: true,
      directives: {
        'frame-ancestors': ["'none'"],
      },
    },
  })(req, res, next);
};
