# Stage 1: Build
FROM node:20-alpine AS builder

WORKDIR /app

COPY package.json yarn.lock tsconfig.base.json nx.json ./
COPY eslint.base.config.mjs eslint.config.mjs jest.config.ts jest.preset.js ./

RUN yarn install
COPY . .

WORKDIR /app/shared-app/auth
RUN yarn install

# Stage 2: Production Image
FROM node:20-alpine AS runner

WORKDIR /app

COPY --from=builder /app/libs /app/libs
COPY --from=builder /app/shared-app/auth /app/shared-app/auth
COPY --from=builder /app/node_modules /app/node_modules
COPY --from=builder /app/package.json /app/package.json
COPY --from=builder /app/yarn.lock /app/yarn.lock
COPY --from=builder /app/nx.json /app/nx.json
COPY --from=builder /app/tsconfig.base.json /app/tsconfig.base.json
COPY --from=builder /app/eslint.base.config.mjs /app/eslint.base.config.mjs
COPY --from=builder /app/eslint.config.mjs /app/eslint.config.mjs
COPY --from=builder /app/jest.config.ts /app/jest.config.ts
COPY --from=builder /app/jest.preset.js /app/jest.preset.js
   
EXPOSE 3000
CMD ["npx", "nx", "serve", "auth"]