import {
	defineMiddlewares,
	authenticate,
	MedusaRequest,
	MedusaResponse,
	validateAndTransformBody,
	validateAndTransformQuery,
	MedusaNextFunction,
} from '@medusajs/framework/http';
// import { helmetMiddleware } from '@org/helmet';
import { parseCorsOrigins } from '@medusajs/framework/utils';
import cors from 'cors';
// import {
//   createDefaultRateLimiter,
//   createAuthRateLimiter,
// } from '@org/rate_limit';

// import { adaptMiddleware } from './middlewares/medusa-middleware-adapter';
// import { customAuthenticate } from './middlewares/custom-auth';
import { Modules } from '@medusajs/framework/utils';
const axios = require('axios');
const jwt = require('jsonwebtoken');
import multer from 'multer';
const upload = multer({ storage: multer.memoryStorage() });
import { ConfigModule } from '@medusajs/framework';
import {
	changePasswordSchema,
	forgotPasswordSchema,
	newPasswordSchema,
} from './v1/user/validators';
import {
	userPreferenceListSchema,
	userPreferenceSchema,
} from './v1/user/user-preference-column-list/validators';
import { customAuthenticate } from '../../../../libs/utils/src/lib/authenticateValidator';
import {
	GuestUserSchema,
	baseResendOtpSchema,
} from './v1/user/signup/validators';

export default defineMiddlewares({
	routes: [
		// Default rate limiter and cors origins for all other routes should be last
		{
			matcher: '/*',
			middlewares: [
				(req: MedusaRequest, res: MedusaResponse, next: MedusaNextFunction) => {
					const configModule: ConfigModule = req.scope.resolve('configModule');
					return cors({
						origin: parseCorsOrigins(configModule.projectConfig.http.storeCors),
						credentials: true,
					})(req, res, next);
				},
				// adaptMiddleware(createDefaultRateLimiter()), helmetMiddleware
			],
		},
		// Auth rate limiter for specific auth endpoints
		{
			matcher: '/auth/*',
			// middlewares: [adaptMiddleware(createAuthRateLimiter())],
		},
		{
			matcher: '*',
			middlewares: [
				(req: MedusaRequest, res: MedusaResponse, next: MedusaNextFunction) => {
					// Set security headers
					res.setHeader('X-Content-Type-Options', 'nosniff');
					res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
					res.setHeader('X-Frame-Options', 'DENY');
					res.setHeader('X-XSS-Protection', '1; mode=block');

					// You can also set Content-Security-Policy here
					res.setHeader('Content-Security-Policy', "default-src 'self'");
					next();
				},
			],
		},
		{
			matcher: '/v1/user/forgot-password',
			method: 'POST',
			middlewares: [validateAndTransformBody(forgotPasswordSchema)],
		},
		{
			matcher: '/v1/user/update-password/:token',
			method: 'PUT',
			middlewares: [validateAndTransformBody(newPasswordSchema)],
		},
		{
			matcher: '/v1/user',
			method: 'GET',
			middlewares: [
				customAuthenticate as unknown as (
					req: MedusaRequest,
					res: MedusaResponse,
					next: MedusaNextFunction
				) => any,
			],
		},
		{
			matcher: '/v1/user',
			method: 'PUT',
			middlewares: [
				customAuthenticate as unknown as (
					req: MedusaRequest,
					res: MedusaResponse,
					next: MedusaNextFunction
				) => any,
			],
		},
		{
			matcher: '/v1/user/authenticate-validator',
			method: 'GET',
		},
		{
			matcher: '/v1/user/change-password',
			method: 'POST',
			middlewares: [
				customAuthenticate as unknown as (
					req: MedusaRequest,
					res: MedusaResponse,
					next: MedusaNextFunction
				) => any,
				validateAndTransformBody(changePasswordSchema),
			],
		},
		// {
		//   matcher: '/v1/user/accounts/*',
		//   middlewares: [
		//     authenticate('cq_user', ['session']) as unknown as (
		//       req: MedusaRequest,
		//       res: MedusaResponse,
		//       next: MedusaNextFunction
		//     ) => any,
		//   ],
		// },
		{
			matcher: '/v1/user/user-preference-column-list',
			middlewares: [
				customAuthenticate as unknown as (
					req: MedusaRequest,
					res: MedusaResponse,
					next: MedusaNextFunction
				) => any,
				validateAndTransformBody(userPreferenceListSchema),
			],
		},
		{
			matcher: '/v1/user/user-preference-column',
			middlewares: [
				customAuthenticate as unknown as (
					req: MedusaRequest,
					res: MedusaResponse,
					next: MedusaNextFunction
				) => any,
				validateAndTransformBody(userPreferenceSchema),
			],
		},
		{
			matcher: '/v1/user/access-management/roles',
			method: 'GET',
			middlewares: [
				customAuthenticate as unknown as (
					req: MedusaRequest,
					res: MedusaResponse,
					next: MedusaNextFunction
				) => any,
			],
		},

		{
			matcher: '/v1/user/signup',
			method: 'POST',
			middlewares: [validateAndTransformBody(GuestUserSchema)],
		},

		{
			matcher: '/v1/user/signup/resend-otp',
			method: 'POST',
			middlewares: [
				customAuthenticate as unknown as (
					req: MedusaRequest,
					res: MedusaResponse,
					next: MedusaNextFunction
				) => any,
				validateAndTransformBody(baseResendOtpSchema),
			],
		},

		{
			matcher: '/v1/user/signup/reset-password/:token',
			method: 'PUT',
			middlewares: [validateAndTransformBody(newPasswordSchema)],
		},
	],
});
