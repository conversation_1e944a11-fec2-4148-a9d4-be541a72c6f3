{"name": "admin", "sourceRoot": "apps/admin/src", "projectType": "application", "targets": {"serve": {"executor": "nx:run-commands", "options": {"command": "cd apps/admin && yarn run dev"}}, "build": {"executor": "nx:run-commands", "options": {"command": "cd apps/admin && yarn run build"}}, "seed": {"executor": "nx:run-commands", "options": {"cwd": "apps/admin", "command": "medusa exec ./src/scripts/seed-designations.ts  && medusa exec ./src/scripts/seed-region.ts && medusa exec ./src/scripts/seed-actor.ts"}}}}