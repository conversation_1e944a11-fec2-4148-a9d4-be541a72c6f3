import logger from "../../utils/logger";
import { GetSubscriptionsByCustomerIdAndMaterialIdResponse } from "../../types/responses/customResponse";
import { getSubscriptionsByCustomerIdAndMaterialIdDAL } from "./getSubscriptionsByCustomerIdAndMaterialIdDAL";

export async function getSubscriptionsByCustomerIdAndMaterialIdService(
  customerId: string,
  materialId: string
): Promise<GetSubscriptionsByCustomerIdAndMaterialIdResponse[]> {
  logger.info(`Entered into BL GetSubscriptionsByCustomerIdAndMaterialId method Going to Hit DAL with customerId-${customerId}, materialId-${materialId}`);

  try {
    // Call DAL method 
    const response = await getSubscriptionsByCustomerIdAndMaterialIdDAL(customerId, materialId);
    
    logger.info(`Response from DAL GetSubscriptionsByCustomerIdAndMaterialId, and returning from BL with response ${JSON.stringify(response)}`);
    
    return response;

  } catch (error: any) {
    logger.error(`Error in getSubscriptionsByCustomerIdAndMaterialIdService: ${error.message}`);
    throw error;
  }
}
