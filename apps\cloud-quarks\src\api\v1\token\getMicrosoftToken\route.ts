import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import { getMsToken } from "../../../../services/index";
import { getStoreDetails } from "../../../../services/index";
import logger from "../../../../utils/logger";

/**
 * @openapi
 * /v1/token/getMicrosoftToken:
 *   get:
 *     summary: Get Microsoft Token
 *     tags:
 *       - Token
 *     parameters:
 *       - in: query
 *         name: storeId
 *         schema:
 *           type: string
 *         required: true
 *         description: Store ID
 *       - in: query
 *         name: brandId
 *         schema:
 *           type: string
 *         required: true
 *         description: Brand ID
 *     responses:
 *       200:
 *         description: Successfully fetched token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 access_token:
 *                   type: string
 *                   example: eyJ0eXAiOiJKV1QiLCJh...
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    logger.info("getMsToken API called");
    const { storeId, brandId } = req.query as {
      storeId: string;
      brandId?: string;
    };

    if (!storeId) {
      return res
        .status(400)
        .json({ message: "Missing required parameter: storeId" });
    }
    if (!brandId) {
      return res
        .status(400)
        .json({ message: "Missing required parameter: brandId" });
    }

    logger.info(
      `Entered into getStoreDetails method with storeId-${storeId} and brandId-${brandId}`
    );
    const storeData = await getStoreDetails({
      storeId,
      brandId,
    });

    const tokenResponse = await getMsToken({
      brand: storeData.brand || "",
      client_id: storeData.clientid || "",
      client_secret: storeData.clientsecret || "",
      grant_type: storeData.granttype || "",
      markValue: storeData.markvalue?.toString() || "",
      redirect_uri: storeData.redirecturi || "",
      refresh_token: storeData.token || "",
      resource: storeData.resource || "",
      store_domain: storeData.storedomain || "",
    });
    logger.info(`Success - Exiting Token-GetMsToken method with access token`);
    console.log("tokenResponse------>", tokenResponse);
    // return res.status(200).json(tokenResponse.access_token);
    return res.status(200).json({
      status: "success",
      access_token: tokenResponse.access_token,
    });
  } catch (error: any) {
    return res.status(500).json({
      message: "Internal server error",
      error: error.message,
    });
  }
}
