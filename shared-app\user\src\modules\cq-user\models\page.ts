import { model } from '@medusajs/framework/utils';
import CQUser from './cq_user';
import Permission from './permission';
import PagePermission from './page-permission';
import Role from './role';
import RolePage from './role-page';
import Module from './module';
import ColumnMaster from './column-master';
import UserColumnPreference from './user-column-preference';

const Page = model.define('cq_page', {
  id: model.id().primaryKey(),
  page: model.text().unique(),
  metadata: model.json().nullable(),
  order_id: model.number(),
  created_by: model.belongsTo(() => CQUser, { mappedBy: undefined }).nullable(),
  updated_by: model.belongsTo(() => CQUser, { mappedBy: undefined }).nullable(),
  deleted_by: model.belongsTo(() => CQUser, { mappedBy: undefined }).nullable(),
   // table_type: model.number().default(1), // 1 = Manage, 2 = Reports
  permissions: model.manyToMany(() => Permission, {
    pivotEntity: () => PagePermission,
    mappedBy: 'pages',
  }),
  roles: model.manyToMany(() => Role, {
    pivotEntity: () => RolePage,
    mappedBy: 'pages',
  }),
  module: model.belongsTo(() => Module, { mappedBy: 'pages' }),
  columns: model.hasMany(() => ColumnMaster, { mappedBy: 'table_master' }),
  preferences: model.hasMany(() => UserColumnPreference, {
    mappedBy: 'table_master',
  }),
});

export default Page;
