import { z } from "zod";
import { GetSAPInvoiceDocumentDto } from "../../types/responses/customResponse";
import { checkInvoiceExistsOrNotService } from "../../services/sap/sapInvoiceResponseService";

// Zod schema for GetSAPInvoiceDocumentDto validation
export const GetSAPInvoiceDocumentSchema = z.object({
  InvoiceNo: z
    .string()
    .min(1, "InvoiceNo is required")
    .max(10, "Max length allowed for InvoiceNo is exceeded")
});

// Custom validation function that includes database check
export async function validateGetSAPInvoiceDocument(
  getSAPInvoiceDocumentDto: GetSAPInvoiceDocumentDto
): Promise<string[]> {
  const errors: string[] = [];

  // Basic validation using Zod
  const validation = GetSAPInvoiceDocumentSchema.safeParse(getSAPInvoiceDocumentDto);
  
  if (!validation.success) {
    errors.push(...validation.error.errors.map(e => e.message));
    return errors; // Return early if basic validation fails
  }

  // Check if invoice exists in database
  try {
    const invoiceExists = await checkInvoiceExistsOrNotService(getSAPInvoiceDocumentDto.InvoiceNo);
    if (!invoiceExists) {
      errors.push("InvoiceNo does not exist in our records");
    }
  } catch (error) {
    errors.push("Error validating invoice existence");
  }

  return errors;
}
