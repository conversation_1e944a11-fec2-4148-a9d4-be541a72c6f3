import logger from "../../utils/logger";
import {
  SubscriptionModel,
  SubscriptionPromotionDetailModel,
  GetSubscriptionResponse
} from "../../types/responses/customResponse";
import { getMsToken } from "../token/microsoft/getTokenService";
import { getStoreDetails } from "../token/microsoft/getStoreDetailsService";
import { getSubscriptionById } from "../entitlement/subscriptionService";
import { checkPromotionWithEligibilityService } from "./checkPromotionWithEligibility";

/**
 * Check Last Promotion Details
 * Matches .NET CheckLastPromotionDetails method
 */
export async function checkLastPromotionDetailsService(
  req: any,
  subscriptionId: string,
  subDetails: SubscriptionModel,
  billType: string,
  term: string,
  materialno: string,
  quantity: number,
  segment: string,
  brandId: number // Add brandId parameter as in .NET
): Promise<SubscriptionPromotionDetailModel> {
  let subscriptionPromotionDetailModel: SubscriptionPromotionDetailModel = {
    promotionEligibilities: []
  };

  logger.info(`Going to get token for store id :${subDetails.StoreId} and Brand Id :${brandId}`);

  // Get store details and token (matching .NET: string token = await _tokenService.GetMSToken(subDetails.StoreId, subDetails.BrandId))
  const storeDetails = await getStoreDetails({
    storeId: subDetails.StoreId,
    brandId: brandId.toString() // Use brandId parameter as in .NET
  });

  const tokenResponse = await getMsToken({
    brand: storeDetails.brand || "",
    client_id: storeDetails.clientid || "",
    client_secret: storeDetails.clientsecret || "",
    grant_type: storeDetails.granttype || "",
    markValue: storeDetails.markvalue?.toString() || "",
    redirect_uri: storeDetails.redirecturi || "",
    refresh_token: storeDetails.token || "",
    resource: storeDetails.resource || "",
    store_domain: storeDetails.storedomain || "",
  });

  if (!tokenResponse || !tokenResponse.access_token) {
    throw new Error("Failed to generate access token");
  }

  const token = tokenResponse.access_token;
  logger.info(`Token length is ${token.length}. Going to get microsoft details for customer id :${subDetails.CustomerId}, subscriptionid :${subscriptionId}`);

  // Get subscription details from Microsoft (matching .NET: GetSubscriptionResponse subDetailsAtMicrosoft = _subscriptionFactory.GetSubscriptionById)
  const subDetailsAtMicrosoft: GetSubscriptionResponse = await getSubscriptionById(
    req,
    subDetails.CustomerId,
    subscriptionId,
    token
  );

  logger.info(`Sub details at microsoft for sub id :${subscriptionId} are ${JSON.stringify(subDetailsAtMicrosoft)}`);

  // Check if active promotion is applied (matching .NET: if (string.IsNullOrEmpty(subDetailsAtMicrosoft.PromotionId)))
  if (!subDetailsAtMicrosoft.PromotionId || subDetailsAtMicrosoft.PromotionId.trim() === "") {
    subscriptionPromotionDetailModel = await checkPromotionWithEligibilityService(
      subDetails,
      billType,
      term,
      materialno,
      quantity,
      segment
    );
  }

  logger.info(`Going to return last promotion id details as ${JSON.stringify(subscriptionPromotionDetailModel)}`);
  return subscriptionPromotionDetailModel;
}
