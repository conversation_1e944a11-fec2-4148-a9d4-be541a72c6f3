import { z } from "zod"
import logger from "../../utils/logger"
import { getSqlServerConnection } from "../../utils/sqlServerClient"

export interface RenewalDto {
  BrandId: number
  AutoRenewStatus: boolean
  MaterialNo?: string
  PlanType?: string
  TermDuration?: string
  PromotionId?: string
  Quantity: number
  CustomTermEndDate?: string // ISO 8601 format
  SubscriptionId: string
  DiscountPercentage?: number
  PromotionRate?: number
  IsOldSubscriptionModified?: boolean
  LegacyBrandId: number
  NCEBrandId: number
  SSBrandId: number
  TaxPercentage?: number
  CustomTermType?: string
  CustomTermPrice?: number
  FirstScheduleEndDate?: string // ISO 8601 format
  QuantityType?: string
  GoogleBrandIds?: string
}

export interface RenewalMasterModel {
  BrandId: number;
  AutoRenewStatus: boolean;
  MaterialNo: string;
  PlanType: string;
  TermDuration: string;
  PromotionId?: string;
  Quantity: number;
  CustomTermEndDate?: string; // ISO date string
  SubscriptionId: string;
  DiscountPercentage?: number;
  TaxPercentage?: number;
  PromotionRate?: number;
  IsOldSubscriptionModified?: boolean;
  CustomTermType?: string;
  CustomTermPrice?: number;
  FirstScheduleEndDate?: string; // ISO date string
  QuantityType?: string;
}


export function mapRenewalDtoToModel(dto: any): RenewalMasterModel {
  return {
    BrandId: dto.BrandId,
    AutoRenewStatus: dto.AutoRenewStatus,
    MaterialNo: dto.MaterialNo,
    PlanType: dto.PlanType,
    TermDuration: dto.TermDuration,
    PromotionId: dto.PromotionId,
    Quantity: dto.Quantity,
    CustomTermEndDate: dto.CustomTermEndDate,
    SubscriptionId: dto.SubscriptionId,
    DiscountPercentage: dto.DiscountPercentage,
    TaxPercentage: dto.TaxPercentage,
    PromotionRate: dto.PromotionRate,
    IsOldSubscriptionModified: dto.IsOldSubscriptionModified,
    CustomTermType: dto.CustomTermType,
    CustomTermPrice: dto.CustomTermPrice,
    FirstScheduleEndDate: dto.FirstScheduleEndDate,
    QuantityType: dto.QuantityType,
  };
}


export interface StatusResponse {
  Status: string
  Message: string
}

export const GoogleRenewalQuantityType = {
  TOTALPURCHASED: 'TOTALPURCHASED',
  CURRENTASSIGNED: 'CURRENTASSIGNED'
}

const isBrandUpgraded = (
  autoRenewStatus: boolean,
  isOldSubscriptionModified: boolean | undefined,
  brandId: number,
  nceBrandId: number,
  ssBrandId: number,
  googleBrandIds: string
) => {
  const isGoogleBrand = googleBrandIds
    .split(",")
    .map(x => parseInt(x.trim()))
    .includes(brandId)

  return !(
    autoRenewStatus &&
    isOldSubscriptionModified &&
    brandId !== nceBrandId &&
    brandId !== ssBrandId &&
    !isGoogleBrand
  )
}

const isGoogleBrand = (
  brandId: number,
  googleBrandIds: string,
  isOldSubscriptionModified?: boolean,
  autoRenewStatus?: boolean
) => {
  if (!isOldSubscriptionModified || !autoRenewStatus) return false
  return googleBrandIds
    .split(",")
    .map(x => parseInt(x.trim()))
    .includes(brandId)
}

const isValidGoogleQuantityType = (
  autoRenewStatus: boolean | undefined,
  quantityType: string | undefined,
  isOldSubscriptionModified: boolean | undefined,
  brandId: number,
  googleBrandIds: string
) => {
  if (!quantityType) return true

  const isGoogle = googleBrandIds
    .split(",")
    .map(x => parseInt(x.trim()))
    .includes(brandId)

  const validTypes = [
    GoogleRenewalQuantityType.TOTALPURCHASED,
    GoogleRenewalQuantityType.CURRENTASSIGNED
  ]

  return !(
    isGoogle &&
    isOldSubscriptionModified &&
    autoRenewStatus &&
    !validTypes.includes(quantityType)
  )
}

export const RenewalDtoSchema = z
  .object({
    BrandId: z.number({ required_error: "BrandId cannot be null or empty" }),
    AutoRenewStatus: z.boolean(),
    MaterialNo: z.string().optional(),
    PlanType: z.string().optional(),
    TermDuration: z.string().optional(),
    PromotionId: z.string().optional(),
    Quantity: z.number(),
    CustomTermEndDate: z.string().datetime().optional(),
    SubscriptionId: z.string({ required_error: "SubscriptionId cannot be null or empty" }).min(1, "SubscriptionId cannot be null or empty"),
    DiscountPercentage: z.number().optional(),
    PromotionRate: z.number().optional(),
    IsOldSubscriptionModified: z.boolean().optional(),
    LegacyBrandId: z.number(),
    NCEBrandId: z.number(),
    SSBrandId: z.number(),
    TaxPercentage: z.number().optional(),
    CustomTermType: z.string().optional(),
    CustomTermPrice: z.number().optional(),
    FirstScheduleEndDate: z.string().datetime().optional(),
    QuantityType: z.string().optional(),
    GoogleBrandIds: z.string()
  })
  .superRefine((data, ctx) => {
    const {
      AutoRenewStatus,
      IsOldSubscriptionModified,
      BrandId,
      NCEBrandId,
      SSBrandId,
      LegacyBrandId,
      GoogleBrandIds,
      DiscountPercentage,
      TaxPercentage,
      PlanType,
      TermDuration,
      QuantityType,
      CustomTermEndDate
    } = data

    if (AutoRenewStatus) {
      if (DiscountPercentage === undefined) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "DiscountPercentage cannot be null or empty",
          path: ["DiscountPercentage"]
        })
      }

      if (TaxPercentage === undefined) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "TaxPercentage cannot be null or empty",
          path: ["TaxPercentage"]
        })
      }

      if (BrandId !== LegacyBrandId) {
        if (!PlanType) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "PlanType cannot be null or empty",
            path: ["PlanType"]
          })
        }

        if (!TermDuration) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "TermDuration cannot be null or empty",
            path: ["TermDuration"]
          })
        }
      }

      if (BrandId !== NCEBrandId && CustomTermEndDate) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "The custom term end date specified is only valid for NCE products.",
          path: ["CustomTermEndDate"]
        })
      }
    }

    if (!isBrandUpgraded(AutoRenewStatus, IsOldSubscriptionModified, BrandId, NCEBrandId, SSBrandId, GoogleBrandIds)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Subscription can be upgraded in case of CSP NCE/ Software Sub only",
        path: ["BrandId"]
      })
    }

    if (isGoogleBrand(BrandId, GoogleBrandIds, IsOldSubscriptionModified, AutoRenewStatus)) {
      if (!QuantityType) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "QuantityType cannot be null or empty",
          path: ["QuantityType"]
        })
      }

      if (!isValidGoogleQuantityType(AutoRenewStatus, QuantityType, IsOldSubscriptionModified, BrandId, GoogleBrandIds)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Invalid Quantity Type",
          path: ["QuantityType"]
        })
      }
    }
  })


  export const ConstantValue = Object.freeze({
  DELETE: "DELETE",
  UPDATE: "UPDATE",
  CREATE: "CREATE",
  VALIDATE: "VALIDATE",
  FAIL: "FAIL",
  PASS: "PASS",
  SEND_TO_PROCESS: "SENDTOPROCESS",
  STATUS_ERROR: "ERROR",
  PROCESSING: "PROCESSING",
  FETCHED: "FETCHED",
  SAP_POSTING_COMPLETED: "COMPLETED",
  SUCCESS: "SUCCESS",
  AUTO_RENEW_ON: "AUTORENEWON",
  AUTO_RENEW_OFF: "AUTORENEWOFF",
  NEW: "NEW",
  EXISTING: "EXISTING",
});

export const Message = Object.freeze({
  TRANSIENT_ERROR: "Something went wrong",
  NO_CHANGES: "No changes were made",
  INVALID_MODEL: "Invalid model passed",
  PRODUCT_NOT_PRESENT_AT_VENDOR: "Product is not available at vendor",
});


export const appConfig = {
  Brands: {
    LegacyBrandId: 14,
    NCEBrandId: 1002,
    SSBrandId: 939,
    GoogleBrandIds: "1004, 1005",
  },
  AppSettings: {
    ThresholdLimitForLegacyRenew: "2024-01-01",
  },
};

export function comparableString(input?: string): string {
  return (input || "").toUpperCase().replace(/\s+/g, "");
}

export const validateGetGoogleRenewalDetailsRequest = async (
  storeId: string,
  googleCustomerId: string,
  entitlementId: string
): Promise<string[]> => {
  const errors: string[] = [];

  if (!storeId?.trim()) {
    errors.push("StoreId can't be null or empty");
  } else {
    const allStores = await getAllStoresService();
    const storeExists = allStores.some(
      (s: { StoreId: string | undefined }) => comparableString(s.StoreId) === comparableString(storeId)
    );
    if (!storeExists) errors.push("StoreId doesn't exists");
  }

  if (!googleCustomerId?.trim()) {
    errors.push("GoogleCustomerId can't be null or empty");
  }

  if (!entitlementId?.trim()) {
    errors.push("EntitlementId can't be null or empty");
  }

  return errors;
};


export const getAllStoresService = async () => {
  logger.info("Entered into GetAllStores Service and going to call DAL method");
  const stores = await getAllStoresDAL();
  return stores;
};

export interface StoreDetailModel {
  StoreKey: number;
  StoreId: string;
  StoreLanguage: string;
  Currency: string;
}


export const getAllStoresDAL = async (): Promise<StoreDetailModel[]> => {
  logger.info("Entered into StoreDAL-GetAllStores");

  const pool = await getSqlServerConnection()
  const request = pool.request();

  const procedureName = "usp_get_allStores";
  logger.info(`Going to hit DB with proc name: ${procedureName}`);

  const result = await request.execute<StoreDetailModel>(procedureName);
  const response = result.recordset || [];

  logger.info(`Response from ${procedureName}: ${JSON.stringify(response)}`);
  return response;
};

