import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import {
  validateGetAzureOperationDetails
} from "../../../../validators/azure/azureOperationDetailsValidator";
import { getAzureOperationDetailsService } from "../../../../services/index";

/**
 * @openapi
 * /v1/azure/getAzureOperationDetails:
 *   get:
 *     summary: Get Azure operation details
 *     description: |
 *       Retrieves details for a specific Azure operation using the operation ID.
 *       This endpoint calls the Microsoft Partner Center API to get operation status and details.
 *     tags:
 *       - Azure
 *     parameters:
 *       - in: query
 *         name: storeId
 *         required: true
 *         schema:
 *           type: string
 *         description: Store ID for authentication and context
 *         example: "AE-EN"
 *       - in: query
 *         name: brandId
 *         required: true
 *         schema:
 *           type: string
 *         description: Brand ID for authentication and context
 *         example: "943"
 *       - in: query
 *         name: operationId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Azure operation ID (UUID format) to get details for
 *         example: "f6fdbbd2-83c5-4925-ae3b-50db13d4bbdf"
 *     responses:
 *       200:
 *         description: Operation details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Success"
 *                   description: Response message indicating success or error details
 *                 data:
 *                   type: object
 *                   description: Azure operation details data from Microsoft API
 *                   properties:
 *                     id:
 *                       type: string
 *                       example: "f6fdbbd2-83c5-4925-ae3b-50db13d4bbdf"
 *                       description: Operation ID
 *                     status:
 *                       type: string
 *                       example: "completed"
 *                       description: Operation status (e.g., completed, failed, in-progress)
 *                     createdDateTime:
 *                       type: string
 *                       format: date-time
 *                       example: "2024-07-29T10:30:00Z"
 *                       description: When the operation was created
 *                     lastActionDateTime:
 *                       type: string
 *                       format: date-time
 *                       example: "2024-07-29T10:35:00Z"
 *                       description: When the operation was last updated
 *                 statusCode:
 *                   type: number
 *                   example: 200
 *                   description: HTTP status code
 *                 isError:
 *                   type: boolean
 *                   example: false
 *                   description: Indicates if the response contains an error
 *       400:
 *         description: Bad Request - Validation error or missing required parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "StoreId can't be null or empty, BrandId can't be null or empty"
 *                   description: Detailed validation error message
 *                 statusCode:
 *                   type: number
 *                   example: 400
 *                   description: HTTP status code
 *                 isError:
 *                   type: boolean
 *                   example: true
 *                   description: Always true for error responses
 *       500:
 *         description: Internal Server Error - Token generation failed or store details not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Error While Generating Token"
 *                   description: Error message describing the internal server error
 *                 statusCode:
 *                   type: number
 *                   example: 500
 *                   description: HTTP status code
 *                 isError:
 *                   type: boolean
 *                   example: true
 *                   description: Always true for error responses
 *       422:
 *         description: Unprocessable Entity - Unexpected error or exception occurred
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Exception: Network timeout occurred"
 *                   description: Exception message with details about the error
 *                 statusCode:
 *                   type: number
 *                   example: 422
 *                   description: HTTP status code
 *                 isError:
 *                   type: boolean
 *                   example: true
 *                   description: Always true for error responses
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req);

  try {
    // Extract query parameters
    const { storeId, brandId, operationId } = req.query as {
      storeId?: string;
      brandId?: string;
      operationId?: string;
    };

    logger.info(
      `Entered into GetAzureOperationDetails API with storeId:${storeId}, brandId:${brandId}, operationId:${operationId} | CorrelationId: ${correlationId}`
    );

    // Validate parameters
    const validationErrors = validateGetAzureOperationDetails(
      storeId || "",
      brandId || "",
      operationId || ""
    );

    logger.info(`Validations error count is ${validationErrors.length}`);

    if (validationErrors.length > 0) {
      const errorMessage = validationErrors.join(', ');

      const apiResponse = {
        message: errorMessage,
        isError: true,
        statusCode: 400,
      };

      logger.info(`Going to send bad request response | CorrelationId: ${correlationId} | Response: ${JSON.stringify(apiResponse)}`);
      return res.status(400).json(apiResponse);
    }

    // Call Azure service
    const apiResponse = await getAzureOperationDetailsService(
      req,
      storeId!,
      brandId!,
      operationId!
    );

    logger.info(
      `Returning response from GetAzureOperationDetails API | CorrelationId: ${correlationId} | ApiResponse: ${JSON.stringify(apiResponse)}`
    );

    return res.status(apiResponse.statusCode).json(apiResponse);
  } catch (exp) {
    const errorMessage = exp instanceof Error ? exp.message : "Unknown error occurred";

    const apiResponse = {
      message: `Exception: ${errorMessage}`,
      isError: true,
      statusCode: 422, // UnprocessableEntity
    };

    logger.error(
      `Error in GetAzureOperationDetails API method | CorrelationId: ${correlationId} | Message: ${errorMessage} | StackTrace: ${exp instanceof Error ? exp.stack : 'No stack trace'}`
    );

    return res.status(422).json(apiResponse);
  }
}