import { getEntitlementById } from "./getEntitlementByIdService";
import logger from "../../utils/logger";
import { GoogleApiResponse, GoogleEntitlementExtension, GoogleRenewalDetails } from "../../types/responses/customResponse";

export const getGoogleRenewalDetailsService = async (
  storeId: string,
  googleCustomerId: string,
  entitlementId: string
): Promise<GoogleApiResponse> => {
  let response = await getEntitlementById(storeId, googleCustomerId, entitlementId);
  logger.info(`Got response from getEntitlementById for GetGoogleRenewalDetails: ${JSON.stringify(response)}`);

  const renewalDetails: GoogleRenewalDetails = {
    Quantities: {
      CurrentAssigned: null,
      TotalPurchased: null,
    },
  };

  if (!response.IsError) {
    const googleEntitlement = response.Data as GoogleEntitlementExtension;

    if (googleEntitlement) {
      for (const item of googleEntitlement.Parameters || []) {
        const paramName = item?.Name ?? "";
        switch (paramName) {
          case "assigned_units":
            renewalDetails.Quantities.CurrentAssigned = item?.Value?.Int64Value ?? null;
            break;
          case "num_units":
            renewalDetails.Quantities.TotalPurchased = item?.Value?.Int64Value ?? null;
            break;
        }
      }

      if (
        renewalDetails.Quantities.TotalPurchased == null ||
        renewalDetails.Quantities.CurrentAssigned == null
      ) {
        response = {
          IsError: true,
          Message: "Quantities not found from Google",
          StatusCode: 200,
          Data: null,
        };
      } else {
        response = {
          IsError: false,
          StatusCode: 200,
          Message: "",
          Data: renewalDetails,
        };
      }
    } else {
      response = {
        IsError: true,
        Message: "Entitlement not found",
        StatusCode: 200,
        Data: null,
      };
    }
  }

  logger.info(`Returning response from getGoogleRenewalDetailsService: ${JSON.stringify(response)}`);
  return response;
};
