import { model } from '@medusajs/framework/utils';
import CQ<PERSON>ser from './cq_user';
import Persona from './persona';
import Page from './page';

const Module = model.define('cq_module', {
  id: model.id().primaryKey(),
  module: model.text().unique(),
  metadata: model.json().nullable(),
  order_id: model.number(),
  created_by: model.belongsTo(() => CQUser, { mappedBy: undefined }).nullable(),
  updated_by: model.belongsTo(() => CQUser, { mappedBy: undefined }).nullable(),
  deleted_by: model.belongsTo(() => CQUser, { mappedBy: undefined }).nullable(),
  persona: model.belongsTo(() => Persona, { mappedBy: 'modules' }),
  pages: model.hasMany(() => Page, { mappedBy: 'module' }),
});

export default Module;
