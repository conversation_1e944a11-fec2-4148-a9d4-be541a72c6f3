import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import CQUserService from '../../../../../modules/cq-user/service';
import { CQUser_MODULE } from '../../../../../modules/cq-user';
import {
  stringConstants,
  sendTransactionalEmail,
  renderOtpEmail,
} from '@org/utils';

/**
 * @openapi
 * /v1/user/signup/email-resend:
 *   post:
 *     summary: Resend OTP to email address
 *     tags:
 *       - Signup - Email
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 example: <EMAIL>
 *     responses:
 *       200:
 *         description: OTP resent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: OTP resent successfully.
 *       400:
 *         description: Email not provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Email is required
 *       404:
 *         description: No unverified OTP request found for the email
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Email OTP request does not exist
 *       429:
 *         description: Resend request rate limited
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Please wait 5 seconds before requesting a new OTP.
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error. Please try again later.
 */

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const emailOtpService: CQUserService = req.scope.resolve(CQUser_MODULE);
  const { email } = req.body as { email: string };

  try {
    if (!email) {
      return res
        .status(400)
        .json({ message: stringConstants.REQUIRED('Email') });
    }

    const entries = await emailOtpService.listEmailOtpVerifications({
      email: email.toLowerCase().trim(),
      is_verified: false,
    });

    if (!entries || entries.length === 0) {
      return res
        .status(404)
        .json({ message: stringConstants.NOT_FOUND('Email OTP request') });
    }

    const latest = entries[entries.length - 1];

    const now = new Date();
    const lastSentAt = new Date(latest.expires_at.getTime() - 60 * 1000);

    const secondsSinceLastOtp = (now.getTime() - lastSentAt.getTime()) / 1000;
    if (secondsSinceLastOtp < 10) {
      return res.status(429).json({
        message: `Please wait ${Math.ceil(
          10 - secondsSinceLastOtp
        )} seconds before requesting a new OTP.`,
      });
    }

    const newOtp = Math.floor(100000 + Math.random() * 900000).toString();
    const newExpiry = new Date(Date.now() + 60 * 1000);
    const htmlBody = await renderOtpEmail(newOtp);

    await emailOtpService.updateEmailOtpVerifications({
      id: latest.id,
      otp_code: newOtp,
      expires_at: newExpiry,
    });
    await sendTransactionalEmail({
      toEmail: email.toLowerCase().trim(),
      subjectTemplate: 'Email Verification Code for Cloudquarks',
      htmlContentTemplate: htmlBody,
      senderEmail: '<EMAIL>',
      senderName: 'Redington',
    });

    return res.status(200).json({ message: stringConstants.OTP_RESEND() });
  } catch (err: any) {
    return res
      .status(500)
      .json({ message: stringConstants.INTERNAL_SERVER_ERROR() });
  }
}
