import { z } from "zod"

export const saveIndirectResellerValidator = z.object({
  partnerId: z.string().min(1, "Partner Id cannot be null or empty."),
  storeId: z.string().min(1, "Store Id cannot be null or empty."),
  vendor: z.string().min(1, "Vendor cannot be null or empty."),
  irName: z.string().min(1, "Reseller Name cannot be null or empty."),
  irId: z
    .string()
    .min(1, "Reseller Id cannot be null or empty.")
    .length(36, "Reseller Id must be of 36 alpha-numeric characters long."),
  mpnId: z.string().min(1, "MPN Id cannot be null or empty."),
  location: z.string().min(1, "Location cannot be null or empty."),
})

export type SaveIndirectResellerDTO = z.infer<typeof saveIndirectResellerValidator>
