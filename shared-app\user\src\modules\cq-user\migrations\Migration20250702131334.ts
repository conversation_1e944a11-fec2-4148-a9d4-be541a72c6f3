import { Migration } from '@mikro-orm/migrations';

export class Migration20250702131334 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table if exists "cq_user_role_page" drop constraint if exists "cq_user_role_page_user_id_foreign";`);

    this.addSql(`alter table if exists "cq_user_role_page" add constraint "cq_user_role_page_rolepage_id_foreign" foreign key ("rolepage_id") references "cq_role_page" ("id") on update cascade;`);
    this.addSql(`alter table if exists "cq_user_role_page" add constraint "cq_user_role_page_user_id_foreign" foreign key ("user_id") references "cq_user" ("id") on update cascade;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table if exists "cq_user_role_page" drop constraint if exists "cq_user_role_page_rolepage_id_foreign";`);
    this.addSql(`alter table if exists "cq_user_role_page" drop constraint if exists "cq_user_role_page_user_id_foreign";`);

    this.addSql(`alter table if exists "cq_user_role_page" add constraint "cq_user_role_page_user_id_foreign" foreign key ("user_id") references "cq_page" ("id") on update cascade;`);
  }

}
