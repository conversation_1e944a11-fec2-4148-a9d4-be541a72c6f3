import swaggerJSDoc from 'swagger-jsdoc';
import yaml from 'js-yaml';
import fs from 'fs';
import path from 'path';

const projectRoot = path.resolve(__dirname, '../../../../');
const apiPath = path.join(projectRoot, 'shared-app/user/src/api');

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Medusa User API',
      version: '1.0.0',
      description: 'User API to manage user details.',
    },
    servers: [
      {
        url: 'http://localhost:8080',
        description: 'Local development server',
      },
    ],
  },
  apis: [path.join(apiPath, '**/*.ts'), path.join(apiPath, 'v1/user/**/*.ts')],
};

const swaggerSpec = swaggerJSDoc(options);
const yamlStr = yaml.dump(swaggerSpec);

const outputPath = path.join(__dirname, '../docs/swagger.yml');
fs.mkdirSync(path.dirname(outputPath), { recursive: true });
fs.writeFileSync(outputPath, yamlStr, 'utf8');

console.log(`Generated Swagger YAML at: ${outputPath}`);
