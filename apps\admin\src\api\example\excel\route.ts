import { MedusaRequest, MedusaResponse } from '@medusajs/framework';
import { generateExcelBuffer, ExcelColumn, ExportedRequest } from '@org/excel';

const originalData = [
  {
    id: '1',
    company_info: {
      id: 'CMY10001',
      country: 'India',
      'pan no': '**********',
      'company name': 'tech logic',
    },
    business_info: {
      id: 'BUS10001',
      'director name': 'Apor<PERSON>',
      'director phone number': '6598569523',
      'sales person name': 'joy',
      'sales person phone number': '8569632515',
    },
    created_at: '2025-06-11T05:23:29.140Z',
    updated_at: '2025-06-11T05:23:29.140Z',
    deleted_at: null,
    user_id: '1',
    requested_by: '1',
    user: {
      id: '1',
      email: '<EMAIL>',
      first_name: 'ap<PERSON>a',
      last_name: 'R',
    },
    requested_by_user: {
      id: '3',
      email: '<EMAIL>',
      first_name: '<PERSON><PERSON><PERSON>',
      last_name: '<PERSON>',
    },
  },
  {
    id: '2',
    company_info: {
      id: 'CMY10002',
      country: 'USA',
      'pan no': '**********',
      'company name': 'Initech',
    },
    business_info: {
      id: 'BUS10002',
      'director name': 'Peter',
      'director phone number': '1234567890',
      'sales person name': 'Milton',
      'sales person phone number': '0987654321',
    },
    created_at: '2025-06-12T10:00:00.000Z',
    updated_at: '2025-06-12T10:00:00.000Z',
    deleted_at: null,
    user_id: '2',
    requested_by: '2',
    user: {
      id: '2',
      email: '<EMAIL>',
      first_name: 'Peter',
      last_name: 'Gibbons',
    },
    requested_by_user: {
      id: '3',
      email: '<EMAIL>',
      first_name: 'Lumbergh',
      last_name: 'Bill',
    },
  },
];

const columns: ExcelColumn<ExportedRequest>[] = [
  { header: 'ID', key: 'id', width: 10 },
  { header: 'Company ID', key: 'companyId', width: 15 },
  { header: 'Country', key: 'country', width: 12 },
  { header: 'PAN No', key: 'panNo', width: 15 },
  { header: 'Company Name', key: 'companyName', width: 25 },
  { header: 'Business ID', key: 'businessId', width: 15 },
  { header: 'Director Name', key: 'directorName', width: 20 },
  { header: 'Director Phone Number', key: 'directorPhone', width: 25 },
  { header: 'Salesperson Name', key: 'salesName', width: 20 },
  { header: 'Salesperson Phone Number', key: 'salesPhone', width: 25 },
  { header: 'User Email', key: 'userEmail', width: 25 },
  { header: 'User First Name', key: 'userFirstName', width: 15 },
  { header: 'User Last Name', key: 'userLastName', width: 15 },
];

export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  const transformedData = originalData.map((entry) => ({
    id: entry.id,
    companyId: entry.company_info?.id || '',
    country: entry.company_info?.country || '',
    panNo: entry.company_info?.['pan no'] || '',
    companyName: entry.company_info?.['company name'] || '',

    businessId: entry.business_info?.id || '',
    directorName: entry.business_info?.['director name'] || '',
    directorPhone: entry.business_info?.['director phone number'] || '',
    salesName: entry.business_info?.['sales person name'] || '',
    salesPhone: entry.business_info?.['sales person phone number'] || '',

    userEmail: entry.user?.email || '',
    userFirstName: entry.user?.first_name || '',
    userLastName: entry.user?.last_name || '',
  }));

  try {
    const buffer = await generateExcelBuffer(
      transformedData,
      columns,
      'Partner Registration'
    );
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    );
    res.setHeader(
      'Content-Disposition',
      'attachment; filename="registration.xlsx"'
    );
    res.send(buffer);
  } catch (error) {
    console.error('Error fetching partner registrations:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to process excel',
    });
  }
}
