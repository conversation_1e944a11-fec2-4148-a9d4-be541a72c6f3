version: '3.8'

services:
  admin:
    build:
      context: .
      dockerfile: ./apps/admin/Dockerfile
    ports:
      - "3000:3000"
    environment:
      DATABASE_URL: "postgres://postgres:<EMAIL>:5432/medusa-my-medusa-store"
    command: ["npx", "nx", "serve", "admin"]

  authentication:
    build:
      context: .
      dockerfile: ./shared-app/auth/Dockerfile
    ports:
      - "9000:9000"
    environment:
      DATABASE_URL: "postgres://postgres:<EMAIL>:5432/medusa-my-medusa-store"
    command: ["npx", "nx", "serve", "auth"]
