import ExcelJS from 'exceljs';

export interface ExcelColumn<T = any> {
  header: string;
  key: keyof T;
  width?: number;
}

export async function generateExcelBuffer<T>(
  data: T[],
  columns: ExcelColumn<T>[],
  sheetName = 'Sheet1'
): Promise<Buffer> {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet(sheetName);

  worksheet.columns = columns.map(col => ({
    header: col.header,
    key: col.key as string,
    width: col.width ?? 15
  }));

  data.forEach(item => worksheet.addRow(item));

  const buffer = await workbook.xlsx.writeBuffer();
  return Buffer.from(buffer);
}
