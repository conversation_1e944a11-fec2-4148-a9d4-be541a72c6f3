import { ExecArgs } from '@medusajs/framework/types';
import { COMPANY_REGISTRATION_MODULE } from '../modules/company';
import CompanyRegistrationService from '../modules/company/service';

export default async function seedAnswerType({ container }: ExecArgs) {
  const answerTypeService: CompanyRegistrationService = container.resolve(
    COMPANY_REGISTRATION_MODULE
  );

  const targetAnswerTypes = [
    'single choice',
    'multiple choice',
    'single choice drop down',
    'multiple choice dropdown',
    'input',
  ];

  const existingAnswerTypes = await answerTypeService.listAnswerTypes({});
  const existingAnswerTypeSet = new Set(
    existingAnswerTypes.map((answerType: any) => answerType.type.toLowerCase())
  );

  const answerTypesToCreate = targetAnswerTypes
    .filter((type) => !existingAnswerTypeSet.has(type.toLowerCase()))
    .map((type) => ({ type }));

  if (answerTypesToCreate.length > 0) {
    await Promise.all(
      answerTypesToCreate.map((answerType) =>
        answerTypeService.createAnswerTypes(answerType)
      )
    );
  }

  console.log(`Seeded ${answerTypesToCreate.length} answer type(s).`);
}
