import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import { 
  GetNCEProductUpgradesResponse,
  GetNCEProductUpgradesMicrosoftResponse,
  EligibleProduct,
  ResponseError
} from "../../types/responses/customResponse";
import { validateGetNCEProductUpgradesRequestDAL } from "./validateNCEProductUpgradesDAL";
import { getNCEProductUpgradesFromMicrosoftService } from "./getNCEProductUpgradesFromMicrosoftService";
import { getValidNCEProductsWithTermAndBillFrequencyDAL } from "./getValidNCEProductsDAL";

/**
 * Validate Get NCE Product Upgrades Request
 * Matches .NET ValidateGetNCEProductUpgradesRequest method
 */
async function validateGetNCEProductUpgradesRequest(
  customerId: string,
  subscriptionId: string,
  storeId: string
) {
  logger.info(`Entered into BL ValidateGetNCEProductUpgrades method Going to Hit DAL with customerId-${customerId}, subscriptionId-${subscriptionId}, storeId-${storeId}`);

  const response = await validateGetNCEProductUpgradesRequestDAL(customerId, subscriptionId, storeId);

  logger.info(`Response from DAL ValidateGetNCEProductUpgradesRequest, and returning from BL with response ${JSON.stringify(response)}`);

  return response;
}

/**
 * Get Valid NCE Products With Term And Bill Frequency
 * Matches .NET GetValidNCEProductsWithTermAndBillFrequency method
 */
async function getValidNCEProductsWithTermAndBillFrequency(
  eligibleProducts: EligibleProduct[],
  storeId: string
) {
  logger.info(`Entered into BL GetValidNCEProductsWithTermAndBillFrequency with catalogItemIds-${JSON.stringify(eligibleProducts)},  storeId-${storeId}`);
  logger.info(`Going to Hit DAL withwith catalogItemIds-${JSON.stringify(eligibleProducts)}, storeId-${storeId}`);

  const response = await getValidNCEProductsWithTermAndBillFrequencyDAL(eligibleProducts, storeId);

  logger.info(`Response from DAL GetValidNCEProductsWithTermAndBillFrequency, and returning from BL with response ${JSON.stringify(response)}`);

  return response;
}

/**
 * Get NCE Product Upgrades
 * Matches .NET TransitionService.GetNCEProductUpgrades method
 */
export async function getNCEProductUpgradesService(
  req: MedusaRequest,
  customerId: string,
  subscriptionId: string,
  storeId: string,
  eligibilityType: string
): Promise<GetNCEProductUpgradesResponse> {
  const getNCEProductUpgradesResponse: GetNCEProductUpgradesResponse = {
    Status: false,
    Message: "",
    Items: []
  };

  try {
    // Validate GetNCEProductUpgrades Request (matching .NET validation)
    logger.info(`Entered into BL GetNCEProductUpgrades method and Entering ValidateGetNCEProductUpgradesRequest method with customerId-${customerId}, subscriptionId-${subscriptionId}, storeId-${storeId},eligibilityType-${eligibilityType}`);
    const validationResponse = await validateGetNCEProductUpgradesRequest(customerId, subscriptionId, storeId);
    logger.info(`Response from BL ValidateGetNCEProductUpgradesRequest method with response ${JSON.stringify(validationResponse)}`);

    // Check validation response (matching .NET validation check)
    logger.info(`Checking If validationResponse is not success -${validationResponse?.IsSuccess}`);
    if (!validationResponse?.IsSuccess) {
      logger.info(`Entered validationResponse is not success with validationResponse.ErrorMessage-${validationResponse?.ErrorMessage}`);
      getNCEProductUpgradesResponse.Status = false;
      getNCEProductUpgradesResponse.Message = validationResponse?.ErrorMessage || "Validation failed";
      logger.info(`Exiting If Any validationMessages and Setting Response and Returning from BL GetNCEProductUpgrades method with response-${JSON.stringify(getNCEProductUpgradesResponse)}`);
      return getNCEProductUpgradesResponse;
    }

    // Get NCE Product Upgrades from Microsoft (matching .NET Microsoft API call)
    logger.info(`Entering GetNCEProductUpgradesFromMicrosoft Method with with customerId (EndCustomerTenantId)-${validationResponse.CustomerId}, subscriptionId-${subscriptionId}, storeId-${storeId}`);
    const response = await getNCEProductUpgradesFromMicrosoftService(
      req,
      validationResponse.CustomerId || customerId,
      subscriptionId,
      storeId,
      eligibilityType
    );
    logger.info(`Response from GetNCEProductUpgradesFromMicrosoft Method ${JSON.stringify(response)}`);

    // Process Microsoft response (matching .NET response processing)
    logger.info(`Entering if of response.IsSuccessStatusCode`);
    if (response.isSuccessStatusCode) {
      // Parse Microsoft response (matching .NET: JsonConvert.DeserializeObject<GetNCEProductUpgradesMicrosoftResponse>)
      logger.info(`Deserialising response.Content-${response.content} to GetNCEProductUpgradesMicrosoftResponse`);
      const microsoftResponse: GetNCEProductUpgradesMicrosoftResponse = JSON.parse(response.content);
      logger.info(`Deserialising completed - ${JSON.stringify(microsoftResponse)}`);

      logger.info(`List of CatalogItemIds From Microsoft Count-${microsoftResponse.Items?.length || 0}, Items-${microsoftResponse.Items?.map(p => p.CatalogItemId) || []}`);

      // Filter eligible products (matching .NET LINQ filtering)
      logger.info(`Getting Eligible list of CatalogItemIds From microsoftResponse`);
      const eligibleCatalogItemIdsFromMicrosoft: EligibleProduct[] = (microsoftResponse.Items || [])
        .filter(p => p.Eligibilities?.some(e => e.TransitionType === "transition_only" && e.IsEligible === true))
        .map(p => ({
          MaterialId: p.CatalogItemId?.substring(0, p.CatalogItemId.lastIndexOf(":")),
          Description: p.Title,
          CatalogItemId: p.CatalogItemId
        }));
      
      logger.info(`List of Eligible CatalogItemIds From Microsoft Count-${eligibleCatalogItemIdsFromMicrosoft.length}, Items-${JSON.stringify(eligibleCatalogItemIdsFromMicrosoft)}`);

      // Check if any eligible products found (matching .NET eligibility check)
      logger.info(`Checking If Not Any eligibleCatalogItemIdsFromMicrosoft Count-${eligibleCatalogItemIdsFromMicrosoft.length}`);
      if (eligibleCatalogItemIdsFromMicrosoft.length === 0) {
        logger.info(`Entered If Not Any eligibleCatalogItemIdsFromMicrosoft`);
        getNCEProductUpgradesResponse.Status = false;
        getNCEProductUpgradesResponse.Message = "No Products Given By Microsoft are Eligible for Upgrade.";
        logger.info(`Exiting If Not Any eligibleCatalogItemIdsFromMicrosoft and Setting Response and Returning from BL GetNCEProductUpgrades method with response-${JSON.stringify(getNCEProductUpgradesResponse)}`);
        return getNCEProductUpgradesResponse;
      }

      // Get valid NCE products from database (matching .NET database call)
      logger.info(`Entering GetValidNCEProductsWithTermAndBillFrequency Method with CatalogItemIds- ${JSON.stringify(eligibleCatalogItemIdsFromMicrosoft)}`);
      let validNCEProductsFromDB = await getValidNCEProductsWithTermAndBillFrequency(eligibleCatalogItemIdsFromMicrosoft, storeId);
      logger.info(`Response from GetValidNCEProductsWithTermAndBillFrequency Method ${JSON.stringify(validNCEProductsFromDB)}`);

      logger.info(`List of CatalogItemIds From DB Count-${validNCEProductsFromDB.length}, Items-${validNCEProductsFromDB.map(p => p.CatalogItemId)}`);

      logger.info(`Checking If Not Any validNCEProductsFromDB Count-${validNCEProductsFromDB.length}`);

      // Filter for immediate eligibility type (matching .NET filtering logic)
      if (eligibilityType.toLowerCase() === "immediate") {
        validNCEProductsFromDB = validNCEProductsFromDB.filter(item => 
          (item.Terms && item.Terms.length > 0) && (item.BillTypes && item.BillTypes.length > 0)
        );
      }

      // Check if any valid products found after filtering
      if (validNCEProductsFromDB.length === 0) {
        logger.info(`Entered If Not Any validNCEProductsFromDB`);
        getNCEProductUpgradesResponse.Status = false;
        getNCEProductUpgradesResponse.Message = "Product Given By Microsoft Not avialable in System.";
        logger.info(`Exiting If Not Any validNCEProductsFromDB and Setting Response and Returning from BL GetNCEProductUpgrades method with response-${JSON.stringify(getNCEProductUpgradesResponse)}`);
        return getNCEProductUpgradesResponse;
      }

      // Set successful response (matching .NET success response)
      getNCEProductUpgradesResponse.Items = validNCEProductsFromDB;
      getNCEProductUpgradesResponse.Status = true;
      logger.info(`Exiting if of response.IsSuccessStatusCode and Setting Response as Response.Items-${JSON.stringify(validNCEProductsFromDB)}, Response.Status-${getNCEProductUpgradesResponse.Status}`);

    } else {
      // Handle error response (matching .NET error handling)
      logger.info(`Entered else of response.IsSuccessStatusCode is not true with response-${JSON.stringify(response)}`);

      try {
        logger.info(`Deserialising response.Content-${response.content} to ResponseError`);
        const responseError: ResponseError = JSON.parse(response.content);
        logger.info(`Deserialising completed - ${JSON.stringify(responseError)}`);

        getNCEProductUpgradesResponse.Status = false;
        logger.info(`Setting Response.Message: with responseError.Description- ${responseError?.description} if not null, else httpresponse.ErrorMessage-${response.errorMessage}`);
        getNCEProductUpgradesResponse.Message = responseError?.description || response.errorMessage || "Error occurred while fetching from Microsoft";
      } catch (parseError) {
        // If parsing fails, use generic error message
        getNCEProductUpgradesResponse.Status = false;
        getNCEProductUpgradesResponse.Message = response.errorMessage || "Error occurred while fetching from Microsoft";
      }
      
      logger.info(`Exiting else of response.IsSuccessStatusCode is not true with response-${JSON.stringify(getNCEProductUpgradesResponse)}`);
    }

    logger.info(`Exiting BL GetNCEProductUpgrades method and returning response-${JSON.stringify(getNCEProductUpgradesResponse)}`);
    return getNCEProductUpgradesResponse;

  } catch (error: any) {
    logger.error(`Error in getNCEProductUpgradesService: ${error.message}`);
    getNCEProductUpgradesResponse.Status = false;
    getNCEProductUpgradesResponse.Message = error.message || "Internal server error";
    return getNCEProductUpgradesResponse;
  }
}
