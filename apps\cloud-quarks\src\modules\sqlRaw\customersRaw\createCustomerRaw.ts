import { masterDB } from "../../../utils/db";
import logger from "../../../utils/logger";
import sql from "mssql"
import { getSqlServerConnection } from "../../../utils/sqlServerClient"

export async function createCustomerStoredProc(
  action: string,
  customer: any
): Promise<{ Status: string; Message: string }> {
  logger.info(
    `Calling stored proc for customer create with payload: ${JSON.stringify(customer)}`
  )

  const pool = await getSqlServerConnection()
  const request = pool.request()

  // Set input parameters
  request.input("CustomerId", sql.<PERSON>ar<PERSON>har(50), customer.CustId)
  request.input("OrgName", sql.NVarChar(100), customer.OrgName)
  request.input("FirstName", sql.NVarChar(100), customer.FirstName)
  request.input("LastName", sql.NVarChar(100), customer.LastName ?? null)
  request.input("EmailId", sql.<PERSON><PERSON><PERSON><PERSON><PERSON>(100), customer.EmailId ?? null)
  request.input("CountryId", sql.Var<PERSON><PERSON>(10), customer.CountryId ?? null)
  request.input("AddressLine1", sql.NVarChar(100), customer.AddressLine1)
  request.input("AddressLine2", sql.NVarChar(100), customer.AddressLine2)
  request.input("UserName", sql.VarChar(50), customer.UserName)
  request.input("StoreId", sql.VarChar(50), customer.StoreId ?? null)
  request.input("PartnerId", sql.VarChar(50), customer.PartnerId ?? null)
  request.input("MiddleName", sql.NVarChar(100), customer.MiddleName ?? null)
  request.input("OrgVatRegNumber", sql.VarChar(100), customer.OrganizationRegistrationNumber ?? null)
  request.input("VerticalName", sql.VarChar(100), customer.CustomerVertical ?? null)
  request.input("SegmentName", sql.VarChar(100), customer.Segment ?? null)
  request.input("SectorName", sql.VarChar(100), customer.Sector ?? null)
  request.input("PanNumber", sql.VarChar(100), customer.PanNumber ?? null)
  request.input("City", sql.VarChar(200), customer.City ?? null)

  // Output parameters
  request.output("RetVal", sql.VarChar(200))
  request.output("Message", sql.VarChar(200))

  try {
    await request.execute("Usp_Ups_Customer")
    const status = request.parameters.RetVal.value
    const message = request.parameters.Message.value

    logger.info(`Stored Proc Result - Status: ${status}, Message: ${message}`)

    return { Status: status, Message: message }
  } catch (error) {
    logger.error("Error executing stored procedure Usp_Ups_Customer:", error)
    throw error
  }
}


// export async function createCustomerRaw(
//   action: string,
//   customer: any
// ): Promise<{ Status: string; Message: string }> {
//   logger.info(
//     `Entered into createCustomerRaw with model: ${JSON.stringify(customer)}`
//   );

//   const {
//     CustId,
//     OrgName,
//     FirstName,
//     LastName,
//     EmailId,
//     CountryId,
//     AddressLine1,
//     AddressLine2,
//     UserName,
//     StoreId,
//     PartnerId,
//     MiddleName,
//     OrganizationRegistrationNumber,
//     CustomerVertical,
//     Segment,
//     Sector,
//     PanNumber,
//     City,
//   } = customer;

//   // Resolve StoreKey
//   const storeRes = await masterDB.query(
//     `SELECT "StoreKey" FROM "tblMstStore" WHERE "StoreId" = $1`,
//     [StoreId]
//   );
//   const StoreKey = storeRes.rows?.[0]?.StoreKey;
//   if (!StoreKey) return { Status: "FAIL", Message: "Invalid Store" };

//   // Resolve PartnerKey
//   const partnerRes = await masterDB.query(
//     `SELECT "PartnerKey" FROM "tblMstPartner" WHERE "PartnerId" = $1`,
//     [PartnerId]
//   );
//   const PartnerKey = partnerRes.rows?.[0]?.PartnerKey;
//   if (!PartnerKey)
//     return { Status: "FAIL", Message: "Partner does not exist in back end" };

//   // Resolve VerticalId
//   let VerticalId: number | null = null;
//   if (CustomerVertical) {
//     const verticalRes = await masterDB.query(
//       `SELECT "VerticalId" FROM "tblMstCustomerVertical" WHERE REPLACE("VerticalName", ' ', '') = REPLACE($1, ' ', '')`,
//       [CustomerVertical]
//     );
//     VerticalId = verticalRes.rows?.[0]?.VerticalId ?? null;
//   }

//   // Resolve SegmentId
//   let SegmentId: number | null = null;
//   if (Segment) {
//     const segmentRes = await masterDB.query(
//       `SELECT "SegmentId" FROM "tblMstCustomerSegment" WHERE REPLACE("SegmentName", ' ', '') = REPLACE($1, ' ', '')`,
//       [Segment]
//     );
//     SegmentId = segmentRes.rows?.[0]?.SegmentId ?? null;
//   }

//   // Resolve SectorId
//   let SectorId: number | null = null;
//   if (Sector) {
//     const sectorRes = await masterDB.query(
//       `SELECT "SectorId" FROM "tblMstCustomerSector" WHERE REPLACE("SectorName", ' ', '') = REPLACE($1, ' ', '')`,
//       [Sector]
//     );
//     SectorId = sectorRes.rows?.[0]?.SectorId ?? null;
//   }

//   // Generate CustKey manually (you can replace with sequence if supported)
//   const custKeyRes = await masterDB.query(
//     `SELECT COALESCE(MAX("CustKey"), 0) + 1 AS "CustKey" FROM "tblMstEndCustomer"`
//   );
//   const CustKey = custKeyRes.rows?.[0]?.CustKey;

//   // Insert into tblMstEndCustomer
//   await masterDB.query(
//     `INSERT INTO "tblMstEndCustomer" (
//   "CustKey", "CustId", "CustName", "FirstName", "LastName", "EmailId", "CountryId",
//   "City", "AddressLine1", "AddressLine2", "CreatedBy", "MiddleName",
//   "OrgVatRegistrationNumber", "VerticalId", "SegmentId", "SectorId", "PANNumber", "CreatedDate"
// )
// VALUES (
//   $1, $2, $3, $4, $5, $6, $7,
//   $8, $9, $10, $11, $12,
//   $13, $14, $15, $16, $17, NOW()
// )`,
//     [
//       CustKey,
//       CustId, // CustId
//       OrgName, // CustName
//       FirstName,
//       LastName,
//       EmailId,
//       CountryId,
//       City,
//       AddressLine1,
//       AddressLine2,
//       UserName, // CreatedBy
//       MiddleName,
//       OrganizationRegistrationNumber, // OrgVatRegistrationNumber
//       VerticalId,
//       SegmentId,
//       SectorId,
//       PanNumber,
//     ]
//   );

//   // Resolve BrandKey(s) from tblLnkBrandStore
//   const brandRes = await masterDB.query(
//     `SELECT DISTINCT "BrandKey", 
//       CASE WHEN "ProvisionType" = 'AUTO' THEN '0' ELSE '1' END AS "Status"
//      FROM "tblLnkBrandStore"
//      WHERE "StoreKey" = $1 AND COALESCE("CredentialType", '') = ''`,
//     [StoreKey]
//   );

//   for (const brand of brandRes.rows) {
//     const BrandKey = brand.BrandKey;
//     const Status = brand.Status;

//     // Insert into tblLnkEndCustomerPartnerBrand (avoid duplicate)
//     await masterDB.query(
//       `INSERT INTO "tblLnkEndCustomerPartnerBrand" (
//         "CustKey", "PartnerKey", "BrandKey", "Status", "CreatedDate"
//       )
//       SELECT $1, $2, $3, $4, NOW()
//       WHERE NOT EXISTS (
//         SELECT 1 FROM "tblLnkEndCustomerPartnerBrand"
//         WHERE "CustKey" = $1 AND "PartnerKey" = $2 AND "BrandKey" = $3
//       )`,
//       [CustKey, PartnerKey, BrandKey, Status]
//     );
//   }

//   return { Status: "PASS", Message: CustKey.toString() };
// }
