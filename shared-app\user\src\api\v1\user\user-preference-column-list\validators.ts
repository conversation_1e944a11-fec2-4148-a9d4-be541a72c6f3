import { z } from 'zod';

export const userPreferenceListSchema = z.object({
  table_master_id: z
    .string()
    .trim()
    .max(100, { message: 'Table master ID must be at most 100 characters' })
    .min(1, { message: 'Table master ID is required' }),
});

export const userPreferenceSchema = z.object({
  table_master_id: z
    .string()
    .trim()
    .min(1, { message: 'Table master ID is required' })
    .max(100, { message: 'Table master ID must be at most 100 characters' }),

  column_details: z
    .array(
      z.object({
        column_master_id: z
          .string()
          .trim()
          .min(1, { message: 'Column master ID is required' })
          .max(100, {
            message: 'Column master ID must be at most 100 characters',
          }),
        sort_order: z
          .number()
          .int({ message: 'Sort order must be an integer' })
          .optional(),
      })
    )
    .min(1, { message: 'At least one column detail is required' }),
});
