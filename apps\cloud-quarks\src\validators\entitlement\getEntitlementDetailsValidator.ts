import { z } from "zod";

// Entitlement Request validation schema (matching .NET EntitlementRequest)
export const EntitlementRequestSchema = z.object({
  orderLineKey: z.number().int().positive("OrderLineKey must be a positive integer"),
});

// Type definitions for TypeScript
export type EntitlementRequestType = z.infer<typeof EntitlementRequestSchema>;

// Validation function for request body
export function validateEntitlementRequest(orderLineKey: any): string[] {
  const errors: string[] = [];
  
  if (orderLineKey === undefined || orderLineKey === null) {
    errors.push("The 'orderLineKey' field is required in the request body.");
    return errors;
  }
  
  if (typeof orderLineKey !== 'number' || !Number.isInteger(orderLineKey)) {
    errors.push("The 'orderLineKey' field must be an integer.");
    return errors;
  }
  
  if (orderLineKey <= 0) {
    errors.push("The 'orderLineKey' field must be a positive integer.");
  }
  
  return errors;
}
