import { z } from "zod";
import logger from "../../utils/logger";

// Zod schema for GetSubscriptionsByCustomerIdAndMaterialId query parameters
export const GetSubscriptionsByCustomerIdAndMaterialIdQuerySchema = z.object({
  customerId: z.string().min(1, "CustomerId cannot be blank or null"),
  materialId: z.string().min(1, "MaterialId cannot be blank or null"),
});

export type GetSubscriptionsByCustomerIdAndMaterialIdQueryType = z.infer<typeof GetSubscriptionsByCustomerIdAndMaterialIdQuerySchema>;

/**
 * Validate GetSubscriptionsByCustomerIdAndMaterialId request parameters
 * Matches .NET ValidationGetSubscriptionsByCustomerIdAndMaterialIdRequest method
 */
export function validateGetSubscriptionsByCustomerIdAndMaterialIdRequest(queryParams: any): string[] {
  logger.info(`Entered into ValidationGetSubscriptionsByCustomerIdAndMaterialIdRequest method with customerId-${queryParams.customerId}, materialId-${queryParams.materialId}`);
  
  const validationErrors: string[] = [];

  // Validate customerId (matching .NET validation logic)
  logger.info(`Validating query parameter customerId-${queryParams.customerId}`);
  if (!queryParams.customerId || queryParams.customerId.trim() === '') {
    logger.info(`Received validation error for query parameter customerId -${queryParams.customerId}, customerId can't be null or empty`);
    validationErrors.push("CustomerId can't be null or empty.");
  }
  logger.info(`Validated query parameter customerId`);

  // Validate materialId (matching .NET validation logic)
  logger.info(`Validating query parameter materialId-${queryParams.materialId}`);
  if (!queryParams.materialId || queryParams.materialId.trim() === '') {
    logger.info(`Received validation error for query parameter materialId -${queryParams.materialId}, materialId can't be null or empty`);
    validationErrors.push("MaterialId can't be null or empty.");
  }
  logger.info(`Validated query parameter materialId`);

  // Also run Zod validation for additional checks
  try {
    GetSubscriptionsByCustomerIdAndMaterialIdQuerySchema.parse(queryParams);
    logger.info(`Zod validation passed for GetSubscriptionsByCustomerIdAndMaterialId request`);
  } catch (error: any) {
    if (error.errors) {
      error.errors.forEach((err: any) => {
        const fieldName = err.path.join('.');
        // Only add if not already added by custom validation
        const errorMessage = `${fieldName}: ${err.message}`;
        if (!validationErrors.some(existing => existing.includes(fieldName))) {
          validationErrors.push(errorMessage);
        }
      });
    }
    logger.error(`Zod validation failed: ${JSON.stringify(error.errors)}`);
  }

  logger.info(`Going to return Validation error list ${JSON.stringify(validationErrors)}`);
  return validationErrors;
}
