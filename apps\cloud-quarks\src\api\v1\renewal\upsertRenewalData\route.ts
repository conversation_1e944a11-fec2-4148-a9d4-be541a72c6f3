import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import {
  appConfig,
  comparableString,
  ConstantValue,
  mapRenewalDtoToModel,
  Message,
  RenewalDto,
  RenewalDtoSchema,
  StatusResponse,
} from "../../../../validators/renewal/customRenewalValidator";
import { getSubscriptionDetailById } from "../../../../services/linkedAccount/getSubscriptionService";
import { upsertRenewalDataService } from "../../../../services/renewal/upsertRenewalDataService";

/**
 * @openapi
 * /v1/renewal/upsertRenewalData:
 *   post:
 *     summary: Upsert (create or update) renewal data
 *     description: This API validates and processes renewal data for subscriptions based on brand and subscription details. It handles business validations for legacy and modified subscriptions.
 *     tags:
 *       - Renewal
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - BrandId
 *               - AutoRenewStatus
 *               - MaterialNo
 *               - PlanType
 *               - TermDuration
 *               - PromotionId
 *               - Quantity
 *               - SubscriptionId
 *             properties:
 *               BrandId:
 *                 type: string
 *               AutoRenewStatus:
 *                 type: boolean
 *               MaterialNo:
 *                 type: string
 *               PlanType:
 *                 type: string
 *               TermDuration:
 *                 type: string
 *               PromotionId:
 *                 type: string
 *               Quantity:
 *                 type: integer
 *               CustomTermEndDate:
 *                 type: string
 *                 format: date-time
 *               SubscriptionId:
 *                 type: string
 *               DiscountPercentage:
 *                 type: number
 *               PromotionRate:
 *                 type: number
 *               IsOldSubscriptionModified:
 *                 type: boolean
 *               LegacyBrandId:
 *                 type: string
 *               NCEBrandId:
 *                 type: string
 *               SSBrandId:
 *                 type: string
 *               TaxPercentage:
 *                 type: number
 *               CustomTermType:
 *                 type: string
 *               CustomTermPrice:
 *                 type: number
 *               FirstScheduleEndDate:
 *                 type: string
 *                 format: date-time
 *               QuantityType:
 *                 type: string
 *               GoogleBrandIds:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: Renewal data processed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                 Message:
 *                   type: string
 *       400:
 *         description: Validation failed or business rule violation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                 Message:
 *                   type: string
 *       404:
 *         description: Subscription not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                 Message:
 *                   type: string
 *       500:
 *         description: Internal server error
 */

export const upsertRenewalDataController = async (
  req: MedusaRequest,
  res: MedusaResponse
) => {
  const correlationId = getCorrelationId(req);
  logger.info(`Entered into UpsertRenewalData API`, { correlationId });

  const result: StatusResponse = {
    Status: "",
    Message: "",
  };

  try {
    const body = req.body as RenewalDto;
    body.LegacyBrandId = appConfig.Brands.LegacyBrandId;
    body.NCEBrandId = appConfig.Brands.NCEBrandId;
    body.SSBrandId = appConfig.Brands.SSBrandId;
    body.GoogleBrandIds = appConfig.Brands.GoogleBrandIds;

    // Zod validation
    const validation = RenewalDtoSchema.safeParse(body);
    if (!validation.success) {
      const errors = validation.error.issues.map((e) => e.message).join(", ");
      logger.info(`Validation failed`, { correlationId, errors });
      result.Status = ConstantValue.FAIL;
      result.Message = errors;
      return res.status(400).json(result);
    }

    logger.info("Renewal DTO passed validation", { correlationId });

    if (body.AutoRenewStatus) {
      const subscriptionDetails = await getSubscriptionDetailById(
        body.SubscriptionId
      );

      logger.info(
        `Subscription details are ${JSON.stringify(subscriptionDetails)}`,
        { correlationId }
      );

      if (!subscriptionDetails) {
        logger.info(`Subscription not found`, { correlationId });
        result.Status = ConstantValue.FAIL;
        result.Message = "Subscription not found.";
        return res.status(404).json(result);
      }

      const thresholdLimit = new Date(
        appConfig.AppSettings.ThresholdLimitForLegacyRenew
      );
      const subscriptionEndDate = new Date(subscriptionDetails.EndDate);

      if (
        body.BrandId === body.LegacyBrandId &&
        subscriptionEndDate.toDateString() >= thresholdLimit.toDateString()
      ) {
        logger.info(
          `Legacy Subscription with end date ${subscriptionEndDate.toDateString()}`,
          {
            correlationId,
          }
        );

        result.Status = ConstantValue.FAIL;
        result.Message = "Renewal for CSP legacy brand is not allowed.";
        return res.status(400).json(result);
      }

      const isSameData =
        body.IsOldSubscriptionModified === true &&
        comparableString(body.MaterialNo) ===
          comparableString(subscriptionDetails.MaterialId) &&
        comparableString(body.PlanType) ===
          comparableString(subscriptionDetails.PlanType) &&
        comparableString(body.TermDuration) ===
          comparableString(subscriptionDetails.Duration) &&
        body.Quantity === subscriptionDetails.Quantity;

      if (isSameData) {
        body.IsOldSubscriptionModified = false;
      }
    }
    logger.info("Proceeding to map Renewal DTO to model", {
      correlationId,
      IsOldSubscriptionModified: body.IsOldSubscriptionModified,
    });

    logger.info(
      `IsOldSubscriptionModified field is ${body.IsOldSubscriptionModified}`
    );
    logger.info(
      `Going to map renewal dto to model. renewalDto: ${JSON.stringify(body)}`
    );

    const renewalModel = mapRenewalDtoToModel(body);

    logger.info(
      `Renewal model after mapping: ${JSON.stringify(renewalModel)}`,
      { correlationId }
    );

    logger.info(`Calling service to upsert renewal data`, {
      correlationId,
      subscriptionId: body.SubscriptionId,
      renewalModel,
    });

    const serviceResult: StatusResponse = await upsertRenewalDataService(
      renewalModel
    );

    logger.info(
      `Result from service full process: ${JSON.stringify(serviceResult)}`,
      { correlationId }
    );

    const statusComparable = comparableString(serviceResult.Status);
    logger.info(`Result's comparable string status: ${statusComparable}`, {
      correlationId,
    });

    return res.status(200).json(serviceResult);
  } catch (error) {
    logger.error("Unexpected error in UpsertRenewalData", {
      correlationId,
      error,
    });

    const result: StatusResponse = {
      Status: ConstantValue.FAIL,
      Message: Message.TRANSIENT_ERROR,
    };

    return res.status(500).json(result);
  }
};
