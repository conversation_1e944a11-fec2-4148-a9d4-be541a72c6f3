import { ExecArgs } from '@medusajs/framework/types';
import { CQUser_MODULE } from '../modules/cq-user';
import CQUserService from '../modules/cq-user/service';

export default async function seedModule({ container }: ExecArgs) {
  const guestUserService: CQUserService = container.resolve(CQUser_MODULE);

  const moduleMapping = {
    Partner: [
      { module: 'Dashboard', order_id: 1 },
      { module: 'Accounts', order_id: 2 },
      { module: 'Credit Management', order_id: 3 },
      { module: 'Brand Linking', order_id: 4 },
      { module: 'Catalog & Pricing', order_id: 5 },
      { module: 'Sales', order_id: 6 },
      { module: 'Store', order_id: 7 },
      { module: 'User & Roles', order_id: 8 },
      { module: 'Notifications', order_id: 9 },
      { module: 'System & Settings', order_id: 10 },
      { module: 'Feedback', order_id: 11 },
      { module: 'Leads', order_id: 12 },
      { module: 'Alerts & Events', order_id: 13 },
      { module: 'Help Center', order_id: 14 },
      { module: 'Reports', order_id: 15 },
      { module: 'Insights', order_id: 16 },
      { module: 'User Interface', order_id: 16 },
    ],
    Vendor: [],
    'Direct End Customer': [],
    'Redington Team': [{ module: 'Test Redington Module', order_id: 1 }],
  };

  const personaList = await guestUserService.listCqPersonas({});

  const personaMap = new Map(
    personaList.map((p) => [p.persona.toLowerCase(), p.id])
  );

  const modulesToCreate = [];

  for (const [personaName, modules] of Object.entries(moduleMapping)) {
    const personaId = personaMap.get(personaName.toLowerCase());
    if (!personaId) {
      console.warn(`Persona not found: ${personaName}`);
      continue;
    }

    for (const mod of modules) {
      modulesToCreate.push({
        ...mod,
        persona_id: personaId,
        metadata: {},
      });
    }
  }

  const existingModules = (await guestUserService.listCqModules?.({})) || [];

  const existingKeys = new Set(
    existingModules.map((m) => `${m.module.toLowerCase()}-${m.persona_id}`)
  );

  const newModules = modulesToCreate.filter((m) => {
    const key = `${m.module.toLowerCase()}-${m.persona_id}`;
    return !existingKeys.has(key);
  });

  if (newModules.length > 0) {
    await Promise.all(
      newModules.map((mod) => guestUserService.createCqModules(mod))
    );
  }
  console.log(`Seeded ${newModules.length} module(s).`);
}
