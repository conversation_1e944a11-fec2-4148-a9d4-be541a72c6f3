import { getSqlServerConnection } from "../../utils/sqlServerClient";
import logger from "../../utils/logger";
import sql from "mssql";

/**
 * Check if customer ID is valid
 * Matches .NET CustomerService.IsCustIdValid and CustomerDAL.IsCustIdValid methods
 */
export async function isCustIdValid(custId: string): Promise<boolean> {
  logger.info(`Entered into Customer Service-IsCustIdValid method with custId-${custId} and Going to call DAL method`);
  return await isCustIdValidDAL(custId);
}

/**
 * DAL method to check if customer ID is valid
 * Matches .NET CustomerDAL.IsCustIdValid method
 */
export async function isCustIdValidDAL(custId: string): Promise<boolean> {
  logger.info(`Entered into DAL IsCustIdValid method,custId ${custId}`);

  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();

    const procedure = "spGetCustomerById"; // Matches .NET Procedures.spGetCustomerById
    
    request.input("CustId", sql.VarChar(50), custId);

    logger.info(`Going to hit DB with proc name : ${procedure} and parameters CustId: ${custId}`);

    const result = await request.execute(procedure);
    const exists = result.recordset && result.recordset.length > 0;

    logger.debug(`Data returned from ${procedure} proc is ${exists}`);
    return exists;

  } catch (error: any) {
    logger.error(`Error in isCustIdValidDAL: ${error.message}`);
    throw error;
  }
}
