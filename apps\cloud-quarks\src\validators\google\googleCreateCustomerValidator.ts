import { z } from "zod";
import { GoogleCreateCustomerDto } from "../../types/responses/customResponse";
import { validateStore } from "../common/storeValidator";
import { validatePartner } from "../common/partnerValidator";
import { validateCustomer } from "../common/customerValidator";

/**
 * Google Create Customer DTO Validation Schema
 * Matches .NET GoogleCreateCustomerValidator
 */
export const googleCreateCustomerDtoSchema = z.object({
  // Internal fields (set from route parameters)
  custId: z.string().optional(),
  partnerId: z.string().optional(),
  storeId: z.string().optional(),

  // Required fields
  brandIds: z.array(z.string().min(1, "BrandId cannot be null or empty"))
    .min(1, "At least one BrandId is required"),

  // Optional customer data fields
  orgDisplayName: z.string().optional(),
  orgPostalAddress: z.object({
    revision: z.number().optional(),
    regionCode: z.string().optional(),
    languageCode: z.string().optional(),
    postalCode: z.string().optional(),
    sortingCode: z.any().optional(),
    administrativeArea: z.string().optional(),
    locality: z.any().optional(),
    sublocality: z.any().optional(),
    addressLines: z.array(z.string()).optional(),
    recipients: z.array(z.string()).optional(),
    organization: z.string().optional()
  }).optional(),

  primaryContactInfo: z.object({
    firstName: z.string().optional(),
    lastName: z.string().optional(),
    displayName: z.string().optional(),
    email: z.string().email().optional(),
    title: z.string().optional(),
    phone: z.string().optional()
  }).optional(),

  alternateEmail: z.string().email().optional(),
  domain: z.string().optional(),
  languageCode: z.string().optional(),

  cloudIdentityInfo: z.object({
    customerType: z.string().optional(),
    primaryDomain: z.string().optional(),
    isDomainVerified: z.string().optional(),
    alternateEmail: z.string().email().optional(),
    phoneNumber: z.string().optional(),
    languageCode: z.string().optional(),
    adminConsoleUri: z.any().optional(),
    eduData: z.object({
      instituteType: z.string().optional(),
      instituteSize: z.string().optional(),
      website: z.string().url().optional()
    }).optional()
  }).optional(),

  channelPartnerId: z.string().optional(),
  correlationId: z.string().optional(),
  googleCustomerId: z.string().optional(),
  cloudIdentityId: z.string().optional()
});

export type GoogleCreateCustomerDtoType = z.infer<typeof googleCreateCustomerDtoSchema>;

/**
 * Validate Google Create Customer DTO
 * Matches .NET GoogleCreateCustomerValidator functionality
 */
export async function validateGoogleCreateCustomerDto(
  dto: GoogleCreateCustomerDto
): Promise<{ isValid: boolean; errors: string[] }> {
  const errors: string[] = [];

  // Basic schema validation
  try {
    googleCreateCustomerDtoSchema.parse(dto);
  } catch (error: any) {
    if (error.errors) {
      error.errors.forEach((err: any) => {
        const fieldName = err.path.join('.');
        errors.push(`${fieldName}: ${err.message}`);
      });
    }
  }

  // Store validation (matching .NET StoreValidator)
  if (dto.storeId) {
    const storeValidation = await validateStore(dto.storeId);
    if (!storeValidation.isValid) {
      errors.push(...storeValidation.errors);
    }
  }

  // Partner validation (matching .NET PartnerValidator)
  if (dto.partnerId) {
    const partnerValidation = await validatePartner(dto.partnerId);
    if (!partnerValidation.isValid) {
      errors.push(...partnerValidation.errors);
    }
  }

  // Customer validation (matching .NET CustomerValidator)
  if (dto.custId) {
    const customerValidation = await validateCustomer(dto.custId);
    if (!customerValidation.isValid) {
      errors.push(...customerValidation.errors);
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Map DTO to Request Model
 * Matches .NET AutoMapper functionality
 */
export function mapDtoToRequest(dto: GoogleCreateCustomerDto) {
  return {
    OrgDisplayName: dto.orgDisplayName,
    OrgPostalAddress: dto.orgPostalAddress ? {
      Revision: dto.orgPostalAddress.revision,
      RegionCode: dto.orgPostalAddress.regionCode,
      LanguageCode: dto.orgPostalAddress.languageCode,
      PostalCode: dto.orgPostalAddress.postalCode,
      SortingCode: dto.orgPostalAddress.sortingCode,
      AdministrativeArea: dto.orgPostalAddress.administrativeArea,
      Locality: dto.orgPostalAddress.locality,
      Sublocality: dto.orgPostalAddress.sublocality,
      AddressLines: dto.orgPostalAddress.addressLines,
      Recipients: dto.orgPostalAddress.recipients,
      Organization: dto.orgPostalAddress.organization
    } : undefined,
    PrimaryContactInfo: dto.primaryContactInfo ? {
      FirstName: dto.primaryContactInfo.firstName,
      LastName: dto.primaryContactInfo.lastName,
      DisplayName: dto.primaryContactInfo.displayName,
      Email: dto.primaryContactInfo.email,
      Title: dto.primaryContactInfo.title,
      Phone: dto.primaryContactInfo.phone
    } : undefined,
    AlternateEmail: dto.alternateEmail,
    Domain: dto.domain,
    LanguageCode: dto.languageCode,
    CloudIdentityInfo: dto.cloudIdentityInfo ? {
      CustomerType: dto.cloudIdentityInfo.customerType,
      PrimaryDomain: dto.cloudIdentityInfo.primaryDomain,
      IsDomainVerified: dto.cloudIdentityInfo.isDomainVerified,
      AlternateEmail: dto.cloudIdentityInfo.alternateEmail,
      PhoneNumber: dto.cloudIdentityInfo.phoneNumber,
      LanguageCode: dto.cloudIdentityInfo.languageCode,
      AdminConsoleUri: dto.cloudIdentityInfo.adminConsoleUri,
      EduData: dto.cloudIdentityInfo.eduData ? {
        InstituteType: dto.cloudIdentityInfo.eduData.instituteType,
        InstituteSize: dto.cloudIdentityInfo.eduData.instituteSize,
        Website: dto.cloudIdentityInfo.eduData.website
      } : undefined
    } : undefined,
    ChannelPartnerId: dto.channelPartnerId,
    CorrelationId: dto.correlationId
  };
}
