import logger from "../../utils/logger";
import { getSqlServerConnection } from "../../utils/sqlServerClient";
import sql from "mssql";
import { SubscriptionRenewal } from "./patchSubscriptionAsync";

/**
 * Get Renewal Request By SKey
 * Matches .NET GetRenewalRequestBySKey DAL method
 */
export async function getRenewalRequestBySKey(sKey: number): Promise<SubscriptionRenewal | null> {
  logger.info(`Entered into DAL GetRenewalRequestBySubscriptionId method with SKey: ${sKey}`);

  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();

    const procedure = "spGetRenewalRequestBySkey";

    // Add input parameter (matching .NET DynamicParameters)
    request.input("SKey", sql.Int, sKey);

    logger.info(`Going to hit DB with proc name : ${procedure} and parameters SKey: ${sKey}`);

    const result = await request.execute<SubscriptionRenewal>(procedure);
    const response = result.recordset?.[0] || null;

    logger.info(`Proc ran successfully, response ${JSON.stringify(response)}`);
    return response;

  } catch (error: any) {
    logger.error(`Error in GetRenewalRequest method with details message= ${error.message}, exception:${error.stack}, StackTrace:${error.stack}`);
    return null;
  }
}
