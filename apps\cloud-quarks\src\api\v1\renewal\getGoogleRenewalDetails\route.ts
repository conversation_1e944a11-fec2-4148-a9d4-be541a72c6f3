import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import { GoogleApiResponse } from "../../../../types/responses/customResponse";
import { validateGetGoogleRenewalDetailsRequest } from "../../../../validators/renewal/customRenewalValidator";
import { getGoogleRenewalDetailsService } from "../../../../services/renewal/getGoogleRenewalDetailsService";

/**
 * @openapi
 * /v1/renewal/getGoogleRenewalDetails:
 *   get:
 *     summary: Fetch Google Renewal Details for a given entitlement
 *     tags:
 *       - Renewal
 *     parameters:
 *       - in: query
 *         name: storeId
 *         schema:
 *           type: string
 *         required: true
 *         description: Store ID
 *       - in: query
 *         name: googleCustomerId
 *         schema:
 *           type: string
 *         required: true
 *         description: Google Customer ID
 *       - in: query
 *         name: entitlementId
 *         schema:
 *           type: string
 *         required: true
 *         description: Google Entitlement ID
 *     responses:
 *       200:
 *         description: Google renewal details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 StatusCode:
 *                   type: integer
 *                   example: 200
 *                 IsError:
 *                   type: boolean
 *                   example: false
 *                 Message:
 *                   type: string
 *                   example: Details fetched successfully
 *                 Data:
 *                   type: object
 *                   description: Renewal details payload
 *       400:
 *         description: Validation error in request parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 StatusCode:
 *                   type: integer
 *                   example: 400
 *                 IsError:
 *                   type: boolean
 *                   example: true
 *                 Message:
 *                   type: string
 *                   example: storeId is required
 *       500:
 *         description: Internal server error or Google API failure
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 StatusCode:
 *                   type: integer
 *                   example: 500
 *                 IsError:
 *                   type: boolean
 *                   example: true
 *                 Message:
 *                   type: string
 *                   example: Internal server error
 */

export const getGoogleRenewalDetailsController = async (
  req: MedusaRequest,
  res: MedusaResponse
) => {
  const { storeId, googleCustomerId, entitlementId } = req.query as Record<
    string,
    string
  >;
  const correlationId = getCorrelationId(req);

  logger.info(
    `Entered into GetGoogleRenewalDetails API | correlationId=${correlationId} | storeId=${storeId}, googleCustomerId=${googleCustomerId}, entitlementId=${entitlementId}`
  );

  const response: GoogleApiResponse = {
    StatusCode: 200,
    IsError: false,
    Message: "",
    Data: null,
  };

  try {
    const validationErrors = await validateGetGoogleRenewalDetailsRequest(
      storeId,
      googleCustomerId,
      entitlementId
    );

    logger.info(
      `Validation errors count = ${
        validationErrors.length
      } | Errors: ${JSON.stringify(validationErrors)}`
    );

    if (validationErrors.length > 0) {
      response.Message = validationErrors.join(", ");
      response.IsError = true;
      response.StatusCode = 400;
      return res.status(400).json(response);
    }

    const data = await getGoogleRenewalDetailsService(
      storeId,
      googleCustomerId,
      entitlementId
    );

    response.Data = data;
    response.StatusCode = 200;
    logger.info(
      `Returning response from GetGoogleRenewalDetails API: ${JSON.stringify(
        response
      )}`
    );
    return res.status(200).json(response);
  } catch (error) {
    logger.error(`Error in GetGoogleRenewalDetails API: ${error}`, {
      correlationId,
    });
    response.StatusCode = 500;
    response.Message = "Internal server error";
    response.IsError = true;
    return res.status(500).json(response);
  }
};
