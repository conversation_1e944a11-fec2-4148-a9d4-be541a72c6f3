import { z } from "zod";
import logger from "../../utils/logger";

// Zod schema for PlaceTransitionRequest (using camelCase to match request body)
export const PlaceTransitionRequestSchema = z.object({
  brandId: z.string().min(1, "brandId cannot be blank or null"),
  storeId: z.string().min(1, "storeId cannot be blank or null"),
  partnerId: z.string().min(1, "partnerId cannot be blank or null"),
  customerId: z.string().min(1, "customerId cannot be blank or null"),
  fromSubscriptionId: z.string().min(1, "fromSubscriptionId cannot be blank or null"),
  fromMaterialId: z.string().min(1, "fromMaterialId cannot be blank or null"),
  fromCatalogItemId: z.string().optional(),
  quantity: z.number().min(0, "quantity must be greater than or equal to 0"),
  toSubscriptionId: z.string().optional(),
  toCatalogItemId: z.string().min(1, "toCatalogItemId cannot be blank or null"),
  promotionId: z.string().optional(),
  term: z.string().min(1, "term cannot be blank or null"),
  billFreq: z.string().min(1, "billFreq cannot be blank or null"),
  updateType: z.string().optional(),
  subUpdateType: z.string().optional(),
  unitPrice: z.number().min(0, "unitPrice must be greater than or equal to 0").optional(),
  taxRate: z.number().min(0, "taxRate must be greater than or equal to 0").optional(),
  promotionRate: z.number().min(0, "promotionRate must be greater than or equal to 0").optional(),
  segment: z.string().optional(),
});

export type PlaceTransitionRequestType = z.infer<typeof PlaceTransitionRequestSchema>;

/**
 * Validate PlaceTransition request body (matching .NET validation patterns)
 */
export function validatePlaceTransitionRequest(requestBody: any): string[] {
  logger.info(`Entered into validatePlaceTransitionRequest method with requestBody: ${JSON.stringify(requestBody)}`);
  
  const validationErrors: string[] = [];

  try {
    PlaceTransitionRequestSchema.parse(requestBody);
    logger.info(`Zod validation passed for PlaceTransition request`);
  } catch (error: any) {
    if (error.errors) {
      error.errors.forEach((err: any) => {
        const fieldName = err.path.join('.');
        validationErrors.push(`${fieldName}: ${err.message}`);
      });
    }
    logger.error(`Zod validation failed: ${JSON.stringify(error.errors)}`);
  }

  // Additional custom validations (using camelCase keys)
  if (!requestBody.brandId || requestBody.brandId.trim() === '') {
    validationErrors.push("The 'brandId' parameter is missing or empty in the request body.");
  }

  if (!requestBody.storeId || requestBody.storeId.trim() === '') {
    validationErrors.push("The 'storeId' parameter is missing or empty in the request body.");
  }

  if (!requestBody.partnerId || requestBody.partnerId.trim() === '') {
    validationErrors.push("The 'partnerId' parameter is missing or empty in the request body.");
  }

  if (!requestBody.customerId || requestBody.customerId.trim() === '') {
    validationErrors.push("The 'customerId' parameter is missing or empty in the request body.");
  }

  if (!requestBody.fromSubscriptionId || requestBody.fromSubscriptionId.trim() === '') {
    validationErrors.push("The 'fromSubscriptionId' parameter is missing or empty in the request body.");
  }

  if (!requestBody.fromMaterialId || requestBody.fromMaterialId.trim() === '') {
    validationErrors.push("The 'fromMaterialId' parameter is missing or empty in the request body.");
  }

  if (!requestBody.toCatalogItemId || requestBody.toCatalogItemId.trim() === '') {
    validationErrors.push("The 'toCatalogItemId' parameter is missing or empty in the request body.");
  }

  if (!requestBody.term || requestBody.term.trim() === '') {
    validationErrors.push("The 'term' parameter is missing or empty in the request body.");
  }

  if (!requestBody.billFreq || requestBody.billFreq.trim() === '') {
    validationErrors.push("The 'billFreq' parameter is missing or empty in the request body.");
  }

  if (requestBody.quantity !== undefined && requestBody.quantity < 0) {
    validationErrors.push("The 'quantity' parameter must be greater than or equal to 0.");
  }

  logger.info(`Validation completed. Errors found: ${validationErrors.length}`);
  return validationErrors;
}
