import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import {
  GoogleAPIResponse,
  GoogleCreateCustomerRequest,
  GoogleCreateCustomerResponse,
  GoogleErrorResponse,
  ResponseStatus
} from "../../types/responses/customResponse";
import { getGoogleTokenService } from "../token/google/getGoogleTokenService";
import { createGoogleRequestHeaders } from "../../config/microsoftEndpoints";
import { GoogleEndpoints } from "../../config/googleEndpoints";
import { PostMethodWithRequestJsonBodyAndHeaders400Handle, EndPointResponseModel } from "../externalService/externalEndPointService";
import { getGoogleAccountIdFromStore } from "../googleOffer/getGooglePurchasableOfferService";
import { Messages } from "./verifyDomainExistsService";
import { provisionCustomerCloudIdentityService } from "./provisionCustomerCloudIdentityService";
import { saveCustomerDAL } from "./googleCustomerDAL";
import { ConstantValue } from "../../validators/renewal/customRenewalValidator";

/**
 * Handle Available Domain
 * Matches .NET HandleAvailableDomain method
 */
export async function handleAvailableDomain(
  req: MedusaRequest,
  isExistingCustomer: boolean,
  custId: string,
  storeId: string,
  brandIds: string[],
  partnerId: string,
  createCustomerRequest: GoogleCreateCustomerRequest
): Promise<GoogleAPIResponse> {
  logger.info(`Entered into HandleAvailableDomain service method with isExistingCustomer ${isExistingCustomer},custId ${custId}, storeId ${storeId}, brandIds ${JSON.stringify(brandIds)}, partnerId ${partnerId}, GoogleCreateCustomerRequest ${JSON.stringify(createCustomerRequest)}.`);

  logger.info(`Going to call service method RegisterCustomerWithGoogle with storeid ${storeId},GoogleCreateCustomerRequest:${JSON.stringify(createCustomerRequest)}`);
  const registerCustomerWithGoogleResponse = await registerCustomerWithGoogle(req, storeId, createCustomerRequest);
  logger.info(`Got response from RegisterCustomerWithGoogle ${JSON.stringify(registerCustomerWithGoogleResponse)}`);

  if (registerCustomerWithGoogleResponse.IsError) {
    logger.info(`Entered into registerCustomerWithGoogleResponse.isError = true section`);
    return registerCustomerWithGoogleResponse;
  }

  logger.info(`Entered into registerCustomerWithGoogleResponse.isError = false section and google customer id ${registerCustomerWithGoogleResponse.Data?.CustomerId}`);
  const googleCustomerId = registerCustomerWithGoogleResponse?.Data?.CustomerId;
  logger.info(`Fetched Google customer id ${googleCustomerId}.`);

  return await handleCustomerProvisioningAndSavingInDB(
    req,
    isExistingCustomer,
    custId,
    storeId,
    googleCustomerId,
    brandIds,
    partnerId,
    createCustomerRequest
  );
}

/**
 * Register Customer With Google
 * Matches .NET RegisterCustomerWithGoogle method
 */
async function registerCustomerWithGoogle(
  req: MedusaRequest,
  storeId: string,
  createCustomerRequest: GoogleCreateCustomerRequest
): Promise<GoogleAPIResponse> {
  logger.info(`Entered into RegisterCustomerWithGoogle service method with storeId ${storeId}, GoogleCreateCustomerRequest:${JSON.stringify(createCustomerRequest)}`);

  let response: GoogleAPIResponse;

  logger.info(`Going to call DAL method GetGoogleAccountId with StoreId ${storeId}`);
  const googleAccountId = await getGoogleAccountIdFromStore(storeId);
  logger.info(`Got googleAccountId ${googleAccountId}`);

  // Build the URL for CreateCustomer (matching .NET URL construction)
  const createCustomerUrl = GoogleEndpoints.createCustomerUrl(googleAccountId);
  logger.info(`CreateCustomer URL: ${createCustomerUrl}`);

  logger.info(`Going to hit GetGoogleToken method with storeId-${storeId}`);
  const token = await getGoogleTokenService(req, storeId);
  logger.info(`Got token from BL TokenService method.`);

  const headerList = createGoogleRequestHeaders(req, token, storeId);
  logger.info(`Prepared HeaderList for external call- ${JSON.stringify(headerList)}`);

  const requestBodyJson = JSON.stringify(createCustomerRequest);
  logger.info(`Prepared requestBody for external call- ${requestBodyJson}`);

  logger.info(`Going to hit ExternalEndPoint PostMethodWithRequestJsonBodyAndHeaders400Handle method with url-${createCustomerUrl}, RequestBody-${requestBodyJson}, headerlist-${JSON.stringify(headerList)}`);

  const configModule = req?.scope?.resolve("configModule");
  const vendorResponse: EndPointResponseModel = await PostMethodWithRequestJsonBodyAndHeaders400Handle(
    {
      url: createCustomerUrl,
      body: requestBodyJson,
      headers: headerList,
      isVendorHit: true,
      module: "RegisterCustomerWithGoogle"
    },
    configModule
  );

  logger.info(`Response from ExternalEndPoint PostMethodWithRequestJsonBodyAndHeaders400Handle is ${JSON.stringify(vendorResponse)}`);

  if (vendorResponse.isSuccessStatusCode) {
    logger.info(`Entered into IsSuccessStatusCode true section. Going to deserialize content ${vendorResponse?.content}`);

    try {
      const createCustomerResponse: GoogleCreateCustomerResponse = JSON.parse(vendorResponse.content);
      logger.info(`Deserialized object : ${JSON.stringify(createCustomerResponse)}`);

      // Extract customer ID from Name field (matching .NET: Name?.Split('/')?.Last())
      const customerId = createCustomerResponse?.Name?.split('/').pop();
      logger.info(`Customer Id fetched from response: ${customerId}`);

      createCustomerResponse.CustomerId = customerId;
      response = {
        Message: "Success",
        Data: createCustomerResponse,
        StatusCode: 200,
        IsError: false
      };
    } catch (parseError) {
      logger.error(`Error parsing create customer response: ${parseError}`);
      response = {
        Message: "Failed to parse create customer response",
        Data: null,
        StatusCode: 500,
        IsError: true
      };
    }
  } else {
    logger.info(`Entered into IsSuccessStatusCode false section.`);
    logger.info(`Setting status Failure`);

    logger.info(`Received errored vendorResponse and deserialising vendorResponse.ErrorMessage-${vendorResponse.errorMessage}`);

    try {
      const errorResponse: GoogleErrorResponse = JSON.parse(vendorResponse.errorMessage || "{}");
      logger.info(`DeserializeObject ${JSON.stringify(errorResponse)}`);

      response = {
        Message: getErrorMessage(errorResponse),
        Data: null,
        StatusCode: errorResponse.Error?.Code || vendorResponse.httpStatusCode || 500,
        IsError: true
      };
    } catch (parseError) {
      logger.error(`Error parsing error response: ${parseError}`);
      response = {
        Message: vendorResponse.errorMessage || "Unknown error occurred",
        Data: null,
        StatusCode: vendorResponse.httpStatusCode || 500,
        IsError: true
      };
    }
  }

  logger.info(`Going to return vendorResponse from BL method with vendorResponse model ${JSON.stringify(response)}.`);
  return response;
}

/**
 * Handle Customer Provisioning And Saving In DB
 * Matches .NET HandleCustomerProvisioningAndSavingInDB method
 */
export async function handleCustomerProvisioningAndSavingInDB(
  req: MedusaRequest,
  isExistingCustomer: boolean,
  custId: string,
  storeId: string,
  googleCustomerId: string,
  brandIds: string[],
  partnerId: string,
  createCustomerRequest: GoogleCreateCustomerRequest
): Promise<GoogleAPIResponse> {
  logger.info(`Going to call service method ProvisionCustomerId with storeId ${storeId}, CustomerId ${googleCustomerId}.`);
  const provisionResponse = await provisionCustomerId(req, storeId, googleCustomerId);
  logger.info(`Got response from service method ProvisionCustomerId response ${JSON.stringify(provisionResponse)}`);

  if (!provisionResponse.IsError) {
    logger.info(`Going to call service method SaveCustomerInDB with isExistingCustomer ${isExistingCustomer},custId ${custId}, CustomerId ${googleCustomerId},operationId ${provisionResponse.Data?.operationId}, operationType ${provisionResponse.Data?.operationType}, storeId ${storeId}, brandIds ${JSON.stringify(brandIds)},  createCustomerRequest ${JSON.stringify(createCustomerRequest)}.`);

    const saveStatus: ResponseStatus = await saveCustomerInDB(
      req,
      isExistingCustomer,
      custId,
      googleCustomerId,
      provisionResponse?.Data?.operationId,
      provisionResponse?.Data?.operationType,
      storeId,
      brandIds,
      partnerId,
      createCustomerRequest
    );

    logger.info(`Got response from service method SaveCustomerInDB with response ${JSON.stringify(saveStatus)}.`);

    if (!saveStatus.Status) {
      return {
        Message: saveStatus.Message || "Failed to save customer",
        Data: null,
        StatusCode: 422, // HttpStatusCode.UnprocessableEntity
        IsError: true
      };
    }
  }

  return provisionResponse;
}

/**
 * Provision Customer ID
 * Matches .NET ProvisionCustomerId method
 */
async function provisionCustomerId(
  req: MedusaRequest,
  storeId: string,
  googleCustomerId: string
): Promise<GoogleAPIResponse> {
  logger.info(`Entered into ProvisionCustomerId service method with storeId ${storeId}, googleCustomerId ${googleCustomerId}`);

  const response: GoogleAPIResponse = {
    Message: "Success",
    Data: null,
    StatusCode: 200,
    IsError: false
  };

  if (googleCustomerId && googleCustomerId.trim() !== '') {
    logger.info(`Entered into section where googleCustomerId is not empty ${googleCustomerId}`);

    logger.info(`Going to call service method ProvisionCustomerCloudIdentity with storeid ${storeId}, googleCustomerId:${googleCustomerId}`);
    const provisionCustomerCloudIdentityResponse = await provisionCustomerCloudIdentityService(storeId, googleCustomerId, req);
    logger.info(`Got response from ProvisionCustomerCloudIdentity ${JSON.stringify(provisionCustomerCloudIdentityResponse)}`);

    if (provisionCustomerCloudIdentityResponse?.IsError === true) {
      logger.info(`Entered into provisionCustomerCloudIdentityResponse.isError = true section`);
      return provisionCustomerCloudIdentityResponse;
    } else {
      logger.info(`Entered into provisionCustomerCloudIdentityResponse.isError = false section`);

      // Create dynamic result object (matching .NET ExpandoObject)
      const result = {
        googleCustomerId: googleCustomerId,
        operationId: provisionCustomerCloudIdentityResponse?.Data?.OperationId,
        operationType: provisionCustomerCloudIdentityResponse?.Data?.Metadata?.OperationType,
        cloudIdentityId: null
      };

      response.Data = result;
      response.Message = Messages.CustomerCreated;
    }
  }

  return response;
}

/**
 * Save Customer In DB (reusing existing DAL function)
 * Matches .NET SaveCustomerInDB method
 */
async function saveCustomerInDB(
  req: MedusaRequest,
  isExistingCustomer: boolean,
  custId: string,
  googleCustomerId: string,
  operationId: string | null,
  operationType: string | null,
  storeId: string,
  brandIds: string[],
  partnerId: string,
  createCustomerRequest: GoogleCreateCustomerRequest,
  cloudIdentityId: string | null = null
): Promise<ResponseStatus> {
  logger.info(`Entered into SaveCustomerInDB service method with isExistingCustomer ${isExistingCustomer},custId ${custId}, CustomerId ${googleCustomerId}, operationId ${operationId}, operationType ${operationType}, storeId ${storeId}, brandIds ${JSON.stringify(brandIds)}, partnerId ${partnerId}, createCustomerRequest ${JSON.stringify(createCustomerRequest)}, cloudIdentityId ${cloudIdentityId}.`);

  const customerType = isExistingCustomer ? ConstantValue.EXISTING : ConstantValue.NEW;
  logger.info(`Customer Type ${customerType}.`);

  logger.info(`Going to call DAL method SaveCustomer with customerType ${customerType},custId ${custId}, CustomerId ${googleCustomerId}, operationId ${operationId}, operationType ${operationType},storeId ${storeId}, brandIds ${JSON.stringify(brandIds)}, createCustomerRequest ${JSON.stringify(createCustomerRequest)}, cloudIdentityId ${cloudIdentityId}.`);

  const statusResponse = await saveCustomerDAL(
    req,
    customerType,
    custId,
    googleCustomerId,
    operationId,
    operationType,
    storeId,
    brandIds,
    partnerId,
    createCustomerRequest,
    cloudIdentityId
  );

  logger.info(`Got response from DAL method SaveCustomer with response ${JSON.stringify(statusResponse)}.`);

  return statusResponse;
}

/**
 * Get Error Message from Google Error Response
 * Matches .NET GoogleErrorResponse.GetErrorMessage() method
 */
function getErrorMessage(errorResponse: GoogleErrorResponse): string {
  if (errorResponse?.Error?.Message) {
    return errorResponse.Error.Message;
  }

  if (errorResponse?.Error?.Details && errorResponse.Error.Details.length > 0) {
    const firstDetail = errorResponse.Error.Details[0];
    if (firstDetail?.ErrorMessages && firstDetail.ErrorMessages.length > 0) {
      return firstDetail.ErrorMessages[0].ErrorMessage || "Unknown error";
    }
  }

  return "Unknown error occurred";
}