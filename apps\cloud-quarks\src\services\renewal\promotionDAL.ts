import logger from "../../utils/logger";
import sql from "mssql";
import { getSqlServerConnection } from "../../utils/sqlServerClient";
import { Promotions } from "../../types/responses/customResponse";

/**
 * Get Promotions By Material ID
 * Matches .NET GetPromotionsByMaterialId method
 */
export async function getPromotionsByMaterialId(
  storeId: string,
  materialId: string,
  term: string,
  billType: string,
  segment: string
): Promise<Promotions | null> {
  logger.info(`Entered into GetPromotionsByMaterialId Service method with storeId ${storeId},materialId ${materialId},term ${term},billType ${billType},segment ${segment}`);

  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();

    // Add parameters (matching .NET SqlParameter setup)
    request.input("iStoreId", sql.<PERSON>ar<PERSON><PERSON>(255), storeId);
    request.input("iMaterialId", sql.Var<PERSON>har(255), materialId);
    request.input("iTerm", sql.VarChar(255), term);
    request.input("iBillType", sql.VarChar(255), billType);
    request.input("iSegment", sql.VarChar(255), segment);

    const procedure = "Usp_GetPromotionsByMaterialId"; // Matches .NET Procedures.Usp_GetPromotionsByMaterialId

    // Log trace DB request (matching .NET logging)
    logger.info(`DB Request intiated for storeId:${storeId} & materialId:${materialId} to SP : ${procedure}`);

    const result = await request.execute<Promotions>(procedure);
    const promotion = result.recordset?.[0] || null;

    // Log trace DB request completion (matching .NET logging)
    logger.info(`DB Request completed :${JSON.stringify(promotion)}`);
    
    return promotion;

  } catch (error: any) {
    logger.error(`Error in getPromotionsByMaterialId: ${error.message}`);
    throw error;
  }
}
