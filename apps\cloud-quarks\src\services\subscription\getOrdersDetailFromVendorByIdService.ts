import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import { getMethod } from "../externalService/externalEndPointService";
import { MicrosoftEndpoints } from "../../config/microsoftEndpoints";
import { OrderDetail, ResponseError } from "../../types/responses/customResponse";

/**
 * Get Orders Detail From Vendor By ID
 * Matches .NET EntitlementFactory.GetOrdersDetailFromVendorByID method
 */
export async function getOrdersDetailFromVendorByIdService(
  req: MedusaRequest,
  customerId: string,
  orderId: string,
  token: string
): Promise<OrderDetail> {
  logger.info(`Entered into GetOrdersDetailFromVendorByID Service method with customerId - ${customerId}, orderId - ${orderId}`);

  try {
    // Construct the URL (matching .NET: string.Format(orderDetailURL, customerId, orderId))
    const orderDetailURL = MicrosoftEndpoints.getOrdersDetailFromVendorByIdUrl(customerId, orderId);
    
    logger.info(`Request for API GetOrdersDetailFromVendorByID orderDetailURL - ${orderDetailURL}`);

    // Prepare headers (matching .NET: client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token))
    const headerList = {
      Authorization: `Bearer ${token}`,
      Accept: "application/json",
    };

    const configModule = req.scope.resolve("configModule");

    // Make the GET request (matching .NET: client.GetAsync(orderDetailURL).Result)
    const response = await getMethod(
      {
        url: orderDetailURL,
        headers: headerList,
        isVendorHit: true,
        module: "GetOrdersDetailFromVendorByID",
      },
      configModule
    );

    logger.info(`Response from GetOrdersDetailFromVendorByID Response - ${response.content}`);

    if (response.isSuccessStatusCode) {
      logger.info(`Entered into IsSuccessStatusCode = true`);

      // Parse the response body (matching .NET: JsonConvert.DeserializeObject<OrderDetail>(dataObjects))
      const order: OrderDetail = JSON.parse(response.content);
      logger.info(`Deserialized Object order - ${JSON.stringify(order)}`);

      // Validate the order object (matching .NET: object.Equals(order, null) && string.IsNullOrEmpty(order.Id))
      if (!order || !order.Id) {
        logger.info(`Throwing error`);
        throw new Error("Error while Fetching orders from Vendor");
      } else {
        logger.info(`Returning Order object - ${JSON.stringify(order)}`);
        return order;
      }
    } else {
      // Handle error response (matching .NET error handling)
      const error: ResponseError = JSON.parse(response.content);
      logger.info(`Throwing error - ${error.description}`);
      throw new Error(error.description);
    }
  } catch (error: any) {
    logger.error(`Error in getOrdersDetailFromVendorByIdService: ${error.message}`);
    throw error;
  }
}
