import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { normalizeQueryParams } from "../../../../utils/mappers/getCustomerUsers";
import { validateCustomerUserQuery } from "../../../../validators/customer/customCustomerValidator";
import { getCustomerBillingInfoService } from "../../../../services/index";

/**
 * @openapi
 * /v1/customer/getCustomerBillingInfo:
 *   get:
 *     summary: Get customer billing info from Microsoft
 *     tags:
 *       - Customer
 *     parameters:
 *       - in: query
 *         name: brandId
 *         schema:
 *           type: string
 *         required: true
 *         description: Brand ID
 *       - in: query
 *         name: storeId
 *         schema:
 *           type: string
 *         required: true
 *         description: Store ID
 *       - in: query
 *         name: tenantId
 *         schema:
 *           type: string
 *         required: true
 *         description: Tenant ID
 *     responses:
 *       200:
 *         description: Successfully retrieved customer billing info
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Id:
 *                   type: string
 *                 Email:
 *                   type: string
 *                 Culture:
 *                   type: string
 *                 Language:
 *                   type: string
 *                 CompanyName:
 *                   type: string
 *                 DefaultAddress:
 *                   type: object
 *                 Links:
 *                   type: object
 *                 Attributes:
 *                   type: object
 *                 FirstName:
 *                   type: string
 *                 LastName:
 *                   type: string
 *                 Code:
 *                   type: integer
 *                 Description:
 *                   type: string
 *                 status:
 *                   type: boolean
 *       400:
 *         description: Bad request due to validation errors
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Validation failed
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: string
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Internal server error
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    logger.info(`GetCustomerBillingInfo API is Hit`);
    const rawQuery = req.query;
    const { brandId, storeId, tenantId } = normalizeQueryParams(rawQuery, [
      "brandId",
      "storeId",
      "tenantId",
    ]);

    logger.info(
      `Normalized query params -> brandId: ${brandId}, storeId: ${storeId}, tenantId: ${tenantId}`
    );

    const validationErrors = validateCustomerUserQuery(
      brandId,
      storeId,
      tenantId
    );
    logger.info(`Validation error count = ${validationErrors.length}`);

    if (validationErrors.length > 0) {
      logger.info(
        `Returning 400 with errors: ${JSON.stringify(validationErrors)}`
      );
      return res.status(400).json({
        status: false,
        errors: validationErrors,
        message: "Validation failed",
      });
    }
    logger.info(
      `Calling GetCustomerBillingInfoService with brandId=${brandId}, storeId=${storeId}, tenantId=${tenantId}`
    );
    const result = await getCustomerBillingInfoService(
      req,
      brandId,
      storeId,
      tenantId
    );

    logger.info(
      `GetCustomerBillingInfoService result: ${JSON.stringify(result)}`
    );

    return res.status(result.status ? 200 : 400).json(result);
  } catch (err) {
    logger.error("GetCustomerBillingInfoService error: ", err as Error);
    return res.status(500).json({
      status: false,
      message: "Internal server error",
    });
  }
}
