import { Migration } from '@mikro-orm/migrations';

export class Migration20250711045410 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table if exists "cq_company_registration" drop constraint if exists "cq_company_registration_redington_account_id_unique";`);
    this.addSql(`alter table if exists "cq_company_registration" drop constraint if exists "cq_company_registration_cin_number_unique";`);
    this.addSql(`alter table if exists "cq_company_registration" drop constraint if exists "cq_company_registration_pan_number_unique";`);
    this.addSql(`alter table if exists "cq_company_registration" drop constraint if exists "cq_company_registration_gst_number_unique";`);
    this.addSql(`create table if not exists "cq_answer_type" ("id" text not null, "type" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_answer_type_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_answer_type_deleted_at" ON "cq_answer_type" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_company_onboarding" ("id" text not null, "redington_account_id" text not null, "user_id" text null, "region_country_id" text null, "country" text not null, "gst_number" text not null, "company_name" text not null, "address1" text not null, "address2" text not null, "country_of_business" text not null, "state" text not null, "city" text not null, "postal_code" text not null, "website" text not null, "camunda_process_id" text null, "director_first_name" text not null, "director_last_name" text not null, "director_email" text not null, "director_country_code" text not null, "director_mobile_number" text not null, "sales_first_name" text not null, "sales_last_name" text not null, "sales_email" text not null, "sales_country_code" text not null, "sales_mobile_number" text not null, "accounts_first_name" text not null, "accounts_last_name" text not null, "accounts_email" text not null, "accounts_country_code" text not null, "accounts_mobile_number" text not null, "requested_by_admin" boolean not null, "requested_source" text null, "status_id" text not null, "store_id" text null, "reason" text null, "metadata" jsonb not null, "created_by" text null, "updated_by" text null, "deleted_by" text null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_company_onboarding_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_company_onboarding_deleted_at" ON "cq_company_onboarding" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_company_registration" ("id" text not null, "user_id" text null, "region_country_id" text null, "gst_number" text not null, "pan_number" text not null, "part_of_sez" boolean not null default false, "cin_number" text not null, "company_name" text not null, "address_1" text not null, "address_2" text not null, "country_of_business" text not null, "postal_code" text not null, "address_name" text not null, "state" text not null, "city" text not null, "country_code" text not null, "contact_number" text not null, "legal_status_of_company" text not null, "website" text not null, "social_media_account" text not null, "number_of_offices_in_region" integer not null, "number_of_warehouses_in_region" integer not null, "number_of_employees" integer not null, "number_of_sales_staff" integer not null, "number_of_technical_staff" integer not null, "twitter_account_url" text not null, "linkedin_account_url" text not null, "facebook_account_url" text not null, "instagram_account_url" text not null, "director_first_name" text not null, "director_last_name" text not null, "director_email" text not null, "director_country_code" text not null, "director_mobile_number" text not null, "sales_first_name" text not null, "sales_last_name" text not null, "sales_email" text not null, "sales_country_code" text not null, "sales_mobile_number" text not null, "accounts_first_name" text not null, "accounts_last_name" text not null, "accounts_email" text not null, "accounts_country_code" text not null, "accounts_mobile_number" text not null, "requested_by_admin" boolean not null, "requested_source" text null, "status_id" text not null, "store_id" text not null, "reason" text null, "redington_account_id" text null, "metadata" jsonb not null, "created_by" text null, "updated_by" text null, "deleted_by" text null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_company_registration_pkey" primary key ("id"));`);
    this.addSql(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_cq_company_registration_gst_number_unique" ON "cq_company_registration" (gst_number) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_cq_company_registration_pan_number_unique" ON "cq_company_registration" (pan_number) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_cq_company_registration_cin_number_unique" ON "cq_company_registration" (cin_number) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_cq_company_registration_redington_account_id_unique" ON "cq_company_registration" (redington_account_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_company_registration_deleted_at" ON "cq_company_registration" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_company_document" ("id" text not null, "document_id" text not null, "company_registration_id" text not null, "document_uploaded_link" text null, "description" text null, "created_by" text null, "updated_by" text null, "deleted_by" text null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_company_document_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_company_document_company_registration_id" ON "cq_company_document" (company_registration_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_company_document_deleted_at" ON "cq_company_document" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_authorized_signatory" ("id" text not null, "company_onboarding_id" text null, "company_registration_id" text null, "first_name" text not null, "last_name" text not null, "email" text not null, "country_code" text not null, "mobile_number" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_authorized_signatory_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_authorized_signatory_company_onboarding_id" ON "cq_authorized_signatory" (company_onboarding_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_authorized_signatory_company_registration_id" ON "cq_authorized_signatory" (company_registration_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_authorized_signatory_deleted_at" ON "cq_authorized_signatory" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_cr_office_country" ("id" text not null, "region_country_id" text not null, "company_registration_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_cr_office_country_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_cr_office_country_company_registration_id" ON "cq_cr_office_country" (company_registration_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_cr_office_country_deleted_at" ON "cq_cr_office_country" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_question" ("id" text not null, "question" text not null, "order_id" serial, "region_id" text not null, "answer_type_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_question_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_question_answer_type_id" ON "cq_question" (answer_type_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_question_deleted_at" ON "cq_question" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_answer" ("id" text not null, "answer" text not null, "questions_id" text not null, "order_id" serial, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_answer_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_answer_questions_id" ON "cq_answer" (questions_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_answer_deleted_at" ON "cq_answer" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_profile_info" ("id" text not null, "questions_id" text not null, "answer_id" text not null, "company_registration_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_profile_info_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_profile_info_questions_id" ON "cq_profile_info" (questions_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_profile_info_answer_id" ON "cq_profile_info" (answer_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_profile_info_company_registration_id" ON "cq_profile_info" (company_registration_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_profile_info_deleted_at" ON "cq_profile_info" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_vendor" ("id" text not null, "vendor_name" text not null, "metadata" jsonb not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_vendor_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_vendor_deleted_at" ON "cq_vendor" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_region_vendor" ("id" text not null, "region_id" text not null, "vendor_id_id" text not null, "metadata" jsonb not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_region_vendor_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_region_vendor_vendor_id_id" ON "cq_region_vendor" (vendor_id_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_region_vendor_deleted_at" ON "cq_region_vendor" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_brand" ("id" text not null, "brand_name" text not null, "vendor_id" text not null, "metadata" jsonb not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_brand_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_brand_vendor_id" ON "cq_brand" (vendor_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_brand_deleted_at" ON "cq_brand" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_region_brand" ("id" text not null, "region_id" text not null, "brand_id_id" text not null, "metadata" jsonb not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_region_brand_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_region_brand_brand_id_id" ON "cq_region_brand" (brand_id_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_region_brand_deleted_at" ON "cq_region_brand" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_brand_category" ("id" text not null, "category_name" text not null, "brand_id" text not null, "metadata" jsonb not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_brand_category_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_brand_category_brand_id" ON "cq_brand_category" (brand_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_brand_category_deleted_at" ON "cq_brand_category" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_user_brand_preferences" ("id" text not null, "vendor_id" text not null, "brand_id" text not null, "brand_category_id" text not null, "company_registration_id" text null, "company_onboarding_id" text null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_user_brand_preferences_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_user_brand_preferences_vendor_id" ON "cq_user_brand_preferences" (vendor_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_user_brand_preferences_brand_id" ON "cq_user_brand_preferences" (brand_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_user_brand_preferences_brand_category_id" ON "cq_user_brand_preferences" (brand_category_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_user_brand_preferences_company_registration_id" ON "cq_user_brand_preferences" (company_registration_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_user_brand_preferences_company_onboarding_id" ON "cq_user_brand_preferences" (company_onboarding_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_user_brand_preferences_deleted_at" ON "cq_user_brand_preferences" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_region_brand_category" ("id" text not null, "region_id" text not null, "brand_category_id_id" text not null, "metadata" jsonb not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_region_brand_category_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_region_brand_category_brand_category_id_id" ON "cq_region_brand_category" (brand_category_id_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_region_brand_category_deleted_at" ON "cq_region_brand_category" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_brand_brand_category" ("id" text not null, "brand_id" text not null, "brand_category_id" text not null, "metadata" jsonb not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_brand_brand_category_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_brand_brand_category_brand_id" ON "cq_brand_brand_category" (brand_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_brand_brand_category_brand_category_id" ON "cq_brand_brand_category" (brand_category_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_brand_brand_category_deleted_at" ON "cq_brand_brand_category" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`alter table if exists "cq_company_document" add constraint "cq_company_document_company_registration_id_foreign" foreign key ("company_registration_id") references "cq_company_registration" ("id") on update cascade;`);

    this.addSql(`alter table if exists "cq_authorized_signatory" add constraint "cq_authorized_signatory_company_onboarding_id_foreign" foreign key ("company_onboarding_id") references "cq_company_onboarding" ("id") on update cascade on delete set null;`);
    this.addSql(`alter table if exists "cq_authorized_signatory" add constraint "cq_authorized_signatory_company_registration_id_foreign" foreign key ("company_registration_id") references "cq_company_registration" ("id") on update cascade on delete set null;`);

    this.addSql(`alter table if exists "cq_cr_office_country" add constraint "cq_cr_office_country_company_registration_id_foreign" foreign key ("company_registration_id") references "cq_company_registration" ("id") on update cascade;`);

    this.addSql(`alter table if exists "cq_question" add constraint "cq_question_answer_type_id_foreign" foreign key ("answer_type_id") references "cq_answer_type" ("id") on update cascade;`);

    this.addSql(`alter table if exists "cq_answer" add constraint "cq_answer_questions_id_foreign" foreign key ("questions_id") references "cq_question" ("id") on update cascade;`);

    this.addSql(`alter table if exists "cq_profile_info" add constraint "cq_profile_info_questions_id_foreign" foreign key ("questions_id") references "cq_question" ("id") on update cascade;`);
    this.addSql(`alter table if exists "cq_profile_info" add constraint "cq_profile_info_answer_id_foreign" foreign key ("answer_id") references "cq_answer" ("id") on update cascade;`);
    this.addSql(`alter table if exists "cq_profile_info" add constraint "cq_profile_info_company_registration_id_foreign" foreign key ("company_registration_id") references "cq_company_registration" ("id") on update cascade;`);

    this.addSql(`alter table if exists "cq_region_vendor" add constraint "cq_region_vendor_vendor_id_id_foreign" foreign key ("vendor_id_id") references "cq_vendor" ("id") on update cascade;`);

    this.addSql(`alter table if exists "cq_brand" add constraint "cq_brand_vendor_id_foreign" foreign key ("vendor_id") references "cq_vendor" ("id") on update cascade;`);

    this.addSql(`alter table if exists "cq_region_brand" add constraint "cq_region_brand_brand_id_id_foreign" foreign key ("brand_id_id") references "cq_brand" ("id") on update cascade;`);

    this.addSql(`alter table if exists "cq_brand_category" add constraint "cq_brand_category_brand_id_foreign" foreign key ("brand_id") references "cq_brand" ("id") on update cascade;`);

    this.addSql(`alter table if exists "cq_user_brand_preferences" add constraint "cq_user_brand_preferences_vendor_id_foreign" foreign key ("vendor_id") references "cq_vendor" ("id") on update cascade;`);
    this.addSql(`alter table if exists "cq_user_brand_preferences" add constraint "cq_user_brand_preferences_brand_id_foreign" foreign key ("brand_id") references "cq_brand" ("id") on update cascade;`);
    this.addSql(`alter table if exists "cq_user_brand_preferences" add constraint "cq_user_brand_preferences_brand_category_id_foreign" foreign key ("brand_category_id") references "cq_brand_category" ("id") on update cascade;`);
    this.addSql(`alter table if exists "cq_user_brand_preferences" add constraint "cq_user_brand_preferences_company_registration_id_foreign" foreign key ("company_registration_id") references "cq_company_registration" ("id") on update cascade on delete set null;`);
    this.addSql(`alter table if exists "cq_user_brand_preferences" add constraint "cq_user_brand_preferences_company_onboarding_id_foreign" foreign key ("company_onboarding_id") references "cq_company_onboarding" ("id") on update cascade on delete set null;`);

    this.addSql(`alter table if exists "cq_region_brand_category" add constraint "cq_region_brand_category_brand_category_id_id_foreign" foreign key ("brand_category_id_id") references "cq_brand_category" ("id") on update cascade;`);

    this.addSql(`alter table if exists "cq_brand_brand_category" add constraint "cq_brand_brand_category_brand_id_foreign" foreign key ("brand_id") references "cq_brand" ("id") on update cascade;`);
    this.addSql(`alter table if exists "cq_brand_brand_category" add constraint "cq_brand_brand_category_brand_category_id_foreign" foreign key ("brand_category_id") references "cq_brand_category" ("id") on update cascade;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table if exists "cq_question" drop constraint if exists "cq_question_answer_type_id_foreign";`);

    this.addSql(`alter table if exists "cq_authorized_signatory" drop constraint if exists "cq_authorized_signatory_company_onboarding_id_foreign";`);

    this.addSql(`alter table if exists "cq_user_brand_preferences" drop constraint if exists "cq_user_brand_preferences_company_onboarding_id_foreign";`);

    this.addSql(`alter table if exists "cq_company_document" drop constraint if exists "cq_company_document_company_registration_id_foreign";`);

    this.addSql(`alter table if exists "cq_authorized_signatory" drop constraint if exists "cq_authorized_signatory_company_registration_id_foreign";`);

    this.addSql(`alter table if exists "cq_cr_office_country" drop constraint if exists "cq_cr_office_country_company_registration_id_foreign";`);

    this.addSql(`alter table if exists "cq_profile_info" drop constraint if exists "cq_profile_info_company_registration_id_foreign";`);

    this.addSql(`alter table if exists "cq_user_brand_preferences" drop constraint if exists "cq_user_brand_preferences_company_registration_id_foreign";`);

    this.addSql(`alter table if exists "cq_answer" drop constraint if exists "cq_answer_questions_id_foreign";`);

    this.addSql(`alter table if exists "cq_profile_info" drop constraint if exists "cq_profile_info_questions_id_foreign";`);

    this.addSql(`alter table if exists "cq_profile_info" drop constraint if exists "cq_profile_info_answer_id_foreign";`);

    this.addSql(`alter table if exists "cq_region_vendor" drop constraint if exists "cq_region_vendor_vendor_id_id_foreign";`);

    this.addSql(`alter table if exists "cq_brand" drop constraint if exists "cq_brand_vendor_id_foreign";`);

    this.addSql(`alter table if exists "cq_user_brand_preferences" drop constraint if exists "cq_user_brand_preferences_vendor_id_foreign";`);

    this.addSql(`alter table if exists "cq_region_brand" drop constraint if exists "cq_region_brand_brand_id_id_foreign";`);

    this.addSql(`alter table if exists "cq_brand_category" drop constraint if exists "cq_brand_category_brand_id_foreign";`);

    this.addSql(`alter table if exists "cq_user_brand_preferences" drop constraint if exists "cq_user_brand_preferences_brand_id_foreign";`);

    this.addSql(`alter table if exists "cq_brand_brand_category" drop constraint if exists "cq_brand_brand_category_brand_id_foreign";`);

    this.addSql(`alter table if exists "cq_user_brand_preferences" drop constraint if exists "cq_user_brand_preferences_brand_category_id_foreign";`);

    this.addSql(`alter table if exists "cq_region_brand_category" drop constraint if exists "cq_region_brand_category_brand_category_id_id_foreign";`);

    this.addSql(`alter table if exists "cq_brand_brand_category" drop constraint if exists "cq_brand_brand_category_brand_category_id_foreign";`);

    this.addSql(`drop table if exists "cq_answer_type" cascade;`);

    this.addSql(`drop table if exists "cq_company_onboarding" cascade;`);

    this.addSql(`drop table if exists "cq_company_registration" cascade;`);

    this.addSql(`drop table if exists "cq_company_document" cascade;`);

    this.addSql(`drop table if exists "cq_authorized_signatory" cascade;`);

    this.addSql(`drop table if exists "cq_cr_office_country" cascade;`);

    this.addSql(`drop table if exists "cq_question" cascade;`);

    this.addSql(`drop table if exists "cq_answer" cascade;`);

    this.addSql(`drop table if exists "cq_profile_info" cascade;`);

    this.addSql(`drop table if exists "cq_vendor" cascade;`);

    this.addSql(`drop table if exists "cq_region_vendor" cascade;`);

    this.addSql(`drop table if exists "cq_brand" cascade;`);

    this.addSql(`drop table if exists "cq_region_brand" cascade;`);

    this.addSql(`drop table if exists "cq_brand_category" cascade;`);

    this.addSql(`drop table if exists "cq_user_brand_preferences" cascade;`);

    this.addSql(`drop table if exists "cq_region_brand_category" cascade;`);

    this.addSql(`drop table if exists "cq_brand_brand_category" cascade;`);
  }

}
