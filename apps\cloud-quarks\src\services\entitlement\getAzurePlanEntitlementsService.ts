import axios from "axios";
import logger from "../../utils/logger";
import {
  AzureEntitlementItem,
  AzureEntitlementResponse,
  TokenResponse,
} from "../../types/responses/customResponse";
import { getSqlServerConnection } from "../../utils/sqlServerClient";
import { MicrosoftEndpoints } from "../../config/microsoftEndpoints";
import { getMethod } from "../externalService/externalEndPointService";

export async function getAzurePlanEntitlementsService(
  req: any,
  subscriptionId: string
): Promise<AzureEntitlementItem[] | null> {
  logger.info(
    `Entered into getAzurePlanEntitlementsService with subscriptionId - ${subscriptionId}`
  );

  try {
    //Fetch subscription details (equivalent to SP call)
    const subscriptionDetails = await getSubscriptionDetailsFromSP(
      subscriptionId
    );

    console.log("subscriptionDetails--------->", subscriptionDetails);

    logger.info(`SubscriptionDetails Fetched: ${JSON.stringify(subscriptionDetails)}`);

    //Prepare token request
    const tokenRequest = {
      client_id: subscriptionDetails.Clientid,
      client_secret: subscriptionDetails.Clientsecret,
      grant_type: subscriptionDetails.granttype,
      redirect_uri: "http://localhost",
      refresh_token: subscriptionDetails.Token,
      resource: subscriptionDetails.Resource,
      store_domain: subscriptionDetails.StoreDomain,
    };

    //Generate Token
    const token = await getTokenFromRefreshToken(tokenRequest);

    //Construct entitlement endpoint URL
    const completeUrlWithParams = MicrosoftEndpoints.getAzurePlanEntitlementUrl(
      subscriptionDetails.CustomerId,
      subscriptionId
    );
    console.log(
      "completeUrlWithParams---getMsToken---->",
      completeUrlWithParams
    );

    logger.info(
      `Request initiated to GetEntitlements with endpoint - ${completeUrlWithParams}`
    );

    const headerList = {
      Authorization: `Bearer ${token}`,
      Accept: "application/json",
    };

    // Send GET request via shared method
    const configModule = req.scope.resolve("configModule");
    const response = await getMethod(
      {
        url: completeUrlWithParams,
        headers: headerList,
        isVendorHit: true,
        module: "GetAzurePlanEntitlements",
      },
      configModule
    );

    logger.info(`Response from GetAzurePlanEntitlements - ${JSON.stringify(response)}`);

    // Validate response and return items
    if (response.isSuccessStatusCode && response.content) {
      logger.info(`Entered into isSuccessStatusCode = true`);

      const entitlementResponse: AzureEntitlementResponse = JSON.parse(response.content);

      logger.info(`Deserialized Object entitlements - ${JSON.stringify(entitlementResponse)}`);

      if (
        entitlementResponse &&
        Array.isArray(entitlementResponse.items) &&
        entitlementResponse.items.length > 0
      ) {
        logger.info(`Returning parsed entitlements - ${JSON.stringify(entitlementResponse.items)}`);
        return entitlementResponse.items;
      }
    }

    logger.info("Returning null - No entitlements found or response invalid");
    return null;  } catch (error) {
    logger.error(
      `ERROR in getAzurePlanEntitlementsService | subscriptionId: ${subscriptionId}, Error: ${error}`
    );
    return null;
  }
}

export async function getSubscriptionDetailsFromSP(
  subscriptionId: string
): Promise<any | null> {
  logger.info(
    `Entered into getSubscriptionDetailsFromSP with subscriptionId - ${subscriptionId}`
  );

  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();

    const proc = "spGetSubscriptionDetails";

    request.input("iSubscriptionId", subscriptionId);

    logger.info(
      `Going to hit DB with proc name: ${proc} and subscriptionId: ${subscriptionId}`
    );

    const result = await request.execute(proc);
    const rows = result.recordset;

    if (rows && rows.length > 0) {
      const row = rows[0];
      logger.info(
        `Returning row from spGetSubscriptionDetails: ${JSON.stringify(row)}`
      );
      return row;
    }

    logger.info(`No result found for subscriptionId: ${subscriptionId}`);
    return null;
  } catch (error) {
    logger.error(`Error in getSubscriptionDetailsFromSP: ${error}`);
    throw error;
  }
}

export async function getTokenFromRefreshToken(
  tokenRequest: any
): Promise<string> {
  try {
    console.log("tokenRequest------------->", tokenRequest);

    const completeUrlWithParams = MicrosoftEndpoints.getMsTokenUrl(
      tokenRequest.store_domain
    );
    console.log(
      "completeUrlWithParams---getMsToken---->",
      completeUrlWithParams
    );

    // Prepare form data
    const formData = new URLSearchParams();
    formData.append("grant_type", tokenRequest.grant_type);
    formData.append("client_id", tokenRequest.client_id);
    formData.append("client_secret", tokenRequest.client_secret);
    formData.append("resource", tokenRequest.resource);
    formData.append("refresh_token", tokenRequest.refresh_token);
    formData.append("redirect_uri", tokenRequest.redirect_uri);
    formData.append("store_domain", tokenRequest.store_domain);

    // Make POST request
    const response = await axios.post(completeUrlWithParams, formData, {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      timeout: 30000, // 30 seconds timeout
    });

    console.log("response-------->", response);

    if (response.status >= 200 && response.status < 300) {
      const tokenResponse = response.data as TokenResponse;
      logger.info(`Token generated successfully`);
      return tokenResponse.access_token;
    } else {
      throw new Error("Error occurred while generating token");
    }
  } catch (error) {
    logger.error(`Error in getTokenFromOrderRow: ${error}`);

    if (axios.isAxiosError(error)) {
      logger.error(
        `Axios Error Details: Status: ${
          error.response?.status
        }, Data: ${JSON.stringify(error.response?.data)}`
      );
    }

    throw new Error("Error occurred while generating token");
  }
}
