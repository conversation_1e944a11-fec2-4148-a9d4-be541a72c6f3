import { model } from '@medusajs/framework/utils';
import CQUser from './cq_user';
import Page from './page';
import PagePermission from './page-permission';
import RolePage from './role-page';
import RolePagePermission from './role-page-permission';

const Permission = model.define('cq_permission', {
  id: model.id().primaryKey(),
  permission: model.text().unique(),
  metadata: model.json().nullable(),
  created_by: model.belongsTo(() => CQUser, { mappedBy: undefined }).nullable(),
  updated_by: model.belongsTo(() => CQUser, { mappedBy: undefined }).nullable(),
  deleted_by: model.belongsTo(() => CQUser, { mappedBy: undefined }).nullable(),
  pages: model.manyToMany(() => Page, {
    pivotEntity: () => PagePermission,
    mappedBy: 'permissions',
  }),
  role_pages: model.manyToMany(() => RolePage, {
    pivotEntity: () => RolePagePermission,
    mappedBy: "permissions",
  }),

});

export default Permission;
