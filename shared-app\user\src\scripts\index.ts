import { ExecArgs } from '@medusajs/framework/types';
import seedRegions from './seed-region';
import seedDesignations from './seed-designations';
import { syncRegionCountry } from './seed-region-country';
import { logger } from '@medusajs/framework';
import seedStatus from './seed-status';
import seedCompanyTypes from './seed-company-type';
import seedPermissions from './seed-permission';
import seedPersona from './seed-persona';
import seedModule from './seed-module';
import seedPage from './seed-page';
import seedPagePermission from './seed-page-permission';
import seedTableMaster from './seed-table-master';
import seedColumnMaster from './seed-column-master';
import seedDocument from './seed-document';

const getRegionFromArgs = (): string => {
  const arg = process.argv.find((arg) => arg.startsWith('--region='));
  return arg?.split('=')[1] ?? 'default';
};

export default async function runSeeder({ container }: ExecArgs) {
  const inputRegion = getRegionFromArgs();

  const seederConfig = {
    default: [
      seedRegions,
      syncRegionCountry,
      seedDesignations,
      seedStatus,
      seedCompanyTypes,
      seedPermissions,
      seedPersona,
      seedModule,
      seedPage,
      seedPagePermission,
      seedColumnMaster,
      seedDocument,
    ],
    india: [],
    mea: [],
    turkey: [],
  } satisfies Record<string, Array<(args: ExecArgs) => Promise<void>>>;

  type Region = keyof typeof seederConfig;

  const isValidRegion = (region: string): region is Region => {
    return region in seederConfig;
  };

  const region: Region = isValidRegion(inputRegion) ? inputRegion : 'default';
  logger.info(` Starting seed for region: "${region}"`);

  for (const seeder of seederConfig[region]) {
    await seeder({ container } as ExecArgs);
  }

  logger.info(`Completed seeding for region: "${region}"`);
}
