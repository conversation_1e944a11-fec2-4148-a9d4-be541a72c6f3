import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import {
  GoogleAPIResponse,
  ResponseStatus,
  GoogleCreateCustomerRequest
} from "../../types/responses/customResponse";
import { ConstantValue } from "../../validators/renewal/customRenewalValidator";
import { Messages } from "./verifyDomainExistsService";
import { saveCustomerDAL } from "./googleCustomerDAL";

/**
 * Handle Existing Customer And Saving In DB
 * Matches .NET HandleExistingCustomerAndSavingInDB method
 */
export async function handleExistingCustomerAndSavingInDB(
  req: MedusaRequest,
  isExistingCustomer: boolean,
  custId: string,
  storeId: string,
  googleCustomerId: string,
  brandIds: string[],
  partnerId: string,
  createCustomerRequest: GoogleCreateCustomerRequest,
  cloudIdentityId: string
): Promise<GoogleAPIResponse> {
  logger.info(`Entered into HandleExistingCustomerAndSavingInDB service method with isExistingCustomer ${isExistingCustomer},custId ${custId}, googleCustomerId ${googleCustomerId}, storeId ${storeId}, brandIds ${JSON.stringify(brandIds)}, partnerId ${partnerId}, createCustomerRequest ${JSON.stringify(createCustomerRequest)}, cloudIdentityId ${cloudIdentityId}.`);

  const saveStatus = await saveCustomerInDB(
    req,
    isExistingCustomer,
    custId,
    googleCustomerId,
    null,
    null,
    storeId,
    brandIds,
    partnerId,
    createCustomerRequest,
    cloudIdentityId
  );

  if (saveStatus.Status) {
    logger.info(`Entered into HandleExistingCustomerAndSavingInDB.saveStatus = true section`);

    // Create dynamic result object (matching .NET ExpandoObject)
    const result = {
      googleCustomerId: googleCustomerId,
      operationId: null,
      operationType: null,
      cloudIdentityId: cloudIdentityId
    };

    return {
      Message: Messages.CustomerExistingCreated,
      Data: result,
      StatusCode: 200,
      IsError: false
    };
  } else {
    return {
      Message: saveStatus.Message || "Failed to save customer",
      Data: null,
      StatusCode: 422, // HttpStatusCode.UnprocessableEntity
      IsError: true
    };
  }
}

/**
 * Save Customer In DB
 * Matches .NET SaveCustomerInDB method
 */
async function saveCustomerInDB(
  req: MedusaRequest,
  isExistingCustomer: boolean,
  custId: string,
  googleCustomerId: string,
  operationId: string | null,
  operationType: string | null,
  storeId: string,
  brandIds: string[],
  partnerId: string,
  createCustomerRequest: GoogleCreateCustomerRequest,
  cloudIdentityId: string | null = null
): Promise<ResponseStatus> {
  logger.info(`Entered into SaveCustomerInDB service method with isExistingCustomer ${isExistingCustomer},custId ${custId}, CustomerId ${googleCustomerId}, operationId ${operationId}, operationType ${operationType}, storeId ${storeId}, brandIds ${JSON.stringify(brandIds)}, partnerId ${partnerId}, createCustomerRequest ${JSON.stringify(createCustomerRequest)}, cloudIdentityId ${cloudIdentityId}.`);

  const customerType = isExistingCustomer ? ConstantValue.EXISTING : ConstantValue.NEW;
  logger.info(`Customer Type ${customerType}.`);

  logger.info(`Going to call DAL method SaveCustomer with customerType ${customerType},custId ${custId}, CustomerId ${googleCustomerId}, operationId ${operationId}, operationType ${operationType},storeId ${storeId}, brandIds ${JSON.stringify(brandIds)}, createCustomerRequest ${JSON.stringify(createCustomerRequest)}, cloudIdentityId ${cloudIdentityId}.`);

  const statusResponse = await saveCustomerDAL(
    req,
    customerType,
    custId,
    googleCustomerId,
    operationId,
    operationType,
    storeId,
    brandIds,
    partnerId,
    createCustomerRequest,
    cloudIdentityId
  );

  logger.info(`Got response from DAL method SaveCustomer with response ${JSON.stringify(statusResponse)}.`);

  return statusResponse;
}