import { model } from "@medusajs/framework/utils";
import { CqCompanyRegistration } from "./company-registration";
import CqQuestion from "./question";
import Answer from "./answer";

export const ProfileInfo = model.define("cq_profile_info", {
	id: model.id().primaryKey(),
	questions: model.belongsTo(() => CqQuestion),
	answer: model.belongsTo(() => Answer),
	company_registration: model.belongsTo(() => CqCompanyRegistration),
});
