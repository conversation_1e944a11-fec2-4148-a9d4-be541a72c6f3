{"compilerOptions": {"target": "ES2021", "esModuleInterop": true, "module": "Node16", "moduleResolution": "Node16", "emitDecoratorMetadata": true, "experimentalDecorators": true, "skipLibCheck": true, "skipDefaultLibCheck": true, "declaration": false, "sourceMap": false, "inlineSourceMap": true, "outDir": "./.medusa/server", "rootDir": "../../", "jsx": "react-jsx", "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "checkJs": false, "strictNullChecks": true, "strict": true, "paths": {"@org/helmet": ["../../libs/helmet/src/index.ts"], "@org/rate_limit": ["../../libs/rate_limit/src/index.ts"], "@org/ses": ["../../libs/ses/src/index.ts"], "@org/utils": ["../../libs/utils/src/index.ts"], "@org/excel": ["../../libs/util-excel/src/index.ts"], "@org/user-country-link": ["../user/src/links/index.ts"], "@shared-app/cq-user/service": ["../user/src/modules/cq-user/service.ts"], "@shared-app/cq-user": ["../user/src/modules/cq-user/index.ts"]}}, "ts-node": {"swc": true}, "include": ["**/*", ".medusa/types/*", "../user/src/modules/cq-user", "../user/src/scripts/index.ts"], "exclude": ["node_modules", ".medusa/server", ".medusa/admin", ".cache"]}