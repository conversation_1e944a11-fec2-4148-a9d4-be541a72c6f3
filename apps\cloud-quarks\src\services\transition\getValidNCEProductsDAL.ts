import logger from "../../utils/logger";
import sql from "mssql";
import { getSqlServerConnection } from "../../utils/sqlServerClient";
import { GetNCEProductUpgradesResponseItem, EligibleProduct } from "../../types/responses/customResponse";

/**
 * Get Valid NCE Products With Term And Bill Frequency DAL
 * Matches .NET TransitionDal.GetValidNCEProductsWithTermAndBillFrequency method
 */
export async function getValidNCEProductsWithTermAndBillFrequencyDAL(
  eligibleProducts: EligibleProduct[],
  storeId: string
): Promise<GetNCEProductUpgradesResponseItem[]> {
  logger.info(`Entered into DAL GetValidNCEProductsWithTermAndBillFrequency method with eligibleProducts-${JSON.stringify(eligibleProducts)} and storeId-${storeId}`);

  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();

    const procedure = "spGetProductsWithTermAndBillFrequency"; // Matches .NET Procedures.spGetProductsWithTermAndBillFrequency

    // Create table-valued parameter for eligible products (matching .NET: table.AsTableValuedParameter("dbo.EligibleProduct"))
    // Note: In Node.js with mssql, we need to create a table type or use JSON parameter
    // For now, we'll use a JSON string approach which is commonly supported
    const eligibleProductsJson = JSON.stringify(eligibleProducts);

    // Add parameters (matching .NET: parameters.Add("@iMaterialIds", table.AsTableValuedParameter("dbo.EligibleProduct")))
    request.input("@iMaterialIds", sql.NVarChar(sql.MAX), eligibleProductsJson);
    request.input("@iStoreId", sql.VarChar(255), storeId);

    logger.info(`Going to hit DB with proc name : ${procedure} and parameters @iMaterialIds: ${eligibleProductsJson}, @iStoreId: ${storeId}`);

    // Execute stored procedure with multiple result sets (matching .NET: QueryMultipleAsync)
    const result = await request.execute(procedure);

    // Access recordsets safely (handling TypeScript type issues)
    const recordsets = Array.isArray(result.recordsets) ? result.recordsets : [result.recordset];

    // First result set: Main product response (matching .NET: await result.ReadAsync<GetNCEProductUpgradesResponseItem>())
    const response: GetNCEProductUpgradesResponseItem[] = recordsets[0] || [];
    logger.info(`Response : ${JSON.stringify(response)}`);

    // Second result set: Terms grouped by MaterialId (matching .NET: await result.ReadAsync())
    const termResults = recordsets[1] || [];
    const termMap = new Map<string, string[]>();

    termResults.forEach((row: any) => {
      if (!termMap.has(row.MaterialId)) {
        termMap.set(row.MaterialId, []);
      }
      if (row.Term) {
        termMap.get(row.MaterialId)!.push(row.Term);
      }
    });

    logger.info(`Term Response : ${JSON.stringify(Object.fromEntries(termMap))}`);

    // Third result set: BillTypes grouped by MaterialId (matching .NET: await result.ReadAsync())
    const billTypeResults = recordsets[2] || [];
    const billTypeMap = new Map<string, string[]>();

    billTypeResults.forEach((row: any) => {
      if (!billTypeMap.has(row.MaterialId)) {
        billTypeMap.set(row.MaterialId, []);
      }
      if (row.BillType) {
        billTypeMap.get(row.MaterialId)!.push(row.BillType);
      }
    });

    logger.info(`billType Response : ${JSON.stringify(Object.fromEntries(billTypeMap))}`);

    // Map 'Term' and 'BillType' to the response items (matching .NET: response.ForEach)
    response.forEach(row => {
      row.Terms = termMap.has(row.MaterialId || "") ? termMap.get(row.MaterialId || "") || [] : [];
      row.BillTypes = billTypeMap.has(row.MaterialId || "") ? billTypeMap.get(row.MaterialId || "") || [] : [];
    });

    logger.info(`Proc ran successfully, response created ${JSON.stringify(response)}`);
    return response;

  } catch (error: any) {
    logger.error(`Error in getValidNCEProductsWithTermAndBillFrequencyDAL: ${error.message}`);
    throw error;
  }
}
