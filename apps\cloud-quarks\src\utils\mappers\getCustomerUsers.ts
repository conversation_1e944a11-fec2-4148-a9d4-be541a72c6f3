export function normalizeQueryParams(
  query: Partial<Record<string, any>>,
  keys: string[]
): Record<string, string> {
  const normalized: Record<string, string> = {}

  keys.forEach((key) => {
    const value = query[key]
    normalized[key] = typeof value === 'string' && value.trim() !== '' ? value : ''
  })

  return normalized
}

function toPascalCase(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1)
}

function convertKeysToPascalCase(obj: any): any {
  if (Array.isArray(obj)) {
    return obj.map(convertKeysToPascalCase)
  } else if (obj !== null && typeof obj === 'object') {
    return Object.keys(obj).reduce((acc: any, key) => {
      const pascalKey = toPascalCase(key)
      acc[pascalKey] = convertKeysToPascalCase(obj[key])
      return acc
    }, {})
  }
  return obj
}

export function toPascalCaseJson(data: any): any {
  return convertKeysToPascalCase(data)
}
