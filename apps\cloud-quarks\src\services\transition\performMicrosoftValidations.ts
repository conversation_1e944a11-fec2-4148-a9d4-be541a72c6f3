import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import { getMethod } from "../externalService/externalEndPointService";
import { MicrosoftEndpoints } from "../../config/microsoftEndpoints";
import {
  ResponseStatus,
  MicrosoftValidationRequest,
  PromotionEligibilityRequest,
  CheckMPNIDResponse,
  ResponseError
} from "../../types/responses/customResponse";
import { verifyPromotionEligibilityService } from "../renewal/verifyPromotionEligibility";

/**
 * Perform Microsoft Validations
 * Matches .NET PerformMicrosoftValidations method
 */
export async function performMicrosoftValidations(
  req: MedusaRequest,
  microsoftValidationRequest: MicrosoftValidationRequest,
  token: string
): Promise<ResponseStatus> {
  let response: ResponseStatus = { Status: false };

  // Validate MPNID
  logger.info(`Entering into ValidateMPNId method with mpnId-${microsoftValidationRequest.MPNId} and Token`);
  response = await validateMPNId(req, microsoftValidationRequest.MPNId || "", token);
  logger.info(`Response from ValidateMPNId method with response ${JSON.stringify(response)}`);
  if (!response.Status) {
    logger.info(`Entered ValidateMPNId Response is not success and returning with response ${JSON.stringify(response)}`);
    return response;
  }

  // Validate MPNID Agreement
  logger.info(`Entering into GetMpnIdAgreementDetail method with mpnId-${microsoftValidationRequest.MPNId} and Token`);
  response = await getMpnIdAgreementDetail(req, microsoftValidationRequest.MPNId || "", token);
  logger.info(`Response from GetMpnIdAgreementDetail method with response ${JSON.stringify(response)}`);
  if (!response.Status) {
    logger.info(`Entered GetMpnIdAgreementDetail Response is not success and returning with response ${JSON.stringify(response)}`);
    return response;
  }

  // Validate Promotions for NCE
  const promotionRequests = microsoftValidationRequest.PromotionEligibilityRequests || [];
  if (promotionRequests.length > 0) {
    const firstPromotion = promotionRequests[0];
    logger.info(`Checking If Request PromotionId is null Or empty. PromotionId-${firstPromotion.PromotionId}`);

    if (firstPromotion.PromotionId && firstPromotion.PromotionId.trim() !== '') {
      logger.info(`Entering into VerifyPromotionEligibility method with request-${JSON.stringify(promotionRequests)}, customerId-${microsoftValidationRequest.CustomerTenantId} and Token`);
      response = await verifyPromotionEligibility(req, promotionRequests, microsoftValidationRequest.CustomerTenantId || "", token);
      logger.info(`Response from VerifyPromotionEligibility method with response ${JSON.stringify(response)}`);
      if (!response.Status) {
        logger.info(`Entered VerifyPromotionEligibility Response is not success and returning with response ${JSON.stringify(response)}`);
        return response;
      }
    }
  }

  logger.info(`Exiting from PerformMicrosoftValidations with response ${JSON.stringify(response)}`);
  return response;
}

/**
 * Validate MPNID
 * Matches .NET ValidateMPNId method
 */
async function validateMPNId(
  req: MedusaRequest,
  mpnId: string,
  token: string
): Promise<ResponseStatus> {
  const response: ResponseStatus = { Status: false };

  logger.info(`Entered into ValidateMPNId method with mpnId-${mpnId} and Token`);

  logger.info(`Getting BaseUrl From Configuration`);
  const baseUrl = MicrosoftEndpoints.getMicrosoftBaseUrl();
  logger.info(`Got BaseUrl From Configuration-${baseUrl}`);

  logger.info(`Getting CheckMPNIDStatus Url From Configuration with mpnId-${mpnId}, and appending with BaseUrl`);
  const endPoint = MicrosoftEndpoints.checkMPNIDStatusUrl(mpnId);
  logger.info(`Got complete url for CheckMPNIDStatus : ${endPoint}`);

  logger.info(`Entering PrepareHeaderAuthorizationwithBearerToken with token`);
  const headerList = prepareHeaderAuthorizationwithBearerToken(token);
  logger.info(`Got Header list from PrepareHeaderAuthorizationwithBearerToken`);

  logger.info(`Going to hit ExternalEndPoint GetMethod method with url-${endPoint} and headerlist with authorization Bearer token`);

  const configModule = req?.scope?.resolve("configModule");
  const validateMpinResponse = await getMethod(
    {
      url: endPoint,
      headers: headerList,
      isVendorHit: true,
      module: "ValidateMPNId",
    },
    configModule
  );

  logger.info(`Response from ExternalEndPoint GetMethod with response ${JSON.stringify(validateMpinResponse)}`);

  logger.info(`Entering if of ValidateMpinResponse.IsSuccessStatusCode`);
  if (validateMpinResponse.isSuccessStatusCode) {
    logger.info(`Entered if of ValidateMpinResponse.IsSuccessStatusCode`);

    logger.info(`Deserialising ValidateMpinResponse.Content-${validateMpinResponse.content} to CheckMPNIDResponse`);
    try {
      const mpnidResponse: CheckMPNIDResponse = JSON.parse(validateMpinResponse.content);
      logger.info(`Deserialising completed - ${JSON.stringify(mpnidResponse)}`);

      logger.info(`Checking If mpnidResponse is null and mpnidResponse.MpnId is null or Empty`);
      if (!mpnidResponse || !mpnidResponse.MpnId || mpnidResponse.MpnId.trim() === '') {
        logger.info(`Entered If mpnidResponse is null and mpnidResponse.MpnId is null or Empty`);
        response.Status = false;
        response.Message = "No Response From Validate MPIN API";
        logger.info(`Exiting If mpnidResponse is null and mpnidResponse.MpnId is null or Empty with response-${JSON.stringify(response)}`);
      } else {
        logger.info(`Entered Else of mpnidResponse is null and mpnidResponse.MpnId is null or Empty and set Status as true`);
        response.Status = true;
        logger.info(`Exiting Else of mpnidResponse is null and mpnidResponse.MpnId is null or Empty with response-${JSON.stringify(response)}`);
      }
    } catch (parseError) {
      logger.error(`Error parsing MPNID response: ${parseError}`);
      response.Status = false;
      response.Message = "Error parsing MPNID validation response";
    }

    logger.info(`Exiting if of ValidateMpinResponse.IsSuccessStatusCode`);
  } else {
    logger.info(`Entered else of ValidateMpinResponse.IsSuccessStatusCode`);

    logger.info(`Deserialising ValidateMpinResponse.Content-${validateMpinResponse.content} to ResponseError`);
    try {
      const responseError: ResponseError = JSON.parse(validateMpinResponse.content);
      logger.info(`Deserialising completed - ${JSON.stringify(responseError)}`);

      response.Status = false;
      response.Message = responseError.description;
    } catch (parseError) {
      logger.error(`Error parsing error response: ${parseError}`);
      response.Status = false;
      response.Message = validateMpinResponse.errorMessage || "Unknown error occurred";
    }
    logger.info(`Exiting else of ValidateMpinResponse.IsSuccessStatusCode with response-${JSON.stringify(response)}`);
  }

  logger.info(`Exiting from ValidateMPNId method with response-${JSON.stringify(response)}`);
  return response;
}

/**
 * Prepare Header Authorization with Bearer Token
 * Matches .NET PrepareHeaderAuthorizationwithBearerToken method
 */
function prepareHeaderAuthorizationwithBearerToken(token: string): Record<string, string> {
  const headerList: Record<string, string> = {};
  headerList["Authorization"] = `Bearer ${token}`;
  return headerList;
}

/**
 * Get MPN ID Agreement Detail
 * Placeholder for .NET GetMpnIdAgreementDetail method
 * TODO: Implement this method based on .NET implementation
 */
async function getMpnIdAgreementDetail(
  req: MedusaRequest,
  mpnId: string,
  token: string
): Promise<ResponseStatus> {
  logger.info(`Entered into GetMpnIdAgreementDetail method with mpnId-${mpnId} and Token`);

  // TODO: Implement the actual logic for MPN ID agreement validation
  // This is a placeholder implementation
  const response: ResponseStatus = {
    Status: true,
    Message: "MPN ID Agreement validation not yet implemented"
  };

  logger.info(`Exiting from GetMpnIdAgreementDetail method with response-${JSON.stringify(response)}`);
  return response;
}

/**
 * Verify Promotion Eligibility
 * Matches .NET VerifyPromotionEligibility method
 */
async function verifyPromotionEligibility(
  req: MedusaRequest,
  promotionEligibilityRequests: PromotionEligibilityRequest[],
  customerTenantId: string,
  token: string
): Promise<ResponseStatus> {
  logger.info(`Entered into VerifyPromotionEligibility method with request-${JSON.stringify(promotionEligibilityRequests)}, customerId-${customerTenantId} and Token`);

  try {
    // Call the actual promotion eligibility service
    const eligibilityResponse = await verifyPromotionEligibilityService(
      promotionEligibilityRequests,
      customerTenantId,
      token
    );

    // Check if any promotion is not eligible
    const hasIneligiblePromotions = eligibilityResponse.Items?.some(item =>
      item.Eligibilities?.some(eligibility => !eligibility.IsEligible)
    );

    const response: ResponseStatus = {
      Status: !hasIneligiblePromotions,
      Message: hasIneligiblePromotions
        ? "One or more promotions are not eligible"
        : "All promotions are eligible"
    };

    logger.info(`Exiting from VerifyPromotionEligibility method with response-${JSON.stringify(response)}`);
    return response;

  } catch (error: any) {
    logger.error(`Error in verifyPromotionEligibility: ${error.message}`);

    const response: ResponseStatus = {
      Status: false,
      Message: error.message || "Error verifying promotion eligibility"
    };

    logger.info(`Returning error response from VerifyPromotionEligibility method with response ${JSON.stringify(response)}`);
    return response;
  }
}