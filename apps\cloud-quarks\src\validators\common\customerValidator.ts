import logger from "../../utils/logger";
import { getSqlServerConnection } from "../../utils/sqlServerClient";
import sql from "mssql";

/**
 * Customer Validator
 * Matches .NET CustomerValidator functionality
 */
export async function validateCustomer(custId: string): Promise<{ isValid: boolean; errors: string[] }> {
  logger.info(`Validating customer with custId: ${custId}`);
  
  const errors: string[] = [];

  if (!custId || custId.trim() === '') {
    errors.push('CustId is required');
    return { isValid: false, errors };
  }

  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();

    // Check if customer exists (matching .NET customer validation logic)
    request.input('CustId', sql.VarChar(50), custId);
    
    const result = await request.query(`
      SELECT COUNT(*) as CustomerCount 
      FROM Customers 
      WHERE CustId = @CustId AND IsActive = 1
    `);

    const customerCount = result.recordset[0]?.CustomerCount || 0;

    if (customerCount === 0) {
      errors.push(`Customer with ID '${custId}' does not exist or is inactive`);
    }

    logger.info(`Customer validation completed for custId: ${custId}, isValid: ${errors.length === 0}`);
    
    return {
      isValid: errors.length === 0,
      errors
    };
  } catch (error: any) {
    logger.error(`Error validating customer: ${error.message}`);
    errors.push('Customer validation failed due to database error');
    return { isValid: false, errors };
  }
}
