import { ExecArgs } from '@medusajs/framework/types';

import seedAnswerType from './seed-answer-type';
import seedQuestion from './seed-question';
import seedBrands from './seed-brand';
import seedBrandCategories from './seed-brand-category';
import seedBrandBrandCategory from './seed-brand-brand-category';
import seedVendor from './seed-vendor';
import { logger } from '@medusajs/framework';

const getRegionFromArgs = (): string => {
  const arg = process.argv.find((arg) => arg.startsWith('--region='));
  return arg?.split('=')[1] ?? 'default';
};

export default async function runSeeder({ container }: ExecArgs) {
  const inputRegion = getRegionFromArgs();

  const seederConfig = {
    default: [

    ],
    india: [
      seedVendor,
      seedAnswerType,
      seedQuestion,
      seedBrands,
      seedBrandCategories,
      seedBrandBrandCategory,
    ],
    mea: [
      seedVendor,
      seedAnswerType,
      seedQuestion,
      seedBrands,
      seedBrandCategories,
      seedBrandBrandCategory,
    ],
    turkey: [
      seedVendor,
      seedAnswerType,
      seedQuestion,
      seedBrands,
      seedBrandCategories,
      seedBrandBrandCategory,
    ],
  } satisfies Record<string, Array<(args: ExecArgs) => Promise<void>>>;

  type Region = keyof typeof seederConfig;

  const isValidRegion = (region: string): region is Region => {
    return region in seederConfig;
  };

  const region: Region = isValidRegion(inputRegion) ? inputRegion : 'default';
  logger.info(` Starting seed for region: "${region}"`);

  for (const seeder of seederConfig[region]) {
    await seeder({ container } as ExecArgs);
  }

  logger.info(`Completed seeding for region: "${region}"`);
}
