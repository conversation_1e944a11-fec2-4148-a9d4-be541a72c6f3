import {
	AuthenticatedMedusaRequest,
	MedusaRequest,
	MedusaResponse,
} from '@medusajs/framework/http';
import { COMPANY_REGISTRATION_MODULE } from '../../../../../modules/company';

import { CompanyOnboardingSchema } from './validator';
import { formatPagination, stringConstants } from '@org/utils';
import CompanyRegistrationService from '../../../../../modules/company/service';
import createCompanyOnboardingWorkflow from '../../../../../workflows/company-onboarding';
import { ContainerRegistrationKeys } from '@medusajs/framework/utils';
import {
	getCompanyRegistrationWorkflow,
	getPendingStatusIdWorkflow,
} from '../../../../../workflows/company-registration';
import { ExportedCompanyOnboardingRequest } from '../../../../../../../../libs/util-excel/src/lib/types';
import {
	ExcelColumn,
	generateExcelBuffer,
	SourceKey,
	allowedSources,
} from '@org/excel';

/**
 * @openapi
 * /v1/company/company-registration/company-onboarding:
 *   post:
 *     summary: Submit company onboarding information
 *     tags:
 *       - Company Onboarding
 *     parameters:
 *       - in: query
 *         name: source
 *         required: true
 *         schema:
 *           type: string
 *         description: The source of the guest user (e.g., partner or directEndCustomer)
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - redington_account_id
 *               - country
 *               - camunda_process_id
 *               - company_name
 *               - address1
 *               - country_of_business
 *               - state
 *               - city
 *               - postal_code
 *               - director_first_name
 *               - director_last_name
 *               - director_email
 *               - director_country_code
 *               - director_mobile_number
 *               - sales_first_name
 *               - sales_last_name
 *               - sales_email
 *               - sales_country_code
 *               - sales_mobile_number
 *               - accounts_first_name
 *               - accounts_last_name
 *               - accounts_email
 *               - accounts_country_code
 *               - accounts_mobile_number
 *               - gst_number
 *               - requested_by_admin
 *               - created_by
 *               - updated_by
 *             properties:
 *               redington_account_id:
 *                 type: string
 *                 example: "**********"
 *               country:
 *                 type: string
 *                 example: "India"
 *               camunda_process_id:
 *                 type: string
 *                 example: "cmp-process-123"
 *               company_name:
 *                 type: string
 *                 example: "Hexalytics LLC"
 *               address1:
 *                 type: string
 *                 example: "123 Industrial Road"
 *               address2:
 *                 type: string
 *                 example: "Suite 456"
 *               country_of_business:
 *                 type: string
 *                 example: "India"
 *               state:
 *                 type: string
 *                 example: "Tamil Nadu"
 *               city:
 *                 type: string
 *                 example: "Chennai"
 *               postal_code:
 *                 type: string
 *                 example: "600001"
 *               website:
 *                 type: string
 *                 format: uri
 *                 example: "https://hexalytics.com"
 *               director_first_name:
 *                 type: string
 *                 example: "John"
 *               director_last_name:
 *                 type: string
 *                 example: "Doe"
 *               director_email:
 *                 type: string
 *                 format: email
 *                 example: "<EMAIL>"
 *               director_country_code:
 *                 type: string
 *                 example: "+91"
 *               director_mobile_number:
 *                 type: string
 *                 example: "9876543210"
 *               sales_first_name:
 *                 type: string
 *                 example: "Jane"
 *               sales_last_name:
 *                 type: string
 *                 example: "Smith"
 *               sales_email:
 *                 type: string
 *                 format: email
 *                 example: "<EMAIL>"
 *               sales_country_code:
 *                 type: string
 *                 example: "+91"
 *               sales_mobile_number:
 *                 type: string
 *                 example: "**********"
 *               accounts_first_name:
 *                 type: string
 *                 example: "Alice"
 *               accounts_last_name:
 *                 type: string
 *                 example: "Brown"
 *               accounts_email:
 *                 type: string
 *                 format: email
 *                 example: "<EMAIL>"
 *               accounts_country_code:
 *                 type: string
 *                 example: "+91"
 *               accounts_mobile_number:
 *                 type: string
 *                 example: "**********"
 *               authorized_signatories:
 *                 type: array
 *                 maxItems: 3
 *                 items:
 *                   type: object
 *                   required:
 *                     - first_name
 *                     - last_name
 *                     - email
 *                     - country_code
 *                     - mobile_number
 *                   properties:
 *                     first_name:
 *                       type: string
 *                       example: "Deepa"
 *                     last_name:
 *                       type: string
 *                       example: "Reddy"
 *                     email:
 *                       type: string
 *                       format: email
 *                       example: "<EMAIL>"
 *                     country_code:
 *                       type: string
 *                       example: "+91"
 *                     mobile_number:
 *                       type: string
 *                       example: "9876500006"
 *               gst_number:
 *                 type: string
 *                 example: "29ABCDE1234F1Z5"
 *               user_brand_preference:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - vendor_id
 *                     - brand_id
 *                     - brand_category_id
 *                   properties:
 *                     vendor_id:
 *                       type: string
 *                       example: "01JXPYZ8DNM6N2DYEP0214QYGV"
 *                     brand_id:
 *                       type: string
 *                       example: "01JXPYZ8EGAB0VBQCBKFYVFHM0"
 *                     brand_category_id:
 *                       type: string
 *                       example: "01JXPYZ8G7TJJ5YEN377QFAZ89"
 *               requested_by_admin:
 *                 type: boolean
 *                 example: true
 *               requested_source:
 *                 type: string
 *                 enum: [partner, directEndCustomer]
 *                 example: "partner"
 *               reason:
 *                 type: string
 *                 example: "Awaiting verification"
 *               store_id:
 *                 type: string
 *                 example: "store-uuid-001"
 *               metadata:
 *                 type: object
 *                 example:
 *                   source: "partner_portal"
 *                   lead_type: "premium"
 *               created_by:
 *                 type: string
 *                 example: "admin-user-uuid"
 *               updated_by:
 *                 type: string
 *                 example: "admin-user-uuid"
 *               deleted_by:
 *                 type: string
 *                 example: "admin"
 *     responses:
 *       200:
 *         description: Company onboarding submitted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Company onboarding submitted successfully."
 *       400:
 *         description: Validation or input error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Invalid source. Must be one of: partner, directEndCustomer"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 */

/**
 * @route POST /company/v1/company-onboarding
 * @group Company - Handles onboarding of a company
 * @param {CompanyOnboardingSchema.model} body.body.required - Onboarding data including signatories and brand preferences
 * @returns {object} 200 - Onboarding successful
 * @returns {Error} 500 - Internal server error
 */
export const POST = async (
	req: AuthenticatedMedusaRequest<typeof CompanyOnboardingSchema>,
	res: MedusaResponse
) => {
	const rawSource = req.query.source;

	const source = rawSource
		? String(Array.isArray(rawSource) ? rawSource[0] : rawSource)
		: undefined;

	if (!source || !allowedSources.includes(source as SourceKey)) {
		return res.status(400).json({
			message: `Invalid source. Must be one of: ${allowedSources.join(', ')}`,
		});
	}

	const companyRegistrationService: CompanyRegistrationService =
		req.scope.resolve(COMPANY_REGISTRATION_MODULE);

	const parsed = CompanyOnboardingSchema.safeParse(req.body);

	if (!parsed.success) {
		return res.status(400).json({
			message: 'Invalid request body',
			errors: parsed.error.flatten(),
		});
	}

	const input = parsed.data;
	const { user_brand_preference, authorized_signatories, ...companyData } =
		input;

	const redingtonAccountNumber = companyData.redington_account_id;
	if (!/^\d{10}$/.test(redingtonAccountNumber)) {
		return res.status(400).json({
			message: stringConstants.VERIFY_REDINGTON_ACCOUNT(),
		});
	}

	try {
		const { result: pendingStatusId } = await getPendingStatusIdWorkflow(
			req.scope
		).run();
		// const { actor_id: user_id } = req.auth_context;

		// 1. Onboard company
		const { result: companyRegistration } =
			await createCompanyOnboardingWorkflow(req.scope).run({
				input: {
					companyOnboarding: {
						...companyData,
						// user_id: user_id,
						status_id: pendingStatusId,
						requested_source: source,
						authorized_signatories,
						user_brand_preference,
					} as any,
				},
			});

		const companyOnboardingId = companyRegistration.id;

		// 2. Store authorized signatories
		if (Array.isArray(authorized_signatories)) {
			if (authorized_signatories.length > 3) {
				return res.status(400).json({
					message: 'A maximum of 3 authorized signatories are allowed.',
				});
			}

			for (const signatory of authorized_signatories) {
				await companyRegistrationService.createAuthorizedSignatories({
					...signatory,
					company_onboarding_id: companyOnboardingId,
				});
			}
		}

		// 3. Store brand preferences
		if (
			Array.isArray(user_brand_preference) &&
			user_brand_preference.length > 0
		) {
			for (const preference of user_brand_preference) {
				await companyRegistrationService.createUserBrandPreferences({
					vendor_id: preference.vendor_id,
					brand_id: preference.brand_id,
					brand_category_id: preference.brand_category_id,
					company_onboarding_id: companyOnboardingId,
					company_registration_id: null,
				});
			}
		}

		// 4. Create Mapping to User Persona for Future purpose
		// try {
		// 	const personas = (await guestUserService.listCqPersonas?.({})) || [];

		// 	const personaKey = source.trim().toLowerCase().replace(/\s+/g, '_'); // "Direct End Customer" => "direct_end_customer"
		// 	const matchedPersona = personas.find(
		// 		(p: any) => p.key?.toLowerCase() === personaKey
		// 	);

		// 	const currentUserId = req?.auth_context?.actor_id;

		// 	console.log(currentUserId, 'currentUserId');

		// 	if (matchedPersona && currentUserId) {
		// 		// ✅ Check if user already mapped to this persona
		// 		const [existing] = await guestUserService.listAndCountCqUserPersonas({
		// 			user_id: currentUserId,
		// 			persona_id: matchedPersona.id,
		// 		});

		// 		if (existing.length === 0) {
		// 			// ❇️ Create mapping only if it doesn't exist
		// 			await guestUserService.createCqUserPersonas({
		// 				user_id: currentUserId,
		// 				persona_id: matchedPersona.id,
		// 			});
		// 		} else {
		// 			console.info(
		// 				`User ${currentUserId} already mapped to persona "${personaKey}". Skipping creation.`
		// 			);
		// 		}
		// 	} else {
		// 		console.warn(
		// 			`Persona mapping skipped: source="${source}", userId=${currentUserId}, personaKey="${personaKey}"`
		// 		);
		// 	}
		// } catch (personaErr) {
		// 	console.warn('Error during persona mapping:', personaErr);
		// }

		return res.status(200).json({
			message: stringConstants.ONBOARDING_SUCCESS(),
		});
	} catch (err: any) {
		console.error('Error during Company Onboarding:', {
			message: err?.message,
			stack: err?.stack,
		});
		return res.status(500).json({
			message: stringConstants.INTERNAL_SERVER_ERROR(),
		});
	}
};

/**
 * @openapi
 * /v1/company/company-registration/company-onboarding:
 *   get:
 *     summary: Get a list of onboarded companies
 *     description: |
 *       Retrieves a paginated list of onboarded companies along with associated user details.
 *       Supports filtering via pagination parameters and optional Excel export via a query flag.
 *     tags:
 *       - Company Onboarding
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: The page number to retrieve (default is 1)
 *       - in: query
 *         name: page_size
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: The number of records per page (default is 10)
 *       - in: query
 *         name: download
 *         schema:
 *           type: string
 *           enum: [excel]
 *         description: Set to `excel` to download the result as an Excel file
 *     responses:
 *       200:
 *         description: Successfully fetched onboarded company list
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       first_name:
 *                         type: string
 *                       last_name:
 *                         type: string
 *                       email:
 *                         type: string
 *                       company_name:
 *                         type: string
 *                       redington_account_id:
 *                         type: string
 *                       requested_by_admin:
 *                         type: boolean
 *                       requested_source:
 *                         type: string
 *                       status:
 *                         type: string
 *                       role:
 *                         type: string
 *                       store:
 *                         type: string
 *                       country:
 *                         type: string
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     pageSize:
 *                       type: integer
 *                     totalItems:
 *                       type: integer
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 */
/**
 * @route GET /v1/company/company-registration/company-onboarding
 * @group Company - Handles company onboarding
 * @param {string} page.query - Page number for pagination
 * @param {string} page_size.query - Number of items per page
 * @param {string} download.query - Set to 'excel' for Excel export
 * @returns {object} 200 - Successfully fetched company Onboarding
 * @returns {Error} 500 - Internal server error
 * @security BearerAuth
 * @description Fetches a list of all company registrations with associated user details.
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
	try {
		const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

		// 🔢 Parse pagination parameters
		let page = parseInt(req.query.page as string, 10);
		let pageSize = parseInt(req.query.page_size as string, 10);
		const isDownload = req.query.download === 'excel';
		if (isNaN(page) || page < 1) page = 1;
		if (isNaN(pageSize) || pageSize < 1) pageSize = 10;

		const skip = (page - 1) * pageSize;

		// 🧾 Fetch company registrations (all for now, pagination can be optimized at query level)
		const { data: companyOnboarding, metadata } = await query.graph({
			entity: 'cq_company_onboarding',
			fields: ['*'],
			pagination: {
				skip,
				take: pageSize,
			},
		});

		// 👤 Fetch related users
		const { result: users } = await getCompanyRegistrationWorkflow(
			req.scope
		).run({});

		// 🗺️ Map user data by user_id
		const userMap: Record<string, any> = {};
		for (const user of users) {
			userMap[user.id] = user;
		}

		// 🔗 Merge user data into company registrations
		const mergedResult = companyOnboarding.map((reg: any) => ({
			...reg,
			user: userMap[reg.user_id] || null,
		}));

		// 📦 Excel Export
		if (isDownload) {
			const columns: ExcelColumn<ExportedCompanyOnboardingRequest>[] = [
				{ header: 'Registration ID', key: 'id', width: 10 },
				{ header: 'Country', key: 'country', width: 25 },
				{ header: 'First Name', key: 'first_name', width: 15 },
				{ header: 'Last Name', key: 'last_name', width: 15 },
				{ header: 'Company Type', key: 'company_type', width: 25 },
				{ header: 'Email', key: 'email', width: 20 },
				{ header: 'Company Name', key: 'company_name', width: 20 },
				{
					header: 'Redington Account ID',
					key: 'redington_account_id',
					width: 20,
				},
				{ header: 'Store', key: 'store', width: 25 },
				{ header: 'Role', key: 'role', width: 25 },
				{ header: 'Requested By', key: 'requested_by_admin', width: 15 },
				{ header: 'Requested Source', key: 'requested_source', width: 15 },
				{ header: 'Status', key: 'status', width: 15 },
			];

			const transformedData: ExportedCompanyOnboardingRequest[] =
				mergedResult.map((user: any) => ({
					id: user.id,
					first_name: user.first_name ?? '',
					last_name: user.last_name ?? '',
					company_type: user.source ?? '',
					email: user.email ?? '',
					store: user.country?.cq_stores?.[0]?.name ?? '',
					company_name: user.company_name ?? '',
					redington_account_id: user.redington_account_id ?? '',
					role: user.role ?? '',
					country: user.country?.display_name ?? '',
					requested_by_admin: user.requested_by_admin ?? false,
					status: user.is_active ? 'Active' : 'Inactive',
					requested_source: user.requested_source ?? 'Direct End Customer', // ✅ Add this
				}));

			const buffer = await generateExcelBuffer(
				transformedData,
				columns,
				'User'
			);

			res.setHeader(
				'Content-Type',
				'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
			);
			res.setHeader('Content-Disposition', 'attachment; filename="user.xlsx"');
			return res.send(buffer);
		}

		const totalItems = metadata?.count ?? 0;
		return res.status(200).json({
			data: mergedResult,
			pagination: formatPagination({ page, pageSize, totalItems }),
		});
	} catch (err: any) {
		console.error('Error fetching company registration:', {
			message: err?.message,
			stack: err?.stack,
		});

		return res.status(500).json({
			message: err.message || 'Internal server error',
		});
	}
};
