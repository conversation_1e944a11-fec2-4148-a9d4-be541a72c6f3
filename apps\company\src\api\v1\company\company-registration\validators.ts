import { z } from 'zod';

export const RegionSchema = z.object({
	region_id: z.string().min(1, 'Region is required'),
});

const AddressSchema = z.object({
	street1: z.string().optional(),
	street2: z.string().optional(),
	country_business: z.string().optional(),
	state: z.string().optional(),
	city: z.string().optional(),
	postal_code: z.string().optional(),
	legal_status: z.string().optional(),
});

const ContactInfoSchema = z.object({
	mobile_number: z.string().optional(),
	country_code: z.string().optional(),
});

const CompanyInfoSchema = z.object({
	gst_number: z.string().optional(),
	pan_number: z.string().optional(),
	cin_number: z.string().min(1, 'CIN number is required'),
	company_name: z.string().optional(),
	address: AddressSchema,
	contact_info: ContactInfoSchema,
	website: z.string().optional(),
});

const AdditionalInfoSchema = z.object({
	number_of_offices_in_region: z.string(),
	other_countries_with_offices: z.array(z.string()).optional(),
	number_of_warehouses_in_region: z.number().nonnegative(),
	number_of_employees: z.number().nonnegative(),
	number_of_sales_staff: z.number().nonnegative(),
	number_of_technical_staff: z.number().nonnegative(),
	twitter_account: z.string().url().optional(),
	facebook_account: z.string().url().optional(),
	linkedin_account: z.string().url().optional(),
	instagram_account: z.string().url().optional(),
});

const PersonWithContactSchema = z.object({
	first_name: z.string().min(1),
	last_name: z.string().min(1),
	email: z.string().email(),
	contact_info: ContactInfoSchema,
});

const BusinessInfoSchema = z.object({
	director_owner: PersonWithContactSchema,
	sales: PersonWithContactSchema,
	accounts_operations: PersonWithContactSchema,
	authorized_signatories: z.array(PersonWithContactSchema).max(3, {
		message: 'A maximum of 3 authorized signatories are allowed',
	}),
});

const DocumentsSchema = z.array(
	z.object({
		document_id: z.string(),
		document_uploaded_link: z.string(),
		description: z.string(),
		// user_id: z.string(),
	})
);

const AuthorizedSignatorySchema = z.object({
	first_name: z.string(),
	last_name: z.string(),
	email: z.string().email(),
	country_code: z.string(),
	mobile_number: z.string(),
});

const PartnerProfileSchema = z.array(
	z.object({
		questions_id: z.string(),
		answer_id: z.string(),
		terms_and_conditions: z.boolean(),
	})
);

const UserBrandPreferencesSchema = z.array(
	z.object({
		vendor_id: z.string(),
		brand_id: z.string(),
		brand_category_id: z.string(),
	})
);

export const RegistrationSchema = z.object({
	gst_number: z.string().min(1, 'GST number is required'),
	pan_number: z.string().min(1, 'PAN number is required'),
	part_of_sez: z.boolean(),
	cin_number: z.string().min(1, 'CIN number is required'),
	company_name: z.string().min(1, 'Company name is required'),
	address_1: z.string().min(1, 'Address Line 1 is required'),
	address_2: z.string().optional(),
	country_of_business: z.string().min(1),
	postal_code: z.string().min(1),
	address_name: z.string().min(1),
	state: z.string().min(1),
	city: z.string().min(1),
	country_code: z.string().min(1),
	contact_number: z.string().min(1),
	legal_status_of_company: z.string(),
	website: z.string().url().optional(),
	social_media_account: z.string().optional(),
	number_of_offices_in_region: z.number().nonnegative(),
	other_countries_offices: z.array(z.string()).optional(),
	number_of_warehouses_in_region: z.number().nonnegative(),
	number_of_employees: z.number().nonnegative(),
	number_of_sales_staff: z.number().nonnegative(),
	number_of_technical_staff: z.number().nonnegative(),
	twitter_account_url: z.string().url().optional(),
	linkedin_account_url: z.string().url().optional(),
	facebook_account_url: z.string().url().optional(),
	instagram_account_url: z.string().url().optional(),
	director_first_name: z.string().min(1),
	director_last_name: z.string().min(1),
	director_email: z.string().email(),
	director_country_code: z.string().min(1),
	director_mobile_number: z.string().min(1),
	sales_first_name: z.string().min(1),
	sales_last_name: z.string().min(1),
	sales_email: z.string().email(),
	sales_country_code: z.string().min(1),
	sales_mobile_number: z.string().min(1),
	accounts_first_name: z.string().min(1),
	accounts_last_name: z.string().min(1),
	accounts_email: z.string().email(),
	accounts_country_code: z.string().min(1),
	accounts_mobile_number: z.string().min(1),
	requested_by_admin: z.boolean(),
	// To do store mapping
	store_id: z.string(),
	reason: z.string().optional(),
	redington_account_id: z.string().nullable().optional(),
	authorized_signatories: z.array(AuthorizedSignatorySchema),
	company_document: DocumentsSchema,
	profile_info: PartnerProfileSchema,
	user_brand_preferences: UserBrandPreferencesSchema,

	created_by: z.string().optional(),
	updated_by: z.string().optional(),
	deleted_by: z.string().optional(),
});

const validStateCodes = [
	'01',
	'02',
	'03',
	'04',
	'05',
	'06',
	'07',
	'08',
	'09',
	'10',
	'11',
	'12',
	'13',
	'14',
	'15',
	'16',
	'17',
	'18',
	'19',
	'20',
	'21',
	'22',
	'23',
	'24',
	'25',
	'26',
	'27',
	'28',
	'29',
	'30',
	'31',
	'32',
	'33',
	'34',
	'35',
	'36',
	'37',
];

export const GSTSchema = z.object({
	gstNo: z.string().superRefine((val, ctx) => {
		const isValidLength = val.length === 15;
		const matchesFormat =
			/^[0-3][0-9][A-Z]{5}[0-9]{4}[A-Z][1-9A-Z]Z[0-9A-Z]$/.test(val);
		const hasValidStateCode = validStateCodes.includes(val.substring(0, 2));

		if (!(isValidLength && matchesFormat && hasValidStateCode)) {
			ctx.addIssue({
				code: z.ZodIssueCode.custom,
				message: 'Please enter a valid GST number',
			});
		}
	}),
});
