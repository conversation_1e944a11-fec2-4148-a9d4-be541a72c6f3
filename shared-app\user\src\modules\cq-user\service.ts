import { MedusaService } from '@medusajs/framework/utils';
import CQUser from './models/cq_user';
import Designation from './models/cq_designation';
import EmailOtpVerification from './models/cq_email_otp_verification';
import CqStore from './models/store';
import Status from './models/cq_status';
import CompanyType from './models/cq_company_type';
import { InjectManager, MedusaContext } from '@medusajs/framework/utils';
import { Context } from '@medusajs/framework/types';
import { EntityManager } from '@mikro-orm/knex';
import CqModule from './models/module';
import CqPagePermission from './models/page-permission';
import CqPage from './models/page';
import CqPermission from './models/permission';
import CqPersona from './models/persona';
import CqRolePagePermission from './models/role-page-permission';
import CqRolePage from './models/role-page';
import CqRole from './models/role';
import CqUserPersona from './models/user-persona';
import CqUserRolePagePermission from './models/user-role-page_permission';
import CqUserRolePage from './models/user-role-page';
import CqUserRoles from './models/user-roles';
import CqCountryExtension from './models/country_extension';
import CqColumnMaster from './models/column-master';
import CqUserColumnPreference from './models/user-column-preference';
import CqDocument from './models/cq_document';

export default class CQUserService extends MedusaService({
  CqUser: CQUser,
  Designation,
  EmailOtpVerification,
  CqStore,
  Status,
  CompanyType,
  CqModule,
  CqPagePermission,
  CqPage,
  CqPermission,
  CqPersona,
  CqRolePagePermission,
  CqRolePage,
  CqRole,
  CqUserPersona,
  CqUserRolePagePermission,
  CqUserRolePage,
  CqUserRoles,
  CqCountryExtension,
  CqColumnMaster,
  CqUserColumnPreference,
  CqDocument
}) {
  @InjectManager()
  async fetchUsersRaw(@MedusaContext() sharedContext?: Context<EntityManager>) {
    const data = await sharedContext?.manager?.execute(`
      SELECT
        u.id AS user_id,
        u.first_name AS user_name,
        r.id AS registration_id,
        r.gst_number,
        r.status
    FROM
        cq_user u
    LEFT JOIN
        mea_schema.cq_company_registration r
    ON u.id = r.user_id
    ORDER BY u.id;
    `);
    return data;
  }
}
