import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import { 
  GoogleEndCustomerVerifyDomainResponse, 
  GoogleDomainExistsResponse, 
  CloudIdentityAccount,
  GoogleErrorResponse 
} from "../../types/responses/customResponse";
import { getGoogleTokenService } from "../token/google/getGoogleTokenService";
import { createGoogleRequestHeaders } from "../../config/microsoftEndpoints";
import { GoogleEndpoints } from "../../config/googleEndpoints";
import { PostMethodWithRequestJsonBodyAndHeaders400Handle } from "../externalService/externalEndPointService";
import { getGoogleAccountIdFromStore } from "../googleOffer/getGooglePurchasableOfferService";

// Messages constants (matching .NET Messages class)
export const Messages = {
  DomainNotExists: (domain: string) => `Domain ${domain} does not exist`,
  DomainAvailable: "Domain is available for registration",
  DomainAvailableForLinking: "Domain is available for linking",
  DomainExistsNotOwned: (domain: string) => `Domain ${domain} exists but is not owned by you`,
  DomainAlreadyExists: "Domain already exists",
  ExceptionMessage: (message: string) => `An error occurred: ${message}`,
  CustomerExistingNotMatched: (domain: string) => `Customer existing details do not match for domain ${domain}`,
  CustomerExistingCreated: "Customer existing created successfully",
  CustomerCreated: "Customer created successfully",
  ChannelPartnerIdInActive: (cpId: string, additionalMessage: string = "") => `Channel Partner ID ${cpId} is inactive${additionalMessage}`,
  CloudIdentityMappingNotSet: (additionalMessage: string = "") => `Cloud Identity mapping is not set${additionalMessage}`,
  PurchasebleOfferNotFound: (additionalMessage: string = "") => `Purchasable offer not found${additionalMessage}`,
  TrialEntitlementNotExpired: (materialId: string, skuId: string) => `Trial entitlement for ${materialId} (SKU: ${skuId}) has not expired yet`,
  GenericExceptionMessage: "An unexpected error occurred",
};

export async function verifyDomainExistsService(
  domain: string,
  storeId: string,
  isExistingCustomer: boolean,
  req?: MedusaRequest
): Promise<GoogleEndCustomerVerifyDomainResponse> {
  logger.info(`Entered into VerifyDomainExists service method with domain:${domain}, storeId ${storeId}, isExistingCustomer ${isExistingCustomer}`);
  
  const googleResponseModel: GoogleEndCustomerVerifyDomainResponse = {
    Message: "",
    IsAvailable: false,
    IsLinkable: false,
    StatusCode: 200
  };

  try {
    logger.info(`Going to call DAL method GetGoogleAccountId with StoreId ${storeId}`);
    const googleAccountId = await getGoogleAccountIdFromStore(storeId);
    logger.info(`Got googleAccountId ${googleAccountId}`);

    // Build the verify domain URL
    const verifyGoogleDomainUrl = GoogleEndpoints.verifyDomainExistsUrl(googleAccountId);
    logger.info(`Verify domain URL: ${verifyGoogleDomainUrl}`);

    logger.info(`Going to hit GetGoogleToken method with storeId-${storeId}`);
    const token = await getGoogleTokenService(req, storeId);
    logger.info(`Got token from BL TokenService method.`);

    // Create Google request headers
    const headerList = req 
      ? createGoogleRequestHeaders(req, token, storeId)
      : {
          Authorization: `Bearer ${token}`,
          "x-region": storeId,
        };
    logger.info(`Prepared HeaderList for external call- ${JSON.stringify(headerList)}`);

    // Prepare request body
    const requestBody = { domain };
    const requestBodyJson = JSON.stringify(requestBody);
    logger.info(`Prepared requestBody for external call- ${requestBodyJson}`);

    logger.info(`Going to hit ExternalEndPoint PostMethodWithRequestJsonBodyAndHeaders400Handle method with url-${verifyGoogleDomainUrl}, RequestBody-${requestBodyJson}, headerlist-${JSON.stringify(headerList)}`);
    
    const configModule = req?.scope?.resolve("configModule");
    const response = await PostMethodWithRequestJsonBodyAndHeaders400Handle(
      {
        url: verifyGoogleDomainUrl,
        body: requestBodyJson,
        headers: headerList,
        isVendorHit: true,
        module: "VerifyDomainExists",
      },
      configModule
    );
    
    logger.info(`Response from ExternalEndPoint PostMethodWithRequestJsonBodyAndHeaders400Handle is ${JSON.stringify(response)}`);

    if (response.isSuccessStatusCode) {
      logger.info(`Entered into IsSuccessStatusCode true section. Going to deserialize content ${response?.content}`);
      
      const cloudIdentityAccountsExistResponse: GoogleDomainExistsResponse = JSON.parse(response.content);
      logger.info(`Deserialized object : ${JSON.stringify(cloudIdentityAccountsExistResponse)}`);

      if (!cloudIdentityAccountsExistResponse?.cloudIdentityAccounts) {
        logger.info(`Entered into section when cloudIdentityAccounts from Google is null`);
        
        googleResponseModel.IsAvailable = true;
        googleResponseModel.IsLinkable = false;
        googleResponseModel.Message = isExistingCustomer 
          ? Messages.DomainNotExists(domain) 
          : Messages.DomainAvailable;
      } else {
        logger.info(`Entered into section when cloudIdentityAccounts from Google is not null`);
        googleResponseModel.Data = {
          cloudIdentityAccounts: cloudIdentityAccountsExistResponse.cloudIdentityAccounts
        };

        const cloudIdentityAccount = cloudIdentityAccountsExistResponse.cloudIdentityAccounts[0];

        if (cloudIdentityAccount.Existing === true) {
          logger.info(`Entered into section when cloudIdentityAccounts Existing is true`);
          
          if (isExistingCustomer) {
            logger.info(`Entered into section Where isExistingCustomer = true`);

            if (cloudIdentityAccount.Owned === true) {
              logger.info(`Entered into section Where cloudIdentityAccounts Owned is true`);
              googleResponseModel.IsAvailable = false;
              googleResponseModel.IsLinkable = true;
              googleResponseModel.Message = Messages.DomainAvailableForLinking;
            } else {
              logger.info(`Entered into section Where cloudIdentityAccounts Owned is false`);
              googleResponseModel.IsAvailable = false;
              googleResponseModel.IsLinkable = false;
              googleResponseModel.Message = Messages.DomainExistsNotOwned(domain);
            }
          } else {
            logger.info(`Entered into section Where isExistingCustomer = false`);
            googleResponseModel.IsAvailable = false;
            googleResponseModel.IsLinkable = false;
            googleResponseModel.Message = Messages.DomainAlreadyExists;
          }
        }
      }
    } else {
      logger.info(`Entered into IsSuccessStatusCode false section.`);
      logger.info(`Received errored vendorResponse and deserialising vendorResponse.ErrorMessage-${response.errorMessage}`);

      const errorResponse: GoogleErrorResponse = JSON.parse(response.errorMessage || response.content || "{}");
      logger.info(`DeserializeObject ${JSON.stringify(errorResponse)}`);

      googleResponseModel.IsAvailable = false;
      googleResponseModel.IsLinkable = false;
      googleResponseModel.Message = errorResponse.Error?.Message || "Unknown error occurred";
      googleResponseModel.StatusCode = errorResponse.Error?.Code || 500;
    }

    logger.info(`Going to return vendorResponse from BL method with vendorResponse model ${JSON.stringify(googleResponseModel)}.`);
    return googleResponseModel;

  } catch (error: any) {
    logger.error(`Error in VerifyDomainExists service method. Message: ${error.message}, StackTrace: ${error.stack}`);
    
    googleResponseModel.Message = Messages.ExceptionMessage(error.message);
    googleResponseModel.IsAvailable = false;
    googleResponseModel.IsLinkable = false;
    googleResponseModel.StatusCode = 422; // UnprocessableEntity
    
    return googleResponseModel;
  }
}
