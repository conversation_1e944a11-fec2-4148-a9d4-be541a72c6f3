import { MedusaRequest } from "@medusajs/framework";
import { getStoreDetails } from "../token/microsoft/getStoreDetailsService";
import { getMsToken } from "../token/microsoft/getTokenService";
import {
  getMethod,
  PostMethodWithRequestJsonBodyAndHeaders400Handle
} from "../externalService/externalEndPointService";
import {
  MicrosoftEndpoints,
  prepareHeaderGetCustomerValidationStatus,
} from "../../config/microsoftEndpoints";
import logger from "../../utils/logger";
import { SetCustomerAgreementType } from "../../validators/customer/setCustomerAgreementValidator";
import {
  SetCustomerAgreementServiceResponse,
  GetMicAgreementResponse
} from "../../types/responses/customResponse";
import { ComparableString } from "../../validators/customer/customCustomerValidator";

// Utility function to transform camelCase request to PascalCase for Microsoft API
function transformToPascalCase(camelCaseRequest: SetCustomerAgreementType): any {
  return {
    PrimaryContact: {
      FirstName: camelCaseRequest.primaryContact.firstName,
      LastName: camelCaseRequest.primaryContact.lastName,
      Email: camelCaseRequest.primaryContact.email,
      PhoneNumber: camelCaseRequest.primaryContact.phoneNumber || "",
      MiddleName: camelCaseRequest.primaryContact.middleName || "",
      OrganizationRegistrationNumber: camelCaseRequest.primaryContact.organizationRegistrationNumber || "",
    },
    TemplateId: camelCaseRequest.templateId || "", // Will be populated from Microsoft API
    DateAgreed: camelCaseRequest.dateAgreed,
    Type: camelCaseRequest.type,
    AgreementLink: camelCaseRequest.agreementLink,
    UserId: camelCaseRequest.userId,
  };
}

export async function setCustomerAgreementService(
  req: MedusaRequest,
  storeId: string,
  brandId: string,
  custId: string,
  setCustomerAgreementRequest: SetCustomerAgreementType
): Promise<SetCustomerAgreementServiceResponse> {
  logger.info(
    `Entered into Business layer's setCustomerAgreement method using storeId-${storeId} & brandId-${brandId} & custId-${custId}`
  );

  try {
    // Get store details for token generation
    const tokenRequest = await getStoreDetails({ storeId, brandId });
    logger.info(`Response from BL GetStoreDetails is ${JSON.stringify(tokenRequest)}`);

    if (!tokenRequest) {
      logger.error(`Store details not found for storeId=${storeId}, brandId=${brandId}`);
      return {
        setCustomerAgreement: {
          status: false,
          message: "Store details not found",
        },
      };
    }

    // Check if brand is Microsoft
    if (ComparableString(tokenRequest.brand) !== "MICROSOFT") {
      logger.info(`Brand is not Microsoft: ${tokenRequest.brand}`);
      return {
        setCustomerAgreement: {
          status: false,
          message: "Brand is not Microsoft",
        },
      };
    }

    logger.info(`Going to hit BCommon GetToken method with tokenRequest-${JSON.stringify(tokenRequest)}`);

    // Generate Microsoft token
    const token = await getMsToken({
      brand: tokenRequest.brand || "",
      client_id: tokenRequest.clientid || "",
      client_secret: tokenRequest.clientsecret || "",
      grant_type: tokenRequest.granttype || "",
      markValue: tokenRequest.markvalue?.toString() || "",
      redirect_uri: tokenRequest.redirecturi || "",
      refresh_token: tokenRequest.token || "",
      resource: tokenRequest.resource || "",
      store_domain: tokenRequest.storedomain || "",
    });

    if (!token || !token.access_token) {
      logger.error(`Token generation failed`);
      return {
        setCustomerAgreement: {
          status: false,
          message: "Error While Generating Token",
        },
      };
    }

    // Prepare headers for Microsoft API calls
    const headerList = prepareHeaderGetCustomerValidationStatus(token.access_token);

    // Get Microsoft agreements to retrieve template ID
    const getMicAgreementUrl = MicrosoftEndpoints.getMicAgreementsUrl();
    logger.info(`Getting Microsoft agreements from: ${getMicAgreementUrl}`);

    const configModule = req.scope.resolve("configModule");

    const response = await getMethod(
      {
        url: getMicAgreementUrl,
        headers: headerList,
        isVendorHit: true,
        module: "GetMicAgreements",
      },
      configModule
    );

    logger.info(`Response from ExternalEndPoint GetMethod is ${JSON.stringify(response)}`);

    if (response.isSuccessStatusCode) {
      const getMicAgreementResponse: GetMicAgreementResponse = JSON.parse(response.content);

      if (!getMicAgreementResponse.items || getMicAgreementResponse.items.length === 0 || !getMicAgreementResponse.items[0].templateId) {
        logger.info(`Template id is not found in GetMicAgreement Response`);
        return {
          setCustomerAgreement: {
            status: false,
            message: "Error while getting template id",
          },
        };
      }

      logger.info(`Assigning response template id to request template id`);

      // Transform camelCase request to PascalCase for Microsoft API
      const pascalCaseRequest = transformToPascalCase(setCustomerAgreementRequest);
      pascalCaseRequest.TemplateId = getMicAgreementResponse.items[0].templateId;

      logger.info(`Transformed request for Microsoft API: ${JSON.stringify(pascalCaseRequest)}`);

      // Post the agreement
      const postAgreementUrl = MicrosoftEndpoints.postAgreementsUrl(custId);
      logger.info(`Posting agreement to: ${postAgreementUrl}`);

      const postAgreementResponse = await PostMethodWithRequestJsonBodyAndHeaders400Handle(
        {
          url: postAgreementUrl,
          body: pascalCaseRequest,
          headers: headerList,
          isVendorHit: true,
          module: "PostAgreements",
        },
        configModule
      );

      if (postAgreementResponse.isSuccessStatusCode) {
        logger.info(`Response from ExternalEndPoint PostMethod is ${JSON.stringify(postAgreementResponse)}`);
        const agreementResult = JSON.parse(postAgreementResponse.content);

        return {
          setCustomerAgreement: {
            status: true,
            message: "Customer agreement set successfully",
            data: agreementResult,
          },
        };
      } else {
        logger.info(`Entered into IsSuccessStatusCode false section for POST.`);
        return {
          error: {
            errors: [`${postAgreementResponse.httpStatusCode} Conflict`],
            statusCode: 500,
          },
        };
      }
    } else {
      logger.info(`Entered into IsSuccessStatusCode false section for GET.`);
      return {
        error: {
          errors: [`${response.httpStatusCode} ${response.errorMessage}`],
          statusCode: 500,
        },
      };
    }
  } catch (error) {
    logger.error(`SetCustomerAgreement Service Error:`, error);
    return {
      error: {
        errors: ["Internal server error"],
        statusCode: 500,
      },
    };
  }
}
