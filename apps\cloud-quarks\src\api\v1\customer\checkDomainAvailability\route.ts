import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import {
  normalizeQueryParams,
  toPascalCaseJson,
} from "../../../../utils/mappers/getCustomerUsers";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import { checkDomainAvailabilityService } from "../../../../services/index";

/**
 * @openapi
 * /v1/customer/checkDomainAvailability:
 *   get:
 *     summary: Check if a domain exists for a given brand and store
 *     tags:
 *       - Customer
 *     parameters:
 *       - in: query
 *         name: brandId
 *         schema:
 *           type: string
 *         required: true
 *         description: Brand ID
 *       - in: query
 *         name: storeId
 *         schema:
 *           type: string
 *         required: true
 *         description: Store ID
 *       - in: query
 *         name: domainId
 *         schema:
 *           type: string
 *         required: true
 *         description: Domain ID to check availability
 *     responses:
 *       200:
 *         description: Domain availability check result
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                   example: "1"
 *                   description: "1 if domain exists, 0 if not"
 *                 Message:
 *                   type: string
 *                   example: Domain exist
 *       500:
 *         description: Internal server error or token/domain API failure
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                   example: "0"
 *                 Message:
 *                   type: string
 *                   example: Internal server error
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    logger.info(`Check Domain Availability API is Hit`);
    const correlationId = getCorrelationId(req);
    const rawQuery = req.query;
    const { storeId, brandId, domainId } = normalizeQueryParams(rawQuery, [
      "storeId",
      "brandId",
      "domainId",
    ]);

    logger.info(
      `Entered into CheckDomainAvailability API with brandId: ${brandId}, storeId: ${storeId}, domainId: ${domainId}`
    );

    logger.info(
      `checkDomainAvailabilityService hit. correlationId=${correlationId}`
    );
    const response = await checkDomainAvailabilityService(
      req,
      brandId,
      storeId,
      domainId
    );

    logger.info(
      `CheckDomain response received by controller: ${correlationId} :: ${JSON.stringify(
        response
      )}`
    );
    const pascalCaseResponse = toPascalCaseJson(response);

    logger.info(
      `Returning PascalCase JSON from API: ${correlationId} :: ${JSON.stringify(
        pascalCaseResponse
      )}`
    );

    return res.status(200).json(pascalCaseResponse);
  } catch (err) {
    logger.error({
      message: "Check Domain Availability API failed",
      error: (err as Error)?.message,
      stack: (err as Error)?.stack,
    });
    return res.status(500).json({
      Status: "0",
      Message: "Internal server error",
    });
  }
}
