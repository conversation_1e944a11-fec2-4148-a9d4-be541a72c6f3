import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import { getStoreDetails } from "../token/microsoft/getStoreDetailsService";
import { getMsToken } from "../token/microsoft/getTokenService";
import { PostMethodWithRequestJsonBodyAndHeaders400Handle } from "../externalService/externalEndPointService";
import {
  createMicrosoftRequestHeaders,
} from "../../config/microsoftEndpoints";
import {
  MicrosoftApiResponse,
  AzureServiceResponse
} from "../../types/responses/customResponse";
import { AzureUnbilledUsageRequestType } from "../../validators/azure/azureUnbilledUsageValidator";
import { Urls } from "../../utils/constants";

/**
 * Export Azure Unbilled Usage Service
 * Matches .NET AzureService.ExportUnbilledUsage method
 */
export async function exportUnbilledUsageService(
  req: MedusaRequest,
  storeId: string,
  brandId: string,
  request: AzureUnbilledUsageRequestType
): Promise<AzureServiceResponse> {
  const apiResponse: MicrosoftApiResponse = {
    message: "Success",
    statusCode: 200,
    isError: false,
  };

  try {
    logger.info(`Going to hit BL GetStoreDetails method with ${JSON.stringify(request)}`);
    
    // Get store details (matching .NET: _storeService.GetStoreDetails(storeId, brandId, provisionType, CustomerCredentialType.Graph))
    const storeDetails = await getStoreDetails({ storeId, brandId });

    if (!storeDetails) {
      logger.error(`Store details not found for storeId=${storeId}, brandId=${brandId}`);
      return {
        response: {
          message: "Store details not found",
          statusCode: 400,
          isError: true,
        },
      };
    }

    // Generate Microsoft token (matching .NET: _tokenService.GetToken(storeDetails, getTokenUrl))
    const token = await getMsToken({
      brand: storeDetails.brand || "",
      client_id: storeDetails.clientid || "",
      client_secret: storeDetails.clientsecret || "",
      grant_type: storeDetails.granttype || "",
      markValue: storeDetails.markvalue?.toString() || "",
      redirect_uri: storeDetails.redirecturi || "",
      refresh_token: storeDetails.token || "",
      resource: storeDetails.resource || "",
      store_domain: storeDetails.storedomain || "",
    });

    if (!token || !token.access_token) {
      logger.error(`Token generation failed`);
      return {
        response: {
          message: "Error While Generating Token",
          statusCode: 500,
          isError: true,
        },
      };
    }

    // Build export URL (matching .NET: $"{baseUrl}{_configuration.GetSection("AppSettings:URL:ExportAzureUnbilledUsage")?.Value}")
    const baseUrl = Urls.MS_BASE_URL || "";
    const exportUrl = `${baseUrl}${Urls.MS_EXPORT_AZURE_UNBILLED_USAGE_URL}`;

    // Create Microsoft request headers (matching .NET: _externalEndPointRequestHeader.CreateMSRequestHeaders(token, storeId))
    const headerList = createMicrosoftRequestHeaders(
      token.access_token,
      storeId
    );

    logger.info(`Going to hit ExternalEndPoint PostMethodWithRequestJsonBodyAndHeaders400Handle method with url-${exportUrl}, RequestBody-${JSON.stringify(request)}, headerlist-${JSON.stringify(headerList)}`);

    const configModule = req.scope.resolve("configModule");
    
    // Call Microsoft API (matching .NET: PostMethodWithRequestJsonBodyAndHeaders400Handle)
    // Note: .NET uses "ExportAzureBilledUsage" as module name (appears to be a copy-paste error in .NET)
    const response = await PostMethodWithRequestJsonBodyAndHeaders400Handle(
      {
        url: exportUrl,
        body: request,
        headers: headerList,
        isVendorHit: true,
        module: "ExportAzureBilledUsage", // Matching .NET exactly (even though it should be "ExportAzureUnbilledUsage")
      },
      configModule
    );

    logger.info(`Response from ExternalEndPoint PostMethodWithRequestJsonBodyAndHeaders400Handle is ${JSON.stringify(response)}`);

    if (response.isSuccessStatusCode) {
      // Success response (matching .NET: apiResponse.StatusCode = response.HttpStatusCode)
      apiResponse.statusCode = response.httpStatusCode;
      
      // Parse response content if available
      if (response.content) {
        try {
          apiResponse.data = JSON.parse(response.content);
        } catch {
          apiResponse.data = response.content;
        }
      }
    } else {
      // Error response (matching .NET error handling)
      logger.info(`Received errored response and deserialising response.ErrorMessage-${response.errorMessage}`);
      
      let errorMessage = "Unknown error";
      try {
        // Try to parse Microsoft error response
        const errorResponse = JSON.parse(response.content || response.errorMessage || "{}");
        errorMessage = errorResponse?.error?.message || errorResponse?.message || response.errorMessage || "Unknown error";
      } catch {
        errorMessage = response.errorMessage || "Unknown error";
      }

      apiResponse.message = errorMessage;
      apiResponse.statusCode = response.httpStatusCode;
      apiResponse.isError = true;
    }

    // In .NET, response.Headers is used, but our EndPointResponseModel doesn't have headers
    // Create empty headers object for now
    const responseHeaders: Record<string, string> = {};
    
    logger.info(`Going to return response from BL. ApiResponse-${JSON.stringify(apiResponse)}, Headers-${JSON.stringify(responseHeaders)}`);
    
    return {
      response: apiResponse,
      headers: responseHeaders,
    };
  } catch (error) {
    logger.error(`ExportUnbilledUsage Service Error:`, error);
    
    const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
    
    return {
      response: {
        message: `Exception: ${errorMessage}`,
        statusCode: 422, // UnprocessableEntity
        isError: true,
      },
    };
  }
}
