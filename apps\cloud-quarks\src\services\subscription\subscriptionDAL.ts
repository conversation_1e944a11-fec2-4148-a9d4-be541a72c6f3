import { getSqlServerConnection } from "../../utils/sqlServerClient";
import logger from "../../utils/logger";
import sql from "mssql";

// Subscription Model (matching .NET SubscriptionModel)
export interface SubscriptionModel {
  SubscriptionId: string;
  EndDate: Date;
  CustomerId: string;
  BrandId: string;
  StoreId: string;
  StoreKey: number;
  PlanType: string;
  Duration: string;
  MaterialId: string;
  MaterialDescription: string;
  Quantity: number;
  Term: string;
  Status: string;
}

/**
 * Get subscription detail by material number
 * Matches .NET SubscriptionDAL.GetSubscriptionDetailByMaterialNo method
 */
export async function getSubscriptionDetailByMaterialNo(
  storeId: string,
  custId: string,
  materialNo: string
): Promise<SubscriptionModel | null> {
  logger.info(`Entered into GetSubscriptionDetailByMaterialNo DAL method with storeId:${storeId}, custId:${custId}, materialNo:${materialNo}`);

  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();

    const procedure = "spGetSubscriptionByMaterialNo";
    
    request.input("istoreId", sql.VarChar(50), storeId);
    request.input("icustId", sql.VarChar(50), custId);
    request.input("imaterialNo", sql.VarChar(100), materialNo);

    logger.info(`Going to hit ${procedure} procedure with parameters storeId:${storeId}, custId:${custId}, materialNo:${materialNo}`);

    const result = await request.execute<SubscriptionModel>(procedure);
    const response = result.recordset?.[0] || null;

    logger.info(`Got response from proc ${JSON.stringify(response)}`);
    return response;

  } catch (error: any) {
    logger.error(`Error in getSubscriptionDetailByMaterialNo: ${error.message}`);
    throw error;
  }
}
