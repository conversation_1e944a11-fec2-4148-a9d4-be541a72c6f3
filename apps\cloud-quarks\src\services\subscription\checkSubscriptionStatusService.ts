import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import { getMethod } from "../externalService/externalEndPointService";
import { MicrosoftEndpoints } from "../../config/microsoftEndpoints";
import { SubscriptionStatusResponse, ResponseError } from "../../types/responses/customResponse";
import { v4 as uuidv4 } from "uuid";

/**
 * Check Subscription Status
 * Matches .NET CheckSubscriptionStatus method
 */
export async function checkSubscriptionStatusService(
  req: MedusaRequest,
  customerId: string,
  subscriptionId: string,
  token: string
): Promise<SubscriptionStatusResponse | null> {
  logger.info(`Entered into CheckSubscriptionStatus Service method with customerId ${customerId}, subscriptionId ${subscriptionId}`);

  try {
    // Construct the URL (matching .NET: string.Format(getSubscriptionStatus, customerId, subscriptionId))
    const getSubscriptionStatusUrl = MicrosoftEndpoints.checkSubscriptionStatusUrl(customerId, subscriptionId);
    
    logger.info(`Request to CheckSubscriptionStatus ${getSubscriptionStatusUrl}`);

    // Prepare headers (matching .NET headers)
    const headerList = {
      Authorization: `Bearer ${token}`,
      Accept: "application/json",
      "MS-RequestId": uuidv4(), // Matches .NET: client.DefaultRequestHeaders.Add("MS-RequestId", new Guid().ToString())
    };

    const configModule = req.scope.resolve("configModule");

    // Make the GET request (matching .NET: client.GetAsync(getSubscriptionStatus).Result)
    const response = await getMethod(
      {
        url: getSubscriptionStatusUrl,
        headers: headerList,
        isVendorHit: true,
        module: "CheckSubscriptionStatus",
      },
      configModule
    );

    logger.info(`Response from CheckSubscriptionStatus ${getSubscriptionStatusUrl}, response ${response.content}`);

    if (response.isSuccessStatusCode) {
      // Parse the response body (matching .NET: JsonConvert.DeserializeObject<SubscriptionStatusResponse>(dataObjects))
      const subscriptionStatusResponse: SubscriptionStatusResponse = JSON.parse(response.content);
      logger.info(`Returning resp ${JSON.stringify(subscriptionStatusResponse)}`);
      return subscriptionStatusResponse;
    } else {
      // Handle error response (matching .NET: return null)
      logger.info(`Returning null`);
      return null;
    }
  } catch (error: any) {
    logger.error(`Error in checkSubscriptionStatusService: ${error.message}`);
    // Return null on error (matching .NET behavior)
    logger.info(`Returning null due to error`);
    return null;
  }
}
