import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import { ProvisionalStatusResponse } from "../../types/responses/customResponse";
import { MicrosoftEndpoints } from "../../config/microsoftEndpoints";
import { getMethod } from "../externalService/externalEndPointService";


export async function getProvisionalStatusService(
  req: MedusaRequest,
  customerId: string,
  orderId: string,
  token: string
): Promise<string> {
  logger.info(
    `Entered into GetProvisionalStatus Service method with customerId - ${customerId}, orderId - ${orderId}`
  );

  try {
    console.log("orderRow.CustomerId--------->", customerId);
    console.log("orderRow.VendorOrderId--------->", orderId);
    console.log("token--------->", token);
    const completeUrlWithParams = MicrosoftEndpoints.getProvisionalStatusURL(
      customerId,
      orderId
    );

    logger.info(
      `Request initiated to GetProvisionalStatus - ${completeUrlWithParams}`
    );

    const headerList = {
      Authorization: `Bearer ${token}`,
      Accept: "application/json",
    };

    const configModule = req.scope.resolve("configModule");

    const response = await getMethod(
      {
        url: completeUrlWithParams,
        headers: headerList,
        isVendorHit: true,
        module: "GetProvisionalStatus",
      },
      configModule
    );

    console.log("response----------->", response);

    logger.info(
      `Response from GetProvisionalStatus - ${JSON.stringify(response)}`
    );

    if (response.isSuccessStatusCode && response.content) {
      logger.info(`Entered into IsSuccessStatusCode = true`);

      const responseData = JSON.parse(
        response.content
      ) as ProvisionalStatusResponse;

      logger.info(`Deserialized Object - ${JSON.stringify(responseData)}`);

      if (responseData?.items?.length > 0) {
        const status = responseData.items[0].status;
        logger.info(`Status - ${status}`);
        return status;
      }
    }

    logger.warn(
      `GetProvisionalStatus failed: HTTP ${response.httpStatusCode}, Error: ${response.errorMessage}`
    );
    return "";
  } catch (error) {
    logger.error(`Error in GetProvisionalStatus Service: ${error}`);
    throw error;
  }
}
