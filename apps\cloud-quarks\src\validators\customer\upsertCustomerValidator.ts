import { z } from "zod"

export const upsertCustomerSchema = z.object({
  type: z.enum(["CREATE", "UPDATE"]),
  id: z.string().optional(),
  custId: z.string().optional(),
  orgName: z.string(),
  firstName: z.string(),
  lastName: z.string(),
  emailId: z.string().email().optional(),
  sapCustId: z.string().optional(),
  sapCompany: z.string().optional(),
  countryId: z.string(),
  addressLine1: z.string(),
  addressLine2: z.string().optional(),
  userName: z.string().optional(),
  storeId: z.string(),
  brandId: z.string().optional(),
  partnerId: z.string().optional(),
  mpnId: z.string().optional(),
  domainId: z.string().optional(),
  newTenant: z.boolean().optional(),
  associatedPartnerId: z.string().optional(),
  creditLimit: z.number().optional(),
  ioNo: z.string().optional(),
  taxable: z.string().optional(),
  yInvoice: z.string().optional(),
  mInvoice: z.string().optional(),
  mazInvoice: z.string().optional(),
  middleName: z.string().optional(),
  organizationRegistrationNumber: z.string().optional(),
  customerVertical: z.string().optional(),
  segment: z.string().optional(),
  sector: z.string().optional(),
  tinNumber: z.string().optional(),
  panNumber: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  postalCode: z.string().optional(),
  phoneNumber: z.string().optional(),
  vatId: z.string().optional(),
});
