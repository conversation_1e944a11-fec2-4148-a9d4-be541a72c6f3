import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';

import { stringConstants } from '@org/utils';

import CQUserService from '../../../../modules/cq-user/service';
import { CQUser_MODULE } from '../../../../modules/cq-user';

/**
 * @openapi
 * /v1/user/designation:
 *   get:
 *     summary: Get Designations List
 *     tags:
 *       - Designation
 *     responses:
 *       200:
 *         description: Successfully fetched designations
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         example: 01JZ2RGD8VQXZ7N2C9KJQ0VZHZ
 *                       name:
 *                         type: string
 *                         example: Manager
 *                       key:
 *                         type: string
 *                         example: manager
 *                 message:
 *                   type: string
 *                   example: Designation fetched successfully
 *       404:
 *         description: No designations found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Designation not found
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error. Please try again later.
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const regionService: CQUserService = req.scope.resolve(CQUser_MODULE);

  try {
    const designation = await regionService.listDesignations(
      {},
      { select: ['name', 'key'], order: { key: 'ASC' } }
    );
    if (!designation || designation?.length === 0) {
      return res.status(404).json({
        data: [],
        message: stringConstants.NOT_FOUND('Designation'),
      });
    }
    return res.status(200).json({
      data: designation,
      message: stringConstants.FETCHED_SUCCESSFULLY('Designation'),
    });
  } catch (err: any) {
    return res.status(500).json({
      message: stringConstants.INTERNAL_SERVER_ERROR(),
    });
  }
}
