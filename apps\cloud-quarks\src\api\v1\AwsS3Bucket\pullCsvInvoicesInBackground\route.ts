import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import {
  validatePullCsvInvoicesBackgroundRequest
} from "../../../../validators/aws/pullCsvInvoicesBackgroundValidator";
import { pullCsvInvoicesInBackgroundService } from "../../../../services/aws/pullCsvInvoicesBackgroundService";

/**
 * @openapi
 * /v1/AwsS3Bucket/pullCsvInvoicesInBackground:
 *   get:
 *     summary: Pull CSV invoices in background
 *     tags:
 *       - AWS S3 Bucket
 *     parameters:
 *       - in: query
 *         name: requestId
 *         required: true
 *         schema:
 *           type: string
 *         description: Request ID for the background process
 *         example: "req-123456"
 *       - in: query
 *         name: username
 *         required: true
 *         schema:
 *           type: string
 *         description: Username for the process
 *         example: "<EMAIL>"
 *       - in: query
 *         name: month
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 12
 *         description: Month for invoice processing
 *         example: 12
 *       - in: query
 *         name: year
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 2000
 *           maximum: 2100
 *         description: Year for invoice processing
 *         example: 2024
 *       - in: query
 *         name: storeKeyInput
 *         required: true
 *         schema:
 *           type: string
 *         description: Store key input for processing
 *         example: "store-key-123"
 *     responses:
 *       200:
 *         description: Background process initiated successfully
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: "SUBMITTED"
 *       400:
 *         description: Validation error
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: "FAILED"
 *       500:
 *         description: Internal server error
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: "FAILED"
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req);

  try {
    // Extract query parameters (matching .NET method parameters)
    const requestId = req.query.requestId as string;
    const username = req.query.username as string;
    const month = parseInt(req.query.month as string);
    const year = parseInt(req.query.year as string);
    const storeKeyInput = req.query.storeKeyInput as string;

    logger.info(
      `Entered PullCsvInvoicesInBackground method with request id:${requestId}, username:${username} month:${month} year:${year} storeKeyInput:${storeKeyInput} | CorrelationId: ${correlationId}`
    );

    let requestStatus: string;

    try {
      // Validate query parameters
      const validationErrors = validatePullCsvInvoicesBackgroundRequest(
        requestId,
        username,
        month,
        year,
        storeKeyInput
      );

      if (validationErrors.length > 0) {
        const errorMessage = validationErrors.join(', ');
        logger.error(`Validation errors: ${errorMessage} | CorrelationId: ${correlationId}`);
        return res.status(400).send("FAILED");
      }

      logger.info(
        `Going to hit PullCsvInvoicesInBackground method with details, RequestId:${requestId}, UserName:${username}, Month:${month}, Year:${year}, storeKeyInput:${storeKeyInput} | CorrelationId: ${correlationId}`
      );

      // Call the background service (matching .NET _awsBackgroundFactory.PullCsvInvoicesInBackground)
      const status = await pullCsvInvoicesInBackgroundService(
        req,
        requestId,
        username,
        month,
        year,
        storeKeyInput
      );

      logger.info(
        `Got result as ${status} from PullCsvInvoicesInBackground method after passing details, RequestId:${requestId}, UserName:${username}, Month:${month}, Year:${year}, storeKeyInput:${storeKeyInput} | CorrelationId: ${correlationId}`
      );

      requestStatus = status;
    } catch (error: any) {
      logger.error(
        `Error in PullCsvInvoicesInBackground method with details, RequestId:${requestId}, UserName:${username}, Month:${month}, Year:${year} Message:${error.message}, StackTrace:${error.stack} | CorrelationId: ${correlationId}`
      );
      logger.info("Going to set return status as FAILED");
      requestStatus = "FAILED";
    }

    logger.info(`Going to return final status of the request as ${requestStatus} | CorrelationId: ${correlationId}`);
    return res.status(200).send(requestStatus);

  } catch (error: any) {
    const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";

    logger.error(
      `Unexpected error in PullCsvInvoicesInBackground API | CorrelationId: ${correlationId} | Message: ${errorMessage} | StackTrace: ${error instanceof Error ? error.stack : 'No stack trace'}`
    );

    return res.status(500).send("FAILED");
  }
}