import { getStoreDetails } from "../token/microsoft/getStoreDetailsService";
import {
  createMicrosoftRequestHeaders,
  MicrosoftEndpoints,
} from "../../config/microsoftEndpoints";
import { getMethod } from "../externalService/externalEndPointService";
import { getMsToken } from "../token/microsoft/getTokenService";
import logger from "../../utils/logger";
import { GetDirectSignStatusOfCustAgreementResponse } from "../../types/responses/customResponse";

export async function getDirectSignStatusOfCustAgreementService(
  req: any,
  storeId: string,
  custTenantId: string
): Promise<GetDirectSignStatusOfCustAgreementResponse> {
  const errors: string[] = [];
  logger.info(`Calling Get Direct Sign Status Of Cust Agreement Service`);
  if (!storeId) errors.push("storeId is required");
  if (!custTenantId) errors.push("custTenantId is required");
  logger.info(
    `Entered into getDirectSignStatusOfCustAgreementService with storeId-${storeId}, custTenantId-${custTenantId}`
  );

  const response: GetDirectSignStatusOfCustAgreementResponse = {
    status: "0",
    message: "Something went wrong",
  };
  logger.info(`Calling getStoreDetails with storeId: ${storeId}`);
  const tokenRequest = await getStoreDetails({ storeId });
  logger.info(`Store details response: ${JSON.stringify(tokenRequest)}`);

  if (
    tokenRequest &&
    tokenRequest.clientid &&
    (tokenRequest.brand || "").replace(/\s/g, "").toUpperCase() === "MICROSOFT"
  ) {
    logger.info(`Brand is Microsoft. Calling getMsToken`);

    const token = await getMsToken({
      brand: tokenRequest.brand || "",
      client_id: tokenRequest.clientid || "",
      client_secret: tokenRequest.clientsecret || "",
      grant_type: tokenRequest.granttype || "",
      markValue: tokenRequest.markvalue?.toString() || "",
      redirect_uri: tokenRequest.redirecturi || "",
      refresh_token: tokenRequest.token || "",
      resource: tokenRequest.resource || "",
      store_domain: tokenRequest.storedomain || "",
    });
    if (!token) {
      response.message = "Token generation failed.";
      return response;
    }

    const headerList = createMicrosoftRequestHeaders(token.access_token, storeId);

    const completeUrlWithParams =
      MicrosoftEndpoints.getDurectSignStatusOfCustAgreementUrl(custTenantId);

    logger.info(
      `Prepared HeaderList for Microsoft call: ${JSON.stringify(headerList)}`
    );
    logger.info(`Hitting external endpoint with URL: ${completeUrlWithParams}`);

    const configModule = req.scope.resolve("configModule");

    const externalResponse = await getMethod(
      {
        url: completeUrlWithParams,
        headers: headerList,
        isVendorHit: true,
        module: "getDirectSignStatusOfCustAgreement",
      },
      configModule
    );

    logger.info(
      `Response from Microsoft API: ${JSON.stringify(externalResponse)}`
    );

    if (externalResponse.isSuccessStatusCode) {
      const data = JSON.parse(externalResponse.content);

      response.status = "1";
      response.isSigned = data.isSigned;
      response.message = data.isSigned
        ? "Agreement is signed"
        : "Agreement is not signed";
    } else {
      logger.info(
        `Received errored response and deserializing response.Content - ${externalResponse.content}`
      );

      let message = externalResponse.errorMessage || "External call failed";
      try {
        const parsed = JSON.parse(externalResponse.content);
        logger.info(`Deserialization completed - ${JSON.stringify(parsed)}`);
        message = parsed?.Description || message;
      } catch (err) {
        logger.warn("Failed to parse external response content", err);
      }
      response.status = "0";
      response.message = message;
    }
  } else {
    response.status = "0";
    response.message =
      "Invalid store or this customer is not registered at Microsoft";
  }

  return response;
}
