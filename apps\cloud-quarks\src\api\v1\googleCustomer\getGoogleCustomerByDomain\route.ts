import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import {
  CloudIdentityAccountDtoRequest,
  GoogleAPIResponse
} from "../../../../types/responses/customResponse";
import { validateCloudIdentityAccountDto } from "../../../../validators/google/verifyCloudIdentityAccountsValidator";
import { verifyAndGetGoogleCustomerByDomainService } from "../../../../services/googleCustomer/verifyAndGetGoogleCustomerByDomainService";
import { Messages } from "../../../../services/googleCustomer/verifyDomainExistsService";

/**
 * @openapi
 * /v1/googleCustomer/getGoogleCustomerByDomain:
 *   post:
 *     summary: Verify and Get Google Customer By Domain
 *     description: verify domain and the get customer details from Google Method is HTTPPOST post because we are combining HTTPPOST(verify call) and HTTPGET(detail call) in single action (to save the magento call)
 *     tags:
 *       - GoogleCustomer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               storeId:
 *                 type: string
 *                 example: "AE-EN"
 *               domain:
 *                 type: string
 *                 example: "example.com"
 *               isExisting:
 *                 type: boolean
 *                 example: true
 *               channelPartnerId:
 *                 type: string
 *                 example: "channel-partner-123"
 *     responses:
 *       200:
 *         description: Successfully retrieved Google customer details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Message:
 *                   type: string
 *                   example: "Success"
 *                 Data:
 *                   type: object
 *                   description: Google customer details
 *                 StatusCode:
 *                   type: integer
 *                   example: 200
 *                 IsError:
 *                   type: boolean
 *                   example: false
 *       400:
 *         description: Bad request - validation errors
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Message:
 *                   type: string
 *                   example: "StoreId cannot be null or empty"
 *                 IsError:
 *                   type: boolean
 *                   example: true
 *                 StatusCode:
 *                   type: integer
 *                   example: 400
 *       422:
 *         description: Unprocessable entity - service error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Message:
 *                   type: string
 *                   example: "An error occurred: [error details]"
 *                 IsError:
 *                   type: boolean
 *                   example: true
 *                 StatusCode:
 *                   type: integer
 *                   example: 422
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req);

  logger.info(`Entered into VerifyAndGetCustomerByDomain API with CorrelationId: ${correlationId}`);

  let apiResponse: GoogleAPIResponse = {
    Message: "Success",
    StatusCode: 200,
    IsError: false
  };

  try {
    const domainDto = req.body as CloudIdentityAccountDtoRequest;
    logger.info(`Entered into VerifyAndGetCustomerByDomain API with CreateCPIdDto - ${JSON.stringify(domainDto)}`);

    // Validate the request DTO (matching .NET validation)
    const validationResult = validateCloudIdentityAccountDto(domainDto);
    logger.info(`Validations error count on VerifyAndGetCustomerByDomain object is ${validationResult.errors.length}`);

    if (!validationResult.isValid) {
      const validationErrors = validationResult.errors;
      logger.info(`validation errors are ${JSON.stringify(validationErrors)}`);

      apiResponse.Message = validationErrors.join(', ');
      apiResponse.IsError = true;
      apiResponse.StatusCode = 400;

      logger.info(`Going to send bad request response ${JSON.stringify(apiResponse)}`);
      return res.status(400).json(apiResponse);
    }

    // Call the service method (matching .NET service call)
    apiResponse = await verifyAndGetGoogleCustomerByDomainService(
      domainDto.domain,
      domainDto.storeId,
      domainDto.isExisting,
      domainDto.channelPartnerId,
      req
    );

  } catch (exp: any) {
    logger.error(`Error in VerifyAndGetCustomerByDomain API method. Message : ${exp.message}, StackTrace: ${exp.stack}`);

    apiResponse.Message = Messages.ExceptionMessage(exp.message);
    apiResponse.IsError = true;
    apiResponse.StatusCode = 422; // UnprocessableEntity
  }

  logger.info(`Returning response from VerifyAndGetCustomerByDomain API - ${JSON.stringify(apiResponse)}`);

  // Return ContentResult equivalent (matching .NET return pattern)
  return res.status(apiResponse.StatusCode).json(apiResponse);
}