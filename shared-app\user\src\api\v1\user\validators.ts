import { z } from 'zod';

export const forgotPasswordSchema = z.object({
	email: z.string().trim().toLowerCase().email().max(100),
});

export const newPasswordSchema = z.object({
	password: z.string(),
	// Validations temporarily commented out:
	// .min(8, 'Password must be at least 8 characters long')
	// .max(12, 'Password must be at most 12 characters long')
	// .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
	// .regex(/[0-9]/, 'Password must contain at least one number')
	// .regex(
	//   /[^A-Za-z0-9]/,
	//   'Password must contain at least one special character'
	// ),
});

export const UpdateGuestUserSchema = z.object({
	first_name: z
		.string()
		.max(50)
		.regex(
			/^[A-Za-z0-9\s'-]+$/,
			'First name can only contain letters, numbers, spaces, hyphens, and apostrophes'
		)
		.optional(),
	last_name: z
		.string()
		.max(50)
		.regex(
			/^[A-Za-z0-9\s'-]+$/,
			'Last name can only contain letters, numbers, spaces, hyphens, and apostrophes'
		)
		.optional(),
	designation: z
		.string()
		.max(100)
		.regex(
			/^[A-Za-z0-9\s\-/]+$/,
			'Designation can only contain letters, numbers, spaces, hyphens, and slashes'
		)
		.optional(),
	mobile_number: z
		.string()
		.max(15)
		.regex(
			/^[+]?[0-9]{1,4}[-\s]?[(]?[0-9]{1,4}[)]?[-\s]?[0-9]+[-\s]?[0-9]+$/,
			'Mobile number must be in a valid international format (e.g., +919876543210)'
		)
		.optional(),
	email: z.string().email().max(100).optional(),
	company_name: z
		.string()
		.max(100)
		.regex(
			/^[A-Za-z0-9\s\-&]+$/,
			'Company name can only contain letters, numbers, spaces, hyphens, and ampersands'
		)
		.optional(),
	registered_country_id: z.string().optional(),
	// region_id: z.string(),
});

export const changePasswordSchema = z.object({
	oldPassword: z.string(),
	newPassword: z.string(),
	// Validations temporarily commented out:
	// .min(8, 'Password must be at least 8 characters long')
	// .max(12, 'Password must be at most 12 characters long')
	// .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
	// .regex(/[0-9]/, 'Password must contain at least one number')
	// .regex(
	//   /[^A-Za-z0-9]/,
	//   'Password must contain at least one special character'
	// ),
});
