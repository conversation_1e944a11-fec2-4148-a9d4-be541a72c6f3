// src/api/custom-upload/route.ts
import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { MedusaError } from '@medusajs/framework/utils';
import { ContainerRegistrationKeys, Modules } from '@medusajs/framework/utils';
import CompanyRegistrationService from '../../../../../../company/src/modules/company/service';

export const PARTNER_REGISTRATION_MODULE = 'partner_registration';
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
    const companyRegistrationModule: CompanyRegistrationService =
      req.scope.resolve(PARTNER_REGISTRATION_MODULE);

    const registrationId = req.params.registrationId;

    // // Query for data
    // const { data: registrations } = await query.graph({
    //   entity: `${PARTNER_REGISTRATION_MODULE}`,
    //   fields: ['*'],
    // });

    let partnerRegistration;
    try {
      partnerRegistration =
        await companyRegistrationModule.retrieveCqCompanyRegistration(
          registrationId,
          {
            relations: [
              'profile_info',
              'profile_info.question',
              'profile_info.answer',
            ],
            // order: {
            //   id: 'ASC',
            // },
            select: ['id', 'company_info'],
          }
        );
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.NOT_FOUND,
        `Partner registration with ID "${registrationId}" not found`
      );
    }
    return res.status(200).json({
      data: partnerRegistration,
    });
  } catch (err: any) {
    console.error(err);
    if (err instanceof MedusaError) {
      // Map MedusaError type to HTTP status code
      let status = 500;
      switch (err.type) {
        case MedusaError.Types.NOT_FOUND:
          status = 404;
          break;
        case MedusaError.Types.INVALID_DATA:
          status = 400;
          break;
        case MedusaError.Types.UNAUTHORIZED:
          status = 401;
          break;
        case MedusaError.Types.CONFLICT:
          status = 409;
          break;
        // Add other cases as needed
        default:
          status = 500;
      }
      return res.status(status).json({
        type: err.type,
        message: err.message,
      });
    }
    // For unknown errors
    return res.status(500).json({
      type: 'unknown_error',
      message: 'An unexpected error occurred.',
    });
  }
}
//   } catch (err: any) {
//     console.error(err);
//     if (err instanceof MedusaError) {
//       return res.status(err?.code as any).json({
//         message: err.message,
//       });
//     }

//     return res.status(500).json({
//       message: templateConstants.INTERNAL_SERVER_ERROR(),
//     });
//   }
// }
