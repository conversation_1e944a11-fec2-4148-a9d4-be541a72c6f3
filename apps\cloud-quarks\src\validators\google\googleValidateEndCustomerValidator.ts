import { z } from "zod";
import logger from "../../utils/logger";
import { getAllStoresService } from "../renewal/customRenewalValidator";

// Helper to normalize string (like .NET ComparableString extension)
function comparableString(str?: string): string {
  return (str || "").trim().toUpperCase();
}

// Channel Partner ID validation helper function
function isValidChannelPartnerId(cpId: string): boolean {
  // Must be 8-15 characters and contain only letters or digits (matching .NET logic)
  return cpId.length >= 8 && cpId.length <= 15 && /^[a-zA-Z0-9]+$/.test(cpId);
}

// Google Validate End Customer DTO validation schema (camelCase for request body)
export const GoogleValidateEndCustomerDtoSchema = z.object({
  storeId: z.string().min(1, "StoreId cannot be null or empty"),
  brandId: z.string().min(1, "BrandId cannot be null or empty"),
  partnerId: z.string().optional(), // Optional as it may not always be validated
  productId: z.string().min(1, "ProductId cannot be null or empty"),
  skuId: z.string().min(1, "SkuId cannot be null or empty"),
  offerId: z.string().min(1, "OfferId cannot be null or empty"),
  custId: z.string().optional(), // Optional as it may not always be validated
  cpId: z.string()
    .min(1, "Channel Partner ID cannot be null or empty")
    .refine(isValidChannelPartnerId, "The Channel Partner ID entered is not valid. Please enter the correct Channel Partner ID."),
  googleCustomerId: z.string().min(1, "GoogleCustomerId cannot be null or empty"),
  cloudIdentityId: z.string().min(1, "CloudIdentityId cannot be null or empty"),
});

// Type definition for TypeScript
export type GoogleValidateEndCustomerDtoType = z.infer<typeof GoogleValidateEndCustomerDtoSchema>;

/**
 * Validate GoogleValidateEndCustomer request parameters
 * Matches .NET GoogleValidateEndCustomerValidator logic
 */
export async function validateGoogleValidateEndCustomerDto(dto: any): Promise<{ isValid: boolean; errors: string[] }> {
  logger.info(`Entered into validateGoogleValidateEndCustomerDto with dto: ${JSON.stringify(dto)}`);
  
  const errors: string[] = [];

  // Basic structure validation
  if (!dto) {
    errors.push("Invalid data structure");
    return { isValid: false, errors };
  }

  try {
    // Zod validation for basic field validation
    GoogleValidateEndCustomerDtoSchema.parse(dto);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const zodErrors = error.errors.map(err => err.message);
      errors.push(...zodErrors);
    } else {
      errors.push("Invalid data structure");
    }
  }

  // Store validation (matching .NET StoreValidator logic)
  if (dto.storeId && dto.storeId.trim() !== "") {
    try {
      const allStores = await getAllStoresService();
      const storeExists = allStores.some(
        (s: { StoreId: string | undefined }) => 
          comparableString(s.StoreId) === comparableString(dto.storeId)
      );
      if (!storeExists) {
        errors.push("StoreId doesn't exists");
      }
    } catch (error) {
      logger.error(`Error checking store existence: ${error}`);
      errors.push("Error validating store existence");
    }
  }

  // TODO: Add Partner validation when partner service is available
  // if (dto.partnerId && dto.partnerId.trim() !== "") {
  //   // Implement partner validation logic
  // }

  // TODO: Add Customer validation when customer service is available
  // if (dto.custId && dto.custId.trim() !== "") {
  //   // Implement customer validation logic
  // }

  logger.info(`Validation completed with ${errors.length} errors: ${JSON.stringify(errors)}`);
  return { isValid: errors.length === 0, errors };
}
