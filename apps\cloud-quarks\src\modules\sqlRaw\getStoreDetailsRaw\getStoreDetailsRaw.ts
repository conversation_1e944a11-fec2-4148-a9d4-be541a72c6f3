import logger from "../../../utils/logger";
import {
  GetStoreDetailsInput,
  StoreDetailsResult,
} from "../../../types/responses/customResponse";
import sql from "mssql";
import { getSqlServerConnection } from "../../../utils/sqlServerClient";

export class SqlServerService {
  async getStoreDetails({
    storeId,
    brandId = null,
    provisionType = "AUTO",
    credentialType = null,
  }: GetStoreDetailsInput): Promise<StoreDetailsResult> {
    try {
      logger.info("----------------SqlServerService called----------------");

      const resolvedBrandId = brandId ?? "14";

      console.log("resolvedBrandId----------------->", resolvedBrandId);

      const pool: sql.ConnectionPool = await getSqlServerConnection();

      const request = pool.request();

      request.input("iStoreId", sql.Var<PERSON>har(50), storeId);
      request.input("iBrandId", sql.Var<PERSON>har(50), resolvedBrandId);
      request.input("iProvisionType", sql.VarChar(50), provisionType);
      request.input("iCredentialType", sql.VarChar(50), credentialType);
      request.output("oRetVal", sql.VarChar(1));

      const credentialsRes = await request.execute("Usp_GetStoreDetails");

      console.log("credentialsRes----------------->", credentialsRes);

      const record = credentialsRes.recordset?.[0];
      const retVal = credentialsRes.output?.oRetVal;

      if (retVal !== "0" || !record) {
        throw new Error("Store credentials not found");
      }

      logger.info("Returning Stored Procedure Response ::", record);
      return {
        clientid: record.Clientid,
        clientsecret: record.Clientsecret,
        username: record.Username,
        upassword: record.uPassword,
        granttype: record.granttype,
        resource: record.Resource,
        brand: record.Brand,
        markvalue: record.MarkValue,
        storedomain: record.StoreDomain,
        token: record.Token,
        redirecturi: record.RedirectUri,
      };
    } catch (err) {
      logger.error("Error in getStoreDetails:", err);
      throw err;
    }
  }
}
