import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import CQUserService from '../../../../../modules/cq-user/service';
import { CQUser_MODULE } from '../../../../../modules/cq-user';
import { stringConstants } from '@org/utils';

/**
 * @openapi
 * /v1/user/signup/email-verify:
 *   put:
 *     summary: Verify Email OTP
 *     tags:
 *       - Signup - Email
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - otp_code
 *             properties:
 *               email:
 *                 type: string
 *                 example: <EMAIL>
 *               otp_code:
 *                 type: string
 *                 example: 123456
 *     responses:
 *       200:
 *         description: OTP verified successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 is_verified:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: email OTP verification was successful.
 *       400:
 *         description: Invalid or expired OTP or missing inputs
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: OTP is invalid or expired.
 *       404:
 *         description: No OTP record found for the given email
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: No OTP record found for the given email.
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error. Please try again later.
 */

export async function PUT(req: MedusaRequest, res: MedusaResponse) {
  const emailOtpService: CQUserService = req.scope.resolve(CQUser_MODULE);

  try {
    const { email, otp_code } = req.body as { email: string; otp_code: string };

    if (!email || !otp_code) {
      return res.status(400).json({ message: stringConstants.EMAIL_VERIFY() });
    }

    const otpRecords = await emailOtpService.listEmailOtpVerifications({
      email: email.toLowerCase().trim(),
    });

    if (!otpRecords || otpRecords.length === 0) {
      return res
        .status(404)
        .json({ message: stringConstants.OTP_RECORD_NOT_FOUND('email') });
    }

    const latestOtp = otpRecords[otpRecords.length - 1];
    const now = new Date();

    if (new Date(latestOtp.expires_at) < now) {
      return res.status(400).json({ message: stringConstants.OTP_EXPIRED() });
    }

    if (latestOtp.otp_code !== otp_code) {
      return res.status(400).json({ message: stringConstants.OTP_INVALID() });
    }

    const data = await emailOtpService.updateEmailOtpVerifications({
      id: latestOtp.id,
      is_verified: true,
    });

    return res.status(200).json({
      is_verified: data.is_verified,
      message: stringConstants.OTP_VERIFIED(),
    });
  } catch (err: any) {
    return res.status(500).json({});
  }
}
