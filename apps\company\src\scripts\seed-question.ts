import { ExecArgs } from '@medusajs/framework/types';
import { COMPANY_REGISTRATION_MODULE } from '../modules/company';
import PartnerRegistrationService from '../modules/company/service';
import { Modules } from '@medusajs/framework/utils';
import { masterDB } from '../utils/master-db';

export default async function seedQuestion({ container }: ExecArgs) {
  const questionService: PartnerRegistrationService = container.resolve(
    COMPANY_REGISTRATION_MODULE
  );

  // Step 1: Resolve regions
  //   const targetRegionNames = ['india', 'turkey', 'mea'];
  const targetRegionNames = ['india'];
  const { rows: allRegions } = await masterDB.query('SELECT id, name FROM region');
  const regionMap = new Map<string, string>();

  for (const regionName of targetRegionNames) {
    const region = allRegions.find(
      (r: any) => r.name.toLowerCase() === regionName
    );
    if (!region) {
      console.warn(`⚠️ Region "${regionName}" not found. Skipping.`);
    } else {
      regionMap.set(regionName, region.id);
    }
  }

  if (regionMap.size === 0) {
    console.error('❌ No valid target regions found. Aborting.');
    return;
  }

  // Step 2: Fetch answer types and map them
  const answerTypes = await questionService.listAnswerTypes({});
  const answerTypeMap = new Map<string, string>(
    answerTypes.map((at: any) => [at.type.toLowerCase(), at.id])
  );

  // Step 3: Define seed questions
  const seedQuestions = [
    {
      question: 'What type of partner are you?',
      answer_type: 'single choice drop down',
      answers: [
        'Independent Software Vendor (ISV)',
        'System Integrator (SI)',
        'Managed Service Provider (MSP)',
        'Value-Added Reseller (VAR)',
        'Cloud Solution Provider (CSP)',
        'Distributor',
        'Consulting Partner',
      ],
    },
    {
      question: 'How you came to know redington cloud?',
      answer_type: 'single choice drop down',
      answers: [
        'Social Media',
        'Webinar',
        'Sales Rep',
        'Email Campaign',
        'Other',
      ],
    },
    {
      question: 'Your core business.',
      answer_type: 'single choice drop down',
      answers: ['Software', 'Hardware', 'Services', 'Consulting'],
    },
    {
      question: 'Focused customer vertical.',
      answer_type: 'multiple choice',
      answers: [
        'Healthcare',
        'Education',
        'Finance',
        'Retail',
        'Manufacturing',
      ],
    },
    {
      question: 'Select your interested workloads to work with redington',
      answer_type: 'multiple choice dropdown',
      answers: [
        'Data & AI',
        'Security',
        'Cloud Infrastructure',
        'DevOps',
        'IoT',
      ],
    },
    {
      question: 'Year of incoporation:',
      answer_type: 'input',
      answers: [],
    },
    {
      question: 'Banker name:',
      answer_type: 'input',
      answers: [],
    },
    {
      question: 'Whether availing CC/OD',
      answer_type: 'single choice',
      answers: ['Yes', 'No'],
    },
  ];

  // Step 4: Loop through each region
  for (const [regionName, regionId] of regionMap.entries()) {
    console.log(`🌍 Seeding for region: ${regionName.toUpperCase()}`);

    const existingQuestions = await questionService.listCqQuestions({
      region_id: regionId,
    });
    const existingSet = new Set(
      existingQuestions.map((q: any) => q.question.toLowerCase())
    );

    const regionQuestions = seedQuestions
      .filter((q) => !existingSet.has(q.question.toLowerCase()))
      .map((q, index) => {
        const answerTypeId = answerTypeMap.get(q.answer_type.toLowerCase());
        if (!answerTypeId) {
          throw new Error(`❌ Missing answer type: "${q.answer_type}"`);
        }

        return {
          question: q.question,
          answer_type_id: answerTypeId,
          region_id: regionId,
          order_id: index + 1,
          answers: q.answers || [],
        };
      });

    for (const q of regionQuestions) {
      const createdQuestion = await questionService.createCqQuestions({
        question: q.question,
        answer_type: q.answer_type_id,
        region_id: q.region_id,
        order_id: q.order_id,
      });

      if (q.answers.length > 0) {
        await Promise.all(
          q.answers.map((answerText, idx) =>
            questionService.createAnswers({
              answer: answerText,
              questions_id: createdQuestion.id,
              order_id: idx + 1,
            })
          )
        );
      }

      //   console.log(`✅ [${regionName}] Seeded: "${q.question}"`);
    }
  }

  console.log('🎉 All questions seeded for India, Turkey, and UAE.');
}
