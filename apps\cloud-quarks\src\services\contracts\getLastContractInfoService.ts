import logger from "../../utils/logger";
import { getSqlServerConnection } from "../../utils/sqlServerClient";
import {
  LastContractInfoDto,
  LastContractInfoModel,
  ContractDetail,
  ContractDetailRaw,
} from "../../types/responses/customResponse";
import { ComparableString } from "../../validators/customer/customCustomerValidator";
function roundTo(value: number, decimals: number): number {
  if (typeof value !== 'number' || isNaN(value)) return 0
  return Math.round(value * Math.pow(10, decimals)) / Math.pow(10, decimals)
}

export async function getLastContractInfoService(
  subscriptionId: string
): Promise<{ data: ContractDetail }> {
  const lastContractInfoDto = { data: {} as ContractDetail };

  const lastContractInfoModel = await GetLastContractInfo(subscriptionId);
  logger.debug(
    `Data fetched from DAL. Preparing DTO. ${JSON.stringify(
      lastContractInfoModel
    )}`
  );

  const detail = lastContractInfoModel.contractDetail;

  console.log("detail--------->", detail);

  const qty = lastContractInfoModel.Quantity;
  const discountPercentage = roundTo(
    (detail.discount * 100) / detail.unitprice,
    1
  );
  const discountedUnitPrice = roundTo(detail.unitprice - detail.discount, 2);
  const subTotal = roundTo(discountedUnitPrice * qty, 2);
  const vatPercentage = roundTo((detail.vat * 100) / discountedUnitPrice, 0);
  const promotionPercentage = getPromotionPercentage(lastContractInfoModel);
  const netPrice = roundTo(subTotal + (vatPercentage * subTotal) / 100, 2);
  const unitPriceAfterPromotion = roundTo(detail.unitprice, 2);
  const unitPriceBeforePromotion = roundTo(
    (100 * detail.unitprice) / (100 - promotionPercentage),
    2
  );
  const promotionId =
    ComparableString(detail.statusreason) === "RENEWED"
      ? lastContractInfoModel.PromotionIdLastRenewal
      : lastContractInfoModel.PromotionIdDuringOrder;

  const contractDetail: ContractDetail = {
    qty,
    discount_percentage: discountPercentage,
    discounted_unit_price: discountedUnitPrice,
    sub_total: subTotal,
    vat_percentage: vatPercentage,
    promotion_percentage: promotionPercentage,
    net_price: netPrice,
    unit_price_after_promotion: unitPriceAfterPromotion,
    unit_price_before_promotion: unitPriceBeforePromotion,
    subscription_id: subscriptionId,
    promotion_id: promotionId,
    promotion_start_date:
      lastContractInfoModel.promotionModel?.PromotionStartDate ?? null,
    promotion_end_date:
      lastContractInfoModel.promotionModel?.PromotionEndDate ?? null,
  };

  lastContractInfoDto.data = contractDetail;

  logger.debug(`Prepared DTO: ${JSON.stringify(lastContractInfoDto)}`);
  return lastContractInfoDto;
}

export async function GetLastContractInfo(
  subscriptionId: string
): Promise<LastContractInfoModel> {
  logger.info(
    `Entered into Contract GetLastContractInfo method with subscriptionId=${subscriptionId}`
  );

  const lastContractInfoModel: LastContractInfoModel =
    {} as LastContractInfoModel;

  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();

    request.input("iSubscriptionId", subscriptionId);

    const procedure = "[usp_sel_last_contract_info]";
    logger.debug(
      `Going to hit proc ${procedure} with parameters: subscriptionId=${subscriptionId}`
    );

    const result = await request.execute(procedure);
    logger.debug(`Proc ran successfully`);

    // Parse multiple result sets as per .NET implementation
    // The stored procedure returns multiple result sets that we need to read sequentially
    const recordsets = result.recordsets as any[];

    // Debug: Log the structure of recordsets to understand the data format
    logger.debug(
      `Recordsets structure: ${JSON.stringify(recordsets, null, 2)}`
    );
    logger.debug(`Number of recordsets: ${recordsets?.length || 0}`);

    if (recordsets && recordsets.length > 0) {
      // First result set: Quantity (single integer)
      // Extract the actual value from the first column of the first row
      const quantityRow = recordsets[0]?.[0];
      logger.debug(`Quantity row: ${JSON.stringify(quantityRow)}`);
      lastContractInfoModel.Quantity = quantityRow
        ? (Object.values(quantityRow)[0] as number)
        : 0;

      // Second result set: Contract Detail
      const contractDetailRow = recordsets[1]?.[0];
      logger.debug(`Contract detail row: ${JSON.stringify(contractDetailRow)}`);
      lastContractInfoModel.contractDetail =
        contractDetailRow || ({} as ContractDetailRaw);

      // Third result set: PromotionIdDuringOrder (single string)
      const promotionIdDuringOrderRow = recordsets[2]?.[0];
      logger.debug(
        `PromotionIdDuringOrder row: ${JSON.stringify(
          promotionIdDuringOrderRow
        )}`
      );
      lastContractInfoModel.PromotionIdDuringOrder = promotionIdDuringOrderRow
        ? (Object.values(promotionIdDuringOrderRow)[0] as string)
        : "";

      // Fourth result set: PromotionRateLastRenewal (single string)
      const promotionRateLastRenewalRow = recordsets[3]?.[0];
      logger.debug(
        `PromotionRateLastRenewal row: ${JSON.stringify(
          promotionRateLastRenewalRow
        )}`
      );
      lastContractInfoModel.PromotionRateLastRenewal =
        promotionRateLastRenewalRow
          ? (Object.values(promotionRateLastRenewalRow)[0] as string)
          : "";

      // Fifth result set: PromotionValue (single decimal)
      const promotionValueRow = recordsets[4]?.[0];
      logger.debug(`PromotionValue row: ${JSON.stringify(promotionValueRow)}`);
      lastContractInfoModel.PromotionValue = promotionValueRow
        ? (Object.values(promotionValueRow)[0] as number)
        : 0;

      // Sixth result set: PromotionIdLastRenewal (single string)
      const promotionIdLastRenewalRow = recordsets[5]?.[0];
      logger.debug(
        `PromotionIdLastRenewal row: ${JSON.stringify(
          promotionIdLastRenewalRow
        )}`
      );
      lastContractInfoModel.PromotionIdLastRenewal = promotionIdLastRenewalRow
        ? (Object.values(promotionIdLastRenewalRow)[0] as string)
        : "";

      // Seventh result set: PromotionModel (single object or null)
      const promotionModelRow = recordsets[6]?.[0];
      logger.debug(`PromotionModel row: ${JSON.stringify(promotionModelRow)}`);
      lastContractInfoModel.promotionModel = promotionModelRow || null;
    }

    logger.debug(`Model prepared successfully`);
  } catch (exp) {
    logger.error(
      `Error in GetLastContractInfo DAL method for sub id: ${subscriptionId}. Message: ${
        (exp as Error).message
      }. StackTrace: ${(exp as Error).stack}`
    );
    throw exp;
  }

  console.log("lastContractInfoModel----------------->", lastContractInfoModel);

  logger.debug(
    `Going to return data from DAL layer ${JSON.stringify(
      lastContractInfoModel
    )}`
  );
  return lastContractInfoModel;
}

export function getPromotionPercentage(
  lastContractInfo: LastContractInfoModel
): number {
  let promotionPercentage = 0;

  const status = ComparableString(lastContractInfo.contractDetail.statusreason);

  if (
    status === "NEWPURCHASE" ||
    status === "INCREASED" ||
    status === "DECREASED"
  ) {
    promotionPercentage = lastContractInfo.PromotionValue * 100;
  }

  if (status === "RENEWED") {
    promotionPercentage = Number(lastContractInfo.PromotionRateLastRenewal);
  }

  return Math.round(promotionPercentage * 100) / 100;
}

// export async function getLastContractInfoService(
//   subscriptionId: string
// ): Promise<LastContractInfoDto> {
//   try {
//     logger.info(
//       `Entered into GetLastContractInfo method with subscriptionId=${subscriptionId}`
//     );

//     const lastContractInfoModel = await GetLastContractInfo(subscriptionId);
//     logger.debug(
//       `Data fetched from DAL layer. Going to prepare DTO. ${JSON.stringify(
//         lastContractInfoModel
//       )}`
//     );

//     const lastContractInfoDto: LastContractInfoDto = {
//       data: {} as ContractDetail,
//     };

//     // Debug: Log the fetched model to understand what data we have
//     logger.debug(
//       `Full lastContractInfoModel: ${JSON.stringify(lastContractInfoModel)}`
//     );
//     logger.debug(
//       `Contract detail from model: ${JSON.stringify(
//         lastContractInfoModel.contractDetail
//       )}`
//     );
//     logger.debug(
//       `Contract detail UnitPrice: ${lastContractInfoModel.contractDetail?.UnitPrice}`
//     );
//     logger.debug(
//       `Contract detail Discount: ${lastContractInfoModel.contractDetail?.Discount}`
//     );
//     logger.debug(
//       `Contract detail VAT: ${lastContractInfoModel.contractDetail?.VAT}`
//     );
//     logger.debug(
//       `Contract detail StatusReason: ${lastContractInfoModel.contractDetail?.StatusReason}`
//     );
//     logger.debug(
//       `Promotion model from model: ${JSON.stringify(
//         lastContractInfoModel.promotionModel
//       )}`
//     );

//     // Business logic calculations - exact translation from .NET
//     // lastContractInfoDto.data.qty = lastContractInfoModel.Quantity;

//     console.log("lastContractInfoDto-------->",lastContractInfoDto)

//     // Validate that we have the required data for calculations
//     if (
//       !lastContractInfoModel.contractDetail ||
//       lastContractInfoModel.contractDetail.UnitPrice === undefined ||
//       lastContractInfoModel.contractDetail.UnitPrice === null ||
//       lastContractInfoModel.contractDetail.Discount === undefined ||
//       lastContractInfoModel.contractDetail.VAT === undefined
//     ) {
//       logger.error(
//         `Missing required contract detail data. UnitPrice: ${lastContractInfoModel.contractDetail?.UnitPrice}, Discount: ${lastContractInfoModel.contractDetail?.Discount}, VAT: ${lastContractInfoModel.contractDetail?.VAT}`
//       );
//       throw new Error(
//         `Missing required contract detail data for subscription: ${subscriptionId}`
//       );
//     }

//     // Business logic calculations - exact translation from .NET
//     // Math.Round in .NET with 1 decimal = Math.round(value * 10) / 10
//     // Math.Round in .NET with 2 decimals = Math.round(value * 100) / 100
//     // Math.Round in .NET with 0 decimals = Math.round(value)

//     lastContractInfoDto.data.discount_percentage =
//       Math.round(
//         ((lastContractInfoModel.contractDetail.Discount * 100) /
//           lastContractInfoModel.contractDetail.UnitPrice) *
//           10
//       ) / 10;
//     lastContractInfoDto.data.discounted_unit_price =
//       Math.round(
//         (lastContractInfoModel.contractDetail.UnitPrice -
//           lastContractInfoModel.contractDetail.Discount) *
//           100
//       ) / 100;
//     lastContractInfoDto.data.sub_total =
//       Math.round(
//         lastContractInfoDto.data.discounted_unit_price *
//           lastContractInfoDto.data.qty *
//           100
//       ) / 100;
//     lastContractInfoDto.data.vat_percentage = Math.round(
//       (lastContractInfoModel.contractDetail.VAT * 100) /
//         lastContractInfoDto.data.discounted_unit_price
//     );
//     lastContractInfoDto.data.promotion_percentage = getPromotionPercentage(
//       lastContractInfoModel
//     );
//     lastContractInfoDto.data.net_price =
//       Math.round(
//         (lastContractInfoDto.data.sub_total +
//           (lastContractInfoDto.data.vat_percentage *
//             lastContractInfoDto.data.sub_total) /
//             100) *
//           100
//       ) / 100;
//     lastContractInfoDto.data.unit_price_after_promotion =
//       Math.round(lastContractInfoModel.contractDetail.UnitPrice * 100) / 100;
//     lastContractInfoDto.data.unit_price_before_promotion =
//       Math.round(
//         ((100 * lastContractInfoModel.contractDetail.UnitPrice) /
//           (100 - lastContractInfoDto.data.promotion_percentage)) *
//           100
//       ) / 100;

//     lastContractInfoDto.data.subscription_id = subscriptionId;
//     lastContractInfoDto.data.promotion_id =
//       lastContractInfoModel.contractDetail?.StatusReason?.toLowerCase() ===
//       "renewed"
//         ? lastContractInfoModel.PromotionIdLastRenewal || null
//         : lastContractInfoModel.PromotionIdDuringOrder || null;

//     // Always set promotion dates (matching .NET behavior)
//     if (lastContractInfoModel.promotionModel) {
//       lastContractInfoDto.data.promotion_start_date =
//         lastContractInfoModel.promotionModel.PromotionStartDate;
//       lastContractInfoDto.data.promotion_end_date =
//         lastContractInfoModel.promotionModel.PromotionEndDate;
//     } else {
//       lastContractInfoDto.data.promotion_start_date = null;
//       lastContractInfoDto.data.promotion_end_date = null;
//     }

//     logger.debug(`Prepared dto ${JSON.stringify(lastContractInfoDto)}`);
//     return lastContractInfoDto;
//   } catch (error) {
//     logger.error(`GetLastContractInfo Error:`, error);
//     throw error;
//   }
// }

// function getPromotionPercentage(
//   lastContractInfo: LastContractInfoModel
// ): number {
//   let promotionPercentage = 0;
//   const statusReason =
//     lastContractInfo.contractDetail.StatusReason?.toLowerCase();

//   if (
//     statusReason === "newpurchase" ||
//     statusReason === "increased" ||
//     statusReason === "decreased"
//   ) {
//     promotionPercentage = lastContractInfo.PromotionValue * 100;
//   }

//   if (statusReason === "renewed") {
//     promotionPercentage =
//       parseFloat(lastContractInfo.PromotionRateLastRenewal) || 0;
//   }

//   return Math.round(promotionPercentage * 100) / 100;
// }
