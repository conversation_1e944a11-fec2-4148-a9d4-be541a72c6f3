import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import { getStoreDetails } from "../token/microsoft/getStoreDetailsService";
import { getMsToken } from "../token/microsoft/getTokenService";

/**
 * Prepare Token
 * Matches .NET PrepareToken method
 */
export async function prepareToken(
  req: MedusaRequest,
  storeId: string
): Promise<string> {
  logger.info(`Entered PrepareToken Method and Going to hit BL GetStoreDetails method with storeId-${storeId}`);
  
  // Get Store Details (matching .NET _storeFactory.GetStoreDetails)
  const tokenRequest = await getStoreDetails({
    storeId: storeId,
    brandId: "14", // Default brand ID for Microsoft
    provisionType: "AUTO",
    credentialType: null
  });
  
  logger.info(`Response from BL GetStoreDetails is ${JSON.stringify(tokenRequest)}`);

  logger.info(`Going to hit BL TokenGenerator method with tokenRequest-${JSON.stringify(tokenRequest)}`);
  
  // Generate Token (matching .NET _tokenGenerator.GetToken)
  const tokenResponse = await getMsToken({
    brand: tokenRequest.brand || "",
    client_id: tokenRequest.clientid || "",
    client_secret: tokenRequest.clientsecret || "",
    grant_type: tokenRequest.granttype || "",
    markValue: tokenRequest.markvalue?.toString() || "",
    redirect_uri: tokenRequest.redirecturi || "",
    refresh_token: tokenRequest.token || "",
    resource: tokenRequest.resource || "",
    store_domain: tokenRequest.storedomain || "",
  });

  logger.info(`Got token from BL TokenGenerator method and returning from PrepareToken Method`);

  return tokenResponse.access_token;
}
