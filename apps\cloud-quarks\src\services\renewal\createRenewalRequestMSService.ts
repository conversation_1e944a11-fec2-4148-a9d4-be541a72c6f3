import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import { 
  PatchSubscriptionResponse, 
  RenewalRequestMSMasterModel,
  SubscriptionRenewal,
  patchSubscriptionAsync 
} from "./patchSubscriptionAsync";
import { getRenewalRequestBySKey } from "./renewalDAL";
import { getStoreDetails } from "../token/microsoft/getStoreDetailsService";
import { getMsToken } from "../token/microsoft/getTokenService";

/**
 * Get MS Token
 * Matches .NET GetMSToken method
 */
async function getMSToken(storeId: string, brandId: string): Promise<string> {
  logger.info(`Getting MS Token for storeId: ${storeId}, brandId: ${brandId}`);

  // Get store details (matching .NET token service pattern)
  const storeDetails = await getStoreDetails({
    storeId: storeId,
    brandId: brandId,
    provisionType: "AUTO",
    credentialType: null
  });

  logger.info(`Store details retrieved: ${JSON.stringify(storeDetails)}`);

  // Generate Microsoft token
  const tokenResponse = await getMsToken({
    brand: storeDetails.brand || "",
    client_id: storeDetails.clientid || "",
    client_secret: storeDetails.clientsecret || "",
    grant_type: storeDetails.granttype || "",
    markValue: storeDetails.markvalue?.toString() || "",
    redirect_uri: storeDetails.redirecturi || "",
    refresh_token: storeDetails.token || "",
    resource: storeDetails.resource || "",
    store_domain: storeDetails.storedomain || "",
  });

  if (!tokenResponse || !tokenResponse.access_token) {
    logger.error(`Token generation failed`);
    throw new Error("Failed to generate access token");
  }

  logger.info(`Token generated successfully`);
  return tokenResponse.access_token;
}

/**
 * Create Renewal Request MC
 * Matches .NET CreateRenewalRequestMC method
 */
export async function createRenewalRequestMC(
  req: MedusaRequest,
  renewalRequestMSMaster: RenewalRequestMSMasterModel
): Promise<PatchSubscriptionResponse> {
  logger.info(`Entered into BL CreateRenewalRequestMC API with details ${JSON.stringify(renewalRequestMSMaster)}`);

  // Fetch payload by Skey (matching .NET: SubscriptionRenewal renewPayload = await _renewalDal.GetRenewalRequestBySKey(renewalRequestMSMaster.Skey))
  const renewPayload = await getRenewalRequestBySKey(renewalRequestMSMaster.Skey || 0);
  
  if (!renewPayload) {
    logger.error(`No renewal payload found for SKey: ${renewalRequestMSMaster.Skey}`);
    return {
      Status: "FAIL",
      ErrorMessage: "No renewal request found for the provided SKey",
      MSResponse: undefined,
      MSErrorResponse: undefined
    };
  }

  // Validate Subscription
  logger.info(`Renew payload: ${JSON.stringify(renewPayload)}`);

  // Get MS Token (matching .NET: string token = await _tokenService.GetMSToken(renewalRequestMSMaster.StoreId, renewalRequestMSMaster.BrandId))
  const token = await getMSToken(renewalRequestMSMaster.StoreId || "", renewalRequestMSMaster.BrandId || "");

  // Phase 1 implementation: pass autorenewal, Subscription only to MS for request
  // (matching .NET: response = await PatchSubscriptionAsync(renewPayload, token, renewalRequestMSMaster))
  const response = await patchSubscriptionAsync(req, renewPayload, token, renewalRequestMSMaster);

  logger.info(`Going to return response as ${JSON.stringify(response)}`);
  return response;
}
