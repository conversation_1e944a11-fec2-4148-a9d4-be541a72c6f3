import logger from "../../utils/logger";
import { getSqlServerConnection } from "../../utils/sqlServerClient";
import { getDeliverySequenceAddresses } from "./getDeliverySequenceAddressesService";

export interface StoreBusinessPartnerMapModel {
  Store: string;
  BusinessPartner: string;
}

export interface SapCustomerUniqueInfoModel {
  CustomerCode: string;
  CompanyCode: string;
}

export interface MulesoftCredential {
  clientId: string;
  clientSecret: string;
  businessPartnerCode: string;
  businessPartner: string;
}

export interface EndPointResponseModels {
  httpStatusCode: number;
  content: any;
  errorMessage?: string;
  isSuccessStatusCode: boolean;
  headers?: Record<string, string>;
}

export interface SapCustomerInput {
  customerCode: string;
  companyCode: string;
  storeId: string;
}

const sapIntegrationConfigModel = {
  baseUrl: "https://sap-api.example.com",
  deliverySeqUrl: "delivery-sequence-endpoint",
};

export type SapIntegrationConfigModel = {
  baseUrl: string;
  deliverySeqUrl: string;
  validateCheckOut: string;
  getCreditInfo: string;
  lookupValue: string;
  clientId: string;
  clientSecret: string;
  grantType: string;
};

export type Address = {
  PartnerType: string
  PartnerCode: string
  Name: string
  Address1: string
  Address2: string
  Address3: string
  City: string
  State: string
  PinCode: string
  PaymentTermsCode: string
  PaymentType: string
}

export type SapDeliverySequenceResponseModel = {
  Status: string
  Message: string
  CustomerParties: Address[]
}

export async function GetAddressFromSAP(
  req: any,
  sapCustomerUniqueInfoModel: SapCustomerUniqueInfoModel,
  storeId: string
): Promise<EndPointResponseModels> {
  logger.info(
    `Entered into getDeliverySequenceAddressesService | customerInfo=${JSON.stringify(
      sapCustomerUniqueInfoModel
    )}, storeId=${storeId}`
  );

  logger.info(`Going to hit getAllBusinessPartnerStoreMapping service`);

  const businessPartnerStoreMappings =
    await getAllBusinessPartnerStoreMapping();

  logger.info(
    `Store-BP mappings are ${JSON.stringify(businessPartnerStoreMappings)}`
  );

  logger.info(`Going to get our ${storeId} business partner from all data`);

  // Find matching business partner from store ID
  const businessPartnerEntry = businessPartnerStoreMappings.find(
    (x) =>
      x.Store.replace(/\s+/g, "").toUpperCase() ===
      storeId.replace(/\s+/g, "").toUpperCase()
  );

  if (!businessPartnerEntry) {
    throw new Error(`Business partner not found for storeId: ${storeId}`);
  }

  const businessPartner = businessPartnerEntry.BusinessPartner;

  console.log("business partner--------------->",businessPartner)
  
  logger.info(`Business partner is ${businessPartner}`);
  
  logger.info(
    `Going to get mulesoft credentials from config for BP-${businessPartner}`
  );
  console.log("business partner---------req.headers------>",req.headers)

  // Find mulesoft credentials from preloaded headers (assumed to be in `requestHeader.Header`)
  // const mulesoftCredentials = req.headers.find(
  //   (x: { BusinessPartner: string }) =>
  //     x.BusinessPartner.replace(/\s+/g, "").toLowerCase() ===
  //     businessPartner.replace(/\s+/g, "").toLowerCase()
  // );

  const mulesoftCredentials = {
    clientId: `9100001001`,
    clientSecret: `HHJADEUI22434KJKFJJFJF`,
    businessPartnerCode: `CHDuYfP6fdnlC/b/VNpS6GLFxdMILd2IaJMZhaslKYY=`,
  }

  // console.log("mulesoftCredentials------------>",mulesoftCredentials)

  // if (!mulesoftCredentials) {
  //   throw new Error(
  //     `Mulesoft credentials not found for business partner: ${businessPartner}`
  //   );
  // }

  logger.info(
    `Going to hit getDeliverySequenceAddresses method of SAP integration service with model ${JSON.stringify(
      sapCustomerUniqueInfoModel
    )} and mulesoft credentials`
  );

  const delSeqDataFromSap = await getDeliverySequenceAddresses(
    req,
    sapCustomerUniqueInfoModel,
    mulesoftCredentials
  );

  logger.info(
    `Returning response from BL layer getDeliverySequenceAddresses: ${JSON.stringify(
      delSeqDataFromSap
    )}`
  );

  return delSeqDataFromSap;
}

export async function getAllBusinessPartnerStoreMapping(): Promise<
  StoreBusinessPartnerMapModel[]
> {
  logger.info(
    `Entered into business service GetAllBusinessPartnerStoreMapping`
  );

  const mappings = await getAllStoreBusinessPartnerMap();

  logger.info(`Got BP-Store mappings: ${JSON.stringify(mappings)}`);

  return mappings;
}

export async function getAllStoreBusinessPartnerMap(): Promise<
  StoreBusinessPartnerMapModel[]
> {
  const query = "SELECT Store, BusinessPartner FROM tbllnkstorebusinesspartner";

  logger.info(`Going to DB with query: ${query}`);

  const pool = await getSqlServerConnection();
  const result = await pool.request().query(query);

  const data: StoreBusinessPartnerMapModel[] = result.recordset;

  logger.info(`Got store-BP mappings from DB: ${JSON.stringify(data)}`);

  return data;
}
