import { model } from '@medusajs/framework/utils';
import { CqCompanyRegistration } from './company-registration';

export const CompanyDocument = model.define('cq_company_document', {
  id: model.id().primaryKey(),
  document_id: model.text(),
  company_registration: model.belongsTo(() => CqCompanyRegistration),
  document_uploaded_link: model.text().nullable(),
  description: model.text().nullable(),
  created_by: model.text().nullable(),
  updated_by: model.text().nullable(),
  deleted_by: model.text().nullable(),
});
