import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import {
  ErrorResponseModel
} from "../../../../types/responses/customResponse";
import {
  validateRegisterSubscriptionRequest,
  SubscriptionObjType
} from "../../../../validators/subscription/validateRegisterSubscriptionValidator";
import { getSubscriptionDetailsDAL } from "../../../../services/subscription/getSubscriptionDetailsDAL";
import { getMsToken } from "../../../../services/token/microsoft/getTokenService";
import { updateSubscriptionIterationService } from "../../../../services/subscription/updateSubscriptionIterationService";
import { getRetryRegisterSubscriptionCount } from "../../../../utils/constants/subscriptionConstants";

/**
 * POST /v1/subscription/validateRegisterSubscription
 * Validate Register Subscription API - matches .NET ValidateRegisterSubscription controller method
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req);

  logger.info(`[${correlationId}] Entered into ValidateRegisterSubscription API with SubscriptionObj-${JSON.stringify(req.body)} and going to call ValidateRegisterSubscription service method`);

  try {
    // Validate the request body
    const validationErrors = validateRegisterSubscriptionRequest(req.body);

    if (validationErrors.length > 0) {
      logger.error(`[${correlationId}] Validation failed: ${JSON.stringify(validationErrors)}`);

      const errorResponse: ErrorResponseModel = {
        errors: validationErrors,
        statusCode: 400,
      };

      return res.status(400).json(errorResponse);
    }

    const subscriptionObj: SubscriptionObjType = req.body as SubscriptionObjType;

    logger.info(`[${correlationId}] Request validation passed, proceeding with subscription validation`);

    // Call the main service method (matching .NET: _subscriptionFactory.ValidateRegisterSubscription(subscriptionObj.SubscriptionId))
    const result = await validateRegisterSubscriptionService(req, subscriptionObj.SubscriptionId);

    logger.info(`[${correlationId}] ValidateRegisterSubscription service completed: ${result}`);

    return res.status(200).json(result);

  } catch (error: any) {
    // Log error (matching .NET exception handling)
    logger.error(`[${correlationId}] Error in ValidateRegisterSubscription, errorMessage: ${error.message}, StackTrace: ${error.stack}`);

    // Return error message (matching .NET: return "Unable to find the subscription on Microsoft")
    return res.status(500).json("Unable to find the subscription on Microsoft");
  }
}

/**
 * Main ValidateRegisterSubscription service method
 * Matches .NET ValidateRegisterSubscription service method
 */
async function validateRegisterSubscriptionService(
  req: MedusaRequest,
  subscriptionId: string
): Promise<string> {
  logger.info(`Entered into ValidateRegisterSubscription Service method with subscriptionId ${subscriptionId}`);

  // Check if subscriptionId is not empty (matching .NET: !string.IsNullOrEmpty(SubscriptionId))
  if (!subscriptionId || subscriptionId.trim() === '') {
    logger.info(`Returning empty response`);
    return "";
  }

  try {
    const retryCount = 0;

    // Get retry configuration (matching .NET: Convert.ToInt16(_configuration.GetSection("AppSettings:RetryRegisterSubscription")?.Value))
    const configModule = req.scope.resolve("configModule");
    const totalRetry = getRetryRegisterSubscriptionCount(configModule);

    // Get subscription details from database (matching .NET: DataTable dt = GetSubscriptionDetails(SubscriptionId))
    const subscriptionDetails = await getSubscriptionDetailsDAL(subscriptionId);

    // Generate Token (matching .NET token generation logic)
    const token = await getMsToken({
      brand: "", // Will be populated from subscription details if needed
      client_id: subscriptionDetails.Clientid,
      client_secret: subscriptionDetails.Clientsecret,
      grant_type: subscriptionDetails.granttype,
      markValue: "0", // Default value
      redirect_uri: "http://localhost", // Matches .NET default
      refresh_token: subscriptionDetails.Token,
      resource: subscriptionDetails.Resource,
      store_domain: subscriptionDetails.StoreDomain,
    });

    if (!token || !token.access_token) {
      logger.error(`Token generation failed`);
      return "Unable to find the subscription on Microsoft";
    }

    logger.info(`Going to call updateSubscriptionIteration Service method with CustomerId ${subscriptionDetails.CustomerId},SubscriptionId ${subscriptionId}, retryCount ${retryCount}, totalRetry ${totalRetry}`);

    // Call updateSubscriptionIteration with retry logic (matching .NET: updateSubscriptionIteration(...))
    return await updateSubscriptionIterationService(
      req,
      subscriptionDetails.CustomerId,
      subscriptionId,
      token.access_token,
      retryCount,
      totalRetry
    );

  } catch (error: any) {
    logger.error(`Error in validateRegisterSubscriptionService: ${error.message}`);
    logger.info(`Returning empty response`);
    return "";
  }
}