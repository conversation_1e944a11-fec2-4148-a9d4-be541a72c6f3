import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import {
  stringConstants,
  sendTransactionalEmail,
  renderResetPasswordEmail,
} from '@org/utils';
import { generateJwtToken } from '@medusajs/framework/utils';
import { MyNotificationProviderService } from '@org/ses';
import { forgotPasswordSchema } from '../../user/validators';
import { z } from 'zod';
import { CQUser_MODULE } from '../../../../modules/cq-user';
import CQUserService from '../../../../modules/cq-user/service';

type forgotPasswordSchemaType = z.infer<typeof forgotPasswordSchema>;
/**
 * @openapi
 * /v1/user/forgot-password:
 *   post:
 *     summary: Request password reset link for user
 *     tags:
 *       - Users
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 example: <EMAIL>
 *     responses:
 *       200:
 *         description: Password reset link sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Password reset link has been sent to your email.
 *       404:
 *         description: User not found with the provided email
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: User with this email not found
 *       500:
 *         description: Error sending reset email or internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Error sending reset email
 */

export async function POST(
  req: MedusaRequest<forgotPasswordSchemaType>,
  res: MedusaResponse
) {
  const userModuleService: CQUserService = req.scope.resolve(CQUser_MODULE);
  const { email: userEmail } = req.validatedBody;

  try {
    const [users, count] = await userModuleService.listAndCountCqUsers({
      email: userEmail,
    });

    if (count === 0 || users.length === 0) {
      return res.status(404).json({
        message: stringConstants.NOT_FOUND('User with this email'),
      });
    }

    const resetToken = generateJwtToken(
      { email: userEmail },
      {
        secret: process.env.JWT_SECRET as string,
        expiresIn: '1h',
      }
    );

    const sesNotificationService = new MyNotificationProviderService({
      region: process.env.AWS_REGION as string,
      accessKeyId: process.env.AWS_ACCESS_KEY_ID as string,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY as string,
    });

    const resetLink = `${process.env.FRONTEND_URL}/reset-password/${resetToken}/?name=${users[0].first_name} ${users[0].last_name}`;
    const data = {
      to: userEmail,
      template: resetLink,
    };

    // try {
    //   await sesNotificationService.send(
    //     data,
    //     process.env.SES_FROM_EMAIL as string
    //   );
    // } catch (error) {
    //   return res.status(500).json({
    //     message: stringConstants.ERROR_SENDING_EMAIL(),
    //   });
    // }
    const name = `${users[0].first_name} ${users[0].last_name}`;
    const htmlBody = await renderResetPasswordEmail(name, resetLink);
    await sendTransactionalEmail({
      toEmail: req.body?.email,
      subjectTemplate: 'Reset Your Cloudquarks Password',
      htmlContentTemplate: htmlBody,
      senderEmail: '<EMAIL>',
      senderName: 'Redington',
    });

    return res.status(200).json({
      message: stringConstants.PASSWORD_RESET(),
    });
  } catch (err: any) {
    return res.status(500).json({
      message: stringConstants.INTERNAL_SERVER_ERROR(),
    });
  }
}