import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { verify } from 'jsonwebtoken';
import { Modules } from '@medusajs/framework/utils';
import GuestUserService from '../../../../../../modules/cq-user/service';
import { CQUser_MODULE } from '../../../../../../modules/cq-user';
import { stringConstants } from '@org/utils';

/**
 * @openapi
 * /v1/user/signup/reset-password/{token}:
 *   put:
 *     summary: Set or reset the password using token
 *     tags:
 *       - Signup
 *     parameters:
 *       - in: path
 *         name: token
 *         required: true
 *         schema:
 *           type: string
 *         description: JWT token containing the email for verification
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - password
 *             properties:
 *               password:
 *                 type: string
 *                 format: password
 *                 example: NewStrongPassword123!
 *     responses:
 *       200:
 *         description: Password set successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                 message:
 *                   type: string
 *                   example: Password updated successfully
 *       400:
 *         description: Invalid token or missing password
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Invalid token
 *       404:
 *         description: Guest user not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: User not found
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error. Please try again later.
 */

export async function PUT(req: MedusaRequest, res: MedusaResponse) {
  const authModuleService: any = req.scope.resolve(Modules.AUTH);
  const { token } = req.params;
  const { password } = req.body as {
    email: string;
    password: string;
  };

  try {
    const decodedToken: any = verify(token, process.env.JWT_SECRET as string);
    const userEmail = decodedToken?.email;
    if (!userEmail) {
      return res.status(400).json({ message: 'Invalid token' });
    }

    const guestUserService: GuestUserService = req.scope.resolve(CQUser_MODULE);

    // ***Later Use***
    // const guestUser = await guestUserService.retrieveGuestUser(req.params.id);

    const [users] = await guestUserService.listAndCountCqUsers({
      email: userEmail.toLowerCase().trim(),
    });
    const guestUser = users.length ? users[0] : null;

    if (!guestUser) {
      res.status(404).json({
        message: stringConstants.NOT_FOUND('User'),
      });
    }

    const guestUserId = guestUser?.id;

    // 1. Create Auth Identity with guest user ID in app_metadata
    await authModuleService.createAuthIdentities({
      provider_identities: [
        {
          provider: 'emailpass',
          entity_id: userEmail.toLowerCase().trim(),
          provider_metadata: { password },
        },
      ],

      // #UPDATE check user id is stored in app metadata or not in auth identity table
      app_metadata: { cq_user_id: guestUserId },
      // app_metadata: {},
    });

    await authModuleService.updateProvider('emailpass', {
      entity_id: userEmail.toLowerCase().trim(),
      password: password,
    });

    if (guestUser) {
      await guestUserService.updateCqUsers({
        id: guestUserId,
        is_active: true,
        is_password_set: true,
      });
    }

    return res.status(200).json({
      message: stringConstants.UPDATED_SUCCESSFULLY('Password'),
    });
  } catch (err: any) {
    console.log(err);

    return res
      .status(500)
      .json({ message: stringConstants.INTERNAL_SERVER_ERROR() });
  }
}
