import { ExecArgs } from "@medusajs/framework/types";
import { CQUser_MODULE } from "../modules/cq-user";

export default async function seedStatus({ container }: ExecArgs) {
	const userService: any = container.resolve(CQUser_MODULE);

	const predefinedStatuses = [
		{ name: "Approved", key: "approved" },
		{ name: "Pending", key: "pending" },
		{ name: "Rejected", key: "rejected" },
	];

	const existingStatuses = await userService.listStatuses({});
	const existingKeys = new Set(
		existingStatuses.map((s: any) => s.key.toLowerCase())
	);

	const statusesToCreate = predefinedStatuses
		.filter((s) => {
			if (!s.key || !s.name) {
				console.warn(`Invalid status skipped: ${JSON.stringify(s)}`);
				return false;
			}
			return !existingKeys.has(s.key.toLowerCase());
		})
		.map((s) => ({ ...s, metadata: {} }));

	if (statusesToCreate.length > 0) {
		await Promise.all(
			statusesToCreate.map((status) => userService.createStatuses(status))
		);
	}
}
