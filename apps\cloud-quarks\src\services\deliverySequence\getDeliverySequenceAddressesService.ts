import logger from "../../utils/logger";

import {
  EndPointResponseModel,
  PostMethodWithRequestJsonBodyAndHeaders400Handle,
} from "../externalService/externalEndPointService";

export interface StoreBusinessPartnerMapModel {
  Store: string;
  BusinessPartner: string;
}

export interface SapCustomerUniqueInfoModel {
  CustomerCode: string;
  CompanyCode: string;
}

export interface MulesoftCredential {
  clientId: string;
  clientSecret: string;
  businessPartnerCode: string;
}

export interface EndPointResponseModels {
  HttpStatusCode: number;
  Content: any;
  ErrorMessage?: string;
  IsSuccessStatusCode: boolean;
  Headers?: Record<string, string>;
}

export interface SapCustomerInput {
  customerCode: string;
  companyCode: string;
  storeId: string;
}

const sapIntegrationConfigModel = {
  baseUrl: "https://api.partnercenter.microsoft.com/api",
  deliverySeqUrl: "/sys/cloudqx/Reports/CustomerParties",
};

export type SapIntegrationConfigModel = {
  baseUrl: string;
  deliverySeqUrl: string;
  validateCheckOut: string;
  getCreditInfo: string;
  lookupValue: string;
  clientId: string;
  clientSecret: string;
  grantType: string;
};

export type Address = {
  PartnerType: string;
  PartnerCode: string;
  Name: string;
  Address1: string;
  Address2: string;
  Address3: string;
  City: string;
  State: string;
  PinCode: string;
  PaymentTermsCode: string;
  PaymentType: string;
};

export type SapDeliverySequenceResponseModel = {
  Status: string;
  Message: string;
  CustomerParties: Address[];
};

export interface ExtendedEndPointResponseModel extends EndPointResponseModel {
  parsedContent?: SapDeliverySequenceResponseModel;
}

export async function getDeliverySequenceAddresses(
  req: any,
  sapCustomerUniqueInfoModel: SapCustomerUniqueInfoModel,
  mulesoftCredentials: MulesoftCredential
): Promise<ExtendedEndPointResponseModel> {
  logger.info(
    `Entered into SAP integration service method with customer info: ${JSON.stringify(
      sapCustomerUniqueInfoModel
    )} and mulesoft credentials: ${JSON.stringify(mulesoftCredentials)}`
  );

  logger.info(
    `Going to get delivery sequence API URL from SAP config: ${JSON.stringify(
      sapIntegrationConfigModel
    )}`
  );

  const urlToGetDeliverySeq = `${sapIntegrationConfigModel.baseUrl}/${sapIntegrationConfigModel.deliverySeqUrl}`;

  logger.info(`URL prepared to get delivery sequence: ${urlToGetDeliverySeq}`);

  // Prepare the request body as JSON
  const requestBodyInJson = JSON.stringify(sapCustomerUniqueInfoModel);

  // Prepare headers as an object (like Dictionary<string, string>)
  // const headerList: Record<string, string> = {
  //   client_id: mulesoftCredentials.clientId,
  //   client_secret: mulesoftCredentials.clientSecret,
  //   business_partner: mulesoftCredentials.businessPartnerCode,
  // };9100001001
    const headerList: Record<string, string> = {
    client_id: `9100001001`,
    client_secret: `HHJADEUI22434KJKFJJFJF`,
    business_partner: `CHDuYfP6fdnlC/b/VNpS6GLFxdMILd2IaJMZhaslKYY=`,
  };
  console.log("headerList------------>",headerList)
  logger.info(
    `Going to hit ExternalEndPoint PostMethodWithRequestJsonBodyAndHeaders400Handle method with url-${urlToGetDeliverySeq}, RequestBody-${requestBodyInJson}, headerlist-${JSON.stringify(
      headerList
    )}`
  );

  const configModule = req.scope.resolve("configModule");

  const response = await PostMethodWithRequestJsonBodyAndHeaders400Handle(
    {
      url: urlToGetDeliverySeq,
      body: requestBodyInJson,
      headers: headerList,
      isVendorHit: false,
      module: "GetDeliverySequenceAddresses",
    },
    configModule
  );
  logger.info(
    `Got response from external end point service as ${JSON.stringify(
      response
    )}`
  );
  logger.info(
    `Response's IsSuccessStatusCode is ${response.isSuccessStatusCode}`
  );

  logger.info(
    `Got response from external endpoint: ${JSON.stringify(response)}`
  );
  logger.info(`Response isSuccessStatusCode: ${response.isSuccessStatusCode}`);

  let parsedContent: SapDeliverySequenceResponseModel | undefined = undefined;

  if (response.isSuccessStatusCode && response.content) {
    try {
      parsedContent = JSON.parse(
        response.content
      ) as SapDeliverySequenceResponseModel;
      logger.info(`Parsed response content: ${JSON.stringify(parsedContent)}`);
    } catch (err) {
      logger.warn("Failed to parse response content as JSON");
    }
  }

  logger.info(
    `Returning from getDeliverySequenceAddresses. Raw response: ${JSON.stringify(
      response
    )}`
  );

  return {
    ...response,
    parsedContent,
  };
}
