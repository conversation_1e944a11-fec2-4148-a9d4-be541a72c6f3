import { model } from "@medusajs/framework/utils"

const EmailOtpVerification = model
  .define("cq_email_otp_verification", {
    id: model.id().primaryKey(),
    email: model.text().nullable(),
    mobile_number: model.text().nullable(),
    otp_code: model.text(),
    expires_at: model.dateTime(),
    is_verified: model.boolean().default(false),
    is_registered: model.boolean().default(false),
  })
  .checks([
    {
      name: "email_or_mobile_required",
      expression: (columns: { email: string; mobile_number: string }) =>
        `(${columns.email} IS NOT NULL OR ${columns.mobile_number} IS NOT NULL)`,
    },
  ])

export default EmailOtpVerification