import { z } from 'zod';

export const toDisplayName = (str: string): string =>
  str.toLowerCase().replace(/\b\w/g, (char) => char.toUpperCase());

export const countrySchema = z.object({
  region_id: z.string({
    required_error: 'region_id is required',
  }),
  name: z
    .string({
      required_error: 'Country name is required',
    })
    .transform((val) => val.toUpperCase()),
  country_flag: z.string({
    required_error: 'Country flag URL is required',
  }),
  num_code: z
    .string()
    .length(3, 'num_code must be exactly 3 digits')
    .regex(/^\d{3}$/, 'num_code must contain numbers only'),
  iso_3: z
    .string()
    .length(3, 'iso_3 must be exactly 3 letters')
    .regex(/^[A-Za-z]{3}$/, 'iso_3 must contain alphabets only'),
  iso_2: z
    .string()
    .length(2, 'iso_2 must be exactly 2 letters')
    .regex(/^[A-Za-z]{2}$/, 'iso_2 must contain alphabets only'),
  country_code: z.string({
    required_error: 'country_code is required',
  }),
});
