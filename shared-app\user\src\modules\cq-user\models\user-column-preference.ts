import { model } from '@medusajs/framework/utils';
import CQUser from './cq_user';
import ColumnMaster from './column-master';
import Page from './page';

const UserColumnPreference = model.define('cq_user_column_preference', {
  id: model.id().primaryKey(),
  sort_order: model.number().default(0),
  user: model.belongsTo(() => CQUser, { mappedBy: 'preferences' }),
  column_master: model.belongsTo(() => ColumnMaster, {
    mappedBy: 'preferences',
  }),
  table_master: model.belongsTo(() => Page, { mappedBy: 'preferences' }),
});

export default UserColumnPreference;
