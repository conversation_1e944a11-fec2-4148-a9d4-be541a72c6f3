{"namespaces": ["public"], "name": "public", "tables": [{"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "name": {"name": "name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "key": {"name": "key", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "metadata": {"name": "metadata", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "json"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_company_type", "schema": "public", "indexes": [{"keyName": "IDX_cq_company_type_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_company_type_deleted_at\" ON \"cq_company_type\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_company_type_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "country_code": {"name": "country_code", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "country_flag": {"name": "country_flag", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "is_custom": {"name": "is_custom", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "true", "mappedType": "boolean"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_country_extension", "schema": "public", "indexes": [{"keyName": "IDX_cq_country_extension_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_country_extension_deleted_at\" ON \"cq_country_extension\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_country_extension_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "name": {"name": "name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "key": {"name": "key", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "metadata": {"name": "metadata", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "json"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_designation", "schema": "public", "indexes": [{"keyName": "IDX_cq_designation_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_designation_deleted_at\" ON \"cq_designation\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_designation_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "document": {"name": "document", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "isActive": {"name": "isActive", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "true", "mappedType": "boolean"}, "is_company_specific": {"name": "is_company_specific", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_document", "schema": "public", "indexes": [{"keyName": "IDX_cq_document_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_document_deleted_at\" ON \"cq_document\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_document_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "email": {"name": "email", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "mobile_number": {"name": "mobile_number", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "otp_code": {"name": "otp_code", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "expires_at": {"name": "expires_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "mappedType": "datetime"}, "is_verified": {"name": "is_verified", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "is_registered": {"name": "is_registered", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_email_otp_verification", "schema": "public", "indexes": [{"keyName": "IDX_cq_email_otp_verification_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_email_otp_verification_deleted_at\" ON \"cq_email_otp_verification\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_email_otp_verification_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [{"name": "email_or_mobile_required", "expression": "(email IS NOT NULL OR mobile_number IS NOT NULL)", "definition": "check (((email IS NOT NULL OR mobile_number IS NOT NULL)))"}], "foreignKeys": {}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "name": {"name": "name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "key": {"name": "key", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "metadata": {"name": "metadata", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "json"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_status", "schema": "public", "indexes": [{"keyName": "IDX_cq_status_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_status_deleted_at\" ON \"cq_status\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_status_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "first_name": {"name": "first_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "last_name": {"name": "last_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "email": {"name": "email", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "country_code": {"name": "country_code", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "mobile_number": {"name": "mobile_number", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "company_name": {"name": "company_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "redington_sales_reference": {"name": "redington_sales_reference", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "profile_pic_file_name": {"name": "profile_pic_file_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "is_active": {"name": "is_active", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "is_delete": {"name": "is_delete", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "is_password_set": {"name": "is_password_set", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "has_sales_reference": {"name": "has_sales_reference", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "terms_and_conditions": {"name": "terms_and_conditions", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "source": {"name": "source", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "is_onboarded": {"name": "is_onboarded", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "cq_designation_id": {"name": "cq_designation_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "registered_country_id": {"name": "registered_country_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_by_id": {"name": "created_by_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "updated_by_id": {"name": "updated_by_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "deleted_by_id": {"name": "deleted_by_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "metadata": {"name": "metadata", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "region_id": {"name": "region_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_user", "schema": "public", "indexes": [{"keyName": "IDX_cq_user_email_unique", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE UNIQUE INDEX IF NOT EXISTS \"IDX_cq_user_email_unique\" ON \"cq_user\" (email) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_user_cq_designation_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_user_cq_designation_id\" ON \"cq_user\" (cq_designation_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_user_created_by_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_user_created_by_id\" ON \"cq_user\" (created_by_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_user_updated_by_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_user_updated_by_id\" ON \"cq_user\" (updated_by_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_user_deleted_by_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_user_deleted_by_id\" ON \"cq_user\" (deleted_by_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_user_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_user_deleted_at\" ON \"cq_user\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_user_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"cq_user_cq_designation_id_foreign": {"constraintName": "cq_user_cq_designation_id_foreign", "columnNames": ["cq_designation_id"], "localTableName": "public.cq_user", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_designation", "updateRule": "cascade"}, "cq_user_created_by_id_foreign": {"constraintName": "cq_user_created_by_id_foreign", "columnNames": ["created_by_id"], "localTableName": "public.cq_user", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_user", "deleteRule": "set null", "updateRule": "cascade"}, "cq_user_updated_by_id_foreign": {"constraintName": "cq_user_updated_by_id_foreign", "columnNames": ["updated_by_id"], "localTableName": "public.cq_user", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_user", "deleteRule": "set null", "updateRule": "cascade"}, "cq_user_deleted_by_id_foreign": {"constraintName": "cq_user_deleted_by_id_foreign", "columnNames": ["deleted_by_id"], "localTableName": "public.cq_user", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_user", "deleteRule": "set null", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "name": {"name": "name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "region_country_id": {"name": "region_country_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "default": {"name": "default", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "metadata": {"name": "metadata", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "created_by_id": {"name": "created_by_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "updated_by_id": {"name": "updated_by_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "deleted_by_id": {"name": "deleted_by_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_store", "schema": "public", "indexes": [{"keyName": "IDX_cq_store_created_by_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_store_created_by_id\" ON \"cq_store\" (created_by_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_store_updated_by_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_store_updated_by_id\" ON \"cq_store\" (updated_by_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_store_deleted_by_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_store_deleted_by_id\" ON \"cq_store\" (deleted_by_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_store_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_store_deleted_at\" ON \"cq_store\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_store_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"cq_store_created_by_id_foreign": {"constraintName": "cq_store_created_by_id_foreign", "columnNames": ["created_by_id"], "localTableName": "public.cq_store", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_user", "deleteRule": "set null", "updateRule": "cascade"}, "cq_store_updated_by_id_foreign": {"constraintName": "cq_store_updated_by_id_foreign", "columnNames": ["updated_by_id"], "localTableName": "public.cq_store", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_user", "deleteRule": "set null", "updateRule": "cascade"}, "cq_store_deleted_by_id_foreign": {"constraintName": "cq_store_deleted_by_id_foreign", "columnNames": ["deleted_by_id"], "localTableName": "public.cq_store", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_user", "deleteRule": "set null", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "persona": {"name": "persona", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "key": {"name": "key", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "metadata": {"name": "metadata", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "order_id": {"name": "order_id", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "created_by_id": {"name": "created_by_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "updated_by_id": {"name": "updated_by_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "deleted_by_id": {"name": "deleted_by_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_persona", "schema": "public", "indexes": [{"keyName": "IDX_cq_persona_persona_unique", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE UNIQUE INDEX IF NOT EXISTS \"IDX_cq_persona_persona_unique\" ON \"cq_persona\" (persona) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_persona_created_by_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_persona_created_by_id\" ON \"cq_persona\" (created_by_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_persona_updated_by_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_persona_updated_by_id\" ON \"cq_persona\" (updated_by_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_persona_deleted_by_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_persona_deleted_by_id\" ON \"cq_persona\" (deleted_by_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_persona_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_persona_deleted_at\" ON \"cq_persona\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_persona_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"cq_persona_created_by_id_foreign": {"constraintName": "cq_persona_created_by_id_foreign", "columnNames": ["created_by_id"], "localTableName": "public.cq_persona", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_user", "deleteRule": "set null", "updateRule": "cascade"}, "cq_persona_updated_by_id_foreign": {"constraintName": "cq_persona_updated_by_id_foreign", "columnNames": ["updated_by_id"], "localTableName": "public.cq_persona", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_user", "deleteRule": "set null", "updateRule": "cascade"}, "cq_persona_deleted_by_id_foreign": {"constraintName": "cq_persona_deleted_by_id_foreign", "columnNames": ["deleted_by_id"], "localTableName": "public.cq_persona", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_user", "deleteRule": "set null", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "role": {"name": "role", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "order_id": {"name": "order_id", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "metadata": {"name": "metadata", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "created_by_id": {"name": "created_by_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "updated_by_id": {"name": "updated_by_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "deleted_by_id": {"name": "deleted_by_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "persona_id": {"name": "persona_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_role", "schema": "public", "indexes": [{"keyName": "IDX_cq_role_created_by_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_role_created_by_id\" ON \"cq_role\" (created_by_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_role_updated_by_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_role_updated_by_id\" ON \"cq_role\" (updated_by_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_role_deleted_by_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_role_deleted_by_id\" ON \"cq_role\" (deleted_by_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_role_persona_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_role_persona_id\" ON \"cq_role\" (persona_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_role_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_role_deleted_at\" ON \"cq_role\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_role_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"cq_role_created_by_id_foreign": {"constraintName": "cq_role_created_by_id_foreign", "columnNames": ["created_by_id"], "localTableName": "public.cq_role", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_user", "deleteRule": "set null", "updateRule": "cascade"}, "cq_role_updated_by_id_foreign": {"constraintName": "cq_role_updated_by_id_foreign", "columnNames": ["updated_by_id"], "localTableName": "public.cq_role", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_user", "deleteRule": "set null", "updateRule": "cascade"}, "cq_role_deleted_by_id_foreign": {"constraintName": "cq_role_deleted_by_id_foreign", "columnNames": ["deleted_by_id"], "localTableName": "public.cq_role", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_user", "deleteRule": "set null", "updateRule": "cascade"}, "cq_role_persona_id_foreign": {"constraintName": "cq_role_persona_id_foreign", "columnNames": ["persona_id"], "localTableName": "public.cq_role", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_persona", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "permission": {"name": "permission", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "metadata": {"name": "metadata", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "created_by_id": {"name": "created_by_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "updated_by_id": {"name": "updated_by_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "deleted_by_id": {"name": "deleted_by_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_permission", "schema": "public", "indexes": [{"keyName": "IDX_cq_permission_permission_unique", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE UNIQUE INDEX IF NOT EXISTS \"IDX_cq_permission_permission_unique\" ON \"cq_permission\" (permission) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_permission_created_by_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_permission_created_by_id\" ON \"cq_permission\" (created_by_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_permission_updated_by_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_permission_updated_by_id\" ON \"cq_permission\" (updated_by_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_permission_deleted_by_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_permission_deleted_by_id\" ON \"cq_permission\" (deleted_by_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_permission_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_permission_deleted_at\" ON \"cq_permission\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_permission_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"cq_permission_created_by_id_foreign": {"constraintName": "cq_permission_created_by_id_foreign", "columnNames": ["created_by_id"], "localTableName": "public.cq_permission", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_user", "deleteRule": "set null", "updateRule": "cascade"}, "cq_permission_updated_by_id_foreign": {"constraintName": "cq_permission_updated_by_id_foreign", "columnNames": ["updated_by_id"], "localTableName": "public.cq_permission", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_user", "deleteRule": "set null", "updateRule": "cascade"}, "cq_permission_deleted_by_id_foreign": {"constraintName": "cq_permission_deleted_by_id_foreign", "columnNames": ["deleted_by_id"], "localTableName": "public.cq_permission", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_user", "deleteRule": "set null", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "module": {"name": "module", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "metadata": {"name": "metadata", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "order_id": {"name": "order_id", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "created_by_id": {"name": "created_by_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "updated_by_id": {"name": "updated_by_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "deleted_by_id": {"name": "deleted_by_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "persona_id": {"name": "persona_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_module", "schema": "public", "indexes": [{"keyName": "IDX_cq_module_module_unique", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE UNIQUE INDEX IF NOT EXISTS \"IDX_cq_module_module_unique\" ON \"cq_module\" (module) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_module_created_by_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_module_created_by_id\" ON \"cq_module\" (created_by_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_module_updated_by_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_module_updated_by_id\" ON \"cq_module\" (updated_by_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_module_deleted_by_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_module_deleted_by_id\" ON \"cq_module\" (deleted_by_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_module_persona_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_module_persona_id\" ON \"cq_module\" (persona_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_module_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_module_deleted_at\" ON \"cq_module\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_module_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"cq_module_created_by_id_foreign": {"constraintName": "cq_module_created_by_id_foreign", "columnNames": ["created_by_id"], "localTableName": "public.cq_module", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_user", "deleteRule": "set null", "updateRule": "cascade"}, "cq_module_updated_by_id_foreign": {"constraintName": "cq_module_updated_by_id_foreign", "columnNames": ["updated_by_id"], "localTableName": "public.cq_module", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_user", "deleteRule": "set null", "updateRule": "cascade"}, "cq_module_deleted_by_id_foreign": {"constraintName": "cq_module_deleted_by_id_foreign", "columnNames": ["deleted_by_id"], "localTableName": "public.cq_module", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_user", "deleteRule": "set null", "updateRule": "cascade"}, "cq_module_persona_id_foreign": {"constraintName": "cq_module_persona_id_foreign", "columnNames": ["persona_id"], "localTableName": "public.cq_module", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_persona", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "page": {"name": "page", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "metadata": {"name": "metadata", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "order_id": {"name": "order_id", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "created_by_id": {"name": "created_by_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "updated_by_id": {"name": "updated_by_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "deleted_by_id": {"name": "deleted_by_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "module_id": {"name": "module_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_page", "schema": "public", "indexes": [{"keyName": "IDX_cq_page_page_unique", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE UNIQUE INDEX IF NOT EXISTS \"IDX_cq_page_page_unique\" ON \"cq_page\" (page) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_page_created_by_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_page_created_by_id\" ON \"cq_page\" (created_by_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_page_updated_by_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_page_updated_by_id\" ON \"cq_page\" (updated_by_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_page_deleted_by_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_page_deleted_by_id\" ON \"cq_page\" (deleted_by_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_page_module_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_page_module_id\" ON \"cq_page\" (module_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_page_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_page_deleted_at\" ON \"cq_page\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_page_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"cq_page_created_by_id_foreign": {"constraintName": "cq_page_created_by_id_foreign", "columnNames": ["created_by_id"], "localTableName": "public.cq_page", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_user", "deleteRule": "set null", "updateRule": "cascade"}, "cq_page_updated_by_id_foreign": {"constraintName": "cq_page_updated_by_id_foreign", "columnNames": ["updated_by_id"], "localTableName": "public.cq_page", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_user", "deleteRule": "set null", "updateRule": "cascade"}, "cq_page_deleted_by_id_foreign": {"constraintName": "cq_page_deleted_by_id_foreign", "columnNames": ["deleted_by_id"], "localTableName": "public.cq_page", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_user", "deleteRule": "set null", "updateRule": "cascade"}, "cq_page_module_id_foreign": {"constraintName": "cq_page_module_id_foreign", "columnNames": ["module_id"], "localTableName": "public.cq_page", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_module", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "page_id": {"name": "page_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "role_id": {"name": "role_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_role_page", "schema": "public", "indexes": [{"keyName": "IDX_cq_role_page_page_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_role_page_page_id\" ON \"cq_role_page\" (page_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_role_page_role_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_role_page_role_id\" ON \"cq_role_page\" (role_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_role_page_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_role_page_deleted_at\" ON \"cq_role_page\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_role_page_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "role_page_id": {"name": "role_page_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "permission_id": {"name": "permission_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_role_page_permission", "schema": "public", "indexes": [{"keyName": "IDX_cq_role_page_permission_role_page_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_role_page_permission_role_page_id\" ON \"cq_role_page_permission\" (role_page_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_role_page_permission_permission_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_role_page_permission_permission_id\" ON \"cq_role_page_permission\" (permission_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_role_page_permission_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_role_page_permission_deleted_at\" ON \"cq_role_page_permission\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_role_page_permission_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "page_id": {"name": "page_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "permission_id": {"name": "permission_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_page_permission", "schema": "public", "indexes": [{"keyName": "IDX_cq_page_permission_page_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_page_permission_page_id\" ON \"cq_page_permission\" (page_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_page_permission_permission_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_page_permission_permission_id\" ON \"cq_page_permission\" (permission_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_page_permission_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_page_permission_deleted_at\" ON \"cq_page_permission\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_page_permission_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "column_name": {"name": "column_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "column_slug": {"name": "column_slug", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "column_type": {"name": "column_type", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "sort_order": {"name": "sort_order", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "created_by": {"name": "created_by", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "updated_by": {"name": "updated_by", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "table_master_id": {"name": "table_master_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_column_master", "schema": "public", "indexes": [{"keyName": "IDX_cq_column_master_table_master_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_column_master_table_master_id\" ON \"cq_column_master\" (table_master_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_column_master_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_column_master_deleted_at\" ON \"cq_column_master\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_column_master_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"cq_column_master_table_master_id_foreign": {"constraintName": "cq_column_master_table_master_id_foreign", "columnNames": ["table_master_id"], "localTableName": "public.cq_column_master", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_page", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "type": {"name": "type", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "module_id": {"name": "module_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "description": {"name": "description", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "is_sensitive": {"name": "is_sensitive", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_activity_types", "schema": "public", "indexes": [{"keyName": "IDX_cq_activity_types_type_unique", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE UNIQUE INDEX IF NOT EXISTS \"IDX_cq_activity_types_type_unique\" ON \"cq_activity_types\" (type) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_activity_types_module_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_activity_types_module_id\" ON \"cq_activity_types\" (module_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_activity_types_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_activity_types_deleted_at\" ON \"cq_activity_types\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_activity_types_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"cq_activity_types_module_id_foreign": {"constraintName": "cq_activity_types_module_id_foreign", "columnNames": ["module_id"], "localTableName": "public.cq_activity_types", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_module", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "user_id": {"name": "user_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "activity_type_id": {"name": "activity_type_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "status": {"name": "status", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'success'", "enumItems": ["success", "error"], "mappedType": "enum"}, "error_code": {"name": "error_code", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "error_message": {"name": "error_message", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "ip_address": {"name": "ip_address", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "device_info": {"name": "device_info", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "location": {"name": "location", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "user_agent": {"name": "user_agent", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "metadata": {"name": "metadata", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_user_activity_logs", "schema": "public", "indexes": [{"keyName": "IDX_cq_user_activity_logs_user_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_user_activity_logs_user_id\" ON \"cq_user_activity_logs\" (user_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_user_activity_logs_activity_type_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_user_activity_logs_activity_type_id\" ON \"cq_user_activity_logs\" (activity_type_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_user_activity_logs_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_user_activity_logs_deleted_at\" ON \"cq_user_activity_logs\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_user_activity_logs_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"cq_user_activity_logs_user_id_foreign": {"constraintName": "cq_user_activity_logs_user_id_foreign", "columnNames": ["user_id"], "localTableName": "public.cq_user_activity_logs", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_user", "updateRule": "cascade"}, "cq_user_activity_logs_activity_type_id_foreign": {"constraintName": "cq_user_activity_logs_activity_type_id_foreign", "columnNames": ["activity_type_id"], "localTableName": "public.cq_user_activity_logs", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_activity_types", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "sort_order": {"name": "sort_order", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "user_id": {"name": "user_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "column_master_id": {"name": "column_master_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "table_master_id": {"name": "table_master_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_user_column_preference", "schema": "public", "indexes": [{"keyName": "IDX_cq_user_column_preference_user_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_user_column_preference_user_id\" ON \"cq_user_column_preference\" (user_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_user_column_preference_column_master_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_user_column_preference_column_master_id\" ON \"cq_user_column_preference\" (column_master_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_user_column_preference_table_master_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_user_column_preference_table_master_id\" ON \"cq_user_column_preference\" (table_master_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_user_column_preference_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_user_column_preference_deleted_at\" ON \"cq_user_column_preference\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_user_column_preference_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"cq_user_column_preference_user_id_foreign": {"constraintName": "cq_user_column_preference_user_id_foreign", "columnNames": ["user_id"], "localTableName": "public.cq_user_column_preference", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_user", "updateRule": "cascade"}, "cq_user_column_preference_column_master_id_foreign": {"constraintName": "cq_user_column_preference_column_master_id_foreign", "columnNames": ["column_master_id"], "localTableName": "public.cq_user_column_preference", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_column_master", "updateRule": "cascade"}, "cq_user_column_preference_table_master_id_foreign": {"constraintName": "cq_user_column_preference_table_master_id_foreign", "columnNames": ["table_master_id"], "localTableName": "public.cq_user_column_preference", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_page", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "user_id": {"name": "user_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "persona_id": {"name": "persona_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_user_persona", "schema": "public", "indexes": [{"keyName": "IDX_cq_user_persona_user_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_user_persona_user_id\" ON \"cq_user_persona\" (user_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_user_persona_persona_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_user_persona_persona_id\" ON \"cq_user_persona\" (persona_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_user_persona_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_user_persona_deleted_at\" ON \"cq_user_persona\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_user_persona_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "user_id": {"name": "user_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "role_id": {"name": "role_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "company_id": {"name": "company_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "assigned_by_id": {"name": "assigned_by_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_user_role", "schema": "public", "indexes": [{"keyName": "IDX_cq_user_role_user_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_user_role_user_id\" ON \"cq_user_role\" (user_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_user_role_role_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_user_role_role_id\" ON \"cq_user_role\" (role_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_user_role_assigned_by_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_user_role_assigned_by_id\" ON \"cq_user_role\" (assigned_by_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_user_role_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_user_role_deleted_at\" ON \"cq_user_role\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_user_role_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"cq_user_role_user_id_foreign": {"constraintName": "cq_user_role_user_id_foreign", "columnNames": ["user_id"], "localTableName": "public.cq_user_role", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_user", "updateRule": "cascade"}, "cq_user_role_role_id_foreign": {"constraintName": "cq_user_role_role_id_foreign", "columnNames": ["role_id"], "localTableName": "public.cq_user_role", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_role", "updateRule": "cascade"}, "cq_user_role_assigned_by_id_foreign": {"constraintName": "cq_user_role_assigned_by_id_foreign", "columnNames": ["assigned_by_id"], "localTableName": "public.cq_user_role", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_user", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "user_id": {"name": "user_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "rolepage_id": {"name": "rolepage_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_user_role_page", "schema": "public", "indexes": [{"keyName": "IDX_cq_user_role_page_user_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_user_role_page_user_id\" ON \"cq_user_role_page\" (user_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_user_role_page_rolepage_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_user_role_page_rolepage_id\" ON \"cq_user_role_page\" (rolepage_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_user_role_page_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_user_role_page_deleted_at\" ON \"cq_user_role_page\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_user_role_page_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"cq_user_role_page_user_id_foreign": {"constraintName": "cq_user_role_page_user_id_foreign", "columnNames": ["user_id"], "localTableName": "public.cq_user_role_page", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_user", "updateRule": "cascade"}, "cq_user_role_page_rolepage_id_foreign": {"constraintName": "cq_user_role_page_rolepage_id_foreign", "columnNames": ["rolepage_id"], "localTableName": "public.cq_user_role_page", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_role_page", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "user_id": {"name": "user_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "role_page_permission_id": {"name": "role_page_permission_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "page_id": {"name": "page_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "permission_id": {"name": "permission_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "is_direct_permission": {"name": "is_direct_permission", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "cq_user_role_page_permission", "schema": "public", "indexes": [{"keyName": "IDX_cq_user_role_page_permission_user_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_user_role_page_permission_user_id\" ON \"cq_user_role_page_permission\" (user_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_user_role_page_permission_role_page_permission_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_user_role_page_permission_role_page_permission_id\" ON \"cq_user_role_page_permission\" (role_page_permission_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_user_role_page_permission_page_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_user_role_page_permission_page_id\" ON \"cq_user_role_page_permission\" (page_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_user_role_page_permission_permission_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_user_role_page_permission_permission_id\" ON \"cq_user_role_page_permission\" (permission_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_cq_user_role_page_permission_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_cq_user_role_page_permission_deleted_at\" ON \"cq_user_role_page_permission\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "cq_user_role_page_permission_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"cq_user_role_page_permission_user_id_foreign": {"constraintName": "cq_user_role_page_permission_user_id_foreign", "columnNames": ["user_id"], "localTableName": "public.cq_user_role_page_permission", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_user", "deleteRule": "set null", "updateRule": "cascade"}, "cq_user_role_page_permission_role_page_permission_id_foreign": {"constraintName": "cq_user_role_page_permission_role_page_permission_id_foreign", "columnNames": ["role_page_permission_id"], "localTableName": "public.cq_user_role_page_permission", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_role_page_permission", "deleteRule": "set null", "updateRule": "cascade"}, "cq_user_role_page_permission_page_id_foreign": {"constraintName": "cq_user_role_page_permission_page_id_foreign", "columnNames": ["page_id"], "localTableName": "public.cq_user_role_page_permission", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_page", "deleteRule": "set null", "updateRule": "cascade"}, "cq_user_role_page_permission_permission_id_foreign": {"constraintName": "cq_user_role_page_permission_permission_id_foreign", "columnNames": ["permission_id"], "localTableName": "public.cq_user_role_page_permission", "referencedColumnNames": ["id"], "referencedTableName": "public.cq_permission", "deleteRule": "set null", "updateRule": "cascade"}}, "nativeEnums": {}}], "nativeEnums": {}}