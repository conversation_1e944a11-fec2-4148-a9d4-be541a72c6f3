import { z } from "zod";
import logger from "../../utils/logger";
import { isValidPatchStatus } from "../../utils/constants/subscriptionStatusConstants";

// Zod schema for PatchSubscriptionModel (using camelCase to match request body)
export const PatchSubscriptionModelSchema = z.object({
  storeId: z.string().min(1, "storeId cannot be blank or null"),
  customerId: z.string().min(1, "customerId cannot be blank or null"),
  subscriptionId: z.string().min(1, "subscriptionId cannot be blank or null"),
  status: z.string().min(1, "status cannot be blank or null"),
  quantity: z.number().optional(),
  requestId: z.string().optional(),
  autoRenewEnabled: z.boolean().optional(),
  actualQty: z.number().optional(),
  updateType: z.string().optional(),
  partnerId: z.string().optional(),
  autoRenewToUpdate: z.boolean().nullable().optional(),
});

export type PatchSubscriptionModelType = z.infer<typeof PatchSubscriptionModelSchema>;

/**
 * Validate PatchSubscription request body (matching .NET validation patterns)
 */
export function validatePatchSubscriptionRequest(requestBody: any): string[] {
  logger.info(`Entered into validatePatchSubscriptionRequest method with requestBody: ${JSON.stringify(requestBody)}`);
  
  const validationErrors: string[] = [];

  try {
    PatchSubscriptionModelSchema.parse(requestBody);
    logger.info(`Zod validation passed for PatchSubscription request`);
  } catch (error: any) {
    if (error.errors) {
      error.errors.forEach((err: any) => {
        const fieldName = err.path.join('.');
        validationErrors.push(`${fieldName}: ${err.message}`);
      });
    }
    logger.error(`Zod validation failed: ${JSON.stringify(error.errors)}`);
  }

  // Additional custom validations (using camelCase keys)
  if (!requestBody.storeId || requestBody.storeId.trim() === '') {
    validationErrors.push("The 'storeId' parameter is missing or empty in the request body.");
  }

  if (!requestBody.customerId || requestBody.customerId.trim() === '') {
    validationErrors.push("The 'customerId' parameter is missing or empty in the request body.");
  }

  if (!requestBody.subscriptionId || requestBody.subscriptionId.trim() === '') {
    validationErrors.push("The 'subscriptionId' parameter is missing or empty in the request body.");
  }

  if (!requestBody.status || requestBody.status.trim() === '') {
    validationErrors.push("The 'status' parameter is missing or empty in the request body.");
  } else {
    console.log("status---------------------------->",requestBody.status);
    // Validate status against allowed values (matching .NET status validation)
    if (!isValidPatchStatus(requestBody.status)) {
      validationErrors.push(`Invalid subscription status: ${requestBody.status}. Valid statuses are: deleted, suspended, active, up, down, autorenewon, autorenewoff, mpnidupdate.`);
    }
  }

  logger.info(`Validation completed. Errors found: ${validationErrors.length}`);
  return validationErrors;
}
