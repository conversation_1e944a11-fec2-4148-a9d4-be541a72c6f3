import { z } from "zod";
import logger from "../../utils/logger";

// Zod schema for GetSubscriptionById query parameters
export const GetSubscriptionByIdQuerySchema = z.object({
  customerId: z.string().min(1, "CustomerId cannot be blank or null"),
  subscriptionId: z.string().min(1, "SubscriptionId cannot be blank or null"),
  storeId: z.string().min(1, "StoreId cannot be blank or null"),
  brandId: z.string().min(1, "BrandId cannot be blank or null"),
});

export type GetSubscriptionByIdQueryType = z.infer<typeof GetSubscriptionByIdQuerySchema>;

/**
 * Validate GetSubscriptionById request parameters (matching .NET validation patterns)
 */
export function validateGetSubscriptionByIdRequest(queryParams: any): string[] {
  logger.info(`Entered into validateGetSubscriptionByIdRequest method with queryParams: ${JSON.stringify(queryParams)}`);
  
  const validationErrors: string[] = [];

  try {
    GetSubscriptionByIdQuerySchema.parse(queryParams);
    logger.info(`Zod validation passed for GetSubscriptionById request`);
  } catch (error: any) {
    if (error.errors) {
      error.errors.forEach((err: any) => {
        const fieldName = err.path.join('.');
        validationErrors.push(`${fieldName}: ${err.message}`);
      });
    }
    logger.error(`Zod validation failed: ${JSON.stringify(error.errors)}`);
  }

  // Additional custom validations (matching .NET patterns)
  if (!queryParams.customerId || queryParams.customerId.trim() === '') {
    validationErrors.push("The 'customerId' query parameter is missing or empty in the request.");
  }

  if (!queryParams.subscriptionId || queryParams.subscriptionId.trim() === '') {
    validationErrors.push("The 'subscriptionId' query parameter is missing or empty in the request.");
  }

  if (!queryParams.storeId || queryParams.storeId.trim() === '') {
    validationErrors.push("The 'storeId' query parameter is missing or empty in the request.");
  }

  if (!queryParams.brandId || queryParams.brandId.trim() === '') {
    validationErrors.push("The 'brandId' query parameter is missing or empty in the request.");
  }

  logger.info(`Validation completed. Errors found: ${validationErrors.length}`);
  return validationErrors;
}
