import express from "express";
import swaggerUi from "swagger-ui-express";
import YAML from "yamljs";
import path from "path";
import cors from "cors";

const app = express();

app.use((req, res, next) => {
  res.setHeader("Access-Control-Allow-Origin", "*"); // or specific origin
  res.setHeader(
    "Access-Control-Allow-Methods",
    "GET, POST, PUT, DELETE, OPTIONS"
  );
  res.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
  next();
});

app.use(
  cors({
    origin: "*",
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: "*", // Accept any custom headers
  })
);

app.use(express.json());

const swaggerPath = path.join(__dirname, "../docs/swagger.yml");
const swaggerDocument = YAML.load(swaggerPath);

app.use("/docs", swaggerUi.serve, swaggerUi.setup(swaggerDocument));

const port = 7001;
app.listen(port, () => {
  console.log(`📚 Swagger docs available at http://localhost:${port}/docs`);
});
