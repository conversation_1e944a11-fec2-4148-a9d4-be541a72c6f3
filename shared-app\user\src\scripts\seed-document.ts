import { ExecArgs } from '@medusajs/framework/types';
import CQUserService from '../modules/cq-user/service';
import { CQUser_MODULE } from '../modules/cq-user/index';
import { Modules } from '@medusajs/framework/utils';

export default async function seedDocument({ container }: ExecArgs) {
  const documentService: CQUserService = container.resolve(
    CQUser_MODULE
  );
  const regionService = container.resolve(Modules.REGION);

  const targetDocuments = [
    { document: 'Copy of Passport (First & Last Page)/Aadhar Card (Front & Back)' },
    { document: 'Latest Audited Financials with Income Tax return Acknowledgment copy' },
    { document: 'Tax Exemption Supporting Document' },
  ];

  const targetRegionNames = ['India', 'Turkey', 'MEA'];

  // Step 1: Get regions and map names to IDs
  const allRegions = await regionService.listRegions();
  const regionMap = new Map(
    allRegions.map((r: any) => [r.name.toLowerCase(), r.id])
  );

  const targetRegionIds = targetRegionNames
    .map((name) => regionMap.get(name.toLowerCase()))
    .filter(Boolean);

  if (targetRegionIds.length !== targetRegionNames.length) {
    console.warn('Some region names were not found in the Region table.');
  }

  // Step 2: Prepare documents
  const documentsToCreate = targetDocuments
    .filter((d) => !!d.document)
    .map((d) => ({
      ...d,
      metadata: {},
    }));

  let createdCount = 0;

  for (const document of documentsToCreate) {
    const existing = await documentService.listCqDocuments({ document: document.document });
    if (!existing.length) {
      await documentService.createCqDocuments(document);
      createdCount++;
    }
  }

  console.log(`Seeded ${createdCount} document(s) with region assignments.`);
}
