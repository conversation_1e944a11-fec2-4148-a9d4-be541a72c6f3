import { ExecArgs } from '@medusajs/framework/types';
import { CQUser_MODULE } from '../modules/cq-user';
import CQUserService from '../modules/cq-user/service';

export default async function seedMaster({ container }: ExecArgs) {
  const cqUserService: CQUserService = container.resolve(CQUser_MODULE);

  const targetMasters = {
    Accounts: [
      'Company Registration',
      'Company Onboarding',
      'Companies',
      'Users',
    ],
    Dashboard: [],
  };

  const [existingModules = []] =
    (await cqUserService.listAndCountCqModules?.({})) || [];

  const moduleMap = new Map(
    existingModules.map((mod) => [mod.module.toLowerCase(), mod.id])
  );

  const mastersToCreate = [];

  for (const [moduleName, masterNames] of Object.entries(targetMasters)) {
    const moduleId = moduleMap.get(moduleName.toLowerCase());

    if (!moduleId) {
      console.warn(`Module not found: ${moduleName}`);
      continue;
    }

    for (const name of masterNames) {
      mastersToCreate.push({
        table_name: name,
        module_id: moduleId,
        metadata: {},
      });
    }
  }

  const existingMasters = (await cqUserService.listTableMasters?.({})) || [];

  const existingKeys = new Set(
    existingMasters.map((m) => `${m.module_id}-${m.table_name.toLowerCase()}`)
  );

  const newMasters = mastersToCreate.filter((m) => {
    const key = `${m.module_id}-${m.table_name.toLowerCase()}`;
    return !existingKeys.has(key);
  });

  if (newMasters.length > 0) {
    await Promise.all(
      newMasters.map((m) => cqUserService.createTableMasters(m))
    );
  }

  console.log(`Seeded ${newMasters.length} master record(s).`);
}
