import { z } from "zod";

// Azure Unbilled Usage DTO validation schema (camelCase to match request body)
export const AzureUnbilledUsageSchema = z.object({
  storeId: z.string().min(1, "StoreId cannot be null or empty"),
  brandId: z.string().min(1, "BrandId cannot be null or empty"),
  currencyCode: z.string().min(1, "CurrencyCode cannot be null or empty"),
  billingPeriod: z.string().min(1, "BillingPeriod cannot be null or empty"),
  attributeSet: z.string().min(1, "AttributeSet cannot be null or empty"),
});

// PascalCase version for service layer (matching .NET)
export const AzureUnbilledUsagePascalSchema = z.object({
  StoreId: z.string().min(1, "StoreId cannot be null or empty"),
  BrandId: z.string().min(1, "BrandId cannot be null or empty"),
  CurrencyCode: z.string().min(1, "CurrencyCode cannot be null or empty"),
  BillingPeriod: z.string().min(1, "BillingPeriod cannot be null or empty"),
  AttributeSet: z.string().min(1, "AttributeSet cannot be null or empty"),
});

// Request schema for service layer (only CurrencyCode, BillingPeriod, and AttributeSet)
export const AzureUnbilledUsageRequestSchema = z.object({
  CurrencyCode: z.string().min(1, "CurrencyCode cannot be null or empty"),
  BillingPeriod: z.string().min(1, "BillingPeriod cannot be null or empty"),
  AttributeSet: z.string().min(1, "AttributeSet cannot be null or empty"),
});

// Type definitions for TypeScript
export type AzureUnbilledUsageType = z.infer<typeof AzureUnbilledUsageSchema>;
export type AzureUnbilledUsagePascalType = z.infer<typeof AzureUnbilledUsagePascalSchema>;
export type AzureUnbilledUsageRequestType = z.infer<typeof AzureUnbilledUsageRequestSchema>;

// Validation function for store and brand (reusing existing validators)
export function validateAzureUnbilledUsageStoreAndBrand(storeId: string, brandId: string): string[] {
  const errors: string[] = [];
  
  if (!storeId || storeId.trim() === "") {
    errors.push("The 'StoreId' parameter is missing in the request.");
  }
  
  if (!brandId || brandId.trim() === "") {
    errors.push("The 'BrandId' parameter is missing in the request.");
  }
  
  return errors;
}
