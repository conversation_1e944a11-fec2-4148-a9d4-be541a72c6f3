import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import {
  ErrorResponseModel
} from "../../../../types/responses/customResponse";
import {
  validateOrderCancellationRequest,
  ValidateOrderCancellationQueryType
} from "../../../../validators/subscription/validateOrderCancellationValidator";
import { validateOrderCancellationDAL } from "../../../../services/subscription/validateOrderCancellationDAL";

/**
 * GET /v1/subscription/validateOrderCancellation
 * Validate Order Cancellation API - matches .NET ValidateOrderCancellation controller method
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req);

  logger.info(`[${correlationId}] Entered into ValidateOrderCancellation API with subscriptionId-${req.query.subscriptionId} and going to call ValidateOrderCancellation service method`);

  try {
    // Validate the query parameters
    const validationErrors = validateOrderCancellationRequest(req.query);

    if (validationErrors.length > 0) {
      logger.error(`[${correlationId}] Validation failed: ${JSON.stringify(validationErrors)}`);

      const errorResponse: ErrorResponseModel = {
        errors: validationErrors,
        statusCode: 400,
      };

      return res.status(400).json(errorResponse);
    }

    const queryParams: ValidateOrderCancellationQueryType = req.query as ValidateOrderCancellationQueryType;

    logger.info(`[${correlationId}] Query parameter validation passed, proceeding with order cancellation validation`);

    // Call the DAL service to validate order cancellation (matching .NET: _subscriptionFactory.ValidateOrderCancellation(subscriptionId))
    const cancellationStatus = await validateOrderCancellationDAL(queryParams.subscriptionId);

    logger.info(`[${correlationId}] ValidateOrderCancellation service completed successfully: ${JSON.stringify(cancellationStatus)}`);

    return res.status(200).json(cancellationStatus);

  } catch (error: any) {
    // Log the exception (matching .NET exception handling)
    logger.error(`[${correlationId}] Error in ValidateOrderCancellation, errorMessage: ${error.message}, StackTrace: ${error.stack}`);

    // Re-throw the exception (matching .NET: throw ex;)
    return res.status(500).json({
      Status: "ERROR",
      Message: error.message
    });
  }
}