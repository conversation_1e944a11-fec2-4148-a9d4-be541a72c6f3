import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import { PatchMethodWithRequestJsonBodyAndHeaders400Handle } from "../externalService/externalEndPointService";
import { MicrosoftEndpoints } from "../../config/microsoftEndpoints";
import { 
  TerminateOrder, 
  SubscriptionStatusResponse, 
  ResponseError 
} from "../../types/responses/customResponse";

/**
 * Terminate Subscription
 * Matches .NET SubscriptionFactory.TerminateSubscription method
 */
export async function terminateSubscriptionService(
  req: MedusaRequest,
  terminate: TerminateOrder,
  customerId: string,
  token: string
): Promise<SubscriptionStatusResponse> {
  logger.info(`Entered into TerminateSubscription Service method with terminate ${JSON.stringify(terminate)}, customerId ${customerId}`);

  try {
    // Construct the endpoint URL (matching .NET: string.Format(endPoint, customerId, terminate.Id))
    const endPoint = MicrosoftEndpoints.terminateOrderUrl(customerId, terminate.Id || "");
    
    logger.info(`Request to TerminateSubscription ${endPoint} with terminate ${JSON.stringify(terminate)}`);

    // Prepare headers (matching .NET: client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token))
    const headerList = {
      Authorization: `Bearer ${token}`,
      Accept: "application/json",
      "Content-Type": "application/json",
    };

    const configModule = req.scope.resolve("configModule");

    // Make the PATCH request (matching .NET: client.PatchAsync(new Uri(endPoint), httpContent).Result)
    const response = await PatchMethodWithRequestJsonBodyAndHeaders400Handle(
      {
        url: endPoint,
        body: terminate,
        headers: headerList,
        isVendorHit: true,
        module: "TerminateSubscription",
      },
      configModule
    );

    logger.info(`Response from TerminateSubscription ${endPoint} response ${response.content}`);

    if (response.isSuccessStatusCode) {
      // Parse the response body (matching .NET: JsonConvert.DeserializeObject<SubscriptionStatusResponse>(dataObjects))
      const subscriptionStatusResponse: SubscriptionStatusResponse = JSON.parse(response.content);
      logger.info(`Successfully terminated subscription: ${JSON.stringify(subscriptionStatusResponse)}`);
      return subscriptionStatusResponse;
    } else {
      // Handle error response (matching .NET error handling)
      try {
        const error: ResponseError = JSON.parse(response.content);
        logger.info(`Returning ErrorMessage - ${("{" + error.code + "} " + error.description)}`);

        return {
          ErrorMessage: "{" + error.code + "} " + error.description
        };
      } catch (parseError) {
        // If parsing fails, return generic error
        logger.error(`Failed to parse error response: ${parseError}`);
        return {
          ErrorMessage: response.errorMessage || "Unknown error occurred during termination"
        };
      }
    }
  } catch (error: any) {
    logger.error(`Error in terminateSubscriptionService: ${error.message}`);
    return {
      ErrorMessage: error.message
    };
  }
}
