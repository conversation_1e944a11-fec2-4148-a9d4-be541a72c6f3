import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import { stringConstants } from '@org/utils';
import { COMPANY_REGISTRATION_MODULE } from "../../../../../../company/src/modules/company";
import companyRegistrationService from "../../../../../../company/src/modules/company/service";

export async function POST(req: MedusaRequest, res: MedusaResponse) {
	const companyService: companyRegistrationService = req.scope.resolve(COMPANY_REGISTRATION_MODULE);

	const { id, redington_account_id, status, reason } = req.body as {
		id: string;
		redington_account_id: string;
		status: string; // "approved" | "rejected"
		reason?: string;
	};

	if (!redington_account_id || !status) {
		return res.status(400).json({
			message: "redington_account_id and status are required.",
		});
	}

	if (status === "rejected" && !reason) {
		return res.status(400).json({
			message: "reason is required when status is rejected.",
		});
	}
	const updateData = {
		id,
		redington_account_id,
		status,
		reason: reason ?? "",
	};

	try {
		const updatedCompany = await companyService.updateCqCompanyRegistrations(
			updateData
		);

		if (updatedCompany.status === "approved") {
			const {
				user_id,
				company_name,
				region_country_id,
				address_1,
				address_2,
				country_of_business,
				state,
				city,
				postal_code,
				website,
				director_first_name,
				director_last_name,
				director_email,
				director_country_code,
				director_mobile_number,
				sales_first_name,
				sales_last_name,
				sales_email,
				sales_country_code,
				sales_mobile_number,
				accounts_first_name,
				accounts_last_name,
				accounts_email,
				accounts_country_code,
				accounts_mobile_number,
				gst_number,
			} = updatedCompany;

			const onboardingData = {
				redington_account_id,
				camunda_process_id: "",
				user_id,
				company_name,
				region_country_id,
				address1: address_1,
				address2: address_2,
				country_of_business,
				state,
				city,
				postal_code,
				website,
				director_first_name,
				director_last_name,
				director_email,
				director_country_code,
				director_mobile_number,
				sales_first_name,
				sales_last_name,
				sales_email,
				sales_country_code,
				sales_mobile_number,
				accounts_first_name,
				accounts_last_name,
				accounts_email,
				accounts_country_code,
				accounts_mobile_number,
				gst_number,
				requested_by_admin: false,
				status: "pending",
				reason: "",
				store_id: "",
				metadata: {},
				created_by: user_id,
				updated_by: user_id,
				deleted_by: null,
			};

			await companyService.createCqCompanyOnboardings(onboardingData);
			// camunda service
		}
		return res.status(200).json({ message: stringConstants.UPDATED_SUCCESSFULLY('Company Details') });
	} catch (err: any) {
		console.error("Error updating company status:", err.message);
		return res.status(500).json({
			message: stringConstants.INTERNAL_SERVER_ERROR(),
		});
	}
}
