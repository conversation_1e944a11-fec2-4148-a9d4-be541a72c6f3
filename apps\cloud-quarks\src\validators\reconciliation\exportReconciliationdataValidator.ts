import { z } from "zod"

export const ExportReconciliationDataValidator = z.object({
  storeId: z.string().min(1, "StoreId cannot be empty"),
  brandId: z.string().min(1, "BrandId cannot be empty"),
  invoiceId: z.string().min(1, "InvoiceId cannot be empty"),
  attributeSet: z.string().min(1, "AttributeSet cannot be empty"),
})

export type ExportReconciliationDataDTO = z.infer<typeof ExportReconciliationDataValidator>
