import { z } from "zod";

// UnBilled Azure Data Download DTO validation schema (camelCase to match request body)
export const UnBilledAzureDataDownloadSchema = z.object({
  storeId: z.string().min(1, "StoreId cannot be null or empty"),
  subscriptionId: z.string().min(1, "SubscriptionId cannot be null or empty"),
  customerId: z.string().min(1, "CustomerId cannot be null or empty"),
  period: z.string().min(1, "Period cannot be null or empty"),
});

// PascalCase version for service layer (matching .NET)
export const UnBilledAzureDataDownloadPascalSchema = z.object({
  StoreId: z.string().min(1, "StoreId cannot be null or empty"),
  SubscriptionId: z.string().min(1, "SubscriptionId cannot be null or empty"),
  CustomerId: z.string().min(1, "CustomerId cannot be null or empty"),
  Period: z.string().min(1, "Period cannot be null or empty"),
});

// Model schema for service layer (includes AWS credentials)
export const UnBilledAzureDataDownloadModelSchema = z.object({
  StoreId: z.string().min(1, "StoreId cannot be null or empty"),
  SubscriptionId: z.string().min(1, "SubscriptionId cannot be null or empty"),
  CustomerId: z.string().min(1, "CustomerId cannot be null or empty"),
  Period: z.string().min(1, "Period cannot be null or empty"),
  AWSCredentials: z.object({
    Id: z.number(),
    AWS_ACCESS_KEY_ID: z.string(),
    AWS_SECRET_ACCESS_KEY: z.string(),
    AWS_REGION: z.string(),
    RESOURCE: z.string(),
    AWS_BUCKETNAME: z.string(),
    APPLICATION: z.string(),
    COMMENTS: z.string(),
    ACTIVE: z.boolean().optional(),
  }).optional(),
});

// Type definitions for TypeScript
export type UnBilledAzureDataDownloadType = z.infer<typeof UnBilledAzureDataDownloadSchema>;
export type UnBilledAzureDataDownloadPascalType = z.infer<typeof UnBilledAzureDataDownloadPascalSchema>;
export type UnBilledAzureDataDownloadModelType = z.infer<typeof UnBilledAzureDataDownloadModelSchema>;

// AWS Credentials type
export type AWSCredentials = {
  Id: number;
  AWS_ACCESS_KEY_ID: string;
  AWS_SECRET_ACCESS_KEY: string;
  AWS_REGION: string;
  RESOURCE: string;
  AWS_BUCKETNAME: string;
  APPLICATION: string;
  COMMENTS: string;
  ACTIVE?: boolean;
};

// Validation function for store (reusing existing pattern)
export function validateUnBilledAzureDataDownloadStore(storeId: string): string[] {
  const errors: string[] = [];
  
  if (!storeId || storeId.trim() === "") {
    errors.push("The 'StoreId' parameter is missing in the request.");
  }
  
  return errors;
}
