import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import {
  ResponseStatus
} from "../../../../types/responses/customResponse";
import {
  validatePlaceTransitionRequest,
  PlaceTransitionRequestType,
  PlaceTransitionRequestSchema
} from "../../../../validators/transition/placeTransitionValidator";
import { placeTransitionService } from "../../../../services/transition/placeTransitionService";

/**
 * @openapi
 * /v1/transition/placeTransition:
 *   post:
 *     summary: Place Transition request (simplified version)
 *     description: Places a transition request for subscription changes without Microsoft validations
 *     tags:
 *       - Transition
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - brandId
 *               - storeId
 *               - partnerId
 *               - customerId
 *               - fromSubscriptionId
 *               - fromMaterialId
 *               - quantity
 *               - toCatalogItemId
 *               - term
 *               - billFreq
 *             properties:
 *               brandId:
 *                 type: string
 *                 description: Brand ID
 *                 example: "string"
 *               storeId:
 *                 type: string
 *                 description: Store ID
 *                 example: "string"
 *               partnerId:
 *                 type: string
 *                 description: Partner ID
 *                 example: "string"
 *               customerId:
 *                 type: string
 *                 description: Customer ID
 *                 example: "string"
 *               fromSubscriptionId:
 *                 type: string
 *                 description: Source subscription ID to transition from
 *                 example: "string"
 *               fromMaterialId:
 *                 type: string
 *                 description: Source material ID
 *                 example: "string"
 *               fromCatalogItemId:
 *                 type: string
 *                 description: Source catalog item ID (optional, auto-populated from fromMaterialId if not provided)
 *                 example: "string"
 *               quantity:
 *                 type: number
 *                 description: Quantity for the transition
 *                 example: 0
 *               toSubscriptionId:
 *                 type: string
 *                 description: Target subscription ID (optional)
 *                 example: "string"
 *               toCatalogItemId:
 *                 type: string
 *                 description: Target catalog item ID
 *                 example: "string"
 *               promotionId:
 *                 type: string
 *                 description: Promotion ID (optional)
 *                 example: "string"
 *               term:
 *                 type: string
 *                 description: Subscription term
 *                 example: "string"
 *               billFreq:
 *                 type: string
 *                 description: Billing frequency
 *                 example: "string"
 *               updateType:
 *                 type: string
 *                 description: Type of update (optional)
 *                 example: "string"
 *               subUpdateType:
 *                 type: string
 *                 description: Sub update type (optional)
 *                 example: "string"
 *               unitPrice:
 *                 type: number
 *                 description: Unit price (optional)
 *                 example: 0
 *               taxRate:
 *                 type: number
 *                 description: Tax rate (optional)
 *                 example: 0
 *               promotionRate:
 *                 type: number
 *                 description: Promotion rate (optional)
 *                 example: 0
 *               segment:
 *                 type: string
 *                 description: Customer segment (optional)
 *                 example: "string"
 *           example:
 *             brandId: "string"
 *             storeId: "string"
 *             partnerId: "string"
 *             customerId: "string"
 *             fromSubscriptionId: "string"
 *             fromMaterialId: "string"
 *             fromCatalogItemId: "string"
 *             quantity: 0
 *             toSubscriptionId: "string"
 *             toCatalogItemId: "string"
 *             promotionId: "string"
 *             term: "string"
 *             billFreq: "string"
 *             updateType: "string"
 *             subUpdateType: "string"
 *             unitPrice: 0
 *             taxRate: 0
 *             promotionRate: 0
 *             segment: "string"
 *     responses:
 *       '200':
 *         description: Transition placed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: boolean
 *                   example: true
 *                 Message:
 *                   type: string
 *                   example: "Transition request processed successfully"
 *       '400':
 *         description: Bad request - validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: string
 *                 statusCode:
 *                   type: number
 *                   example: 400
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: boolean
 *                   example: false
 *                 Message:
 *                   type: string
 *                   example: "Internal server error"
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req);

  logger.info(`[${correlationId}] Entered into Transition/PlaceTransition API with Request-${JSON.stringify(req.body)}`);

  try {
    logger.info(`[${correlationId}] Going to validate request items`);
    const validationErrors = validatePlaceTransitionRequest(req.body);
    logger.info(`[${correlationId}] Got validation messages from validate method: ${JSON.stringify(validationErrors)}`);

    if (validationErrors.length > 0) {
      logger.info(`[${correlationId}] Going to extract errors from validationResult object`);
      logger.info(`[${correlationId}] validation errors are ${JSON.stringify(validationErrors)}`);

      const response: ResponseStatus = {
        Status: false,
        Message: validationErrors.join(", ")
      };

      return res.status(400).json(response);
    }

    const request: PlaceTransitionRequestType = PlaceTransitionRequestSchema.parse(req.body);

    // Convert camelCase to PascalCase for service layer
    const serviceRequest = {
      BrandId: request.brandId,
      StoreId: request.storeId,
      PartnerId: request.partnerId,
      CustomerId: request.customerId,
      FromSubscriptionId: request.fromSubscriptionId,
      FromMaterialId: request.fromMaterialId,
      FromCatalogItemId: request.fromCatalogItemId,
      Quantity: request.quantity,
      ToSubscriptionId: request.toSubscriptionId,
      ToCatalogItemId: request.toCatalogItemId,
      PromotionId: request.promotionId,
      Term: request.term,
      BillFreq: request.billFreq,
      UpdateType: request.updateType,
      SubUpdateType: request.subUpdateType,
      UnitPrice: request.unitPrice,
      TaxRate: request.taxRate,
      PromotionRate: request.promotionRate,
      Segment: request.segment,
    };

    logger.info(`[${correlationId}] Going to hit BL PlaceTransition method`);
    const response = await placeTransitionService(req, serviceRequest);
    logger.info(`[${correlationId}] Result from BL PlaceTransition is ${JSON.stringify(response)}`);

    return res.status(200).json(response);

  } catch (ex: any) {
    logger.error(`[${correlationId}] Error in PlaceTransition|ErrorDetail,errorMessage : ${ex.message}, StackTrace : ${ex.stack}`);

    const errorResponse: ResponseStatus = {
      Status: false,
      Message: ex.message
    };

    return res.status(500).json(errorResponse);
  }
}