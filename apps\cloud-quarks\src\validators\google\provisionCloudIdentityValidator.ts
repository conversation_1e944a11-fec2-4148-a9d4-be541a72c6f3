import logger from "../../utils/logger";
import { getAllStoresService } from "../renewal/customRenewalValidator";

// Helper to normalize string (like .NET ComparableString extension)
function comparableString(str?: string): string {
  return (str || "").trim().toUpperCase();
}

export async function validateProvisionCustomerCloudIdentityRequest(
  storeId: string,
  googleCustomerId: string
): Promise<string[]> {
  logger.info(`Entered into ValidateProvisionCustomerCloudIdentityRequest Method with storeId-${storeId}, googleCustomerId-${googleCustomerId}`);
  
  const validationErrors: string[] = [];

  // StoreId validation (matching .NET logic)
  if (!storeId || storeId.trim() === "") {
    validationErrors.push("StoreId can't be null or empty");
  } else {
    // Check if store exists (matching .NET: _storeService.IsStoreExists(storeId))
    try {
      const allStores = await getAllStoresService();
      const storeExists = allStores.some(
        (s: { StoreId: string | undefined }) => 
          comparableString(s.StoreId) === comparableString(storeId)
      );
      if (!storeExists) {
        validationErrors.push("StoreId doesn't exists");
      }
    } catch (error) {
      logger.error(`Error checking store existence: ${error}`);
      validationErrors.push("Error validating store existence");
    }
  }

  // GoogleCustomerId validation (matching .NET logic)
  if (!googleCustomerId || googleCustomerId.trim() === "") {
    validationErrors.push("GoogleCustomerId can't be null or empty");
  }

  logger.info(`Going to return Validation error list ${JSON.stringify(validationErrors)}`);
  return validationErrors;
}
