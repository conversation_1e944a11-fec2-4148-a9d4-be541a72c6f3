import { z } from "zod";

// Domain validation helper function
function isValidDomainName(domain: string): boolean {
  // Simple domain validation - matches .NET logic (domain.Length <= 253)
  return domain.length <= 253 && domain.length > 0;
}

// CloudIdentityAccount DTO validation schema (camelCase for request body)
export const CloudIdentityAccountDtoSchema = z.object({
  storeId: z.string().min(1, "StoreId cannot be null or empty"),
  domain: z.string()
    .min(1, "Domain cannot be null or empty")
    .refine(isValidDomainName, "Domain is not valid"),
  isExisting: z.boolean(),
  channelPartnerId: z.string().optional(),
});

// Type definition for TypeScript
export type CloudIdentityAccountDtoType = z.infer<typeof CloudIdentityAccountDtoSchema>;

// Validation function
export function validateCloudIdentityAccountDto(dto: any): { isValid: boolean; errors: string[] } {
  try {
    CloudIdentityAccountDtoSchema.parse(dto);
    return { isValid: true, errors: [] };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors = error.errors.map(err => err.message);
      return { isValid: false, errors };
    }
    return { isValid: false, errors: ["Invalid data structure"] };
  }
}
