import { z } from 'zod';

const emailAddressSchema = z.object({
  email: z.string().email(),
  name: z.string().optional(),
});

export const EmailPayloadSchema = z
  .object({
    to: z.array(emailAddressSchema).min(1),
    cc: z.array(emailAddressSchema).optional(),
    bcc: z.array(emailAddressSchema).optional(),
    from: emailAddressSchema,
    reply_to: emailAddressSchema.optional(),
    subject: z.string(),
    content: z
      .object({
        text: z.string().optional(),
        html: z.string().optional(),
      })
      .optional(),
    template: z
      .object({
        template_id: z.string(),
        template_data: z.record(z.unknown()),
      })
      .optional(),
  })
  .refine(
    (data) => {
      return data.content?.text || data.content?.html || data.template;
    },
    {
      message: 'Either content (text/html) or template is required',
    }
  );
