import { ExecArgs } from '@medusajs/framework/types';
import { ContainerRegistrationKeys, Modules } from '@medusajs/framework/utils';

export default async function seedRegions({ container }: ExecArgs) {
  const regionService = container.resolve(Modules.REGION);
  const logger = container.resolve(ContainerRegistrationKeys.LOGGER);


  logger.info('Seeding region data...');

  const targetRegions = [
    {
      name: 'India',
      currency_code: 'inr',
      automatic_taxes: false,
      countries: ['IN'],
      metadata: {},
    },
    {
      name: 'Turkey',
      currency_code: 'try',
      automatic_taxes: false,
      countries: ['TR'],
      metadata: {},
    },
    {
      name: 'MEA',
      currency_code: 'aed',
      automatic_taxes: false,
      countries: [
        'EG',
        'KW',
        'QA',
        'OM',
        'SA',
        'BH',
        'AE',
        'LB',
        'JO',
        'IQ',
        'NG',
        'KE',
        'GH',
        'ET',
        'RW',
        'UG',
        'ZA',
        'CI',
        'MU',
        'TZ',
      ],
      metadata: {},
    },
  ];

  const existingRegions = await regionService.listRegions({});

  // Create a set of target region names (case-insensitive)
  const targetRegionNames = new Set(
    targetRegions.map((region) => region.name.toLowerCase())
  );

  // Find regions to delete (those not in targetRegions)
  const regionsToDelete = existingRegions
    .filter((region) => !targetRegionNames.has(region.name.toLowerCase()))
    .map((region) => region.id);

  // Delete regions not in targetRegions
  if (regionsToDelete.length > 0) {
    await regionService.deleteRegions(regionsToDelete);
  }

  // Find regions to create (those in targetRegions but not in DB)
  const existingRegionNames = new Set(
    existingRegions.map((region) => region.name.toLowerCase())
  );
  const regionsToCreate = targetRegions.filter(
    (region) => !existingRegionNames.has(region.name.toLowerCase())
  );

  if (regionsToCreate.length > 0) {
    await Promise.all(
      regionsToCreate.map((region) => regionService.createRegions(region))
    );
  }

  logger.info(`Seeded ${regionsToCreate.length} region(s).`);
}