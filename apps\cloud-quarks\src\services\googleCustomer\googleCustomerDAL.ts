import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import { getSqlServerConnection } from "../../utils/sqlServerClient";
import sql from "mssql";
import {
  ResponseStatus,
  GoogleCreateCustomerRequest
} from "../../types/responses/customResponse";

/**
 * Save Customer in Database
 * Matches .NET GoogleCustomerDAL.SaveCustomer method
 */
export async function saveCustomerDAL(
  req: MedusaRequest,
  type: string,
  custId: string,
  googleCustomerId: string,
  operationId: string | null,
  operationType: string | null,
  storeId: string,
  brandIds: string[],
  partnerId: string,
  createCustomerRequest: GoogleCreateCustomerRequest,
  cloudIdentityId: string | null = null
): Promise<ResponseStatus> {
  logger.info(`Entered into DAL method SaveCustomer with customerType ${type},custId ${custId}, CustomerId ${googleCustomerId}, operationId ${operationId}, operationType ${operationType},storeId ${storeId},brandId ${JSON.stringify(brandIds)},partnerId ${partnerId}, createCustomerRequest ${JSON.stringify(createCustomerRequest)}, cloudIdentityId ${cloudIdentityId}.`);
  
  const response: ResponseStatus = { Status: false };

  try {
    const pool = await getSqlServerConnection();
    const sqlRequest = pool.request();

    const procedure = "spCreateGoogleCustomer";

    // Create brand IDs table (matching .NET: Utility.ConvertToSingleColumnDatatable(brandIds))
    const brandIdTable = createBrandIdTable(brandIds);

    // Add input parameters (matching .NET parameter structure)
    sqlRequest.input("CustId", sql.VarChar(50), custId);
    sqlRequest.input("StoreId", sql.VarChar(50), storeId);
    sqlRequest.input("BrandIds", brandIdTable); // Table-valued parameter
    sqlRequest.input("PartnerId", sql.VarChar(50), partnerId);
    sqlRequest.input("Type", sql.VarChar(20), type);
    sqlRequest.input("GoogleCustomerId", sql.VarChar(100), googleCustomerId);
    sqlRequest.input("DisplayName", sql.VarChar(255), createCustomerRequest.OrgDisplayName);
    sqlRequest.input("Revision", sql.Int, createCustomerRequest.OrgPostalAddress?.Revision);
    sqlRequest.input("RegionCode", sql.VarChar(10), createCustomerRequest.OrgPostalAddress?.RegionCode);
    sqlRequest.input("PostalLanguageCode", sql.VarChar(10), createCustomerRequest.OrgPostalAddress?.LanguageCode);
    sqlRequest.input("PostalCode", sql.VarChar(20), createCustomerRequest.OrgPostalAddress?.PostalCode);
    sqlRequest.input("SortingCode", sql.VarChar(50), createCustomerRequest.OrgPostalAddress?.SortingCode);
    sqlRequest.input("AdministrativeArea", sql.VarChar(100), createCustomerRequest.OrgPostalAddress?.AdministrativeArea);
    sqlRequest.input("Locality", sql.VarChar(100), createCustomerRequest.OrgPostalAddress?.Locality);
    sqlRequest.input("Sublocality", sql.VarChar(100), createCustomerRequest.OrgPostalAddress?.Sublocality);
    sqlRequest.input("AddressLine1", sql.VarChar(255), createCustomerRequest.OrgPostalAddress?.AddressLines?.[0]);
    sqlRequest.input("AddressLine2", sql.VarChar(255), createCustomerRequest.OrgPostalAddress?.AddressLines?.[1]);
    sqlRequest.input("AddressLine3", sql.VarChar(255), createCustomerRequest.OrgPostalAddress?.AddressLines?.[2]);
    sqlRequest.input("Recipients", sql.VarChar(500), createCustomerRequest.OrgPostalAddress?.Recipients?.join(", "));
    sqlRequest.input("Organization", sql.VarChar(255), createCustomerRequest.OrgPostalAddress?.Organization);
    sqlRequest.input("FirstName", sql.VarChar(100), createCustomerRequest.PrimaryContactInfo?.FirstName);
    sqlRequest.input("LastName", sql.VarChar(100), createCustomerRequest.PrimaryContactInfo?.LastName);
    sqlRequest.input("ContactDisplayName", sql.VarChar(255), createCustomerRequest.PrimaryContactInfo?.DisplayName);
    sqlRequest.input("ContactEmail", sql.VarChar(255), createCustomerRequest.PrimaryContactInfo?.Email);
    sqlRequest.input("Title", sql.VarChar(100), createCustomerRequest.PrimaryContactInfo?.Title);
    sqlRequest.input("Phone", sql.VarChar(50), createCustomerRequest.PrimaryContactInfo?.Phone);
    sqlRequest.input("AlternateEmail", sql.VarChar(255), createCustomerRequest.AlternateEmail);
    sqlRequest.input("Domain", sql.VarChar(255), createCustomerRequest.Domain);
    sqlRequest.input("LanguageCode", sql.VarChar(10), createCustomerRequest.LanguageCode);
    sqlRequest.input("CustomerType", sql.VarChar(50), createCustomerRequest.CloudIdentityInfo?.CustomerType);
    sqlRequest.input("PrimaryDomain", sql.VarChar(255), createCustomerRequest.CloudIdentityInfo?.PrimaryDomain);
    sqlRequest.input("IsDomainVerified", sql.VarChar(10), createCustomerRequest.CloudIdentityInfo?.IsDomainVerified);
    sqlRequest.input("CloudIdentityAlternateEmail", sql.VarChar(255), createCustomerRequest.CloudIdentityInfo?.AlternateEmail);
    sqlRequest.input("CloudIdentityPhone", sql.VarChar(50), createCustomerRequest.CloudIdentityInfo?.PhoneNumber);
    sqlRequest.input("CloudIdentityLanguageCode", sql.VarChar(10), createCustomerRequest.CloudIdentityInfo?.LanguageCode);
    sqlRequest.input("AdminConsoleUri", sql.VarChar(500), createCustomerRequest.CloudIdentityInfo?.AdminConsoleUri);
    sqlRequest.input("InstituteType", sql.VarChar(100), createCustomerRequest.CloudIdentityInfo?.EduData?.InstituteType);
    sqlRequest.input("InstituteSize", sql.VarChar(100), createCustomerRequest.CloudIdentityInfo?.EduData?.InstituteSize);
    sqlRequest.input("Website", sql.VarChar(500), createCustomerRequest.CloudIdentityInfo?.EduData?.Website);
    sqlRequest.input("ChannelPartnerId", sql.VarChar(100), createCustomerRequest.ChannelPartnerId);
    sqlRequest.input("CorrelationId", sql.VarChar(100), createCustomerRequest.CorrelationId);
    sqlRequest.input("OperationId", sql.VarChar(100), operationId);
    sqlRequest.input("OperationType", sql.VarChar(50), operationType);
    sqlRequest.input("CloudIdentityId", sql.VarChar(100), cloudIdentityId);

    // Add output parameters (matching .NET output parameters)
    sqlRequest.output("RetVal", sql.Bit);
    sqlRequest.output("Message", sql.VarChar(2000));

    logger.info(`Going to hit DB with proc name : ${procedure} and parameters`);
    await sqlRequest.execute(procedure);

    response.Status = sqlRequest.parameters.RetVal.value;
    logger.info(`Return value is ${response.Status}`);

    response.Message = sqlRequest.parameters.Message.value;
    logger.info(`Return message is ${response.Message}`);

    return response;

  } catch (error: any) {
    logger.error(`Error in saveCustomerDAL: ${error.message}`);
    response.Status = false;
    response.Message = `Database error: ${error.message}`;
    return response;
  }
}

/**
 * Create Brand ID Table for SQL Server Table-Valued Parameter
 * Matches .NET Utility.ConvertToSingleColumnDatatable(brandIds)
 */
function createBrandIdTable(brandIds: string[]): sql.Table {
  const table = new sql.Table();
  table.columns.add('Value', sql.VarChar(50));
  
  brandIds.forEach(brandId => {
    table.rows.add(brandId);
  });
  
  return table;
}
