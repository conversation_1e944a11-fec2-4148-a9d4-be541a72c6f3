import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import {
  ResponseStatus,
  PlaceTransitionRequest,
  MicrosoftValidationRequest,
  PromotionEligibilityRequest
} from "../../types/responses/customResponse";
import { performMicrosoftValidations } from "./performMicrosoftValidations";
import { validateSubscriptionTransition } from "./transitionDAL";

/**
 * Validate Place NCE Transition
 * Matches .NET ValidatePlaceNCETransition method
 */
export async function validatePlaceNCETransition(
  req: MedusaRequest,
  request: PlaceTransitionRequest,
  customerTenantId: string,
  mPNId: string,
  token: string
): Promise<ResponseStatus> {
  let response: ResponseStatus = { Status: false };

  logger.info(`Entered into ValidatePlaceNCETransition method with request-${JSON.stringify(request)}`);

  // Microsoft Validations
  // Validate MPNID
  // Validate MPNID Agreement
  // Validate Promotions for NCE
  const microsoftValidationRequest: MicrosoftValidationRequest = {
    CustomerTenantId: customerTenantId,
    MPNId: mPNId,
    PromotionEligibilityRequests: []
  };

  logger.info(`Preparing PromotionEligibility Request`);
  const peRequest: PromotionEligibilityRequest = {
    PromotionId: request.PromotionId,
    CatalogItemId: request.ToCatalogItemId,
    Quantity: request.Quantity,
    TermDuration: request.Term,
    BillingCycle: request.BillFreq
  };
  
  microsoftValidationRequest.PromotionEligibilityRequests.push(peRequest);

  logger.info(`Entering into PerformMicrosoftValidations method with request-${JSON.stringify(microsoftValidationRequest)}`);
  response = await performMicrosoftValidations(req, microsoftValidationRequest, token);
  logger.info(`Response from PerformMicrosoftValidations method with response-${JSON.stringify(response)}`);
  if (!response.Status) {
    logger.info(`Entered PerformMicrosoftValidations Response is not success and returning with response ${JSON.stringify(response)}`);
    return response;
  }

  // Validation SP
  // -Is Existing Transition request is in progress.
  // -Software upgrade is allowed only 26 hours before End date of an existing subscription
  // -Only one request can be taken that is either of upgrade, increase, decrease and terminate, suspend, resume.
  logger.info(`Entering into DAL ValidateSubscriptionTransition method with request-${JSON.stringify(request)}`);
  response = await validateSubscriptionTransition(req, request);
  logger.info(`Response from DAL ValidateSubscriptionTransition method with response-${JSON.stringify(response)}`);
  if (!response.Status) {
    logger.info(`Entered ValidateSubscriptionTransition Response is not success and returning with response ${JSON.stringify(response)}`);
    return response;
  }

  logger.info(`Exiting from ValidatePlaceNCETransition with response ${JSON.stringify(response)}`);
  return response;
}
