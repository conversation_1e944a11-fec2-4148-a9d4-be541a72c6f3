import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import {
  normalizeQueryParams,
  toPascalCaseJson,
} from "../../../../utils/mappers/getCustomerUsers";
import { validateGetCustomerByIdQuery } from "../../../../validators/customer/customCustomerValidator";
import { getCustomerByIdService } from "../../../../services/index";
import { ErrorResponseModel } from "../../../../types/responses/customResponse";

/**
 * @openapi
 * /v1/customer/getCustomerById:
 *   get:
 *     summary: Get customer details by type and tenant ID
 *     tags:
 *       - Customer
 *     parameters:
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [VALIDATE-ID, VALIDATE-DOMAIN]
 *         required: true
 *         description: Validation type (either VALIDATE-ID or VALIDATE-DOMAIN)
 *       - in: query
 *         name: brandId
 *         schema:
 *           type: string
 *         required: true
 *         description: Brand ID
 *       - in: query
 *         name: storeId
 *         schema:
 *           type: string
 *         required: true
 *         description: Store ID
 *       - in: query
 *         name: tenantId
 *         schema:
 *           type: string
 *         required: true
 *         description: Microsoft Tenant ID (used as either ID or domain depending on validation type)
 *     responses:
 *       200:
 *         description: Successfully retrieved customer information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                   example: "1"
 *                 Message:
 *                   type: string
 *                   example: "tenant.onmicrosoft.com"
 *       400:
 *         description: Bad request due to validation errors
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 statusCode:
 *                   type: integer
 *                   example: 400
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: string
 *       500:
 *         description: Internal server error or external endpoint failure
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 statusCode:
 *                   type: integer
 *                   example: 500
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: string
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    logger.info(`Get Customer By Id API is Hit`);
    const rawQuery = req.query;
    const { type, storeId, brandId, tenantId } = normalizeQueryParams(
      rawQuery,
      ["type", "storeId", "brandId", "tenantId"]
    );

    logger.info(
      `Normalized query params -> type: ${type}, storeId: ${storeId}, brandId: ${brandId}, tenantId: ${tenantId}`
    );

    const validationErrors = validateGetCustomerByIdQuery(
      type,
      storeId,
      brandId,
      tenantId
    );
    logger.info(
      `Validation error count = ${
        validationErrors.length
      }; Errors: ${JSON.stringify(validationErrors)}`
    );

    if (validationErrors.length > 0) {
      logger.info(`Returning 400 Bad Request with validation errors`);
      const errorResponse: ErrorResponseModel = {
        errors: validationErrors,
        statusCode: 400,
      };
      return res.status(400).json(errorResponse);
    }
    logger.info(`Calling getCustomerByIdService with params`);

    const response = await getCustomerByIdService(
      req,
      type,
      storeId,
      brandId,
      tenantId
    );

    const okResponse = response.GetCustomerById;
    const error = response.Error;

    if (error) {
      return res.status(500).json(error);
    }

    const pascalCased = toPascalCaseJson(okResponse);
    return res.status(200).json(pascalCased);
  } catch (err) {
    logger.error("Get Customer By Id API : ", err as Error);
    return res.status(500).json({
      status: false,
      message: "Internal server error",
    });
  }
}
