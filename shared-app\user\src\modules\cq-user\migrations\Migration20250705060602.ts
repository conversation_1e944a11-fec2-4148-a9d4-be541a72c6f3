import { Migration } from '@mikro-orm/migrations';

export class Migration20250705060602 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`create table if not exists "cq_document" ("id" text not null, "document" text not null, "isActive" boolean not null default true, "is_company_specific" boolean not null default false, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_document_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_document_deleted_at" ON "cq_document" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_column_master" ("id" text not null, "column_name" text not null, "column_slug" text null, "column_type" text null, "sort_order" integer not null default 0, "created_by" text null, "updated_by" text null, "table_master_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_column_master_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_column_master_table_master_id" ON "cq_column_master" (table_master_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_column_master_deleted_at" ON "cq_column_master" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_user_column_preference" ("id" text not null, "sort_order" integer not null default 0, "user_id" text not null, "column_master_id" text not null, "table_master_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_user_column_preference_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_user_column_preference_user_id" ON "cq_user_column_preference" (user_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_user_column_preference_column_master_id" ON "cq_user_column_preference" (column_master_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_user_column_preference_table_master_id" ON "cq_user_column_preference" (table_master_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_user_column_preference_deleted_at" ON "cq_user_column_preference" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`alter table if exists "cq_column_master" add constraint "cq_column_master_table_master_id_foreign" foreign key ("table_master_id") references "cq_page" ("id") on update cascade;`);

    this.addSql(`alter table if exists "cq_user_column_preference" add constraint "cq_user_column_preference_user_id_foreign" foreign key ("user_id") references "cq_user" ("id") on update cascade;`);
    this.addSql(`alter table if exists "cq_user_column_preference" add constraint "cq_user_column_preference_column_master_id_foreign" foreign key ("column_master_id") references "cq_column_master" ("id") on update cascade;`);
    this.addSql(`alter table if exists "cq_user_column_preference" add constraint "cq_user_column_preference_table_master_id_foreign" foreign key ("table_master_id") references "cq_page" ("id") on update cascade;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table if exists "cq_user_column_preference" drop constraint if exists "cq_user_column_preference_column_master_id_foreign";`);

    this.addSql(`drop table if exists "cq_document" cascade;`);

    this.addSql(`drop table if exists "cq_column_master" cascade;`);

    this.addSql(`drop table if exists "cq_user_column_preference" cascade;`);
  }

}
