import { ExecArgs } from '@medusajs/framework/types';
import { CQUser_MODULE } from '../modules/cq-user';
import CQUserService from '../modules/cq-user/service';

export default async function seedDesignations({ container }: ExecArgs) {
  const guestUserService: CQUserService = container.resolve(CQUser_MODULE);

  const targetDesignations = [
    { name: 'CEO', key: 'ceo' },
    { name: 'Marketing Manager', key: 'marketing_manager' },
    { name: 'President', key: 'president' },
    { name: 'Vice President', key: 'vice_president' },
    { name: 'CFO', key: 'cfo' },
    { name: 'General Manager', key: 'general_manager' },
    { name: 'Purchase Head', key: 'purchase_head' },
    { name: 'Sales Head', key: 'sales_head' },
    { name: 'Sales Executive', key: 'sales_executive' },
    { name: 'Purchase Executive', key: 'purchase_executive' },
    { name: 'Finance Executive', key: 'finance_executive' },
    { name: 'Marketing Executive', key: 'marketing_executive' },
    { name: 'Others', key: 'others' },
  ];

  const existingDesignations = await guestUserService.listDesignations({});
  const existingKeys = new Set(
    existingDesignations.map((d) => d.key.toLowerCase())
  );

  const designationsToCreate = targetDesignations
    .filter((d) => {
      if (!d.key || !d.name) {
        console.warn(`Invalid designation skipped: ${JSON.stringify(d)}`);
        return false;
      }
      return !existingKeys.has(d.key.toLowerCase());
    })
    .map((d) => ({ ...d, metadata: {} }));

  if (designationsToCreate.length > 0) {
    await Promise.all(
      designationsToCreate.map((designation) =>
        guestUserService.createDesignations(designation)
      )
    );
  }

  console.log(`Seeded ${designationsToCreate.length} designation(s).`);
}
