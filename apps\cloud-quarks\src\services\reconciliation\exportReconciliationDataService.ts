import logger from "../../utils/logger";
import { MedusaRequest } from "@medusajs/framework";
import { ExportReconciliationDataDTO } from "../../validators/reconciliation/exportReconciliationdataValidator";
import { getStoreDetails } from "../token/microsoft/getStoreDetailsService";
import { getMsToken } from "../token/microsoft/getTokenService";
import {
  createMicrosoftRequestHeaders,
  MicrosoftEndpoints,
} from "../../config/microsoftEndpoints";
import { PostMethodWithRequestJsonBodyAndHeaders400Handle } from "../externalService/externalEndPointService";

export type MicrosoftApiResponse = {
  message: string;
  data?: any;
  statusCode: number;
  isError: boolean;
};

type MicrosoftErrorResponse = {
  error?: {
    code?: string;
    message?: string;
    innerError?: {
      date?: string;
      requestId?: string;
      clientRequestId?: string;
    };
  };
};

export const MicrosoftDetails = {
  ProvisionTypeInDB: "AUTO",
};

export const CustomerCredentialType = {
  GRAPH: "GRAPH",
} as const;

export async function exportReconciliationDataService(
  req: MedusaRequest,
  input: ExportReconciliationDataDTO
): Promise<{
  response: MicrosoftApiResponse;
  headers: Record<string, string>;
}> {
  logger.info(
    `Going to hit BL GetStoreDetails method with ${JSON.stringify(input)}`
  );

  const storeDetails = await getStoreDetails({
    storeId: input.storeId,
    brandId: input.brandId,
    provisionType: "AUTO",
    credentialType: "GRAPH",
  });

  const token = await getMsToken({
    brand: storeDetails.brand || "",
    client_id: storeDetails.clientid || "",
    client_secret: storeDetails.clientsecret || "",
    grant_type: storeDetails.granttype || "",
    markValue: storeDetails.markvalue?.toString() || "",
    redirect_uri: storeDetails.redirecturi || "",
    refresh_token: storeDetails.token || "",
    resource: storeDetails.resource || "",
    store_domain: storeDetails.storedomain || "",
  });

  const headerList = createMicrosoftRequestHeaders(
    token.access_token,
    input.storeId
  );

  const completeurlWithParams =
    MicrosoftEndpoints.exportReconciliationDatauUrl();

  logger.info(
    `Calling external endpoint with URL: ${completeurlWithParams}, Body: ${JSON.stringify(
      input
    )}, Headers: ${JSON.stringify(headerList)}`
  );

  const configModule = req.scope.resolve("configModule");

  const response = await PostMethodWithRequestJsonBodyAndHeaders400Handle(
    {
      url: completeurlWithParams,
      body: input,
      headers: headerList,
      isVendorHit: true,
      module: "ExportReconciliationData",
    },
    configModule
  );

  logger.info(
    `Response from ExternalEndPoint PostMethodWithRequestJsonBodyAndHeaders400Handle is ${JSON.stringify(
      response
    )}`
  );

  const apiResponse: MicrosoftApiResponse = {
    message: "Success",
    statusCode: response.httpStatusCode,
    isError: false,
  };

  if (response.isSuccessStatusCode) {
    try {
      apiResponse.data = JSON.parse(response.content);
    } catch {
      apiResponse.data = response.content;
    }
  } else {
    logger.info(
      `Received errored response and deserialising response.Content-${response.errorMessage}`
    );

    try {
      const parsed: MicrosoftErrorResponse = JSON.parse(
        response.errorMessage || response.content || "{}"
      );

      apiResponse.message = parsed?.error?.message || "Unknown error";
    } catch {
      apiResponse.message = response.errorMessage || "Unknown error";
    }

    apiResponse.statusCode = response.httpStatusCode;
    apiResponse.isError = true;
  }

  logger.info(
    `Going to return response from BL. ApiResponse-${JSON.stringify(
      apiResponse
    )}, Headers-{}`
  );

  return {
    response: apiResponse,
    headers: {}, // add actual headers if needed
  };
}
