import { MedusaError } from '@medusajs/framework/utils';
import {
	createWorkflow,
	createStep,
	StepResponse,
	WorkflowResponse,
} from '@medusajs/framework/workflows-sdk';
import { z } from 'zod';
import { ContainerRegistrationKeys } from '@medusajs/framework/utils';
import { CompanyOnboardingSchema } from '../api/v1/company/company-registration/company-onboarding/validator';

type CompanyOnboardingSchemaType = z.infer<typeof CompanyOnboardingSchema>;

type CreateCompanyRegistrationWorkflowInput = {
	companyOnboarding: CompanyOnboardingSchemaType;
};

export async function generateCustomCompanyOnboardingIds(
	companyInfo: any[],
	regionCode: string
): Promise<string> {
	const date = new Date();
	const year = date.getFullYear().toString().slice(-2);
	let maxSequence = 0;

	if (Array.isArray(companyInfo)) {
		for (const info of companyInfo) {
			const match = info.id?.match(/^CO-([A-Z]+)-(\d{2})(\d{4})$/);
			if (match && match[1] === regionCode && match[2] === year) {
				const num = parseInt(match[3], 10);
				if (!isNaN(num)) {
					maxSequence = Math.max(maxSequence, num);
				}
			}
		}
	}

	const newSequence = String(maxSequence + 1).padStart(4, '0');
	return `CO-${regionCode}-${year}${newSequence}`;
}

const createCompanyOnboardingStep = createStep(
	'create-company-onboarding-step',
	async (
		{
			companyOnboarding: companyOnboardingData,
		}: Pick<CreateCompanyRegistrationWorkflowInput, 'companyOnboarding'>,
		{ container }
	) => {
		const query = container.resolve(ContainerRegistrationKeys.QUERY);
		const companyRegistrationService: any = container.resolve(
			'company_registration'
		);


		const regionData =
			await companyRegistrationService.getAllRegionsFromForeignTable();
		let regionCode = '';

		if (Array.isArray(regionData) && regionData.length > 0) {
			const matchedRegion = regionData.find((r: any) =>
				['India', 'Turkey', 'MEA'].includes(r.name)
			);

			if (matchedRegion) {
				regionCode =
					matchedRegion.name === 'India'
						? 'IN'
						: matchedRegion.name === 'Turkey'
						? 'TUR'
						: 'MEA';
			}
		}

		const currentYear = new Date().getFullYear().toString().slice(-2);
		const idPrefix = `CO-${regionCode}-${currentYear}`;

		const { data: onboardingInfo } = await query.graph({
			entity: 'cq_company_onboarding',
			fields: ['id'],
			filters: {
				id: {
					$like: `${idPrefix}%`,
				},
			},
			pagination: {
				skip: 0,
				take: 1000,
			},
		});

		const newId = await generateCustomCompanyOnboardingIds(
			onboardingInfo,
			regionCode
		);

		const {
			authorized_signatories,
			user_brand_preference,
			...filteredCompanyOnboardingData
		} = companyOnboardingData;
		const companyOnboarding =
			await companyRegistrationService.createCqCompanyOnboardings({
				...filteredCompanyOnboardingData,
				id: newId,
			});

		return new StepResponse(companyOnboarding);
	}
);

const createCompanyOnboardingWorkflow = createWorkflow(
	'create-company-onboarding',
	function (input: CreateCompanyRegistrationWorkflowInput) {
		const company = createCompanyOnboardingStep({
			companyOnboarding: input.companyOnboarding,
		});

		return new WorkflowResponse(company);
	}
);

export default createCompanyOnboardingWorkflow;

const getAllUserPersonaInfoStep = createStep(
	'get-all-user-persona-info-step',
	async (_input, { container }) => {
		const companyRegistrationService: any = container.resolve(
			'company_registration'
		);
		const companyType =
			await companyRegistrationService.getAllUserPersonasFromForeignTable();

		if (!companyType || companyType.length === 0) {
			throw new MedusaError(
				MedusaError.Types.NOT_FOUND,
				`No Company Type found in cq_company_type_foreign table`
			);
		}

		return new StepResponse(companyType);
	}
);

export const getAllCompanyTypeWorkflow = createWorkflow(
	'get-all-user-persona-workflow',
	function () {
		const companyType = getAllUserPersonaInfoStep();
		return new WorkflowResponse(companyType);
	}
);
