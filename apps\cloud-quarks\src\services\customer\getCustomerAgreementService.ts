import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import { getStoreDetails } from "../token/microsoft/getStoreDetailsService";
import { getMsToken } from "../token/microsoft/getTokenService";
import { getMethod } from "../externalService/externalEndPointService";
import {
  MicrosoftEndpoints,
  prepareHeaderGetCustomerValidationStatus,
} from "../../config/microsoftEndpoints";
import logger from "../../utils/logger";
import { GetCustomerAgreementServiceResponse } from "../../types/responses/customResponse";

export async function getCustomerAgreementService(
  req: MedusaRequest,
  brandId: string,
  storeId: string,
  custId: string
): Promise<GetCustomerAgreementServiceResponse> {
  const errors: string[] = [];
  logger.info(`Calling getCustomerAgreementService`);
  if (!brandId) errors.push("brandId is required");
  if (!storeId) errors.push("storeId is required");
  if (!custId) errors.push("custId is required");

  if (errors.length > 0) {
    return {
      error: {
        errors: [],
        statusCode: 400,
      },
    };
  }

  logger.info(
    `Calling GetStoreDetails for storeId=${storeId}, brandId=${brandId}`
  );
  const storeData = await getStoreDetails({ storeId, brandId });

  // Brand check
  const brandComparable = (storeData.brand || "")
    .replace(/\s+/g, "")
    .toUpperCase();
  if (brandComparable !== "MICROSOFT") {
    logger.info(
      `Brand is not MICROSOFT (${brandComparable}), skipping MS call`
    );
    return {
      error: {
        errors: ["Not Processed"],
        statusCode: -1,
      },
    };
  }

  // Log token request
  logger.info(
    `Preparing token request with storeData: ${JSON.stringify(storeData)}`
  );

  const token = await getMsToken({
    brand: storeData.brand || "",
    client_id: storeData.clientid || "",
    client_secret: storeData.clientsecret || "",
    grant_type: storeData.granttype || "",
    markValue: storeData.markvalue?.toString() || "",
    redirect_uri: storeData.redirecturi || "",
    refresh_token: storeData.token || "",
    resource: storeData.resource || "",
    store_domain: storeData.storedomain || "",
  });

  logger.info(`Token received: ${JSON.stringify(token)}`);

  if (!token || !token.access_token) {
    logger.error(`Token generation failed`);
    return {
      error: {
        errors: ["Error While Generating Token"],
        statusCode: 200,
      },
    };
  }

  const completeUrlWithParams =
    MicrosoftEndpoints.getCustomerAgreementUrl(custId);

  // Generate Microsoft headers
  const headerList = prepareHeaderGetCustomerValidationStatus(
    token.access_token
  );

  logger.info(
    `Prepared HeaderList for Microsoft call: ${JSON.stringify(headerList)}`
  );
  logger.info(`Hitting external endpoint with URL: ${completeUrlWithParams}`);

  const configModule = req.scope.resolve("configModule");

  const response = await getMethod(
    {
      url: completeUrlWithParams,
      headers: headerList,
      isVendorHit: true,
      module: "getCustomerAgreement",
    },
    configModule
  );

  logger.info(`Response from Microsoft: ${JSON.stringify(response)}`);
  logger.info(
    `Deserializing response.content into GetCustomerAgreementResponse`
  );

  const data = JSON.parse(response.content);

  if (!data || !data.totalCount || data.totalCount === 0) {
    return {
      error: {
        errors: ["No agreement data found"],
        statusCode: 200,
      },
    };
  }

  logger.debug("Raw parsed agreement data: " + JSON.stringify(data));
  const parsed = {
    getCustomerAgreement: {
      totalCount: data.totalCount,
      items: data.items,
    },
  };

  return parsed;
}
