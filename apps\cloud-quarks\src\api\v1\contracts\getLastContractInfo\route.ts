import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import { getLastContractInfoService } from "../../../../services/contracts/getLastContractInfoService";
import { validateGetLastContractInfoRequest } from "../../../../validators/contracts/customContractValidator";

/**
 * @openapi
 * /v1/contracts/getLastContractInfo:
 *   get:
 *     summary: Get last contract information by subscription ID
 *     tags:
 *       - Contracts
 *     parameters:
 *       - in: query
 *         name: subscriptionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Unique Subscription ID (e.g., S0BvabJVs3lBNl)
 *     responses:
 *       200:
 *         description: Last contract information retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   properties:
 *                     qty:
 *                       type: integer
 *                       example: 6
 *                     discount_percentage:
 *                       type: number
 *                       format: float
 *                       example: 0
 *                     discounted_unit_price:
 *                       type: number
 *                       format: float
 *                       example: 553
 *                     sub_total:
 *                       type: number
 *                       format: float
 *                       example: 3318
 *                     vat_percentage:
 *                       type: number
 *                       format: float
 *                       example: 5
 *                     promotion_percentage:
 *                       type: number
 *                       format: float
 *                       example: 0
 *                     net_price:
 *                       type: number
 *                       format: float
 *                       example: 3483.9
 *                     unit_price_after_promotion:
 *                       type: number
 *                       format: float
 *                       example: 553
 *                     unit_price_before_promotion:
 *                       type: number
 *                       format: float
 *                       example: 553
 *                     subscription_id:
 *                       type: string
 *                       example: "string"
 *                     promotion_id:
 *                       type: string
 *                       nullable: true
 *                       example: null
 *                     promotion_start_date:
 *                       type: string
 *                       format: date-time
 *                       nullable: true
 *                       example: null
 *                     promotion_end_date:
 *                       type: string
 *                       format: date-time
 *                       nullable: true
 *                       example: null
 *       400:
 *         description: Invalid or missing subscription ID
 *       500:
 *         description: Internal server error
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    logger.info(`Get Last Contract Info API is Hit`);
    const correlationId = getCorrelationId(req);
    const { subscriptionId } = req.query as { subscriptionId?: string };

    logger.info(
      `GetLastContractInfo API called | CorrelationId: ${correlationId}, subscriptionId: ${subscriptionId}`
    );

    // Validate subscriptionId
    const validationErrors = validateGetLastContractInfoRequest(subscriptionId || "");
    if (validationErrors.length > 0) {
      logger.info(
        `Validation errors | CorrelationId: ${correlationId} | Errors: ${JSON.stringify(
          validationErrors
        )}`
      );
      return res.status(400).json({
        statusCode: 400,
        errors: validationErrors,
      });
    }

    // At this point, validation has passed, so subscriptionId is guaranteed to be a non-empty string
    const validSubscriptionId = subscriptionId!;

    logger.info(`Calling getLastContractInfoService | CorrelationId: ${correlationId}`);
    const response = await getLastContractInfoService(validSubscriptionId);
    logger.info(`GetLastContractInfo service success | CorrelationId: ${correlationId}, response: ${JSON.stringify(response)}`);
    return res.status(200).json(response);
  } catch (err) {
    logger.error("Get Last Contract Info error: ", err as Error);
    return res.status(500).json({
      status: false,
      message: "Internal server error",
    });
  }
}