import { model } from '@medusajs/framework/utils';
import { Vendor } from './vendor';
import { UserBrandPreferences } from './user-brand-preferences';

export const Brand = model.define('cq_brand', {
  id: model.id().primaryKey(),
  brand_name: model.text(),
  vendor: model.belongsTo(() => Vendor, { mappedBy: 'vendor' }),
  user_brand_preferences: model.hasMany(() => UserBrandPreferences, {
    mappedBy: 'brand',
  }),
  metadata: model.json(),
});
