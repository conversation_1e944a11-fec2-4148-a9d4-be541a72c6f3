import logger from "../../utils/logger";
import { getSqlServerConnection } from "../../utils/sqlServerClient";
import sql from "mssql";

/**
 * Partner Validator
 * Matches .NET PartnerValidator functionality
 */
export async function validatePartner(partnerId: string): Promise<{ isValid: boolean; errors: string[] }> {
  logger.info(`Validating partner with partnerId: ${partnerId}`);
  
  const errors: string[] = [];

  if (!partnerId || partnerId.trim() === '') {
    errors.push('PartnerId is required');
    return { isValid: false, errors };
  }

  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();

    // Check if partner exists (matching .NET partner validation logic)
    request.input('PartnerId', sql.VarChar(50), partnerId);
    
    const result = await request.query(`
      SELECT COUNT(*) as PartnerCount 
      FROM Partners 
      WHERE PartnerId = @PartnerId AND IsActive = 1
    `);

    const partnerCount = result.recordset[0]?.PartnerCount || 0;

    if (partnerCount === 0) {
      errors.push(`Partner with ID '${partnerId}' does not exist or is inactive`);
    }

    logger.info(`Partner validation completed for partnerId: ${partnerId}, isValid: ${errors.length === 0}`);
    
    return {
      isValid: errors.length === 0,
      errors
    };
  } catch (error: any) {
    logger.error(`Error validating partner: ${error.message}`);
    errors.push('Partner validation failed due to database error');
    return { isValid: false, errors };
  }
}
