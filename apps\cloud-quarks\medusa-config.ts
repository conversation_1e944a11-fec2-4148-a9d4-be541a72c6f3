import { loadEnv, defineConfig } from '@medusajs/framework/utils'

loadEnv(process.env.NODE_ENV || 'development', process.cwd())

const getModules = () => {
  const baseModules = [
    {
      resolve: './src/modules/custom-loader',
    },
  ];

  return baseModules;
};

module.exports = defineConfig({
  projectConfig: {
    databaseUrl: process.env.DATABASE_URL,
    http: {
      storeCors: process.env.STORE_CORS!,
      adminCors: process.env.ADMIN_CORS!,
      authCors: process.env.AUTH_CORS!,
      jwtSecret: process.env.JWT_SECRET || 'supersecret',
      cookieSecret: process.env.COOKIE_SECRET || 'supersecret',
    },
    sessionOptions: {
      name: process.env.SESSION_NAME || 'auth_cookie',
      resave: false,
      rolling: false,
      saveUninitialized: false,
      ttl: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
      cookie: {
        secure: true,
        httpOnly: true,
        sameSite: 'Strict',
        path: '/',
      },
    } as any,
  },
  modules: getModules(),
});
