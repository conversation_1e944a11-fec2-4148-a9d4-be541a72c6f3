import { model } from '@medusajs/framework/utils';
import CQUser from './cq_user';
import UserPersona from './user-persona';
import Role from './role';
import Module from './module';

const Persona = model.define('cq_persona', {
	id: model.id().primaryKey(),
	persona: model.text().unique(),
	key: model.text(),
	metadata: model.json().nullable(),
	order_id: model.number(),
	created_by: model.belongsTo(() => CQUser, { mappedBy: undefined }).nullable(),
	updated_by: model.belongsTo(() => CQUser, { mappedBy: undefined }).nullable(),
	deleted_by: model.belongsTo(() => CQUser, { mappedBy: undefined }).nullable(),
	users: model.manyToMany(() => CQUser, {
		pivotEntity: () => UserPersona,
		mappedBy: 'personas',
	}),
	roles: model.hasMany(() => Role, { mappedBy: 'persona' }),
	modules: model.hasMany(() => Module, { mappedBy: 'persona' }),
});

export default Persona;
