import { Urls } from "../utils/constants";

export const GoogleEndpoints = {
  getGooglePurchasableOffersUrl: (googleAccountId: string, googleCustomerId: string, productId: string, skuId: string) => {
    const base = Urls.GGOGLE_BASE_URL || "";
    const path = Urls.GET_GOOGLE_PURCHASABLE_OFFERS || "";
    return `${base}${path.replace("{0}", googleAccountId).replace("{1}", googleCustomerId).replace("{2}", productId).replace("{3}", skuId)}`;
  },
  getEntitlementByIdUrl: (googleAccountId: string, googleCustomerId: string, entitlementId: string) => {
    const base = Urls.GGOGLE_BASE_URL || "";
    const path = Urls.GET_GOOGLE_ENTITLEMENT_BY_ID || "";
    return `${base}${path.replace("{0}", googleAccountId).replace("{1}", googleCustomerId).replace("{2}", entitlementId)}`;
  },
  getGoogleCustomerByIdUrl: (googleAccountId: string, googleCustomerId: string) => {
    const base = Urls.GGOGLE_BASE_URL || "";
    const path = Urls.GET_GOOGLE_CUSTOMER_BY_ID || "";
    return `${base}${path.replace("{0}", googleAccountId).replace("{1}", googleCustomerId)}`;
  },
  getChannelPartnerLinkStateUrl: (googleAccountId: string, cpId: string) => {
    const base = Urls.GGOGLE_BASE_URL || "";
    const path = Urls.GET_CHANNEL_PARTNER_LINK_STATE || "";
    return `${base}${path.replace("{0}", googleAccountId).replace("{1}", cpId)}`;
  },
  verifyDomainExistsUrl: (googleAccountId: string) => {
    const base = Urls.GGOGLE_BASE_URL || "";
    const path = Urls.GOOGLE_VERIFY_DOMAIN_EXISTS || "";
    return `${base}${path.replace("{0}", googleAccountId)}`;
  },
  createCustomerUrl: (googleAccountId: string) => {
    const base = Urls.GGOGLE_BASE_URL || "";
    const path = Urls.GOOGLE_CREATE_CUSTOMER || "";
    return `${base}${path.replace("{0}", googleAccountId)}`;
  },
  provisionCustomerCloudIdentityUrl: (googleAccountId: string, googleCustomerId: string) => {
    const base = Urls.GGOGLE_BASE_URL || "";
    const path = Urls.GOOGLE_PROVISION_CUSTOMER_CLOUD_IDENTITY || "";
    return `${base}${path.replace("{0}", googleAccountId).replace("{1}", googleCustomerId)}`;
  },
};