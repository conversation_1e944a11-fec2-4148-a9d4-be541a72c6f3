import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import {
  GoogleEndCustomerVerifyDomainResponse,
  GoogleDomainExistsResponse,
  GoogleErrorResponse
} from "../../types/responses/customResponse";
import { getGoogleTokenService } from "../token/google/getGoogleTokenService";
import { createGoogleRequestHeaders } from "../../config/microsoftEndpoints";
import { GoogleEndpoints } from "../../config/googleEndpoints";
import { PostMethodWithRequestJsonBodyAndHeaders400Handle, EndPointResponseModel } from "../externalService/externalEndPointService";
import { getGoogleAccountIdFromStore } from "../googleOffer/getGooglePurchasableOfferService";
import { Messages } from "./verifyDomainExistsService";

/**
 * Verify Domain Exists
 * Matches .NET VerifyDomainExists method
 */
export async function verifyDomainExists(
  req: MedusaRequest,
  domain: string,
  storeId: string,
  isExistingCustomer: boolean
): Promise<GoogleEndCustomerVerifyDomainResponse> {
  logger.info(`Entered into VerifyDomainExists service method with domain:${domain}, storeId ${storeId}, isExistingCustomer ${isExistingCustomer}`);

  const googleResponseModel: GoogleEndCustomerVerifyDomainResponse = {
    Message: "",
    IsAvailable: false,
    IsLinkable: false,
    Data: undefined,
    StatusCode: 200
  };

  logger.info(`Going to call DAL method GetGoogleAccountId with StoreId ${storeId}`);
  const googleAccountId = await getGoogleAccountIdFromStore(storeId);
  logger.info(`Got googleAccountId ${googleAccountId}`);

  // Build the URL for VerifyDomainExists (matching .NET URL construction)
  const verifyGoogleDomainUrl = GoogleEndpoints.verifyDomainExistsUrl(googleAccountId);
  logger.info(`VerifyDomainExists URL: ${verifyGoogleDomainUrl}`);

  logger.info(`Going to hit GetGoogleToken method with storeId-${storeId}`);
  const token = await getGoogleTokenService(req, storeId);
  logger.info(`Got token from BL TokenService method.`);

  const headerList = createGoogleRequestHeaders(req, token, storeId);
  logger.info(`Prepared HeaderList for external call- ${JSON.stringify(headerList)}`);

  // Create request body (matching .NET ExpandoObject)
  const requestBody = {
    domain: domain
  };

  const requestBodyJson = JSON.stringify(requestBody);
  logger.info(`Prepared requestBody for external call- ${requestBodyJson}`);

  logger.info(`Going to hit ExternalEndPoint PostMethodWithRequestJsonBodyAndHeaders400Handle method with url-${verifyGoogleDomainUrl}, RequestBody-${requestBodyJson}, headerlist-${JSON.stringify(headerList)}`);

  const configModule = req?.scope?.resolve("configModule");
  const response: EndPointResponseModel = await PostMethodWithRequestJsonBodyAndHeaders400Handle(
    {
      url: verifyGoogleDomainUrl,
      body: requestBodyJson,
      headers: headerList,
      isVendorHit: true,
      module: "VerifyDomainExists"
    },
    configModule
  );

  logger.info(`Response from ExternalEndPoint PostMethodWithRequestJsonBodyAndHeaders400Handle is ${JSON.stringify(response)}`);

  if (response.isSuccessStatusCode) {
    logger.info(`Entered into IsSuccessStatusCode true section. Going to deserialize content ${response?.content}`);

    try {
      const cloudIdentityAccountsExistResponse: GoogleDomainExistsResponse = JSON.parse(response.content);
      logger.info(`Deserialized object : ${JSON.stringify(cloudIdentityAccountsExistResponse)}`);

      if (!cloudIdentityAccountsExistResponse?.cloudIdentityAccounts) {
        logger.info(`Entered into section when cloudIdentityAccounts from Google is null`);

        googleResponseModel.IsAvailable = true;
        googleResponseModel.IsLinkable = false;
        googleResponseModel.Message = isExistingCustomer
          ? Messages.DomainNotExists(domain)
          : Messages.DomainAvailable;
      } else {
        logger.info(`Entered into section when cloudIdentityAccounts from Google is not null`);
        googleResponseModel.Data = {
          cloudIdentityAccounts: cloudIdentityAccountsExistResponse.cloudIdentityAccounts
        };

        const cloudIdentityAccount = cloudIdentityAccountsExistResponse.cloudIdentityAccounts[0];

        if (cloudIdentityAccount?.Existing === true) {
          logger.info(`Entered into section when cloudIdentityAccounts Existing is true`);

          if (isExistingCustomer) {
            logger.info(`Entered into section Where isExistingCustomer = true`);

            if (cloudIdentityAccount?.Owned === true) {
              logger.info(`Entered into section Where cloudIdentityAccounts Owned is true`);
              googleResponseModel.IsAvailable = false;
              googleResponseModel.IsLinkable = true;
              googleResponseModel.Message = Messages.DomainAvailableForLinking;
            } else {
              logger.info(`Entered into section Where cloudIdentityAccounts Owned is false`);
              googleResponseModel.IsAvailable = false;
              googleResponseModel.IsLinkable = false;
              googleResponseModel.Message = Messages.DomainExistsNotOwned(domain);
            }
          } else {
            logger.info(`Entered into section Where isExistingCustomer = false`);
            googleResponseModel.IsAvailable = false;
            googleResponseModel.IsLinkable = false;
            googleResponseModel.Message = Messages.DomainAlreadyExists;
          }
        }
      }
    } catch (parseError) {
      logger.error(`Error parsing response: ${parseError}`);
      googleResponseModel.IsAvailable = false;
      googleResponseModel.IsLinkable = false;
      googleResponseModel.Message = Messages.ExceptionMessage("Failed to parse response");
    }
  } else {
    logger.info(`Entered into IsSuccessStatusCode false section.`);

    logger.info(`Received errored vendorResponse and deserialising vendorResponse.ErrorMessage-${response.errorMessage}`);

    try {
      const errorResponse: GoogleErrorResponse = JSON.parse(response.errorMessage || "{}");
      logger.info(`DeserializeObject ${JSON.stringify(errorResponse)}`);

      googleResponseModel.IsAvailable = false;
      googleResponseModel.IsLinkable = false;
      googleResponseModel.Message = getErrorMessage(errorResponse);
      googleResponseModel.StatusCode = errorResponse.Error?.Code || 500;
    } catch (parseError) {
      logger.error(`Error parsing error response: ${parseError}`);
      googleResponseModel.IsAvailable = false;
      googleResponseModel.IsLinkable = false;
      googleResponseModel.Message = response.errorMessage || "Unknown error occurred";
      googleResponseModel.StatusCode = response.httpStatusCode || 500;
    }
  }

  logger.info(`Going to return vendorResponse from BL method with vendorResponse model ${JSON.stringify(googleResponseModel)}.`);

  return googleResponseModel;
}

/**
 * Get Error Message from Google Error Response
 * Matches .NET GoogleErrorResponse.GetErrorMessage() method
 */
function getErrorMessage(errorResponse: GoogleErrorResponse): string {
  if (errorResponse?.Error?.Message) {
    return errorResponse.Error.Message;
  }

  if (errorResponse?.Error?.Details && errorResponse.Error.Details.length > 0) {
    const firstDetail = errorResponse.Error.Details[0];
    if (firstDetail?.ErrorMessages && firstDetail.ErrorMessages.length > 0) {
      return firstDetail.ErrorMessages[0].ErrorMessage || "Unknown error";
    }
  }

  return "Unknown error occurred";
}