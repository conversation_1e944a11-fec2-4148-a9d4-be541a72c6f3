import { ExecArgs } from '@medusajs/framework/types';
import { CQUser_MODULE } from '../modules/cq-user';
import CQUserService from '../modules/cq-user/service';

export default async function seedPagePermissions({ container }: ExecArgs) {
  const guestUserService: CQUserService = container.resolve(CQUser_MODULE);

  const pagePermissionMapping = [
    {
      page: 'Company Registration',
      permission: ['Edit', 'View', 'Download'],
    },
    {
      page: 'Company Onboarding',
      permission: ['Edit', 'View', 'Download'],
    },
    {
      page: 'Companies',
      permission: ['Edit', 'View', 'Download'],
    },
    {
      page: 'Users',
      permission: ['Edit', 'View', 'Download'],
    },
  ];

  const [pages, permissions] = await Promise.all([
    guestUserService.listCqPages?.({}) || [],
    guestUserService.listCqPermissions?.({}) || [],
  ]);

  const pageMap = new Map(pages.map((p) => [p.page.toLowerCase(), p.id]));
  const permissionMap = new Map(
    permissions.map((p) => [p.permission.toLowerCase(), p.id])
  );

  const pagePermissionsToCreate = [];

  for (const entry of pagePermissionMapping) {
    const pageId = pageMap.get(entry.page.toLowerCase());
    if (!pageId) {
      console.warn(`Page not found: ${entry.page}`);
      continue;
    }

    for (const permName of entry.permission) {
      const permissionId = permissionMap.get(permName.toLowerCase());
      if (!permissionId) {
        console.warn(`Permission not found: ${permName}`);
        continue;
      }

      pagePermissionsToCreate.push({
        page_id: pageId,
        permission_id: permissionId,
        // metadata: {},
      });
    }
  }

  const existingPagePermissions =
    (await guestUserService.listCqPagePermissions?.({})) || [];

  const existingKeys = new Set(
    existingPagePermissions.map((p) => `${p.page_id}-${p.permission_id}`)
  );

  const newPagePermissions = pagePermissionsToCreate.filter((p) => {
    const key = `${p.page_id}-${p.permission_id}`;
    return !existingKeys.has(key);
  });

  if (newPagePermissions.length > 0) {
    await Promise.all(
      newPagePermissions.map((val) =>
        guestUserService.createCqPagePermissions(val)
      )
    );
  }

  console.log(
    `Seeded ${newPagePermissions.length} page-permission relation(s).`
  );
}
