##################### <<<List Country>>> #####################

START
DEFINE function "GET"
INPUT request (req), response (res)

INITIALIZE regionService using request scope

TRY
CALL regionService to list countries with selected fields: name, display_name
STORE result in "countries"

    IF countries is empty OR not found THEN
      RESPOND with status 404
      INCLUDE empty data and message "Countries not found"
      RETURN
    ENDIF

    RESPOND with status 200
    INCLUDE countries data and message "Countries fetched successfully"
    RETURN

CATCH error
RESPOND with status 500 and empty response
RETURN
END

##################### <<<List Designation>>> #####################

FUNCTION GET(req, res)
Get GuestUserService from request scope

    TRY
        Fetch designation list (select only name and key)

        IF no designations found THEN
            Return 404: "Designation not found"
        ENDIF

        Return 200: "Designation fetched successfully", with data

    CATCH error
        Return 500: "Error fetching designation"
    ENDTRY

END FUNCTION

##################### <<<List Region>>> #####################

FUNCTION GET(req, res)
Get RegionService from request scope

    TRY
        Fetch list of regions (only select 'name' field)

        IF no regions found THEN
            Return 404: "regions not found" with empty data array
        ENDIF

        Return 200: "regions fetched successfully" with region data

    CATCH error
        Return 500: Internal server error
    ENDTRY

END FUNCTION

##################### <<<Send Verification Code>>> #####################

FUNCTION POST(req, res)
Get email and mobile_number from request body
Set otp_code = "123456"

    IF email or mobile_number is provided THEN
        Search for existing OTP records with the same email or mobile number

        IF any record found THEN
            IF email matches existing record THEN
                Return 400: "Email already exists"
            ENDIF

            IF mobile_number matches existing record THEN
                Return 400: "Mobile number already exists"
            ENDIF
        ENDIF
    ENDIF

    Set expires_at = current time + 1 hour

    Create new OTP record with email, mobile_number, otp_code, and expires_at

    Return 200: "Verification code sent successfully"

END FUNCTION

##################### <<<Verify OTP>>> #####################

FUNCTION PUT(req, res)
Get email and otp_code from request body

    Search for OTP records with the given email

    IF no records found THEN
        Return 404: "No OTP record found"
    ENDIF

    Get the latest OTP record

    IF current time > OTP expiration time THEN
        Return 400: "OTP has expired"
    ENDIF

    IF otp_code does not match THEN
        Return 400: "Invalid OTP code"
    ENDIF

    Mark the OTP record as verified (is_verified = true)

    Return 200: "Verification successful", is_verified = true

END FUNCTION

##################### <<<Register Guest User>>> #####################

START
DEFINE function "createGuestUser"
INPUT email, mobileVerified, emailVerified, designationId, regionId, countryId, actorId

// Step 1: Check if email is verified
IF NOT emailVerified THEN
DISPLAY "Email not verified"
RETURN

// Step 2: Check if both email and mobile number are verified
IF NOT (emailVerified AND mobileVerified) THEN
DISPLAY "Email or Mobile number not verified"
RETURN

// Step 3: Retrieve and validate designation, region, country, and actor
designation = getDesignation(designationId)
region = getRegion(regionId)
country = getCountry(countryId)
actor = getActor(actorId)

IF designation IS NULL OR region IS NULL OR country IS NULL OR actor IS NULL THEN
DISPLAY "Invalid designation, region, country, or actor"
RETURN

// Step 4: Create guest user
guestUser = createUser(email, designation, region, country, actor)

IF guestUser IS NULL THEN
DISPLAY "User creation failed"
RETURN

// Step 5: Generate JWT token for email verification
verificationToken = generateJWTToken(guestUser.email)

// Step 6: Send verification email
emailSent = sendVerificationEmail(guestUser.email, verificationToken)

IF NOT emailSent THEN
DISPLAY "Failed to send verification email"
RETURN

// Step 7: Return success response
DISPLAY "Guest user created successfully"
RETURN guestUser
END

##################### <<<Reset Password>>> #####################

FUNCTION PUT(req, res)
Get authModuleService from request scope
Get token from request parameters
Get password from request body

    TRY
        Decode token using JWT_SECRET
        Extract email from decoded token

        IF email is missing THEN
            Return 400: "Invalid token"
        ENDIF

        Get guestUserService from request scope

        Search for guest user by email
        IF no user found THEN
            Return 404: "Guest user not found"
        ENDIF

        // Step 1: Create authentication identity with email and password
        CALL authModuleService.createAuthIdentities WITH:
            provider: "emailpass"
            entity_id: userEmail
            provider_metadata: password
            app_metadata: {}

        // Step 2: Update the password for that identity
        CALL authModuleService.updateProvider WITH:
            provider: "emailpass"
            entity_id: userEmail
            password: password

        Return 200: "Password updated successfully"

    CATCH error
        Log error
        Return 500: "Internal server error"
    ENDTRY

END FUNCTION

##################### <<<Resend OTP>>> #####################


START
  DEFINE function "POST"
  INPUT request containing a common key (email or mobile number)
  EXTRACT common_key from the request body
  IF common_key is empty OR INVALID (e.g., format of email or mobile number is incorrect) THEN
    RETURN "Invalid input" response
  ENDIF
  
  QUERY email_otp_verification table for entry WHERE (email = common_key OR mobile_number = common_key)
    AND is_verified = false AND expires_at < current_time
  IF entry is not found THEN
    RETURN "Request not exist" response
  ELSE
    // Handle OTP expiration
    IF entry.expires_at < current_time THEN
      DELETE expired entry or handle accordingly
    ENDIF
    GENERATE new OTP code securely (using random string/number)
    COMMIT changes to the database
    RETURN "Success" response
  ENDIF
END
