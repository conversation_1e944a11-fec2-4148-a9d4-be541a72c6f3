import { model } from "@medusajs/framework/utils";
import { Vendor } from "./vendor";
import { Brand } from "./brand";
import { BrandCategory } from "./brand-category";
import { CqCompanyRegistration } from "./company-registration";
import { CqCompanyOnboarding } from "./company-onboarding";

export const UserBrandPreferences = model.define("cq_user_brand_preferences", {
	id: model.id().primaryKey(),
	vendor: model.belongsTo(() => Vendor),
	brand: model.belongsTo(() => Brand),
	brand_category: model.belongsTo(() => BrandCategory),
	company_registration: model.belongsTo(() => CqCompanyRegistration).nullable(),
	company_onboarding: model.belongsTo(() => CqCompanyOnboarding).nullable(),
});
