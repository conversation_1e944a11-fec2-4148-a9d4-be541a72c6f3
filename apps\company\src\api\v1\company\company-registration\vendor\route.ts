import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { stringConstants } from '@org/utils';
import { COMPANY_REGISTRATION_MODULE } from '../../../../../modules/company';
import CompanyRegistrationModuleService from '../../../../../modules/company/service';

/**
 * @openapi
 * /v1/company/company-registration/vendor:
 *   get:
 *     summary: Get Vendors List by Region (with optional filters)
 *     tags:
 *       - Company Registration
 *     parameters:
 *       - in: query
 *         name: region_id
 *         required: true
 *         schema:
 *           type: string
 *         description: Region ID to filter vendors (required)
 *       - in: query
 *         name: brand_id
 *         required: false
 *         schema:
 *           type: string
 *         description: Brand ID to further filter vendors (optional)
 *       - in: query
 *         name: brand_category_id
 *         required: false
 *         schema:
 *           type: string
 *         description: Brand Category ID to further filter vendors (optional)
 *     responses:
 *       200:
 *         description: Successfully fetched vendors
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         example: 01JW80ZPBEPSKF6J7VM1HE3MGZ
 *                       vendor_name:
 *                         type: string
 *                         example: Microsoft
 *                 message:
 *                   type: string
 *                   example: Vendor fetched successfully
 *       400:
 *         description: Missing required region_id parameter
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: region_id parameter is missing
 *       404:
 *         description: No vendors found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items: {}
 *                 message:
 *                   type: string
 *                   example: Vendor not found
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error. Please try again later.
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
	try {
		const vendorService: CompanyRegistrationModuleService = req.scope.resolve(
			COMPANY_REGISTRATION_MODULE
		);

		const { region_id } = req.query as {
			region_id?: string;
		};

		if (!region_id) {
			return res.status(400).json({
				message: stringConstants.PARAMETER_MISSING('region_id'),
			});
		}

		// Fetch vendors by region
		const regionVendors = await vendorService.listRegionVendors({
			region_id,
		});
		const regionVendorIds = regionVendors.map((rb) => rb.vendor_id);

		if (!regionVendorIds.length) {
			return res.status(404).json({
				data: [],
				message: stringConstants.NOT_FOUND('Vendor'),
			});
		}

		let finalVendorIds = regionVendorIds;

		if (!finalVendorIds.length) {
			return res.status(404).json({
				data: [],
				message: stringConstants.NOT_FOUND('Vendor'),
			});
		}

		const ids = finalVendorIds.map((v) => v.id);
		const vendors = await vendorService.listVendors({ id: ids });
		const filteredVendors = vendors.map(({ id, vendor_name }) => ({
			id,
			vendor_name,
		}));

		return res.status(200).json({
			data: filteredVendors,
			message: stringConstants.FETCHED_SUCCESSFULLY('Vendor'),
		});
	} catch (err) {
		return res.status(500).json({
			message: stringConstants.INTERNAL_SERVER_ERROR(),
		});
	}
}
