import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import { 
  CPIdResponseModel, 
  CPIdData, 
  ChannelPartnerLinkStateResponse,
  GoogleErrorResponse 
} from "../../types/responses/customResponse";
import { getGoogleTokenService } from "../token/google/getGoogleTokenService";
import { createGoogleRequestHeaders } from "../../config/microsoftEndpoints";
import { GoogleEndpoints } from "../../config/googleEndpoints";
import { getMethod } from "../externalService/externalEndPointService";
import { getGoogleAccountIdFromStore } from "../googleOffer/getGooglePurchasableOfferService";
import { Messages } from "../googleCustomer/verifyDomainExistsService";

/**
 * Get CP ID Details
 * Matches .NET GooglePartnerManagementService.GetCPIDDetails method
 */
export async function getCPIDDetailsService(
  cpId: string,
  storeId: string,
  partnerId: string,
  brandId: string,
  req?: MedusaRequest
): Promise<CPIdResponseModel> {
  logger.info(`Entered into GetCPIDDetails service method with cpId:${cpId}, storeId as ${storeId}, partnerId as ${partnerId}, brandId as ${brandId}`);

  let cpIdResponse: CPIdResponseModel;

  try {
    // Get Google Account ID (matching .NET: _googlePartnerManagementDAL.GetGoogleRedingtonAccountId(storeId))
    logger.info(`Going to call DAL method GetGoogleRedingtonAccountId with StoreId ${storeId}`);
    const googleAccountId = await getGoogleAccountIdFromStore(storeId);
    logger.info(`Got googleAccountId ${googleAccountId}`);

    // Build the channel partner link state URL (matching .NET URL construction)
    const getChannelPartnerLinkStateUrl = GoogleEndpoints.getChannelPartnerLinkStateUrl(googleAccountId, cpId);
    logger.info(`Channel partner link state URL: ${getChannelPartnerLinkStateUrl}`);

    // Get Google token (matching .NET: _tokenService.GetGoogleToken(storeId))
    logger.info(`Going to hit GetGoogleToken method with storeId-${storeId}`);
    const token = await getGoogleTokenService(req, storeId);
    logger.info(`Got token from BL TokenService method.`);

    // Create Google request headers (matching .NET: _externalEndpointRequestHeaders.CreateGoogleRequestHeaders(token, storeId))
    const headerList = req 
      ? createGoogleRequestHeaders(req, token, storeId)
      : {
          Authorization: `Bearer ${token}`,
          "x-region": storeId,
        };
    
    logger.info(`Prepared HeaderList for external call- ${JSON.stringify(headerList)}`);

    logger.info(`Going to hit ExternalEndPoint GetMethod method with url-${getChannelPartnerLinkStateUrl} and headerlist-${JSON.stringify(headerList)}`);
    
    const configModule = req?.scope?.resolve("configModule");
    
    // Call Google API (matching .NET: GetMethod)
    const response = await getMethod(
      {
        url: getChannelPartnerLinkStateUrl,
        headers: headerList,
        isVendorHit: true,
        module: "GetChannelPartnerId",
      },
      configModule
    );

    logger.info(`Response from ExternalEndPoint GetMethod is ${JSON.stringify(response)}`);

    if (response.isSuccessStatusCode) {
      logger.info(`Received success response and deserializing response.Content-${response.content} to ChannelPartnerLinkStateResponse`);
      
      // Deserialize response to ChannelPartnerLinkStateResponse (matching .NET deserialization)
      const linkStateModel: ChannelPartnerLinkStateResponse = JSON.parse(response.content);
      
      cpIdResponse = {
        Data: {
          CpiStatus: linkStateModel.LinkState,
          InvitationLink: linkStateModel.InviteLinkUri,
          PrimaryDomain: linkStateModel.channelPartnerCloudIdentityInfo?.PrimaryDomain,
          IsDomainVerified: linkStateModel.channelPartnerCloudIdentityInfo?.IsDomainVerified,
          Name: linkStateModel.Name,
          PublicId: linkStateModel.PublicId,
        },
        StatusCode: 200,
        IsError: false
      };
    } else {
      logger.info(`Received errored response and deserialising response.Content-${response.content}`);
      
      // Handle error response (matching .NET error handling)
      const errorResponse: GoogleErrorResponse = JSON.parse(response.content || response.errorMessage || "{}");
      
      // Fetch first error message from array else fetch message (matching .NET logic)
      const errorMessageFromGoogle = errorResponse.Error?.Details?.[0]?.ErrorMessages?.[0]?.ErrorMessage ?? errorResponse.Error?.Message;
      
      cpIdResponse = {
        Message: errorMessageFromGoogle || "Generic exception occurred",
        StatusCode: errorResponse.Error?.Code || 500,
        IsError: true
      };
    }

  } catch (error: any) {
    logger.error(`Error in GetCPIDDetails service: ${error.message}`);
    
    cpIdResponse = {
      Message: Messages.ExceptionMessage(error.message),
      StatusCode: 500,
      IsError: true
    };
  }

  // TODO: Implement SaveCPIDHistory DAL call when needed
  // logger.info(`Going to call DAL method SaveCPIDHistory`);
  // await saveCPIDHistory(storeId, partnerId, cpId, brandId, "FETCHCPID", cpIdResponse);

  logger.info(`Returning response BL ${JSON.stringify(cpIdResponse)}`);
  return cpIdResponse;
}
