import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import {
  PatchSubscriptionModel,
  GetSubscriptionResponse,
} from "../../types/responses/customResponse";
import { isValidPatchStatus } from "../../utils/constants/subscriptionStatusConstants";
import { getStoreDetails } from "../token/microsoft/getStoreDetailsService";
import { getMsToken } from "../token/microsoft/getTokenService";
import { MICROSOFT_CONSTANTS, MicrosoftEndpoints } from "../../config/microsoftEndpoints";
import { tryPatchSubscriptionService } from "./tryPatchSubscriptionService";

export async function patchSubscriptionService(
  req: MedusaRequest,
  request: PatchSubscriptionModel
): Promise<GetSubscriptionResponse> {
  logger.info(
    `Entered into PatchSubscription Service method with request ${JSON.stringify(
      request
    )}`
  );

  const status = request.status || "";

  console.log("status--------->", status);

  if (!isValidPatchStatus(status)) {
    logger.info(`Returning Exception - Invalid subscription status`);
    throw new Error("Invalid subscription status");
  }

  try {
    // Get store details
    const storeData = await getStoreDetails({
      storeId: request.storeId || "",
    });

    logger.info(`Store details retrieved successfully`);

    // Get token from Microsoft
    const token = await getMsToken({
      brand: storeData.brand || "",
      client_id: storeData.clientid || "",
      client_secret: storeData.clientsecret || "",
      grant_type: storeData.granttype || "",
      markValue: storeData.markvalue?.toString() || "0",
      redirect_uri: storeData.redirecturi || "http://localhost",
      refresh_token: storeData.token || "",
      resource: storeData.resource || "",
      store_domain: storeData.storedomain || "",
    });

    if (!token || !token.access_token) {
      logger.error(`Token generation failed`);
      throw new Error("Failed to generate access token");
    }

    logger.info(`Token generated successfully`);

    // Construct endpoint URL
    const endPoint = MicrosoftEndpoints.patchSubscriptionUrl(
      request.customerId || "",
      request.subscriptionId || ""
    );

    logger.info(`Constructed endpoint URL: ${endPoint}`);

    // Use retry values from constants
    const totalRetry = MICROSOFT_CONSTANTS.RETRY_PATCH_SUBSCRIPTION_COUNT;
    const retryTimeSpanRaw =
      MICROSOFT_CONSTANTS.RETRY_PATCH_SUBSCRIPTION_TIMESPAN;

    // Parse hh:mm:ss to milliseconds
    function parseTimeSpanToMs(timespan: string): number {
      const [hours, minutes, seconds] = timespan.split(":").map(Number);
      return (hours * 3600 + minutes * 60 + seconds) * 1000;
    }

    const retryTimeSpanMs = parseTimeSpanToMs(retryTimeSpanRaw);

    console.log("totalRetry--------------->",totalRetry);
    console.log("retryTimeSpanMs--------------->",retryTimeSpanMs);

    logger.info(
      `Retry configuration - totalRetry: ${totalRetry}, retryTimeSpanMs: ${retryTimeSpanMs}`
    );

    // Execute with retry logic
    return await executeWithRetry(
      () =>
        tryPatchSubscriptionService(req, request, token.access_token, endPoint),
      retryTimeSpanMs,
      totalRetry
    );
  } catch (error: any) {
    logger.error(`Error in patchSubscriptionService: ${error.message}`);
    throw error;
  }
}

/**
 * Execute function with retry logic
 * Matches .NET Retry.Execute pattern
 */
async function executeWithRetry<T>(
  operation: () => Promise<T>,
  retryTimeSpanMs: number,
  maxRetries: number
): Promise<T> {
  let lastError: Error | undefined;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      logger.info(
        `Executing operation, attempt ${attempt + 1} of ${maxRetries + 1}`
      );
      return await operation();
    } catch (error: any) {
      lastError = error;
      logger.error(`Attempt ${attempt + 1} failed: ${error.message}`);

      if (attempt < maxRetries) {
        logger.info(`Waiting ${retryTimeSpanMs}ms before retry...`);
        await delay(retryTimeSpanMs);
      }
    }
  }

  logger.error(
    `All retry attempts exhausted. Throwing last error: ${
      lastError?.message || "Unknown error"
    }`
  );
  throw (
    lastError || new Error("All retry attempts exhausted with unknown error")
  );
}

/**
 * Delay function for retry logic
 */
function delay(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}
