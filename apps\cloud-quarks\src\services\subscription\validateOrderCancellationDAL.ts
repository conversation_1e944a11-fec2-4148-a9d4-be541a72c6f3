import logger from "../../utils/logger";
import sql from "mssql";
import { getSqlServerConnection } from "../../utils/sqlServerClient";
import { CancellationStatus } from "../../types/responses/customResponse";

/**
 * Validate Order Cancellation DAL
 * Matches .NET SubscriptionFactory.ValidateOrderCancellation method
 */
export async function validateOrderCancellationDAL(
  subscriptionId: string
): Promise<CancellationStatus> {
  logger.info(`Entered into ValidateOrderCancellation Service method with subscriptionId ${subscriptionId}`);

  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();

    // Add input parameter (matching .NET: param[0] = new SqlParameter("@iSubscriptionId", subscriptionId))
    request.input("iSubscriptionId", sql.VarChar(255), subscriptionId);
    
    // Add output parameters (matching .NET output parameters)
    // param[1] = new SqlParameter("@oStatus", SqlDbType.VarChar, 100); param[1].Direction = ParameterDirection.Output;
    request.output("oStatus", sql.VarChar(100));
    
    // param[2] = new SqlParameter("@oMessage", SqlDbType.VarChar, 500); param[2].Direction = ParameterDirection.Output;
    request.output("oMessage", sql.VarChar(500));

    const procedure = "spValidateOrderCancellation"; // Matches .NET stored procedure name

    // Log trace DB request (matching .NET logging)
    logger.info(`Request DB to ${procedure} @iSubscriptionId:${subscriptionId}`);

    // Execute stored procedure (matching .NET: cmd.ExecuteNonQuery())
    const result = await request.execute(procedure);

    // Extract output parameter values (matching .NET: Convert.ToString(param[1].Value))
    const status = result.output?.oStatus || "";
    const message = result.output?.oMessage || "";

    // Create CancellationStatus object (matching .NET CancellationStatus creation)
    const cancellationStatus: CancellationStatus = {
      Status: status,
      Message: message
    };

    logger.info(`Response from ${procedure} @iSubscriptionId: ${subscriptionId}, status ${JSON.stringify(cancellationStatus)}`);
    
    return cancellationStatus;

  } catch (error: any) {
    logger.error(`Error in validateOrderCancellationDAL: ${error.message}`);
    
    // Return error status (following established error handling patterns)
    return {
      Status: "ERROR",
      Message: error.message
    };
  }
}
