import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import { 
  CiscoCustomerRequest, 
  CiscoCustomerResponse,
  CiscoCustomerPageSize,
  CiscoCustomerParty
} from "../../types/responses/customResponse";
import { getCiscoTokenService } from "../token/cisco/getCiscoTokenService";
import { PostMethodWithRequestJsonBodyAndHeaders400Handle } from "../externalService/externalEndPointService";
import { CiscoDetails } from "../../config/microsoftEndpoints";
import { CiscoConstants } from "../../validators/reseller/addUpdateCiscoResellerValidator";
import { Urls } from "../../utils/constants";

/**
 * Search Cisco Customers
 * Matches .NET CustomerFactory.SearchCiscoCustomers method
 */
export async function searchCiscoCustomersService(
  customerRequestModel: CiscoCustomerRequest,
  req?: MedusaRequest
): Promise<CiscoCustomerResponse> {
  logger.info(`Entered into SearchCiscoCustomers method with customerRequestModel as ${JSON.stringify(customerRequestModel)}`);

  try {
    // Set default page size if not provided (matching .NET logic)
    if (!customerRequestModel.get || customerRequestModel.get.pageSize === 0) {
      logger.info(`If page size is null and not received from payload, then we are fetching it from AppSetting`);
      
      logger.info(`Going to fetch page size from AppSetting`);
      const pageSizeLimit = CiscoDetails.GetAllCustomerPageSize;
      logger.info(`Fetched page size : ${pageSizeLimit}`);

      logger.info(`Setting the page size to request object : ${pageSizeLimit}`);
      customerRequestModel.get = { pageSize: pageSizeLimit };
      logger.info(`Cisco Customer Request Object : ${JSON.stringify(customerRequestModel)}`);
    }

    // Get URL configurations (matching .NET configuration fetching)
    logger.info(`Going to fetch Customer Name Url from AppSetting`);
    const searchCustomerUrl = Urls.CS_GET_CUSTOMER_NAMES;
    logger.info(`Fetched Customer Name Url : ${searchCustomerUrl}`);

    logger.info(`Going to fetch ciscoCustomerBaseUrl from AppSetting`);
    const ciscoCustomerBaseUrl = Urls.CS_CUSTOMER_BASE_URL;
    logger.info(`Fetched ciscoCustomerBaseUrl : ${ciscoCustomerBaseUrl}`);

    logger.info(`Going to fetch mulesoftBaseUrl from AppSetting`);
    const mulesoftBaseUrl = process.env.MS_BASE_URL || "";
    logger.info(`Fetched mulesoftBaseUrl : ${mulesoftBaseUrl}`);

    // Generate Cisco token (matching .NET: _tokenService.GetCiscoToken)
    logger.info(`Going to generate token using tokenService.GetCiscoToken method using storeId: ${customerRequestModel.storeId}, CredentialType: ${CiscoConstants.CustomerCredentialType}`);
    const token = await getCiscoTokenService(req!, customerRequestModel.storeId!, CiscoConstants.CustomerCredentialType);
    logger.info(`Token generated successfully`);

    // Serialize request to JSON (matching .NET: JsonConvert.SerializeObject)
    logger.info(`Serializing Cisco Customer Request Object into json : ${JSON.stringify(customerRequestModel)}`);
    const payloadInJson = JSON.stringify(customerRequestModel);
    logger.info(`Serialized json ${payloadInJson}`);

    // Create authorization headers (matching .NET header creation)
    logger.info(`Creating Authorization header`);
    const headerList: Record<string, string> = {
      "Authorization": `Bearer ${token}`
    };

    // Build search endpoint URL (matching .NET URL construction)
    const searchEndpoint = `${mulesoftBaseUrl}${searchCustomerUrl.replace("{0}", ciscoCustomerBaseUrl)}`;
    logger.info(`Search endpoint URL: ${searchEndpoint}`);

    let responseFromCisco: CiscoCustomerResponse = {};

    logger.info(`Going to call PostMethodWithRequestJsonBodyAndHeaders400Handle with searchEndpoint: ${searchEndpoint}, payloadInJson: ${payloadInJson}, headerList: ${JSON.stringify(headerList)}`);

    const configModule = req?.scope?.resolve("configModule");

    // Call Cisco API (matching .NET: PostMethodWithRequestJsonBodyAndHeaders400Handle)
    const response = await PostMethodWithRequestJsonBodyAndHeaders400Handle(
      {
        url: searchEndpoint,
        body: payloadInJson,
        headers: headerList,
        isVendorHit: true,
        module: "SearchCiscoCustomers",
      },
      configModule
    );

    logger.info(`Response got from Cisco ${JSON.stringify(response)}`);

    if (response.isSuccessStatusCode) {
      logger.info(`Entered into IsSuccessStatusCode true section. Going to deserialize string ${response?.content}`);
      
      // Deserialize successful response (matching .NET: JsonConvert.DeserializeObject<CiscoCustomerResponse>)
      responseFromCisco = JSON.parse(response.content) as CiscoCustomerResponse;
      logger.info(`Deserialized object : ${JSON.stringify(responseFromCisco)}`);
    } else {
      logger.info(`Entered into IsSuccessStatusCode false section.`);
      logger.info(`Setting status Failure`);
      
      // Handle error response (matching .NET error handling)
      responseFromCisco = {
        status: "FAILURE",
        errorCode: response.httpStatusCode.toString(),
        message: response.errorMessage || "Unknown error occurred"
      };
    }

    logger.info(`Going to send the response ${JSON.stringify(responseFromCisco)}`);
    return responseFromCisco;

  } catch (error: any) {
    logger.error(`Error in SearchCiscoCustomers service: ${error.message}`);
    
    // Return error response
    return {
      status: "FAILURE",
      errorCode: "500",
      message: `An error occurred: ${error.message}`
    };
  }
}
