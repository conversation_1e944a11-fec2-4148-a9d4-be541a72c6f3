import logger from "../../utils/logger";
import sql from "mssql";
import { getSqlServerConnection } from "../../utils/sqlServerClient";
import { AwsPayerAccountRequest } from "../../types/responses/customResponse";

/**
 * Get Payer Account IDs by Request ID
 * Matches .NET GetPayerAccountIdsByRequestId method
 */
export async function getPayerAccountIdsByRequestId(
  requestId: string,
  month: number,
  year: number
): Promise<AwsPayerAccountRequest[]> {
  logger.info(`Entered into GetPayerAccountIdsByRequestId with requestid:${requestId}, month:${month}, year:${year}`);

  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();

    // Add parameters (matching .NET SqlParameter setup)
    request.input("iRequestId", sql.Var<PERSON>har(255), requestId);
    request.input("iMonth", sql.Int, month);
    request.input("iYear", sql.Int, year);

    const procedure = "GetPayerAccountIdsByRequestId"; // Matches .NET Procedures.GetPayerAccountIdsByRequestId

    logger.info(`Going to hit DB with proc name: ${procedure} with parameters requestId:${requestId}, month:${month}, year:${year}`);

    const result = await request.execute<AwsPayerAccountRequest>(procedure);
    const response = result.recordset || [];

    logger.info(`Going to return datatable: ${JSON.stringify(response)}`);
    return response;

  } catch (error: any) {
    logger.error(`Error in getPayerAccountIdsByRequestId: ${error.message}`);
    throw error;
  }
}

/**
 * Create list from table utility function
 * Matches .NET Utility.CreateListFromTable<AwsPayerAccountRequest>(dt)
 */
export function createAwsPayerAccountListFromRecords(records: any[]): AwsPayerAccountRequest[] {
  logger.info(`Converting ${records.length} records to AwsPayerAccountRequest list`);
  
  return records.map(record => ({
    PayerAccountId: record.PayerAccountId || "",
    RequestId: record.RequestId || "",
    Month: record.Month || 0,
    Year: record.Year || 0,
  }));
}

/**
 * Extract Payer Account IDs from list
 * Matches .NET awsPayerAccountIds.Select(k => k.PayerAccountId).ToList()
 */
export function extractPayerAccountIds(awsPayerAccountRequests: AwsPayerAccountRequest[]): string[] {
  const payerAccountIds = awsPayerAccountRequests.map(item => item.PayerAccountId);
  logger.info(`Extracted payer account IDs: ${JSON.stringify(payerAccountIds)}`);
  return payerAccountIds;
}
