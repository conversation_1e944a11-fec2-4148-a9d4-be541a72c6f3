import { ExecArgs } from '@medusajs/framework/types';
import { CQUser_MODULE } from '../modules/cq-user';
import CQUserService from '../modules/cq-user/service';

export default async function seedPermission({ container }: ExecArgs) {
  const guestUserService: CQUserService = container.resolve(CQUser_MODULE);

  const targetPermissions = [
    { permission: 'View' },
    { permission: 'Add' },
    { permission: 'Edit' },
    { permission: 'Delete' },
    { permission: 'Download' },
  ];

  const existingPermissions =
    (await guestUserService.listCqPermissions({})) || [];

  const existingKeys = new Set(
    existingPermissions.map((d) => d.permission.toLowerCase())
  );

  const permissionsToCreate = targetPermissions
    .filter((d) => {
      if (!d.permission || !d.permission) {
        console.warn(`Invalid Permission skipped: ${JSON.stringify(d)}`);
        return false;
      }
      return !existingKeys.has(d.permission.toLowerCase());
    })
    .map((d) => ({ ...d, metadata: {} }));

  if (permissionsToCreate.length > 0) {
    await Promise.all(
      permissionsToCreate.map((val) =>
        guestUserService.createCqPermissions(val)
      )
    );
  }

  console.log(`Seeded ${permissionsToCreate.length} permission(s).`);
}
