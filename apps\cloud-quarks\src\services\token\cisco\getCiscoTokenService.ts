import { CiscoDetails } from "../../../config/microsoftEndpoints";
import logger from "../../../utils/logger";
import sql from "mssql";
import { getSqlServerConnection } from "../../../utils/sqlServerClient";
import { PostMethodFormEncoded } from "../../externalService/externalEndPointService";

interface CiscoTokenRequestHeaders {
  granttype: string;
  Clientid: string;
  Clientsecret: string;
  Resource: string;
  Token: string;
  RedirectUri: string;
  StoreDomain: string;
  Brand: string;
  Username: string;
  uPassword: string;
}

export async function getCiscoTokenService(
  req: any,
  storeId: string,
  credentialType: string | null = null
): Promise<any> {
  try {
    logger.info(
      `Entered into getCiscoTokenService with storeId=${storeId}, credentialType=${credentialType}`
    );

    const brandName = CiscoDetails.BrandNameInDB;

    const brandId = await GetBrandIdByName(brandName);
    console.log("Resolved BrandId:", brandId);

    const storeDetails = await getStoreTokenDetails(
      storeId,
      brandId,
      credentialType
    );
    console.log("Store token info:", storeDetails);

    logger.info(`Prepare Cisco Token headers`);
    const headers = getCiscoTokenApiHeaders(storeDetails);

    logger.info(
      `Going to hit Cisco token endpoint with headers: ${JSON.stringify(
        headers
      )}`
    );

    const configModule = req.scope.resolve("configModule");

    const c_url = `https://apx.cisco.com/as/token.oauth2`;

    const response = await PostMethodFormEncoded(
      {
        url: c_url,
        data: headers,
        isVendorHit: true,
        module: "GetCiscoToken",
      },
      configModule
    );

    if (!response.isSuccessStatusCode) {
      throw new Error(
        `Cisco token generation failed with status ${response.httpStatusCode}`
      );
    }

    const parsed = JSON.parse(response.content);
    const token = parsed.access_token;
    logger.info(`Cisco token generated successfully`);
    return token;
  } catch (error) {
    logger.error(`Cisco Token Error :`);
    throw error;
  }
}

export async function GetBrandIdByName(
  storeId: string,
  credentialType: string | null = null
): Promise<any> {
  logger.info(
    `Entered into getCiscoTokenService with storeId=${storeId}, credentialType=${credentialType}`
  );

  const brandName = CiscoDetails.BrandNameInDB;

  const pool = await getSqlServerConnection();
  const request = pool.request();

  request.input("iBrandName", sql.VarChar(100), brandName);

  logger.info(
    `Calling stored procedure spGetBrandIdByName with iBrandName=${brandName}`
  );

  const result = await request.execute("spGetBrandIdByName");

  console.log("result---------->", result);

  logger.info(
    `Stored procedure executed successfully. Raw result: ${JSON.stringify(
      result
    )}`
  );

  const brandId = result?.recordset?.[0]?.BrandId || result?.recordset?.[0];

  console.log("brandId-------->", brandId);

  if (!brandId) {
    throw new Error(
      `No BrandId returned from spGetBrandIdByName for brand=${brandName}`
    );
  }

  logger.info(`BrandId retrieved: ${brandId}`);
  return brandId;
}

export async function getStoreTokenDetails(
  storeId: string,
  brandId: string,
  credentialType: string | null = null
): Promise<CiscoTokenRequestHeaders> {
  logger.info(
    `Entered into getStoreTokenDetails with storeId=${storeId}, brandId=${brandId}, credentialType=${credentialType}`
  );

  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();

    const provisionType = "AUTO-CISCO";

    request.input("iStoreId", sql.VarChar(50), storeId);
    request.input("iBrandId", sql.VarChar(50), brandId);
    request.input("iProvisionType", sql.VarChar(50), provisionType);
    request.input("iCredentialType", sql.VarChar(50), credentialType);
    request.output("oRetVal", sql.VarChar(100));

    logger.info("Calling stored procedure Usp_GetStoreDetails");

    const result = await request.execute("Usp_GetStoreDetails");

    logger.info(
      `Stored procedure executed. Result: ${JSON.stringify(result.recordset)}`
    );

    const response = result.recordset?.[0];

    if (!response) {
      throw new Error(
        `No store token details found for storeId=${storeId}, brandId=${brandId}`
      );
    }

    return response as CiscoTokenRequestHeaders;
  } catch (error: any) {
    logger.error(`Error in getStoreTokenDetails: ${error.message}`);
    throw error;
  }
}

export function getCiscoTokenApiHeaders(
  storeDetails: CiscoTokenRequestHeaders
): Record<string, string> {
  logger.info(
    `Entered into getCiscoTokenApiHeaders with storeDetails: ${JSON.stringify(
      storeDetails
    )}`
  );

  return {
    grant_type: storeDetails.granttype,
    client_id: storeDetails.Clientid,
    client_secret: storeDetails.Clientsecret,
    resource: storeDetails.Resource,
    refresh_token: storeDetails.Token,
    redirect_uri: storeDetails.RedirectUri,
    username: storeDetails.Username,
    password: storeDetails.uPassword,
  };
}
