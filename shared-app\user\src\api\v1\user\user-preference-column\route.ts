import {
  MedusaRequest,
  MedusaResponse,
  AuthenticatedMedusaRequest,
} from '@medusajs/framework/http';
import { ContainerRegistrationKeys, Modules } from '@medusajs/framework/utils';
import { logger } from '@medusajs/framework';
import { z } from 'zod';
import { stringConstants } from '@org/utils';
import { CQUser_MODULE } from '../../../../modules/cq-user';
import CQUserService from '../../../../modules/cq-user/service';
import { userPreferenceSchema } from '../user-preference-column-list/validators';

type UserPreferenceSchemaType = z.infer<typeof userPreferenceSchema>;

/**
 * @openapi
 * /v1/user/user-preference-column:
 *   post:
 *     summary: Update user-specific column preferences for a table
 *     tags:
 *       - User Preferences
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - table_master_id
 *               - column_details
 *             properties:
 *               table_master_id:
 *                 type: string
 *                 example: "tbl_abc123"
 *                 description: The ID of the table whose column preferences are being updated
 *               column_details:
 *                 type: array
 *                 description: Array of column preference objects
 *                 items:
 *                   type: object
 *                   required:
 *                     - column_master_id
 *                     - sort_order
 *                   properties:
 *                     column_master_id:
 *                       type: string
 *                       example: "col_xyz789"
 *                       description: The ID of the column
 *                     sort_order:
 *                       type: integer
 *                       example: 2
 *                       description: The order in which the column should appear
 *     responses:
 *       200:
 *         description: User column preferences updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: User column preferences updated successfully.
 *       404:
 *         description: Table or columns not found or invalid
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Some column_master_ids are not associated with the specified table_master_id.
 *                 invalid_column_ids:
 *                   type: array
 *                   items:
 *                     type: string
 *                   example: ["col_invalid_1", "col_invalid_2"]
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Internal server error while updating preferences.
 */

export async function POST(
  req: AuthenticatedMedusaRequest<UserPreferenceSchemaType>,
  res: MedusaResponse
) {
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
  const CQUserService: CQUserService = req.scope.resolve(CQUser_MODULE);

  const { actor_id: user_id } = req.auth_context;
  const { table_master_id, column_details } = req.validatedBody;

  try {
    const { data: [table] = [] } = await query.graph({
      entity: 'cq_page',
      fields: ['id', 'columns.id'],
      filters: { id: table_master_id },
    });

    if (!table) {
      return res.status(404).json({
        success: false,
        message: `Table master with id "${table_master_id}" not found.`,
      });
    }

    const validColumnIds = new Set(table.columns.map((col: any) => col.id));
  const incomingColumnMap = new Map(
  column_details.map((cd) => [cd.column_master_id, cd.sort_order ?? 0])
);

    const invalidColumnIds = Array.from(incomingColumnMap.keys()).filter(
      (id) => !validColumnIds.has(id)
    );

    if (invalidColumnIds.length > 0) {
      return res.status(404).json({
        success: false,
        message:
          'Some column_master_ids are not associated with the specified table_master_id.',
        invalid_column_ids: invalidColumnIds,
      });
    }

    const { data: existingPrefs = [] } = await query.graph({
      entity: 'cq_user_column_preference',
      fields: ['id', 'column_master_id', 'sort_order'],
      filters: { table_master_id, user_id },
    });

    const existingMap = new Map(
      existingPrefs.map((pref: any) => [pref.column_master_id, pref])
    );

    const ops = {
      create: [] as any[],
      update: [] as any[],
      delete: [] as string[],
    };

    for (const [column_master_id, sort_order] of incomingColumnMap.entries()) {
      const existing = existingMap.get(column_master_id);

      const incomingSortOrder = sort_order ?? 0;

      if (!existing) {
        ops.create.push({
          user_id,
          table_master_id,
          column_master_id,
          sort_order: incomingSortOrder,
        });
      } else if (existing.sort_order !== incomingSortOrder) {
        ops.update.push({
          id: existing.id,
          sort_order: incomingSortOrder,
        });
      }
    }

    for (const pref of existingPrefs) {
      if (!incomingColumnMap.has(pref.column_master_id)) {
        ops.delete.push(pref.id);
      }
    }

    await Promise.all([
      ...ops.create.map((data) =>
        CQUserService.createCqUserColumnPreferences(data)
      ),
      ...ops.update.map((data) =>
        CQUserService.updateCqUserColumnPreferences(data)
      ),
      ...ops.delete.map((id) =>
        CQUserService.deleteCqUserColumnPreferences(id)
      ),
    ]);

    return res.status(200).json({
      success: true,
      message: stringConstants.UPDATED_SUCCESSFULLY('User column preferences'),
    });
  } catch (err: any) {
    return res.status(500).json({
      success: false,
      message: stringConstants.INTERNAL_SERVER_ERROR(),
    });
  }
}
