import {
  MedusaResponse,
  AuthenticatedMedusaRequest,
} from '@medusajs/framework/http';
import { logger } from '@medusajs/framework';
import { formatPagination, stringConstants } from '@org/utils';
import CQUserService from '../../../../../modules/cq-user/service';
import { CQUser_MODULE } from '../../../../../modules/cq-user';
import jwt from 'jsonwebtoken';

/**
 * @openapi
 * /v1/user/access-management/roles:
 *   get:
 *     summary: Retrieve list of roles with associated pages and permissions
 *     tags:
 *       - Roles
 *     parameters:
 *       - name: page
 *         in: query
 *         required: false
 *         schema:
 *           type: integer
 *           default: 1
 *       - name: page_size
 *         in: query
 *         required: false
 *         schema:
 *           type: integer
 *           default: 10
 *     responses:
 *       200:
 *         description: Roles retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       role:
 *                         type: string
 *                       persona:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           persona:
 *                             type: string
 *                       created_at:
 *                         type: string
 *                         format: date-time
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     pageSize:
 *                       type: integer
 *                     totalItems:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *                     hasNextPage:
 *                       type: boolean
 *                     hasPrevPage:
 *                       type: boolean
 *               example:
 *                 message: Roles fetched successfully
 *                 data:
 *                   - id: "01JZ79N8M6K6ZQ7KE1DCRHAPSB"
 *                     role: "Admin"
 *                     persona:
 *                       id: "01JZ2EHJRX9W4B76RCBRH5985Z"
 *                       persona: "Partner"
 *                     created_at: "2025-07-03T04:45:30.119Z"
 *                   - id: "01JZ7C2FCS4GQ2TWC08HNB5N5X"
 *                     role: "Admin"
 *                     persona:
 *                       id: "01JZ2EHJRX9W4B76RCBRH5985Z"
 *                       persona: "Partner"
 *                     created_at: "2025-07-03T05:27:40.185Z"
 *                 pagination:
 *                   page: 1
 *                   pageSize: 10
 *                   totalItems: 2
 *                   totalPages: 1
 *                   hasNextPage: false
 *                   hasPrevPage: false
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error occurred
 */

export async function GET(
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) {
  const guestUserService: CQUserService = req.scope.resolve(CQUser_MODULE);
  try {
    let page = parseInt(req.query.page as string, 10);
    let pageSize = parseInt(req.query.page_size as string, 10);

    if (isNaN(page) || page < 1) page = 1;
    if (isNaN(pageSize) || pageSize < 1) pageSize = 10;

    const [roles, count] = await guestUserService.listAndCountCqRoles();

    const personaIds = Array.from(
      new Set(roles.map((role: any) => role.persona_id).filter(Boolean))
    );

    let personaMap: Record<string, any> = {};
    if (personaIds.length > 0) {
      const personas = await guestUserService.listCqPersonas({
        id: personaIds,
      });
      personaMap = personas.reduce((acc: any, persona: any) => {
        acc[persona.id] = persona;
        return acc;
      }, {});
    }

    const enrichedRoles = roles.map((role: any) => {
      const persona = personaMap[role.persona_id];
      return {
        ...role,
        persona: persona
          ? { id: persona.id, persona: persona.persona }
          : role.persona,
      };
    });

    const sanitizedModules = sanitize(enrichedRoles);

    const totalItems = count ?? 0;

    return res.status(200).json({
      message: stringConstants.FETCHED_SUCCESSFULLY('Roles'),
      data: sanitizedModules,
      pagination: formatPagination({ page, pageSize, totalItems }),
    });
  } catch (err: any) {
    logger.error(err);
    return res
      .status(500)
      .json({ message: stringConstants.INTERNAL_SERVER_ERROR() });
  }
}

/**
 * @openapi
 * /v1/user/access-management/roles:
 *   post:
 *     summary: Create a role with associated pages and permissions
 *     tags:
 *       - Roles
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               role:
 *                 type: string
 *                 example: Admin
 *               persona:
 *                 type: string
 *                 example: 01JZ2EHJRX9W4B76RCBRH5985Z
 *               modules:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     pages:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           page_id:
 *                             type: string
 *                             example: 01JZ79C7APGQPA3AG8ZWTZP2HR
 *                           permission_id:
 *                             type: array
 *                             items:
 *                               type: string
 *                             example: ["01JZ2EHJR7V21FXE8M5V7RPAZJ"]
 *     responses:
 *       200:
 *         description: Role created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       example: 01JZ7C2FCS4GQ2TWC08HNB5N5X
 *                     role:
 *                       type: string
 *                       example: Admin
 *                     order_id:
 *                       type: integer
 *                       nullable: true
 *                       example: null
 *                     metadata:
 *                       type: object
 *                       nullable: true
 *                       example: null
 *                     persona_id:
 *                       type: string
 *                       example: 01JZ2EHJRX9W4B76RCBRH5985Z
 *                     created_at:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-07-03T05:27:40.185Z
 *                     updated_at:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-07-03T05:27:40.185Z
 *                     deleted_at:
 *                       type: string
 *                       nullable: true
 *                       example: null
 *                     pages:
 *                       type: array
 *                       items:
 *                         type: object
 *                       example: []
 *               example:
 *                 message: Roles created successfully
 *                 data:
 *                   id: "01JZ7C2FCS4GQ2TWC08HNB5N5X"
 *                   role: "Admin"
 *                   order_id: null
 *                   metadata: null
 *                   persona_id: "01JZ2EHJRX9W4B76RCBRH5985Z"
 *                   created_at: "2025-07-03T05:27:40.185Z"
 *                   updated_at: "2025-07-03T05:27:40.185Z"
 *                   deleted_at: null
 *                   pages: []
 *       400:
 *         description: Bad request (e.g., permission not found or failed to create)
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Some permissions do not exist. Role and permissions not created.
 *                 failedPermissions:
 *                   type: array
 *                   items:
 *                     type: string
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error occurred
 */

export async function POST(
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) {
  const authHeader = req.headers.authorization;
  if (!authHeader) {
    return res.status(401).json({ message: 'No authorization header' });
  }

  const token = authHeader.split(' ')[1];
  const decoded: any = jwt.decode(token);
  const cq_user_id = decoded?.app_metadata?.cq_user_id;

  const guestUserService: CQUserService = req.scope.resolve(CQUser_MODULE);

  try {
    const { role, persona, modules } = req.body as any;
    const allPages = modules.flatMap((module: any) => module.pages || []);

    const permissionPairs = allPages.flatMap((page: any) =>
      (page.permission_id || []).map((permission: string) => ({
        page_id: page.page_id,
        permission_id: permission,
      }))
    );

    const failedPermissions: string[] = [];
    const permissionChecks = await Promise.all(
      permissionPairs.map((pair: { page_id: string; permission_id: string }) =>
        guestUserService.listCqPagePermissions(pair)
      )
    );
    permissionChecks.forEach((result, idx) => {
      if (!result || result.length === 0) {
        failedPermissions.push(permissionPairs[idx].permission_id);
      }
    });

    if (failedPermissions.length > 0) {
      return res.status(400).json({
        message:
          'Some permissions do not exist. Role and permissions not created.',
        failedPermissions,
      });
    }

    const rolePagePermissionResults = await Promise.all(
      permissionPairs.map(
        async (pair: { page_id: string; permission_id: string }) => {
          try {
            const rolePagePermission =
              await guestUserService.createCqRolePagePermissions({
                role_page: pair.page_id,
                permission: pair.permission_id,
              });
            await guestUserService.createCqUserRolePagePermissions({
              user_id: cq_user_id,
              role_page_permission_id: rolePagePermission.id,
              page_id: rolePagePermission.role_page_id,
              permission_id: rolePagePermission.permission_id,
            });
            return null;
          } catch (err) {
            logger.error(
              `Failed to create permission ${pair.permission_id}:`,
              err as any
            );
            return pair.permission_id;
          }
        }
      )
    );
    const failedCreatePermissions = rolePagePermissionResults.filter(Boolean);

    if (failedCreatePermissions.length > 0) {
      return res.status(400).json({
        message:
          'Failed to create some role page permissions. Role not created.',
        failedPermissions: failedCreatePermissions,
      });
    }

    const roles = await guestUserService.createCqRoles({
      role,
      persona_id: persona,
    });

    await guestUserService.createCqUserRoles({
      user_id: cq_user_id,
      role_id: roles.id,
      assigned_by_id: cq_user_id,
      company_id: '123',
    });

    const rolePages = await guestUserService.createCqRolePages({
      role_id: roles.id,
      page_id: allPages.map((page: any) => page.page_id),
    });

    await guestUserService.createCqUserRolePages({
      user_id: cq_user_id,
      rolepage_id: rolePages.id,
    });

    const sanitizedModules = sanitize(roles);

    return res.status(200).json({
      message: stringConstants.CREATED_SUCCESSFULLY('Roles'),
      data: sanitizedModules,
    });
  } catch (err: any) {
    logger.error(err);
    return res
      .status(500)
      .json({ message: stringConstants.INTERNAL_SERVER_ERROR() });
  }
}

/**
 * @openapi
 * /v1/user/access-management/roles:
 *   put:
 *     summary: Update a role and its associated pages and permissions
 *     tags:
 *       - Roles
 *     parameters:
 *       - name: role_id
 *         in: query
 *         required: true
 *         schema:
 *           type: string
 *           example: 01JZ7C2FCS4GQ2TWC08HNB5N5X
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               role:
 *                 type: string
 *                 example: Admin
 *               persona:
 *                 type: string
 *                 example: 01JZ2EHJRZAQ6Y9P6W6DTB74QS
 *               modules:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     pages:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           page_id:
 *                             type: string
 *                             example: 01JZ79C7APGQPA3AG8ZWTZP2HR
 *                           permission_id:
 *                             type: array
 *                             items:
 *                               type: string
 *                             example: ["01JZ2EHJR7V21FXE8M5V7RPAZJ"]
 *     responses:
 *       200:
 *         description: Role updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Roles updated successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       example: 01JZ7C2FCS4GQ2TWC08HNB5N5X
 *                     role:
 *                       type: string
 *                       example: Admin
 *                     persona_id:
 *                       type: string
 *                       example: 01JZ2EHJRZAQ6Y9P6W6DTB74QS
 *                     persona:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: string
 *                           example: 01JZ2EHJRZAQ6Y9P6W6DTB74QS
 *               example:
 *                 message: Roles updated successfully
 *                 data:
 *                   id: "01JZ7C2FCS4GQ2TWC08HNB5N5X"
 *                   role: "Admin"
 *                   persona_id: "01JZ2EHJRZAQ6Y9P6W6DTB74QS"
 *                   persona:
 *                     id: "01JZ2EHJRZAQ6Y9P6W6DTB74QS"
 *       400:
 *         description: Bad request (e.g., missing role_id or permission failure)
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: role_id is required in query parameters
 *                 failedPermissions:
 *                   type: array
 *                   items:
 *                     type: string
 *       404:
 *         description: Role not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Role not found
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error occurred
 */

export async function PUT(
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) {
  const authHeader = req.headers.authorization;
  if (!authHeader) {
    return res.status(401).json({ message: 'No authorization header' });
  }

  const token = authHeader.split(' ')[1];
  const decoded: any = jwt.decode(token);
  const cq_user_id = decoded?.app_metadata?.cq_user_id;

  const guestUserService: CQUserService = req.scope.resolve(CQUser_MODULE);

  try {
    const { role, persona, modules } = req.body as any;
    const roleId = req.query.role_id;

    if (!roleId) {
      return res
        .status(400)
        .json({ message: 'role_id is required in query parameters' });
    }

    let roles;
    roles = await guestUserService.listCqRoles({ id: roleId });
    if (!roles) {
      return res.status(404).json({ message: 'Role not found' });
    }

    await guestUserService.updateCqRoles({
      id: roles[0].id,
      role: role,
      persona_id: persona,
    });

    if (Array.isArray(roles)) {
      roles = roles[0];
    }

    const allPages = modules.flatMap((module: any) => module.pages || []);
    const permissionPairs = allPages.flatMap((page: any) =>
      (page.permission_id || []).map((permission: string) => ({
        page_id: page.page_id,
        permission_id: permission,
      }))
    );

    const failedPermissions: string[] = [];
    const permissionChecks = await Promise.all(
      permissionPairs.map((pair: { page_id: string; permission_id: string }) =>
        guestUserService.listCqPagePermissions(pair)
      )
    );
    permissionChecks.forEach((result, idx) => {
      if (!result || result.length === 0) {
        failedPermissions.push(permissionPairs[idx].permission_id);
      }
    });

    if (failedPermissions.length > 0) {
      return res.status(400).json({
        message:
          'Some permissions do not exist. Role and permissions not created.',
        failedPermissions,
      });
    }

    const rolePagePermissionResults = await Promise.all(
      permissionPairs.map(
        async (pair: { page_id: string; permission_id: string }) => {
          try {
            await guestUserService.deleteCqRolePagePermissions({
              role_page: pair.page_id,
            });

            const rolePagePermission =
              await guestUserService.createCqRolePagePermissions({
                role_page: pair.page_id,
                permission: pair.permission_id,
              });

            await guestUserService.deleteCqUserRolePagePermissions({
              page_id: pair.page_id,
            });

            await guestUserService.createCqUserRolePagePermissions({
              user_id: cq_user_id,
              role_page_permission_id: rolePagePermission.id,
              page_id: rolePagePermission.role_page_id,
              permission_id: rolePagePermission.permission_id,
            });
            return null;
          } catch (err) {
            logger.error(
              `Failed to create permission ${pair.permission_id}:`,
              err as any
            );
            return pair.permission_id;
          }
        }
      )
    );
    const failedCreatePermissions = rolePagePermissionResults.filter(Boolean);

    if (failedCreatePermissions.length > 0) {
      return res.status(400).json({
        message:
          'Failed to create some role page permissions. Role not created.',
        failedPermissions: failedCreatePermissions,
      });
    }

    const sanitizedModules = sanitize(roles);

    return res.status(200).json({
      message: stringConstants.UPDATED_SUCCESSFULLY('Roles'),
      data: sanitizedModules,
    });
  } catch (err: any) {
    logger.error(err);
    return res
      .status(500)
      .json({ message: stringConstants.INTERNAL_SERVER_ERROR() });
  }
}

function sanitize(obj: any): any {
  if (Array.isArray(obj)) {
    return obj.map(sanitize);
  } else if (obj && typeof obj === 'object') {
    if (obj instanceof Date) {
      return obj;
    }

    const {
      metadata,
      persona_id,
      order_id,
      created_by_id,
      updated_by_id,
      deleted_by_id,
      updated_at,
      deleted_at,
      created_by,
      updated_by,
      deleted_by,
      ...rest
    } = obj;

    for (const key in rest) {
      if (
        rest[key] &&
        typeof rest[key] === 'object' &&
        !(rest[key] instanceof Date)
      ) {
        rest[key] = sanitize(rest[key]);
      }
    }
    return rest;
  }
  return obj;
}
