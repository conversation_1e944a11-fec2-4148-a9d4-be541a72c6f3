import { z } from "zod";

// Address object validation
export const DefaultAddressSchema = z.object({
  Country: z.string().min(1, "Country cannot be null or empty"),
  City: z.string().min(1, "City cannot be null or empty"),
  AddressLine1: z.string().min(1, "AddressLine1 cannot be null or empty"),
  PostalCode: z.string().optional(),
  PhoneNumber: z.string().optional(),
  AddressLine2: z.string().optional(),
  FirstName: z.string().min(1, "FirstName cannot be null or empty"),
  LastName: z.string().min(1, "LastName cannot be null or empty"),
  State: z.string().optional(),
});

// Links object (optional, just for completeness)
export const USelfSchema = z.object({
  Uri: z.string().optional(),
  Method: z.string().optional(),
});

export const ULinksSchema = z.object({
  Self: USelfSchema.optional(),
  Offer: USelfSchema.optional(),
  Next: USelfSchema.optional(),
  Availabilities: USelfSchema.optional(),
  Skus: USelfSchema.optional(),
});

// Attributes object
export const UserAttributesSchema = z.object({
  ObjectType: z.string().optional(),
  Etag: z.string().min(1, "Etag cannot be null or empty"),
});

// Main DTO validation schema
export const SetCustomerBillingInfoSchema = z.object({
  Id: z.string().min(1, "Id cannot be null or empty"),
  Email: z.string().min(1, "Email cannot be null or empty"),
  Culture: z.string().min(1, "Culture cannot be null or empty"),
  Language: z.string().min(1, "Language cannot be null or empty"),
  CompanyName: z.string().min(1, "CompanyName cannot be null or empty"),
  DefaultAddress: DefaultAddressSchema,
  Links: ULinksSchema.optional(),
  Attributes: UserAttributesSchema,
  FirstName: z.string().optional(),
  LastName: z.string().optional(),
  Code: z.number().optional(),
  Description: z.string().optional(),
});
