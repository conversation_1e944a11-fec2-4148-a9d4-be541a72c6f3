import axios, { AxiosError } from "axios";
import { v4 as uuidv4 } from "uuid";
import { logToFile } from "../../utils/logToFile";
import logger from "../../utils/logger";
import https from "https";
import qs from "qs";
import { URL } from "url";

export type EndPointResponseModel = {
  httpStatusCode: number;
  isSuccessStatusCode: boolean;
  content: string;
  errorMessage?: string;
};

type GetMethodOptions = {
  url: string;
  headers?: Record<string, string>;
  isVendorHit?: boolean;
  module?: string;
};

interface PutMethodOptions {
  url: string;
  body: any;
  headers?: Record<string, string>;
  isVendorHit?: boolean;
  module?: string;
}

interface PostFormEncodedOptions {
  url: string;
  data: Record<string, string>;
  isVendorHit?: boolean;
  module?: string;
}

interface PostMethodOptions {
  url: string;
  body: any;
  headers?: Record<string, string>;
  isVendorHit?: boolean;
  module?: string;
}

interface PatchMethodOptions {
  url: string;
  body: any;
  headers?: Record<string, string>;
  isVendorHit?: boolean;
  module?: string;
}

export async function getMethod(
  options: GetMethodOptions,
  configModule: any
): Promise<EndPointResponseModel> {
  console.log("options--------->", options);

  const { url, headers = {}, isVendorHit = false, module = "" } = options;
  const identifier = uuidv4();

  const maxRetries = parseInt(configModule.projectConfig.maxRetries || "3");
  const delaySeconds = parseInt(
    configModule.projectConfig.incrementalRetryDelayInSeconds || "1"
  );

  logger.info(`GET request to: ${url}`);
  logger.info(`Headers: ${JSON.stringify(headers)}`);
  logger.info(`MaxRetries: ${maxRetries}, RetryDelay: ${delaySeconds}s`);

  if (isVendorHit) {
    logToFile(`${identifier}_REQUEST`, {
      id: identifier,
      type: "REQUEST",
      url,
      headers,
      module,
      timestamp: new Date().toISOString(),
    });
  }

  const delay = (ms: number) => new Promise((res) => setTimeout(res, ms));

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      logger.info(`[Attempt ${attempt}] Sending GET request to: ${url}`);

      const start = Date.now();

      const httpsAgent = new https.Agent({
        rejectUnauthorized: false,
      });

      console.log("httpsAgent----->", httpsAgent);

      // const response = await axios.get(url, {
      //   headers: {
      //     Accept: "*/*",
      //     ...headers,
      //   },
      //   httpsAgent,
      //   timeout: 20000, // ⏱️ 10-second timeout to prevent hanging
      // });
      const response = await axios.get(url, {
        httpsAgent,
        timeout: 20000,
        headers: {
          Accept: "*/*",
          ...headers,
        },
      });

      const duration = Date.now() - start;

      logger.info(
        `[Attempt ${attempt}] Response received in ${duration}ms with status ${response.status}`
      );
      logger.info(
        `[Attempt ${attempt}] Response data: ${JSON.stringify(response.data)}`
      );

      if (isVendorHit) {
        logToFile(`${identifier}_RESPONSE`, {
          id: identifier,
          type: "RESPONSE",
          url,
          headers,
          body: response.data,
          module,
          duration,
          timestamp: new Date().toISOString(),
        });
      }

      return {
        httpStatusCode: response.status,
        isSuccessStatusCode: response.status >= 200 && response.status < 300,
        content: JSON.stringify(response.data),
        errorMessage: response.statusText,
      };
    } catch (error: any) {
      const isTimeout = error.code === "ECONNABORTED";

      logger.error(`[Attempt ${attempt}] GET failed: ${error.message}`);
      logger.error(`[Attempt ${attempt}] Stack: ${error.stack}`);

      if (error.response) {
        logger.error(
          `[Attempt ${attempt}] Response data: ${JSON.stringify(
            error.response.data
          )}`
        );
        logger.error(
          `[Attempt ${attempt}] Response status: ${error.response.status}`
        );
      } else if (error.request) {
        logger.error(`[Attempt ${attempt}] No response received.`);
      } else {
        logger.error(`[Attempt ${attempt}] Unexpected error: ${error.message}`);
      }

      if (isTimeout) {
        logger.error(`[Attempt ${attempt}] Request timed out.`);
      }

      if (attempt === maxRetries) {
        logger.error(`Max retries reached. Returning failure response.`);

        if (isVendorHit) {
          logToFile(`${identifier}_RESPONSE_ERROR`, {
            id: identifier,
            type: "ERROR",
            url,
            headers,
            errorMessage: error.message,
            stack: error.stack,
            module,
            attempt,
            timestamp: new Date().toISOString(),
          });
        }

        return {
          httpStatusCode: error?.response?.status || (isTimeout ? 408 : 500),
          isSuccessStatusCode: false,
          content: "",
          errorMessage: error.message,
        };
      }

      logger.warn(
        `Retrying in ${attempt * delaySeconds}s before next attempt...`
      );
      await delay(attempt * delaySeconds * 1000);
    }
  }

  // This should never be hit, but we return something just in case.
  return {
    httpStatusCode: 500,
    isSuccessStatusCode: false,
    content: "",
    errorMessage: "Unknown error after retries",
  };
}

export async function checkMicrosoftDomainExists(
  domainUrl: string,
  token: string
): Promise<{ Status: string; Message: string }> {
  return new Promise((resolve) => {
    const parsedUrl = new URL(domainUrl);

    logger.info(`Parsed Url :: ${parsedUrl}`);

    const options: https.RequestOptions = {
      method: "HEAD",
      hostname: parsedUrl.hostname,
      path: parsedUrl.pathname,
      headers: {
        Authorization: `Bearer ${token}`,
      },
      agent: new https.Agent({
        rejectUnauthorized: false,
      }),
    };

    logger.info(`options :: ${options}`);
    const req = https.request(options, (res) => {
      const statusCode = res.statusCode ?? 0;

      // Log like .NET does
      console.info(`HEAD request status code: ${statusCode}`);
      console.info(
        `Success code is ${
          res.statusCode && res.statusCode >= 200 && res.statusCode < 300
        }. So, Response is ${res.statusMessage}`
      );

      if (res.statusCode && res.statusCode >= 200 && res.statusCode < 300) {
        resolve({
          Status: "1",
          Message: "Domain exist",
        });
      } else if (res.statusCode === 404) {
        resolve({
          Status: "0",
          Message: "Domain doesn't exist",
        });
      } else {
        resolve({
          Status: "0",
          Message: res.statusMessage || "Unknown error",
        });
      }
    });

    logger.info(`req :: ${req}`);

    req.on("error", (err) => {
      console.error("HEAD request error: ", err.message);
      resolve({
        Status: "0",
        Message: err.message,
      });
    });

    req.end();
  });
}

export async function PutMethodWithRequestJsonBodyAndHeaders400Handle(
  options: PutMethodOptions,
  configModule: any
): Promise<EndPointResponseModel> {
  const { url, body, headers = {}, isVendorHit = false, module = "" } = options;

  const identifier = uuidv4();

  const maxRetries = parseInt(configModule.projectConfig.maxRetries || "3");
  const delaySeconds = parseInt(
    configModule.projectConfig.incrementalRetryDelayInSeconds || "1"
  );

  logger.info(`PUT request to: ${url}`);
  logger.info(`Headers: ${JSON.stringify(headers)}`);
  logger.info(`MaxRetries: ${maxRetries}, RetryDelay: ${delaySeconds}s`);

  if (isVendorHit) {
    logToFile(`${identifier}_REQUEST`, {
      id: identifier,
      type: "REQUEST",
      url,
      headers,
      body,
      module,
      timestamp: new Date().toISOString(),
    });
  }

  const delay = (ms: number) => new Promise((res) => setTimeout(res, ms));

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      logger.info(`[Attempt ${attempt}] Sending PUT request to: ${url}`);

      const start = Date.now();

      const httpsAgent = new https.Agent({
        rejectUnauthorized: false,
      });

      const response = await axios.put(url, body, {
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          ...headers,
        },
        httpsAgent,
        timeout: 20000,
        validateStatus: () => true, // allow all status codes
      });

      const duration = Date.now() - start;

      const isSuccess = response.status >= 200 && response.status < 300;

      logger.info(
        `[Attempt ${attempt}] Response received in ${duration}ms with status ${response.status}`
      );
      logger.info(
        `[Attempt ${attempt}] Response data: ${JSON.stringify(response.data)}`
      );

      if (isVendorHit) {
        logToFile(`${identifier}_RESPONSE`, {
          id: identifier,
          type: "RESPONSE",
          url,
          headers,
          body: response.data,
          module,
          duration,
          timestamp: new Date().toISOString(),
        });
      }

      return {
        httpStatusCode: response.status,
        isSuccessStatusCode: isSuccess,
        content: JSON.stringify(response.data),
        errorMessage: isSuccess ? undefined : response.statusText,
      };
    } catch (error) {
      const err = error as AxiosError;
      const isTimeout = err.code === "ECONNABORTED";

      logger.error(`[Attempt ${attempt}] PUT failed: ${err.message}`);
      logger.error(`[Attempt ${attempt}] Stack: ${err.stack}`);

      if (err.response) {
        logger.error(
          `[Attempt ${attempt}] Response data: ${JSON.stringify(
            err.response.data
          )}`
        );
        logger.error(
          `[Attempt ${attempt}] Response status: ${err.response.status}`
        );
      }

      if (attempt === maxRetries) {
        logger.error(`Max retries reached. Returning failure response.`);

        if (isVendorHit) {
          logToFile(`${identifier}_RESPONSE_ERROR`, {
            id: identifier,
            type: "ERROR",
            url,
            headers,
            errorMessage: err.message,
            stack: err.stack,
            module,
            attempt,
            timestamp: new Date().toISOString(),
          });
        }

        return {
          httpStatusCode: err.response?.status || (isTimeout ? 408 : 500),
          isSuccessStatusCode: false,
          content: "",
          errorMessage: err.message,
        };
      }

      logger.warn(
        `Retrying in ${attempt * delaySeconds}s before next attempt...`
      );
      await delay(attempt * delaySeconds * 1000);
    }
  }

  return {
    httpStatusCode: 500,
    isSuccessStatusCode: false,
    content: "",
    errorMessage: "Unknown error after retries",
  };
}

export async function PatchMethodWithRequestJsonBodyAndHeaders400Handle(
  options: PatchMethodOptions,
  configModule: any
): Promise<EndPointResponseModel> {
  const { url, body, headers = {}, isVendorHit = false, module = "" } = options;

  const identifier = uuidv4();

  const maxRetries = parseInt(configModule.projectConfig.maxRetries || "3");
  const delaySeconds = parseInt(
    configModule.projectConfig.incrementalRetryDelayInSeconds || "1"
  );

  logger.info(`PATCH request to: ${url}`);
  logger.info(`Headers: ${JSON.stringify(headers)}`);
  logger.info(`MaxRetries: ${maxRetries}, RetryDelay: ${delaySeconds}s`);

  if (isVendorHit) {
    logToFile(`${identifier}_REQUEST`, {
      id: identifier,
      type: "REQUEST",
      url,
      headers,
      body,
      module,
      timestamp: new Date().toISOString(),
    });
  }

  const delay = (ms: number) => new Promise((res) => setTimeout(res, ms));

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      logger.info(`[Attempt ${attempt}] Sending PATCH request to: ${url}`);

      const start = Date.now();

      const httpsAgent = new https.Agent({
        rejectUnauthorized: false,
      });

      const response = await axios.patch(url, body, {
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          ...headers,
        },
        httpsAgent,
        timeout: 20000,
        validateStatus: () => true, // allow all status codes
      });

      const duration = Date.now() - start;

      const isSuccess = response.status >= 200 && response.status < 300;

      logger.info(
        `[Attempt ${attempt}] Response received in ${duration}ms with status ${response.status}`
      );
      logger.info(
        `[Attempt ${attempt}] Response data: ${JSON.stringify(response.data)}`
      );

      if (isVendorHit) {
        logToFile(`${identifier}_RESPONSE`, {
          id: identifier,
          type: "RESPONSE",
          url,
          headers,
          body: response.data,
          module,
          duration,
          timestamp: new Date().toISOString(),
        });
      }

      return {
        httpStatusCode: response.status,
        isSuccessStatusCode: isSuccess,
        content: JSON.stringify(response.data),
        errorMessage: isSuccess ? undefined : response.statusText,
      };
    } catch (error) {
      const err = error as AxiosError;
      const isTimeout = err.code === "ECONNABORTED";

      logger.error(`[Attempt ${attempt}] PATCH failed: ${err.message}`);
      logger.error(`[Attempt ${attempt}] Stack: ${err.stack}`);

      if (err.response) {
        logger.error(
          `[Attempt ${attempt}] Response data: ${JSON.stringify(
            err.response.data
          )}`
        );
        logger.error(
          `[Attempt ${attempt}] Response status: ${err.response.status}`
        );
      }

      if (attempt === maxRetries) {
        logger.error(`Max retries reached. Returning failure response.`);

        if (isVendorHit) {
          logToFile(`${identifier}_RESPONSE_ERROR`, {
            id: identifier,
            type: "ERROR",
            url,
            headers,
            errorMessage: err.message,
            stack: err.stack,
            module,
            attempt,
            timestamp: new Date().toISOString(),
          });
        }

        return {
          httpStatusCode: err.response?.status || (isTimeout ? 408 : 500),
          isSuccessStatusCode: false,
          content: "",
          errorMessage: err.message,
        };
      }

      logger.warn(
        `Retrying in ${attempt * delaySeconds}s before next attempt...`
      );
      await delay(attempt * delaySeconds * 1000);
    }
  }

  return {
    httpStatusCode: 500,
    isSuccessStatusCode: false,
    content: "",
    errorMessage: "Unknown error after retries",
  };
}

export async function PostMethodFormEncoded(
  options: PostFormEncodedOptions,
  configModule: any
): Promise<EndPointResponseModel> {
  const { url, data, isVendorHit = false, module = "" } = options;
  const identifier = uuidv4();

  const maxRetries = parseInt(configModule.projectConfig.maxRetries || "3");
  const delaySeconds = parseInt(
    configModule.projectConfig.incrementalRetryDelayInSeconds || "1"
  );

  logger.info(`POST (x-www-form-urlencoded) request to: ${url}`);
  logger.info(`Form data: ${JSON.stringify(data)}`);
  logger.info(`MaxRetries: ${maxRetries}, RetryDelay: ${delaySeconds}s`);

  if (isVendorHit) {
    logToFile(`${identifier}_REQUEST`, {
      id: identifier,
      type: "REQUEST",
      url,
      data,
      module,
      timestamp: new Date().toISOString(),
    });
  }

  const delay = (ms: number) => new Promise((res) => setTimeout(res, ms));

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      logger.info(
        `[Attempt ${attempt}] Sending POST form-encoded request to: ${url}`
      );

      const start = Date.now();

      const httpsAgent = new https.Agent({ rejectUnauthorized: false });

      const response = await axios.post(url, qs.stringify(data), {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          Accept: "*/*",
        },
        httpsAgent,
        timeout: 20000,
        validateStatus: () => true,
      });

      const duration = Date.now() - start;

      logger.info(
        `[Attempt ${attempt}] Response received in ${duration}ms with status ${response.status}`
      );
      logger.info(
        `[Attempt ${attempt}] Response data: ${JSON.stringify(response.data)}`
      );

      if (isVendorHit) {
        logToFile(`${identifier}_RESPONSE`, {
          id: identifier,
          type: "RESPONSE",
          url,
          data,
          body: response.data,
          module,
          duration,
          timestamp: new Date().toISOString(),
        });
      }

      return {
        httpStatusCode: response.status,
        isSuccessStatusCode: response.status >= 200 && response.status < 300,
        content:
          typeof response.data === "string"
            ? response.data
            : JSON.stringify(response.data),
        errorMessage: response.statusText,
      };
    } catch (error: any) {
      logger.error(`[Attempt ${attempt}] POST failed: ${error.message}`);
      logger.error(`[Attempt ${attempt}] Stack: ${error.stack}`);

      if (attempt === maxRetries) {
        logger.error(`Max retries reached. Returning failure response.`);

        if (isVendorHit) {
          logToFile(`${identifier}_RESPONSE_ERROR`, {
            id: identifier,
            type: "ERROR",
            url,
            data,
            errorMessage: error.message,
            stack: error.stack,
            module,
            attempt,
            timestamp: new Date().toISOString(),
          });
        }

        return {
          httpStatusCode: error.response?.status || 500,
          isSuccessStatusCode: false,
          content: "",
          errorMessage: error.message,
        };
      }

      logger.warn(
        `Retrying in ${attempt * delaySeconds}s before next attempt...`
      );
      await delay(attempt * delaySeconds * 1000);
    }
  }

  return {
    httpStatusCode: 500,
    isSuccessStatusCode: false,
    content: "",
    errorMessage: "Unknown error after retries",
  };
}

export async function PostMethodWithRequestJsonBodyAndHeaders400Handle(
  options: PostMethodOptions,
  configModule: any
): Promise<EndPointResponseModel> {
  const { url, body, headers = {}, isVendorHit = false, module = "" } = options;

  const identifier = uuidv4();

  const maxRetries = parseInt(configModule.projectConfig.maxRetries || "3");
  const delaySeconds = parseInt(
    configModule.projectConfig.incrementalRetryDelayInSeconds || "1"
  );

  logger.info(`POST request to: ${url}`);
  logger.info(`Headers: ${JSON.stringify(headers)}`);
  logger.info(`MaxRetries: ${maxRetries}, RetryDelay: ${delaySeconds}s`);

  if (isVendorHit) {
    logToFile(`${identifier}_REQUEST`, {
      id: identifier,
      type: "REQUEST",
      url,
      headers,
      body,
      module,
      timestamp: new Date().toISOString(),
    });
  }

  const delay = (ms: number) => new Promise((res) => setTimeout(res, ms));

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      logger.info(`[Attempt ${attempt}] Sending POST request to: ${url}`);

      const start = Date.now();

      const httpsAgent = new https.Agent({
        rejectUnauthorized: false,
      });

      const response = await axios.post(url, body, {
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          ...headers,
        },
        httpsAgent,
        timeout: 20000,
        validateStatus: () => true, // allow all status codes
      });

      const duration = Date.now() - start;

      const isSuccess = response.status >= 200 && response.status < 300;

      logger.info(
        `[Attempt ${attempt}] Response received in ${duration}ms with status ${response.status}`
      );
      logger.info(
        `[Attempt ${attempt}] Response data: ${JSON.stringify(response.data)}`
      );

      if (isVendorHit) {
        logToFile(`${identifier}_RESPONSE`, {
          id: identifier,
          type: "RESPONSE",
          url,
          headers,
          body: response.data,
          module,
          duration,
          timestamp: new Date().toISOString(),
        });
      }

      return {
        httpStatusCode: response.status,
        isSuccessStatusCode: isSuccess,
        content: JSON.stringify(response.data),
        errorMessage: isSuccess ? undefined : response.statusText,
      };
    } catch (error) {
      const err = error as AxiosError;
      const isTimeout = err.code === "ECONNABORTED";

      logger.error(`[Attempt ${attempt}] POST failed: ${err.message}`);
      logger.error(`[Attempt ${attempt}] Stack: ${err.stack}`);

      if (err.response) {
        logger.error(
          `[Attempt ${attempt}] Response data: ${JSON.stringify(
            err.response.data
          )}`
        );
        logger.error(
          `[Attempt ${attempt}] Response status: ${err.response.status}`
        );
      }

      if (attempt === maxRetries) {
        logger.error(`Max retries reached. Returning failure response.`);

        if (isVendorHit) {
          logToFile(`${identifier}_RESPONSE_ERROR`, {
            id: identifier,
            type: "ERROR",
            url,
            headers,
            errorMessage: err.message,
            stack: err.stack,
            module,
            attempt,
            timestamp: new Date().toISOString(),
          });
        }

        return {
          httpStatusCode: err.response?.status || (isTimeout ? 408 : 500),
          isSuccessStatusCode: false,
          content: "",
          errorMessage: err.message,
        };
      }

      logger.warn(
        `Retrying in ${attempt * delaySeconds}s before next attempt...`
      );
      await delay(attempt * delaySeconds * 1000);
    }
  }

  return {
    httpStatusCode: 500,
    isSuccessStatusCode: false,
    content: "",
    errorMessage: "Unknown error after retries",
  };
}
