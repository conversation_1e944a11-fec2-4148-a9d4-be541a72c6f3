import * as dotenv from 'dotenv';
dotenv.config();

export const databaseConfig = {
  master: {
    url: process.env.DATABASE_URL_MASTER || '',
    type: 'master',
  },
  india: {
    url: process.env.DATABASE_URL_INDIA || '',
    type: 'india',
  },
  mea: {
    url: process.env.DATABASE_URL_MEA || '',
    type: 'mea',
  },
  turkey: {
    url: process.env.DATABASE_URL_TURKEY || '',
    type: 'turkey',
  },
};
