import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { normalizeQueryParams } from "../../../../utils/mappers/getCustomerUsers";
import { validateCustomerAgreementQuery } from "../../../../validators/customer/customCustomerValidator";
import { getCustomerAgreementService } from "../../../../services/index";

/**
 * @openapi
 * /v1/customer/getCustomerAgreement:
 *   get:
 *     summary: Get Customer Agreement
 *     description: Retrieves agreement details for a given customer based on brandId, storeId, and custId.
 *     tags:
 *       - Customer
 *     parameters:
 *       - in: query
 *         name: brandId
 *         schema:
 *           type: string
 *         required: true
 *         description: The brand identifier.
 *       - in: query
 *         name: storeId
 *         schema:
 *           type: string
 *         required: true
 *         description: The store identifier.
 *       - in: query
 *         name: custId
 *         schema:
 *           type: string
 *         required: true
 *         description: The customer ID (tenant).
 *     responses:
 *       200:
 *         description: Agreement details retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 totalCount:
 *                   type: number
 *                 items:
 *                   type: array
 *                   items:
 *                     type: object
 *                     description: Agreement item
 *       400:
 *         description: Validation failed.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: string
 *       500:
 *         description: Internal server error or external API failure.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: string
 *                 statusCode:
 *                   type: number
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    logger.info(`getCustomerAgreement API is Hit`);
    const rawQuery = req.query;
    const { brandId, storeId, custId } = normalizeQueryParams(rawQuery, [
      "brandId",
      "storeId",
      "custId",
    ]);

    logger.info(
      `Normalized query params -> brandId: ${brandId}, storeId: ${storeId}, tenantId: ${custId}`
    );

    const validationErrors = validateCustomerAgreementQuery(
      brandId,
      storeId,
      custId
    );
    logger.info(`Validation error count = ${validationErrors.length}`);

    if (validationErrors.length > 0) {
      logger.info(
        `Returning 400 with errors: ${JSON.stringify(validationErrors)}`
      );
      return res.status(400).json({
        status: false,
        errors: validationErrors,
        message: "Validation failed",
      });
    }
    logger.info(
      `Calling getCustomerAgreementService with brandId=${brandId}, storeId=${storeId}, tenantId=${custId}`
    );
    const result = await getCustomerAgreementService(
      req,
      brandId,
      storeId,
      custId
    );

    logger.info(`getCustomerAgreementService result: ${JSON.stringify(result)}`);
    if (!result.getCustomerAgreement) {
      return res.status(500).json(result.error);
    }
    return res.status(200).json(result.getCustomerAgreement);
  } catch (err) {
    logger.error("getCustomerAgreement error: ", err as Error);
    return res.status(500).json({
      status: false,
      message: "Internal server error",
    });
  }
}
