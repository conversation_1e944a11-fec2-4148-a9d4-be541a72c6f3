import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import { getGooglePurchasableOfferService } from "../../../../services/googleOffer/getGooglePurchasableOfferService";

/**
 * @openapi
 * /v1/googleOffer/getPurchasableOffer:
 *   get:
 *     summary: Get Google Purchasable Offer by Offer ID
 *     description: Retrieves a specific purchasable offer for a given store and Google customer based on the provided identifiers.
 *     tags:
 *       - Google Offers
 *     parameters:
 *       - in: query
 *         name: storeId
 *         required: true
 *         schema:
 *           type: string
 *         description: Store identifier
 *         example: "store-001"
 *       - in: query
 *         name: googleCustomerId
 *         required: true
 *         schema:
 *           type: string
 *         description: Google customer ID
 *         example: "cust-abc-123"
 *       - in: query
 *         name: productId
 *         required: true
 *         schema:
 *           type: string
 *         description: Google product ID
 *         example: "google-cloud-product"
 *       - in: query
 *         name: skuId
 *         required: true
 *         schema:
 *           type: string
 *         description: Google SKU ID
 *         example: "sku-987"
 *       - in: query
 *         name: offerId
 *         required: true
 *         schema:
 *           type: string
 *         description: Google Offer ID
 *         example: "offer-xyz"
 *     responses:
 *       200:
 *         description: Successfully retrieved the purchasable offer
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Message:
 *                   type: string
 *                   example: Success
 *                 StatusCode:
 *                   type: integer
 *                   example: 200
 *                 IsError:
 *                   type: boolean
 *                   example: false
 *                 Data:
 *                   type: object
 *                   nullable: true
 *                   description: The matched offer details (if found)
 *       404:
 *         description: Offer not found
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error
 *                 isError:
 *                   type: boolean
 *                   example: true
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const { storeId, googleCustomerId, productId, skuId, offerId } = req.query as Record<string, string>;
  const correlationId = getCorrelationId(req);

  logger.info(
    `Entered into GetPurchasableOffersById API with correlationId-${correlationId}, ||  storeId-${storeId},googleCustomerId-${googleCustomerId},productId-${productId},skuId-${skuId},offerId-${offerId}`
  );

  try {
    const response = await getGooglePurchasableOffer(req, storeId, googleCustomerId, productId, skuId, offerId);
    res.status(response.StatusCode).json(response);
  } catch (err) {
    logger.error(`Error in GetPurchasableOffersById API: ${err}`);
    res.status(500).json({ message: "Internal server error", isError: true });
  }
}



export async function getGooglePurchasableOffer(
  req: MedusaRequest,
  storeId: string,
  googleCustomerId: string,
  productId: string,
  skuId: string,
  offerId: string
) {
  const apiResponse = await getGooglePurchasableOfferService(req, storeId, googleCustomerId, productId, skuId);

  if (!apiResponse.IsError && Array.isArray(apiResponse.Data)) {
    const matched = apiResponse.Data.find(
      (x: any) => x.offer?.name?.split("/").pop() === offerId
    );
    apiResponse.Data = matched || null;
  }

  logger.info(`Going to return GetGooglePurchasableOffer API response- ${JSON.stringify(apiResponse)}`);
  return apiResponse;
}