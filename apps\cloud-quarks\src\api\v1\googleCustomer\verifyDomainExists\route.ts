import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import {
  CloudIdentityAccountDtoRequest,
  GoogleEndCustomerVerifyDomainResponse
} from "../../../../types/responses/customResponse";
import { validateCloudIdentityAccountDto } from "../../../../validators/google/verifyCloudIdentityAccountsValidator";
import { verifyDomainExistsService } from "../../../../services/googleCustomer/verifyDomainExistsService";

/**
 * @openapi
 * /v1/googleCustomer/verifyDomainExists:
 *   post:
 *     summary: Verify if a domain exists and is linkable
 *     description: Validates the input and checks if a given domain is available and linkable for a store.
 *     tags:
 *       - GoogleCustomer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               storeId:
 *                 type: string
 *                 example: "AE-EN"
 *               domain:
 *                 type: string
 *                 example: "example.com"
 *               isExisting:
 *                 type: boolean
 *                 example: true
 *               channelPartnerId:
 *                 type: string
 *                 example: "channel-partner-123"
 *     responses:
 *       200:
 *         description: Domain verification successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Message:
 *                   type: string
 *                   example: "Domain is available and linkable"
 *                 IsAvailable:
 *                   type: boolean
 *                   example: true
 *                 IsLinkable:
 *                   type: boolean
 *                   example: true
 *                 StatusCode:
 *                   type: integer
 *                   example: 200
 *       400:
 *         description: Bad request - validation failed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Message:
 *                   type: string
 *                   example: "StoreId must not be empty"
 *                 IsAvailable:
 *                   type: boolean
 *                   example: false
 *                 IsLinkable:
 *                   type: boolean
 *                   example: false
 *                 StatusCode:
 *                   type: integer
 *                   example: 400
 *       422:
 *         description: Unprocessable entity - internal error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Message:
 *                   type: string
 *                   example: "An error occurred: Unexpected server error"
 *                 IsAvailable:
 *                   type: boolean
 *                   example: false
 *                 IsLinkable:
 *                   type: boolean
 *                   example: false
 *                 StatusCode:
 *                   type: integer
 *                   example: 422
 */

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req);

  logger.info(`Entered into VerifyDomainExists API with CorrelationId: ${correlationId}`);

  let googleEndCustomerVerifyDomainResponse: GoogleEndCustomerVerifyDomainResponse = {
    Message: "",
    IsAvailable: false,
    IsLinkable: false,
    StatusCode: 200
  };

  try {
    const domainDto = req.body as CloudIdentityAccountDtoRequest;
    logger.info(`Entered into VerifyDomainExists API with CreateCPIdDto - ${JSON.stringify(domainDto)}`);

    // Validate the request DTO
    const validationResult = validateCloudIdentityAccountDto(domainDto);
    logger.info(`Validations error count on VerifyDomainExists object is ${validationResult.errors.length}`);

    if (!validationResult.isValid) {
      logger.info(`validation errors are ${JSON.stringify(validationResult.errors)}`);

      googleEndCustomerVerifyDomainResponse.Message = validationResult.errors.join(', ');
      googleEndCustomerVerifyDomainResponse.IsAvailable = false;
      googleEndCustomerVerifyDomainResponse.IsLinkable = false;
      googleEndCustomerVerifyDomainResponse.StatusCode = 400; // BadRequest

      logger.info(`Going to send bad request response ${JSON.stringify(googleEndCustomerVerifyDomainResponse)}`);
      return res.status(400).json(googleEndCustomerVerifyDomainResponse);
    }

    // Call the service method (map camelCase to service parameters)
    googleEndCustomerVerifyDomainResponse = await verifyDomainExistsService(
      domainDto.domain,
      domainDto.storeId,
      domainDto.isExisting,
      req
    );

  } catch (exp: any) {
    logger.error(`Error in VerifyDomainExists API method. Message : ${exp.message}, StackTrace: ${exp.stack}`);

    googleEndCustomerVerifyDomainResponse.Message = `An error occurred: ${exp.message}`;
    googleEndCustomerVerifyDomainResponse.IsAvailable = false;
    googleEndCustomerVerifyDomainResponse.IsLinkable = false;
    googleEndCustomerVerifyDomainResponse.StatusCode = 422; // UnprocessableEntity
  }

  logger.info(`Returning response from VerifyDomainExists API - ${JSON.stringify(googleEndCustomerVerifyDomainResponse)}`);

  return res
    .status(googleEndCustomerVerifyDomainResponse.StatusCode)
    .json(googleEndCustomerVerifyDomainResponse);
}