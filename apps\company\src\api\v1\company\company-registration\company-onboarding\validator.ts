import { z } from 'zod';

export const CompanyOnboardingSchema = z.object({
	redington_account_id: z.string(),
	camunda_process_id: z.string(),
	country: z.string(),	
	company_name: z.string(),
	address1: z.string(),
	address2: z.string().optional(),
	country_of_business: z.string(),
	state: z.string(),
	city: z.string(),
	postal_code: z.string(),
	website: z.string().url().optional(),

	director_first_name: z.string(),
	director_last_name: z.string(),
	director_email: z.string().email(),
	director_country_code: z.string(),
	director_mobile_number: z.string(),

	sales_first_name: z.string(),
	sales_last_name: z.string(),
	sales_email: z.string().email(),
	sales_country_code: z.string(),
	sales_mobile_number: z.string(),

	accounts_first_name: z.string(),
	accounts_last_name: z.string(),
	accounts_email: z.string().email(),
	accounts_country_code: z.string(),
	accounts_mobile_number: z.string(),

	authorized_signatories: z
		.array(
			z.object({
				first_name: z.string(),
				last_name: z.string(),
				email: z.string().email(),
				country_code: z.string(),
				mobile_number: z.string(),
			})
		)
		.max(3, { message: 'You can add up to 3 authorized signatories only.' }),

	gst_number: z.string(),

	user_brand_preference: z
		.array(
			z.object({
				vendor_id: z.string(),
				brand_id: z.string(),
				brand_category_id: z.string(),
			})
		)
		.optional(),

	requested_by_admin: z.boolean(),
	reason: z.string().optional(),

	store_id: z.string().optional(),
	metadata: z.record(z.any()).optional(),

	created_by: z.string(),
	updated_by: z.string(),
	deleted_by: z.string().optional(),
});
