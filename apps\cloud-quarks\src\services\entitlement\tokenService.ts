import axios from "axios";
import logger from "../../utils/logger";
import { TokenRequest, TokenResponse } from "../../types/responses/customResponse";
import { OrderDetailRow } from "./entitlementDAL";
import { Urls } from "../../utils/constants";
import { MicrosoftEndpoints } from "../../config/microsoftEndpoints";

/**
 * Get token from Microsoft
 * Matches .NET BusinessCommon.GetToken method
 */
export async function getTokenFromOrderRow(
  orderRow: OrderDetailRow,
): Promise<string> {
  logger.info(`Getting token for customer: ${orderRow.CustomerId}`);

  try {
    // Build token request from order row data
    const tokenRequest: TokenRequest = {
      client_id: orderRow.Clientid,
      client_secret: orderRow.Clientsecret,
      grant_type: orderRow.GrantType,
      redirect_uri: "http://localhost",
      refresh_token: orderRow.Token,
      resource: orderRow.Resource,
      store_domain: orderRow.StoreDomain,
    };

    console.log("tokenRequest------------->", tokenRequest);

  const completeUrlWithParams = MicrosoftEndpoints.getMsTokenUrl(orderRow.StoreDomain)
  console.log("completeUrlWithParams---getMsToken---->", completeUrlWithParams);

    // Prepare form data
    const formData = new URLSearchParams();
    formData.append("grant_type", tokenRequest.grant_type);
    formData.append("client_id", tokenRequest.client_id);
    formData.append("client_secret", tokenRequest.client_secret);
    formData.append("resource", tokenRequest.resource);
    formData.append("refresh_token", tokenRequest.refresh_token);
    formData.append("redirect_uri", tokenRequest.redirect_uri);

    // Make POST request
    const response = await axios.post(completeUrlWithParams, formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      timeout: 30000, // 30 seconds timeout
    });

    console.log("response-------->",response)

    if (response.status >= 200 && response.status < 300) {
      const tokenResponse = response.data as TokenResponse;
      logger.info(`Token generated successfully`);
      return tokenResponse.access_token;
    } else {
      throw new Error("Error occurred while generating token");
    }
  } catch (error) {
    logger.error(`Error in getTokenFromOrderRow: ${error}`);
    
    if (axios.isAxiosError(error)) {
      logger.error(`Axios Error Details: Status: ${error.response?.status}, Data: ${JSON.stringify(error.response?.data)}`);
    }
    
    throw new Error("Error occurred while generating token");
  }
}

