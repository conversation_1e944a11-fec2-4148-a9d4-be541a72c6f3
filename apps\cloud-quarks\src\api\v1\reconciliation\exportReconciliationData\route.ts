import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import { toPascalCaseJson } from "../../../../utils/mappers/getCustomerUsers";
import { ExportReconciliationDataValidator } from "../../../../validators/reconciliation/exportReconciliationdataValidator";
import { exportReconciliationDataService } from "../../../../services/reconciliation/exportReconciliationDataService";

/**
 * @openapi
 * /v1/reconciliation/export:
 *   post:
 *     summary: Export reconciliation data to Microsoft
 *     tags:
 *       - Reconciliation
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - storeId
 *               - brandId
 *               - invoiceId
 *               - attributeSet
 *             properties:
 *               storeId:
 *                 type: string
 *               brandId:
 *                 type: string
 *               invoiceId:
 *                 type: string
 *               attributeSet:
 *                 type: string
 *     responses:
 *       200:
 *         description: Reconciliation export successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Message:
 *                   type: string
 *                 StatusCode:
 *                   type: integer
 *                 IsError:
 *                   type: boolean
 *                 Data:
 *                   type: object
 *       400:
 *         description: Validation error
 *       500:
 *         description: Internal server error
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req);

  logger.info(
    `ExportReconciliationData API called | correlationId: ${correlationId} | Body: ${JSON.stringify(
      req.body
    )}`
  );

  const validation = ExportReconciliationDataValidator.safeParse(req.body);

  if (!validation.success) {
    const errors = validation.error.errors.map((e) => e.message);
    logger.info(
      `Validation failed | correlationId: ${correlationId} | Errors: ${errors.join(
        ", "
      )}`
    );
    return res.status(400).json({
      isError: true,
      statusCode: 400,
      message: errors.join(", "),
    });
  }

  try {
    const dto = validation.data;

    const result = await exportReconciliationDataService(req, dto);

    logger.info(
      `Returning from ExportReconciliationData | correlationId: ${correlationId} | Result: ${JSON.stringify(
        result
      )}`
    );

    return res
      .status(result.response.statusCode || 200)
      .json(toPascalCaseJson(result.response));
  } catch (err) {
    logger.error(
      `Error in ExportReconciliationData | correlationId: ${correlationId}`,
      err
    );

    return res.status(500).json({
      Message: "Internal server error",
      IsError: true,
      StatusCode: 500,
    });
  }
}
