import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import { ErrorResponseModel, SubscriptionResponseModel } from "../../../../types/responses/customResponse";
import {
  emptyValuelessParameterInRequest,
  validateGetSubscriptionRequest
} from "../../../../validators/subscription/getSubscriptionsValidator";
import { getSubscriptionsService } from "../../../../services/subscription/getSubscriptionsService";

/**
 * @openapi
 * /v1/subscription/getSubscriptions:
 *   get:
 *     summary: Get Subscriptions
 *     description: Retrieves subscriptions based on store, partner, customer, and brand filters
 *     tags:
 *       - Subscription
 *     parameters:
 *       - in: query
 *         name: storeId
 *         required: true
 *         schema:
 *           type: string
 *         description: Store identifier
 *         example: "store123"
 *       - in: query
 *         name: partnerId
 *         required: true
 *         schema:
 *           type: string
 *         description: Partner identifier
 *         example: "partner123"
 *       - in: query
 *         name: customerId
 *         required: true
 *         schema:
 *           type: string
 *         description: Customer identifier
 *         example: "customer123"
 *       - in: query
 *         name: brandId
 *         required: true
 *         schema:
 *           type: string
 *         description: Brand identifier
 *         example: "brand123"
 *       - in: query
 *         name: subscriptionStatuses
 *         required: false
 *         schema:
 *           type: string
 *           default: "active,suspended"
 *         description: Comma-separated list of subscription statuses
 *         example: "active,suspended"
 *       - in: query
 *         name: expiryInDays
 *         required: false
 *         schema:
 *           type: string
 *           default: "ALL"
 *         description: Expiry filter in days
 *         example: "ALL"
 *     responses:
 *       200:
 *         description: Successfully retrieved subscriptions
 *         content:
 *           application/json:
 *             schema:
 *               type: string
 *               description: JSON string of subscription array (matching .NET response format)
 *               example: '[{"SubscriptionId":"sub123","MaterialNo":"MAT123","Qty":10}]'
 *       400:
 *         description: Bad request - validation errors
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Errors:
 *                   type: array
 *                   items:
 *                     type: string
 *                   example: ["The 'StoreId' URL parameter is missing in the request."]
 *                 StatusCode:
 *                   type: integer
 *                   example: 400
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Internal server error"
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req);

  logger.info(`Entered into GetSubscriptions API with CorrelationId: ${correlationId}`);

  try {
    // Extract query parameters
    const {
      storeId: rawStoreId,
      partnerId: rawPartnerId,
      customerId: rawCustomerId,
      brandId: rawBrandId,
      subscriptionStatuses = "active,suspended",
      expiryInDays = "ALL"
    } = req.query as {
      storeId?: string;
      partnerId?: string;
      customerId?: string;
      brandId?: string;
      subscriptionStatuses?: string;
      expiryInDays?: string;
    };

    logger.info(`Entered into GetSubscriptions method with storeId-${rawStoreId}, partner-${rawPartnerId}, customerId-${rawCustomerId}, brandId-${rawBrandId}, subscriptionStatuses--${subscriptionStatuses}, expiryInDays--${expiryInDays}`);

    logger.info(`Going to replace null to empty string for value less parameter, storeId-${rawStoreId}, partnerId-${rawPartnerId}, customerId-${rawCustomerId}, brandId-${rawBrandId}`);

    // Handle empty valueless parameters (matching .NET EmptyValuelessParameterInRequest)
    const { storeId, partnerId, customerId, brandId } = emptyValuelessParameterInRequest({
      storeId: rawStoreId,
      partnerId: rawPartnerId,
      customerId: rawCustomerId,
      brandId: rawBrandId
    });

    logger.info(`Going to validate the input query parameters with storeId-${storeId}, partnerId-${partnerId}, customerId-${customerId}, brandId-${brandId}`);

    // Validate request parameters (matching .NET validation)
    const validationErrors = validateGetSubscriptionRequest(storeId, partnerId, customerId, brandId);
    logger.info(`Validation errors count = ${validationErrors.length}; Errors : ${JSON.stringify(validationErrors)}`);

    if (validationErrors.length > 0) {
      logger.info(`Received validations error ${JSON.stringify(validationErrors)}`);
      logger.info(`Going to return Bad request response due to Bad query paramaters in request ${JSON.stringify(validationErrors)}`);

      const errorResponse: ErrorResponseModel = {
        errors: validationErrors,
        statusCode: 400
      };

      return res.status(400).json(errorResponse);
    }

    logger.info(`Going to hit BL GetSubscriptions method with storeId-${storeId}, partner-${partnerId}, customerId-${customerId}, brandId-${brandId}, subscriptionStatuses--${subscriptionStatuses}, expiryInDays--${expiryInDays}`);

    // Call the service method (matching .NET service call)
    const response: SubscriptionResponseModel[] = await getSubscriptionsService(
      storeId,
      partnerId,
      customerId,
      brandId,
      subscriptionStatuses,
      expiryInDays
    );

    logger.info(`Received response from BL GetSubscriptions method, response count: ${response.length}`);

    logger.info(`Adding JsonSerializerSettings`);
    // Note: In Node.js, we don't need special JsonSerializerSettings like .NET
    // The response will maintain the same property names as defined in the interface
    logger.info(`Created JsonSerializerSettings with NamingStrategy (Node.js maintains original property names)`);

    // Serialize response to JSON string (matching .NET: JsonConvert.SerializeObject(response, options))
    const jsonResponse = JSON.stringify(response);
    logger.info(`Json response after adding JsonSerializerSettings, response length: ${jsonResponse.length}`);
    logger.info(`Going to return OK response code with response`);

    // Return JSON string (matching .NET: return Ok(jsonResponse))
    return res.status(200).json(jsonResponse);

  } catch (exp: any) {
    logger.error(`Exception in GetSubscriptions API method. Message: ${exp.message}, StackTrace: ${exp.stack}`);

    return res.status(500).json({ error: "Internal server error" });
  }
}