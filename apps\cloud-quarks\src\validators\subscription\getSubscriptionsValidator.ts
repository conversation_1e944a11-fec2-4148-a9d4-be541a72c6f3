import logger from "../../utils/logger";

/**
 * Empty valueless parameters in request (matching .NET EmptyValuelessParameterInRequest)
 */
export function emptyValuelessParameterInRequest(
  queryParams: Record<string, any>
): { storeId: string; partnerId: string; customerId: string; brandId: string } {
  logger.info(`Entered into EmptyValuelessParameterInRequest method with queryParams: ${JSON.stringify(queryParams)}`);

  let { storeId, partnerId, customerId, brandId } = queryParams;

  // Find keys with empty or whitespace values (matching .NET logic)
  const emptyKeys = Object.keys(queryParams).filter(key => 
    queryParams[key] === null || 
    queryParams[key] === undefined || 
    (typeof queryParams[key] === 'string' && queryParams[key].trim() === '')
  );

  logger.info(`Keys passed without value ${JSON.stringify(emptyKeys)}`);

  // Set empty values for parameters that were passed without values (matching .NET logic)
  emptyKeys.forEach(key => {
    if (key.toLowerCase() === 'storeid') {
      storeId = '';
      logger.info(`StoreId value changed ${storeId}`);
    } else if (key.toLowerCase() === 'partnerid') {
      partnerId = '';
      logger.info(`partnerId value changed ${partnerId}`);
    } else if (key.toLowerCase() === 'customerid') {
      customerId = '';
      logger.info(`customerId value changed ${customerId}`);
    } else if (key.toLowerCase() === 'brandid') {
      brandId = '';
      logger.info(`brandId value changed ${brandId}`);
    }
  });

  logger.info(`Going to return values storeId-${storeId}, partner-${partnerId}, customerId-${customerId}, brandId-${brandId}`);

  return { storeId, partnerId, customerId, brandId };
}

/**
 * Validate GetSubscription request parameters (matching .NET ValidationGetSubscriptionRequest)
 */
export function validateGetSubscriptionRequest(
  storeId: string,
  partnerId: string,
  customerId: string,
  brandId: string
): string[] {
  logger.info(`Entered into ValidationGetSubscriptionRequest method with storeId-${storeId}, partner-${partnerId}, customerId-${customerId}, brandId-${brandId}`);
  
  const validationErrors: string[] = [];

  // Validate storeId (matching .NET validation)
  logger.info(`Validating query parameter store Id-${storeId}`);
  if (storeId === null || storeId === undefined) {
    logger.info(`Received validation error for query parameter store Id -${storeId}, StoreId can't be null or empty`);
    validationErrors.push("The 'StoreId' URL parameter is missing in the request.");
  }
  logger.info(`Validated query parameter store Id`);

  // Validate partnerId (matching .NET validation)
  logger.info(`Validating query parameter partner Id -${partnerId}`);
  if (partnerId === null || partnerId === undefined) {
    logger.info(`Received validation error for query parameter PartnerId -${partnerId}, PartnerId can't be null or empty`);
    validationErrors.push("The 'PartnerId' URL parameter is missing in the request.");
  }
  logger.info(`Validated query parameter partner Id`);

  // Validate customerId (matching .NET validation)
  logger.info(`Validating query parameter customerId-${customerId}`);
  if (customerId === null || customerId === undefined) {
    logger.info(`Received validation error for query parameter customerId -${customerId}, CustomerId can't be null or empty`);
    validationErrors.push("The 'CustomerId' URL parameter is missing in the request.");
  }
  logger.info(`Validated query parameter Customer Id`);

  // Validate brandId (matching .NET validation)
  logger.info(`Validating query parameter brand Id -${brandId}`);
  if (brandId === null || brandId === undefined) {
    logger.info(`Received validation error for query parameter brandId -${brandId}, BrandId can't be null or empty`);
    validationErrors.push("The 'BrandId' URL parameter is missing in the request.");
  }
  logger.info(`Validated query parameter Brand Id`);

  logger.info(`Going to return Validation error list ${JSON.stringify(validationErrors)}`);
  return validationErrors;
}
