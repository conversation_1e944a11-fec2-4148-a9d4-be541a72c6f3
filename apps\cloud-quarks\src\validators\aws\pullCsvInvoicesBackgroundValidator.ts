import { z } from "zod";

/**
 * Pull CSV Invoices Background Request Schema
 * Matches .NET PullCsvInvoicesInBackground method parameters
 */
export const PullCsvInvoicesBackgroundSchema = z.object({
  requestId: z.string().min(1, "Request ID is required"),
  username: z.string().min(1, "Username is required"),
  month: z.number().int().min(1).max(12, "Month must be between 1 and 12"),
  year: z.number().int().min(2000).max(2100, "Year must be between 2000 and 2100"),
  storeKeyInput: z.string().min(1, "Store key input is required"),
});

export type PullCsvInvoicesBackgroundRequest = z.infer<typeof PullCsvInvoicesBackgroundSchema>;

/**
 * Validate Pull CSV Invoices Background request
 * Matches .NET validation patterns
 */
export function validatePullCsvInvoicesBackgroundRequest(
  requestId: string,
  username: string,
  month: number,
  year: number,
  storeKeyInput: string
): string[] {
  const errors: string[] = [];

  if (!requestId || requestId.trim() === "") {
    errors.push("Request ID cannot be empty");
  }

  if (!username || username.trim() === "") {
    errors.push("Username cannot be empty");
  }

  if (!month || month < 1 || month > 12) {
    errors.push("Month must be between 1 and 12");
  }

  if (!year || year < 2000 || year > 2100) {
    errors.push("Year must be between 2000 and 2100");
  }

  if (!storeKeyInput || storeKeyInput.trim() === "") {
    errors.push("Store key input cannot be empty");
  }

  return errors;
}
