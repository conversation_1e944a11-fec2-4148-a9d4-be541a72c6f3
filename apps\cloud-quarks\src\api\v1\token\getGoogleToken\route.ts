import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import { getGoogleTokenService } from "../../../../services/token/google/getGoogleTokenService";

/**
 * @openapi
 * /v1/token/getGoogleToken:
 *   get:
 *     summary: Retrieve Google Access Token
 *     description: Returns an access token for the given storeId by calling Google's OAuth token service.
 *     tags:
 *       - Token
 *     parameters:
 *       - in: query
 *         name: storeId
 *         required: true
 *         schema:
 *           type: string
 *         description: Store identifier to retrieve token credentials.
 *     responses:
 *       200:
 *         description: Successfully retrieved Google token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 access_token:
 *                   type: string
 *                   description: The OAuth access token.
 *                 token_type:
 *                   type: string
 *                 expires_in:
 *                   type: integer
 *                 scope:
 *                   type: string
 *       400:
 *         description: Missing or invalid storeId
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: storeId cannot be null or empty
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 error:
 *                   type: string
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { storeId } = req.query as { storeId?: string };
    const correlationId = getCorrelationId(req);

    logger.info(
      `Get Google Token API called | CorrelationId: ${correlationId}, storeId: ${storeId}`
    );

    // Validate storeId
    if (!storeId || storeId.trim() === "") {
      logger.info(
        `Invalid request - Missing or empty storeId | CorrelationId: ${correlationId}`
      );
      return res
        .status(400)
        .json({ message: "storeId cannot be null or empty" });
    }

    // Call service
    const token = await getGoogleTokenService(req, storeId);

    console.log("token---------->", token);

    logger.info(
      `Successfully retrieved Google token | CorrelationId: ${correlationId}`
    );

    return res.status(200).json(token);
  } catch (error: any) {
    logger.error(
      `Error in Get Google Token API: ${error.message} | Stack: ${error.stack}`
    );

    return res.status(500).json({
      message: "Internal server error",
      error: error.message,
    });
  }
}
