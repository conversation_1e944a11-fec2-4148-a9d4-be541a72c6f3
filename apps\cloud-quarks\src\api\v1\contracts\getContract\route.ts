import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { normalizeQueryParams } from "../../../../utils/mappers/getCustomerUsers";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import { validateGetContractRequest } from "../../../../validators/contracts/customContractValidator";
import { getContractService } from "../../../../services/contracts/getContractService";

/**
 * @openapi
 * /v1/contracts/getContract:
 *   get:
 *     summary: Get contract details by storeId and partnerId
 *     tags:
 *       - Contracts
 *     parameters:
 *       - in: query
 *         name: storeId
 *         required: true
 *         schema:
 *           type: string
 *         description: Store ID
 *       - in: query
 *         name: partnerId
 *         required: true
 *         schema:
 *           type: string
 *         description: Partner ID
 *       - in: query
 *         name: expiryInDays
 *         required: false
 *         schema:
 *           type: string
 *           default: "ALL"
 *         description: Expiry filter (number of days or 'ALL')
 *     responses:
 *       200:
 *         description: List of contracts
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/GetContractResponse'
 *       400:
 *         description: Validation error
 *       500:
 *         description: Internal server error
 *
 * components:
 *   schemas:
 *     GetContractResponse:
 *       type: object
 *       properties:
 *         ContractId:
 *           type: string
 *           example: "string"
 *         OrderId:
 *           type: string
 *           example: "string"
 *         SubscriptionId:
 *           type: string
 *           example: "string"
 *         MaterialId:
 *           type: string
 *           example: "string"
 *         MaterialNo:
 *           type: string
 *           example: "string"
 *         MaterialDesc:
 *           type: string
 *           example: "string"
 *         Qty:
 *           type: number
 *           example: -1
 *         MaterialType:
 *           type: string
 *           example: "Base"
 *         PlanType:
 *           type: string
 *           example: "Annual"
 *         StartDate:
 *           type: string
 *           format: date
 *           example: "29/07/2024"
 *           description: "Date in DD/MM/YYYY format"
 *         EndDate:
 *           type: string
 *           format: date
 *           example: "28/07/2025"
 *           description: "Date in DD/MM/YYYY format"
 *         PartnerCompany:
 *           type: string
 *           example: "string"
 *         CustomerCompany:
 *           type: string
 *           example: "string"
 *         ContractValue:
 *           type: number
 *           example: -21.2
 *         Status:
 *           type: string
 *           example: "ACTIVE"
 *         Brand:
 *           type: string
 *           example: "Software Subscription"
 *         ProvType:
 *           type: string
 *           example: "AUTO"
 *         CreateDate:
 *           type: string
 *           format: date
 *           example: "28/07/2025"
 *           description: "Date in DD/MM/YYYY format"
 *         ListPrice:
 *           type: number
 *           example: 21.25
 *         UnitPrice:
 *           type: number
 *           example: 21.25
 *         StatusReason:
 *           type: string
 *           example: "EOT"
 *         Segment:
 *           type: string
 *           example: "Commercial"
 *         Term:
 *           type: string
 *           example: "P1Y"
 *         PaymentTermsCode:
 *           type: string
 *           example: "R030"
 *         PaymentTerms:
 *           type: string
 *           example: "45 Days  BG from Invoice date"
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    logger.info(`Get Contract API is Hit`);
    const correlationId = getCorrelationId(req);
    const rawQuery = req.query as {
      storeId?: string;
      partnerId?: string;
      expiryInDays?: string;
    };

    logger.info(
      `GetContract API called | CorrelationId: ${correlationId}, rawQuery: ${JSON.stringify(
        rawQuery
      )}`
    );

    const {
      storeId,
      partnerId,
      expiryInDays = "ALL",
    } = normalizeQueryParams(rawQuery, [
      "storeId",
      "partnerId",
      "expiryInDays",
    ]);

    logger.info(
      `Normalized query params | CorrelationId: ${correlationId}, storeId=${storeId}, partnerId=${partnerId}, expiryInDays=${expiryInDays}`
    );

    const validationErrors = validateGetContractRequest(storeId, partnerId);
    if (validationErrors.length > 0) {
      logger.info(
        `Validation errors | CorrelationId: ${correlationId} | Errors: ${JSON.stringify(
          validationErrors
        )}`
      );
      return res.status(400).json({
        statusCode: 400,
        errors: validationErrors,
      });
    }

    logger.info(`Calling getContractService | CorrelationId: ${correlationId}`)
    const response = await getContractService(storeId, partnerId, expiryInDays)
    logger.info(`GetContract service success | CorrelationId: ${correlationId}, response: ${JSON.stringify(response)}`)
    return res.status(200).json(response)
  } catch (err) {
    logger.error("Get Contract error: ", err as Error);
    return res.status(500).json({
      status: false,
      message: "Internal server error",
    });
  }
}
