import AWS from 'aws-sdk';

interface SesConfig {
  region: string;
  accessKeyId: string;
  secretAccessKey: string;
}

interface EmailAddress {
  email: string;
  name?: string;
}

interface EmailTemplate {
  template_id: string;
  template_data: Record<string, unknown>;
}

interface EmailContent {
  text?: string;
  html?: string;
}

interface EmailPayload {
  to: EmailAddress[];
  cc?: EmailAddress[];
  bcc?: EmailAddress[];
  from: EmailAddress;
  reply_to?: EmailAddress;
  subject: string;
  content?: EmailContent;
  template?: EmailTemplate;
}

interface Notification {
  to: string;
  template: string;
}

export function ses(): string {
  return 'ses';
}



export class MyNotificationProviderService {
  static identifier = 'ses-notification';
  private client: AWS.SES;

  constructor({ region, accessKeyId, secretAccessKey }: SesConfig) {
    this.client = new AWS.SES({
      region,
      credentials: {
        accessKeyId,
        secretAccessKey,
      },
    });
  }

  private formatEmailAddress(address: EmailAddress): string {
    return address.name ? `${address.name} <${address.email}>` : address.email;
  }

  private validateEmailPayload(payload: EmailPayload): void {
    if (!payload.to || payload.to.length === 0) {
      throw new Error('At least one recipient is required');
    }
    if (!payload.from) {
      throw new Error('From address is required');
    }
    if (!payload.subject) {
      throw new Error('Subject is required');
    }
    if (!payload.content?.text && !payload.content?.html && !payload.template) {
      throw new Error('Either content (text/html) or template is required');
    }
  }

  private createDestination(payload: EmailPayload): AWS.SES.Destination {
    const destination: AWS.SES.Destination = {
      ToAddresses: payload.to.map(recipient => this.formatEmailAddress(recipient)),
    };

    if (payload.cc?.length) {
      destination.CcAddresses = payload.cc.map(cc => this.formatEmailAddress(cc));
    }

    if (payload.bcc?.length) {
      destination.BccAddresses = payload.bcc.map(bcc => this.formatEmailAddress(bcc));
    }

    return destination;
  }

  async sendTemplatedEmail(payload: EmailPayload): Promise<{ id: string }> {
    if (!payload.template?.template_id) {
      throw new Error('Template ID is required for template-based emails');
    }

    const params: AWS.SES.SendTemplatedEmailRequest = {
      Source: this.formatEmailAddress(payload.from),
      Destination: this.createDestination(payload),
      Template: payload.template.template_id,
      TemplateData: JSON.stringify(payload.template.template_data || {}),
    };

    if (payload.reply_to) {
      params.ReplyToAddresses = [this.formatEmailAddress(payload.reply_to)];
    }

    try {
      const data = await this.client.sendTemplatedEmail(params).promise();
      return { id: data.MessageId };
    } catch (error) {
      console.error('SES Template Email Error:', error);
      throw error;
    }
  }

  async sendRegularEmail(payload: EmailPayload): Promise<{ id: string }> {
    const params: AWS.SES.SendEmailRequest = {
      Source: this.formatEmailAddress(payload.from),
      Destination: this.createDestination(payload),
      Message: {
        Subject: {
          Data: payload.subject,
          Charset: 'UTF-8',
        },
        Body: {},
      },
    };

    if (payload.content?.html) {
      params.Message.Body.Html = {
        Data: payload.content.html,
        Charset: 'UTF-8',
      };
    }

    if (payload.content?.text) {
      params.Message.Body.Text = {
        Data: payload.content.text,
        Charset: 'UTF-8',
      };
    }

    if (payload.reply_to) {
      params.ReplyToAddresses = [this.formatEmailAddress(payload.reply_to)];
    }

    try {
      const data = await this.client.sendEmail(params).promise();
      return { id: data.MessageId };
    } catch (error) {
      console.error('SES Regular Email Error:', error);
      throw error;
    }
  }

  async sendEmail(payload: EmailPayload): Promise<{ id: string }> {
    this.validateEmailPayload(payload);

    if (payload.template?.template_id) {
      return this.sendTemplatedEmail(payload);
    }
    return this.sendRegularEmail(payload);
  }

    async send(notification: Notification, from: string) {
    const emailParams: AWS.SES.SendEmailRequest = {
      Source: from,
      Destination: {
        ToAddresses: [notification.to],
      },
      Message: {
        Subject: {
          Data: 'subject',
          Charset: 'UTF-8',
        },
        Body: {
          Html: {
            Data: notification.template,
            Charset: 'UTF-8',
          },
        },
      },
    };

    try {
      const data = await this.client.sendEmail(emailParams).promise();

      return {
        id: data.MessageId,
      };
    } catch (error) {
      console.error('error ====>>>> ', error);
      throw error;
    }
  }
}
