import { getSqlServerConnection } from "../../utils/sqlServerClient";
import logger from "../../utils/logger";

// Order Detail Row type (representing DataRow from .NET)
export type OrderDetailRow = {
  CustomerId: string;
  VendorOrderId: string;
  SubsProvStatus: boolean;
  BrandKey: number;
  BrandName: string;
  SubscriptionId?: string;
  Clientid: string;
  Clientsecret: string;
  GrantType: string;
  Token: string;
  Resource: string;
  StoreDomain: string;
  [key: string]: any; // Allow additional properties
};

export async function getOrderDetail(orderLineKey: number): Promise<OrderDetailRow | null> {
  logger.info(`Entered into GetOrderDetail Service method with orderLineKey - ${orderLineKey}`);

  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();
    
    const proc = "spGetOrderLineItemDetail";
    

    console.log("iOrderLineItemId, orderLineKey--------->","iOrderLineItemId", orderLineKey)

    request.input("iOrderLineItemId", orderLineKey);
    
    logger.info(`Going to hit DB with proc name: ${proc} with parameters orderLineKey: ${orderLineKey}`);
    
    const result = await request.execute(proc);
    const rows = result.recordset;
    
    if (rows && rows.length > 0) {
      const orderRow = rows[0] as OrderDetailRow;
      logger.info(`Going to return - ${JSON.stringify(orderRow)}`);
      return orderRow;
    }
    
    logger.info(`No order found for orderLineKey: ${orderLineKey}`);
    return null;
  } catch (error) {
    logger.error(`Error in getOrderDetail: ${error}`);
    throw error;
  }
}
