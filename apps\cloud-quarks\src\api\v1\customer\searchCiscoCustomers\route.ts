import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import {
  SearchCiscoCustomerDto,
  CiscoCustomerRequest,
  CiscoCustomerResponse
} from "../../../../types/responses/customResponse";
import { validateSearchCiscoCustomerDto } from "../../../../validators/customer/searchCiscoCustomerValidator";
import { searchCiscoCustomersService } from "../../../../services/customer/searchCiscoCustomersService";
import { CiscoConstants } from "../../../../validators/reseller/addUpdateCiscoResellerValidator";

/**
 * @openapi
 * /v1/customer/searchCiscoCustomers:
 *   post:
 *     summary: Search Cisco Customers By Name
 *     description: Searches for Cisco customers by name and country code
 *     tags:
 *       - Customer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               countryCode:
 *                 type: string
 *                 example: "US"
 *               name:
 *                 type: string
 *                 example: "Example Corp"
 *               storeId:
 *                 type: string
 *                 example: "store123"
 *               pageSize:
 *                 type: integer
 *                 example: 100
 *     responses:
 *       200:
 *         description: Successfully retrieved Cisco customers
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 bodID:
 *                   type: string
 *                   example: "bod123"
 *                 status:
 *                   type: string
 *                   example: "SUCCESS"
 *                 matchFound:
 *                   type: integer
 *                   example: 5
 *                 party:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       partyName:
 *                         type: string
 *                         example: "Example Corp"
 *                       localeCode:
 *                         type: string
 *                         example: "US"
 *       400:
 *         description: Bad request - validation errors
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "FAIL"
 *                 message:
 *                   type: string
 *                   example: "Country code cannot be null or empty."
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "FAILURE"
 *                 message:
 *                   type: string
 *                   example: "An error occurred: [error details]"
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req);

  logger.info(`Entered into SearchCiscoCustomersByName API with CorrelationId: ${correlationId}`);

  try {
    const customerDto = req.body as SearchCiscoCustomerDto;
    logger.info(`Entered into SearchCiscoCustomersByName API with details ${JSON.stringify(customerDto)}`);

    logger.info("Going to validate the customer dto object");

    // Validate the request DTO (matching .NET validation)
    const validationResult = validateSearchCiscoCustomerDto(customerDto);
    logger.info(`Validations error count on customer dto object is ${validationResult.errors.length}`);
    logger.info(`Validations error : ${JSON.stringify(validationResult)}`);

    if (validationResult.errors.length > 0) {
      logger.info("Entered into section where validationResult.Errors.Count > 0");
      logger.info("Going to extract errors from validationResult object");

      const validationErrors = validationResult.errors;
      logger.info(`validation errors are ${JSON.stringify(validationErrors)}`);

      logger.info(`creating bad request object ${JSON.stringify(validationErrors)}`);
      const errorResponse: CiscoCustomerResponse = {
        status: CiscoConstants.StatusFail,
        message: validationErrors.join(",")
      };
      logger.info(`Bad request object ${JSON.stringify(errorResponse)}`);

      logger.info(`Going to return bad response ${JSON.stringify(validationErrors)}`);
      return res.status(400).json(errorResponse);
    } else {
      logger.info("Entered into section where there is no validation error");

      // Map DTO to Request (matching .NET: _mapper.Map<CiscoCustomerRequest>(customerDto))
      logger.info(`Going to map customer dto to CiscoCustomerRequest. CustomerDto: ${JSON.stringify(customerDto)}`);
      const customerMasterModel: CiscoCustomerRequest = {
        bodID: "", // Will be set by service if needed
        get: {
          pageSize: customerDto.pageSize
        },
        party: {
          countryCode: customerDto.countryCode,
          name: customerDto.name
        },
        timestamp: new Date().toISOString(),
        storeId: customerDto.storeId
      };
      logger.info(`Cisco Customer request model after mapping : ${JSON.stringify(customerMasterModel)}`);

      logger.info(`Going to call Customer service -- SearchCiscoCustomers method`);

      // Call the service method (matching .NET service call)
      const response = await searchCiscoCustomersService(customerMasterModel, req);
      logger.info(`Recieved response from service method ${JSON.stringify(response)}`);

      logger.info(`Going to return OK object response: ${JSON.stringify(response)}`);
      return res.status(200).json(response);
    }

  } catch (exp: any) {
    logger.error(`Exception in SearchCiscoCustomersByName API method. Message: ${exp.message}, StackTrace: ${exp.stack}`);

    const errorResponse: CiscoCustomerResponse = {
      status: CiscoConstants.StatusFail,
      message: `An error occurred: ${exp.message}`
    };

    return res.status(500).json(errorResponse);
  }
}