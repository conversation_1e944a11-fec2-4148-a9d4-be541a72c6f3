import * as ejs from 'ejs';
import * as path from 'path';

export async function renderResetPasswordEmail(name: string, resetLink: string): Promise<string> {
  const filePath = path.join(__dirname, 'templates', 'set-password.ejs');
  return ejs.renderFile(filePath, { name, resetLink });
}

export async function renderOtpEmail(otpCode: string): Promise<string> {
  const filePath = path.join(__dirname, 'templates', 'otp-verification.ejs');
  return ejs.renderFile(filePath, { otpCode });
}