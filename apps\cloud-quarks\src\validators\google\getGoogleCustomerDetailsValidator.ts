import logger from "../../utils/logger";
import { isCustIdValid } from "../../services/customer/customerValidationService";

/**
 * Validate GetGoogleCustomerDetails request parameters
 * Matches .NET ValidateGetGoogleCustomerDetailsRequest method
 */
export async function validateGetGoogleCustomerDetailsRequest(custId: string): Promise<string[]> {
  logger.info(`Entered into ValidateGetDetailsRequest Method with custId-${custId}`);
  
  const validationErrors: string[] = [];

  // CustId validation (matching .NET logic)
  if (!custId || custId.trim() === "") {
    validationErrors.push("CustId can't be null or empty");
  } else {
    // Check if customer ID is valid (matching .NET: _customerService.IsCustIdValid(custId))
    try {
      const isValid = await isCustIdValid(custId);
      if (!isValid) {
        validationErrors.push("CustId doesn't exists");
      }
    } catch (error) {
      logger.error(`Error checking customer ID validity: ${error}`);
      validationErrors.push("Error validating customer ID");
    }
  }

  logger.info(`Going to return Validation error list ${JSON.stringify(validationErrors)}`);
  return validationErrors;
}
