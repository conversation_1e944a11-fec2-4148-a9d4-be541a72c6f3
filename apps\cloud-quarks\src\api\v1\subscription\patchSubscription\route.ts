import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import {
  GetSubscriptionResponse,
  ErrorResponseModel
} from "../../../../types/responses/customResponse";
import {
  validatePatchSubscriptionRequest,
  PatchSubscriptionModelType
} from "../../../../validators/subscription/patchSubscriptionValidator";
import { patchSubscriptionService } from "../../../../services/subscription/patchSubscriptionService";

/**
 * @openapi
 * /v1/subscription/patchSubscription:
 *   post:
 *     summary: Update subscription status or quantity
 *     tags:
 *       - Subscription
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - storeId
 *               - customerId
 *               - subscriptionId
 *               - status
 *               - quantity
 *               - requestId
 *               - autoRenewEnabled
 *               - actualQty
 *               - updateType
 *               - partnerId
 *               - autoRenewToUpdate
 *             properties:
 *               storeId:
 *                 type: string
 *                 description: Store ID
 *               customerId:
 *                 type: string
 *                 description: Customer ID
 *               subscriptionId:
 *                 type: string
 *                 description: Subscription ID
 *               status:
 *                 type: string
 *                 description: Subscription status (e.g., Active, Cancelled)
 *               quantity:
 *                 type: integer
 *                 description: New subscription quantity
 *               requestId:
 *                 type: string
 *                 description: Unique request identifier
 *               autoRenewEnabled:
 *                 type: boolean
 *                 description: Whether auto-renew is enabled
 *               actualQty:
 *                 type: integer
 *                 description: Actual quantity to reflect
 *               updateType:
 *                 type: string
 *                 description: Type of update (e.g., status-only, quantity-only)
 *               partnerId:
 *                 type: string
 *                 description: Partner or reseller ID
 *               autoRenewToUpdate:
 *                 type: boolean
 *                 description: Whether auto-renew should be updated
 *           example:
 *             storeId: "store-123"
 *             customerId: "cust-456"
 *             subscriptionId: "sub-789"
 *             status: "Active"
 *             quantity: 5
 *             requestId: "req-12345"
 *             autoRenewEnabled: true
 *             actualQty: 5
 *             updateType: "status-only"
 *             partnerId: "partner-001"
 *             autoRenewToUpdate: true
 *     responses:
 *       200:
 *         description: Subscription updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                   example: "SUCCESS"
 *                 Message:
 *                   type: string
 *                   example: Subscription updated
 *       400:
 *         description: Validation errors
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 statusCode:
 *                   type: integer
 *                   example: 400
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: string
 *                   example:
 *                     - "subscriptionId is required"
 *                     - "quantity must be a positive number"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                   example: "FAIL"
 *                 ErrorMessage:
 *                   type: string
 *                   example: "Something went wrong"
 */

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req);

  logger.info(`[${correlationId}] Entered into PatchSubscription API with PatchSubscriptionModel-${JSON.stringify(req.body)} and going to call PatchSubscription service method`);

  try {
    console.log("req.body----------------------->",req.body);
    // Validate the request body
    const validationErrors = validatePatchSubscriptionRequest(req.body);

    if (validationErrors.length > 0) {
      logger.error(`[${correlationId}] Validation failed: ${JSON.stringify(validationErrors)}`);

      const errorResponse: ErrorResponseModel = {
        errors: validationErrors,
        statusCode: 400,
      };

      return res.status(400).json(errorResponse);
    }

    const patchModel: PatchSubscriptionModelType = req.body as PatchSubscriptionModelType;

    logger.info(`[${correlationId}] Request validation passed, proceeding with patch subscription`);

    // Call the main service method (matching .NET: return _subscriptionFactory.PatchSubscription(model))
    const subscriptionResponse = await patchSubscriptionService(req, patchModel);

    logger.info(`[${correlationId}] PatchSubscription service completed successfully: ${JSON.stringify(subscriptionResponse)}`);

    return res.status(200).json(subscriptionResponse);

  } catch (error: any) {
    // Log error (matching .NET exception handling)
    logger.error(`[${correlationId}] Error in PatchSubscription|ErrorDetail, errorMessage: ${error.message}, StackTrace: ${error.stack}`);

    // Return error response (matching .NET: return new GetSubscriptionResponse() { Status = "FAIL", ErrorMessage = ex.Message })
    const errorResponse: GetSubscriptionResponse = {
      Status: "FAIL",
      ErrorMessage: error.message
    };

    return res.status(500).json(errorResponse);
  }
}