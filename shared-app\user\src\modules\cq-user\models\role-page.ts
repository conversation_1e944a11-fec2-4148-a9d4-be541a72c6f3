import { model } from '@medusajs/framework/utils';
import Page from './page';
import Role from './role';
import CQUser from './cq_user';
import UserRolePage from './user-role-page';
import Permission from './permission';
import RolePagePermission from './role-page-permission';

const RolePage = model.define('cq_role_page', {
  id: model.id().primaryKey(),
  page: model.belongsTo(() => Page, { mappedBy: 'roles' }),
  role: model.belongsTo(() => Role, { mappedBy: 'pages' }),
  users: model.manyToMany(() => CQUser, {
    pivotEntity: () => UserRolePage,
    mappedBy: 'role_pages',
  }),
  permissions: model.manyToMany(() => Permission, {
    pivotEntity: () => RolePagePermission,
    mappedBy: 'role_pages',
  }),
});

export default RolePage;
