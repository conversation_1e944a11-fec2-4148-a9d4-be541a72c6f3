import { container, MedusaRequest, MedusaResponse } from '@medusajs/framework';
import axios from 'axios';
import jwt from 'jsonwebtoken';
import { CQUser_MODULE } from '@shared-app/cq-user';
import GuestUserService from '@shared-app/cq-user/service';
import { Modules } from '@medusajs/framework/utils';
const authModuleService = container.resolve(Modules.AUTH);
const {
  COGNITO_DOMAIN,
  COGNITO_CLIENT_ID,
  COGNITO_CLIENT_SECRET,
  REDIRECT_URI,
} = process.env;
interface DecodedToken {
  email?: string;
  identities?: {
    providerName?: string;
    [key: string]: any;
  }[];
  [key: string]: any;
}

// /**
//  * @openapi
//  * /auth/callback:
//  *   get:
//  *     summary: Exchange OAuth authorization code for tokens
//  *     tags:
//  *       - Auth
//  *     description: |
//  *       Exchanges the authorization code received from the OAuth provider (Cognito)
//  *       for access, refresh, and ID tokens.
//  *     parameters:
//  *       - name: code
//  *         in: query
//  *         required: true
//  *         description: Authorization code returned from the OAuth provider redirect
//  *         schema:
//  *           type: string
//  *     responses:
//  *       200:
//  *         description: Token exchange successful
//  *         content:
//  *           application/json:
//  *             schema:
//  *               type: object
//  *               properties:
//  *                 data:
//  *                   type: object
//  *                   properties:
//  *                     token:
//  *                       type: object
//  *                       description: Token response object from Cognito
//  *                       additionalProperties: true
//  *       400:
//  *         description: Missing required query parameter 'code'
//  *         content:
//  *           application/json:
//  *             schema:
//  *               type: object
//  *               properties:
//  *                 error:
//  *                   type: string
//  *                   example: Code is missing
//  *                 message:
//  *                   type: string
//  *                   example: Authentication code is required.
//  *       500:
//  *         description: Server error or misconfiguration (e.g., missing REDIRECT_URI)
//  *         content:
//  *           application/json:
//  *             schema:
//  *               type: object
//  *               properties:
//  *                 error:
//  *                   type: string
//  *                   example: Redirect URI not configured
//  *                 message:
//  *                   type: string
//  *                   example: Missing REDIRECT_URI environment variable.
//  */

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const guestUserService: GuestUserService =
    req.scope.resolve(CQUser_MODULE);
  const { code } = req.query as { code?: string };

  if (!code) {
    res
      .status(400)
      .json({
        error: 'Code is missing',
        message: 'Authentication code is required.',
      });
    return;
  }
  if (!REDIRECT_URI) {
    res.status(500).json({
      error: 'Redirect URI not configured',
      message: 'Missing REDIRECT_URI environment variable.',
    });
    return;
  }

  try {
    const tokenResponse = await axios.post(
      `${COGNITO_DOMAIN}/oauth2/token`,
      new URLSearchParams({
        grant_type: 'authorization_code',
        code: code as string,
        redirect_uri: REDIRECT_URI,
      }),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Authorization:
            'Basic ' +
            Buffer.from(
              `${COGNITO_CLIENT_ID}:${COGNITO_CLIENT_SECRET}`
            ).toString('base64'),
        },
      }
    );

    // // Decode the ID token to get user info
    // const decodedToken = jwt.decode(tokenResponse.data.id_token) as DecodedToken;
    // const userEmail = decodedToken?.email;
    // const providerIdentityInfo = decodedToken?.identities?.[0];
    // const providerName = providerIdentityInfo?.providerName;

    // const guestUser = await guestUserService.listGuestUsers({ email: userEmail });
    // if (!guestUser) {
    //   const createGuestUser = await guestUserService.createGuestUsers({
    //     email: userEmail,
    //   })
    //   // Create Auth Identity with Provider Identity
    //   const authIdentity = await authModuleService.createAuthIdentities({
    //     app_metadata: { 'guest_user_id': createGuestUser.id },
    //   });

    //   if (userEmail && authIdentity?.id && providerName) {
    //     await authModuleService.createProviderIdentities({
    //       provider: providerName,
    //       entity_id: userEmail,
    //       auth_identity_id: authIdentity.id,
    //       provider_metadata: {
    //         password: "123456",
    //       }
    //     });
    //   } else {
    //     res.status(400).json({ message: "Missing userEmail or authIdentity id" });
    //     return;
    //   }
    // }
    res.json({ data: { token: tokenResponse.data } });
  } catch (err) {
    res.status(500).send('Authentication failed');
  }
};
