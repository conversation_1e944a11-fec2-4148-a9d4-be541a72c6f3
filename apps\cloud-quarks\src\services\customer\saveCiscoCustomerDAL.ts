import logger from "../../utils/logger";
import sql from "mssql";
import { getSqlServerConnection } from "../../utils/sqlServerClient";
import { CiscoCustomerModel, CiscoStatusResponseType } from "../../types/responses/customResponse";
import { CiscoConstants } from "../../validators/reseller/addUpdateCiscoResellerValidator";

/**
 * Save Cisco Customer
 * Matches .NET CustomerDAL.SaveCiscoCustomer method
 */
export async function saveCiscoCustomerDAL(ciscoCustomerModel: CiscoCustomerModel): Promise<CiscoStatusResponseType> {
  logger.info(`Entered into SaveCiscoCustomer DAL method with model: ${JSON.stringify(ciscoCustomerModel)}`);

  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();

    // TODO: Replace with actual stored procedure name from .NET implementation
    const procedure = "spSaveCiscoCustomer"; // This should match the actual stored procedure name

    // Add input parameters (matching .NET stored procedure parameters)
    request.input("custId", sql.VarChar(50), ciscoCustomerModel.custId);
    request.input("storeId", sql.VarChar(50), ciscoCustomerModel.storeId);
    request.input("partyName", sql.VarChar(255), ciscoCustomerModel.partyName);
    request.input("addressLine1", sql.VarChar(255), ciscoCustomerModel.addressLine1);
    request.input("addressLine2", sql.VarChar(255), ciscoCustomerModel.addressLine2 || "");
    request.input("city", sql.VarChar(100), ciscoCustomerModel.city);
    request.input("state", sql.VarChar(100), ciscoCustomerModel.state);
    request.input("postalCode", sql.VarChar(20), ciscoCustomerModel.postalCode);
    request.input("country", sql.VarChar(100), ciscoCustomerModel.country);
    request.input("countryCode", sql.VarChar(10), ciscoCustomerModel.countryCode);
    request.input("businessContactName", sql.VarChar(255), ciscoCustomerModel.businessContactName);
    request.input("businessContactEmail", sql.VarChar(255), ciscoCustomerModel.businessContactEmail);
    request.input("businessContactNumber", sql.VarChar(50), ciscoCustomerModel.businessContactNumber);

    logger.info(`Going to execute ${procedure} procedure with parameters: ${JSON.stringify(ciscoCustomerModel)}`);

    const result = await request.execute<any>(procedure);
    
    // Process the result from stored procedure
    // The stored procedure should return Status, Message, and ActionType
    const response = result.recordset?.[0];

    if (!response) {
      throw new Error("No response received from stored procedure");
    }

    const statusResponse: CiscoStatusResponseType = {
      Status: response.Status || CiscoConstants.StatusFail,
      Message: response.Message || "Unknown error occurred",
      ActionType: response.ActionType || ""
    };

    logger.info(`Response from stored procedure: ${JSON.stringify(statusResponse)}`);
    return statusResponse;

  } catch (error: any) {
    logger.error(`Error in saveCiscoCustomerDAL: ${error.message}`);
    
    // Return error response
    return {
      Status: CiscoConstants.StatusFail,
      Message: `Database error: ${error.message}`,
      ActionType: ""
    };
  }
}
