import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import { getSqlServerConnection } from "../../../utils/sqlServerClient";

/**
 * @openapi
 * /v1/testSwagger:
 *   get:
 *     summary: Test Swagger response
 *     tags:
 *       - Test API
 *     responses:
 *       200:
 *         description: Test response successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Hello Swagger
 *                 success:
 *                   type: boolean
 *                   example: true
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const pool = await getSqlServerConnection();
    const result = await pool
      .request()
      .query("SELECT TOP 10 * FROM tblMstStore");

    return res.status(200).json({
      status: true,
      data: result.recordset,
    });
  } catch (err) {
    console.error("SQL Error:", err);
    return res.status(500).json({
      status: false,
      message: "SQL Server connection failed",
    });
  }
}
