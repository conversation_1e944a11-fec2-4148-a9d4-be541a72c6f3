import { model } from '@medusajs/framework/utils';
import Designation from './cq_designation';
import Persona from './persona';
import UserPersona from './user-persona';
import RolePage from './role-page';
import UserRolePage from './user-role-page';
import CQStore from './store';
import UserColumnPreference from './user-column-preference';

const CQUser = model.define('cq_user', {
	id: model.id().primaryKey(),
	first_name: model.text(),
	last_name: model.text(),
	email: model.text().unique(),
	country_code: model.text(),
	mobile_number: model.text(),
	company_name: model.text().nullable(),
	redington_sales_reference: model.text().nullable(),
	profile_pic_file_name: model.text().nullable(),
	is_active: model.boolean().default(false),
	is_delete: model.boolean().default(false),
	is_password_set: model.boolean().default(false),
	has_sales_reference: model.boolean().default(false),
	terms_and_conditions: model.boolean().default(false),
	source: model.text(),
	is_onboarded: model.boolean().default(false),
	cq_designation: model.belongsTo(() => Designation, { mappedBy: 'users' }),
	registered_country_id: model.text(),
	created_by: model.belongsTo(() => CQUser, { mappedBy: undefined }).nullable(),
	updated_by: model.belongsTo(() => CQUser, { mappedBy: undefined }).nullable(),
	deleted_by: model.belongsTo(() => CQUser, { mappedBy: undefined }).nullable(),
	metadata: model.json().nullable(),
	personas: model.manyToMany(() => Persona, {
		pivotEntity: () => UserPersona,
		mappedBy: 'users',
	}),
	role_pages: model.manyToMany(() => RolePage, {
		pivotEntity: () => UserRolePage,
		mappedBy: 'users',
	}),

	// cq_store: model.belongsTo(() => CQStore, { mappedBy: 'users' }),

	preferences: model.hasMany(() => UserColumnPreference, { mappedBy: 'user' }),
	region_id: model.text(),
});

export default CQUser;
