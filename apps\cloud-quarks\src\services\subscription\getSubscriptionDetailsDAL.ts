import logger from "../../utils/logger";
import sql from "mssql";
import { getSqlServerConnection } from "../../utils/sqlServerClient";

// Subscription Details Model (matching .NET DataTable structure)
export interface SubscriptionDetailsModel {
  CustomerId: string;
  Clientid: string;
  Clientsecret: string;
  granttype: string;
  Token: string;
  Resource: string;
  StoreDomain: string;
  // Add other fields as needed based on the actual stored procedure response
}

/**
 * Get Subscription Details from database
 * Matches .NET GetSubscriptionDetails method that returns DataTable
 */
export async function getSubscriptionDetailsDAL(
  subscriptionId: string
): Promise<SubscriptionDetailsModel> {
  logger.info(`Entered into GetSubscriptionDetails DAL method with subscriptionId: ${subscriptionId}`);

  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();

    // This would typically be a stored procedure that gets subscription details
    // including store credentials based on subscription ID
    const procedure = "spGetSubscriptionDetails"; // Adjust procedure name as needed

    request.input("iSubscriptionId", sql.Var<PERSON>har(255), subscriptionId);

    logger.info(`Going to hit DB with proc name: ${procedure} and subscriptionId: ${subscriptionId}`);

    const result = await request.execute<SubscriptionDetailsModel>(procedure);
    const response = result.recordset?.[0];

    if (!response) {
      throw new Error(`No subscription details found for subscriptionId: ${subscriptionId}`);
    }

    logger.info(`Got subscription details from database: ${JSON.stringify(response)}`);
    return response;

  } catch (error: any) {
    logger.error(`Error in getSubscriptionDetailsDAL: ${error.message}`);
    throw error;
  }
}
