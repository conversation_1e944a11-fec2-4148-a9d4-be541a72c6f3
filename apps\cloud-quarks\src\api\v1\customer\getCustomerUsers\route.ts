import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCustomerUsersService } from "../../../../services/index";
import {
  normalizeQueryParams,
  toPascalCaseJson,
} from "../../../../utils/mappers/getCustomerUsers";
import { validateCustomerUserQuery } from "../../../../validators/customer/customCustomerValidator";

/**
 * @openapi
 * /v1/customer/getCustomerUsers:
 *   get:
 *     summary: Get Microsoft Customer Users
 *     tags:
 *       - Customer
 *     parameters:
 *       - in: query
 *         name: brandId
 *         schema:
 *           type: string
 *         required: true
 *         description: Brand ID
 *       - in: query
 *         name: storeId
 *         schema:
 *           type: string
 *         required: true
 *         description: Store ID
 *       - in: query
 *         name: tenantId
 *         schema:
 *           type: string
 *         required: true
 *         description: Microsoft Tenant ID
 *     responses:
 *       200:
 *         description: Successfully retrieved customer users
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 TotalCount:
 *                   type: integer
 *                 Items:
 *                   type: array
 *                   items:
 *                     type: object
 *                 Links:
 *                   type: object
 *                 Attributes:
 *                   type: object
 *                 Code:
 *                   type: integer
 *                 Description:
 *                   type: string
 *       400:
 *         description: Validation failed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: string
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                 message:
 *                   type: string
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    logger.info(`getCustomerUsers API is Hit`);
    const rawQuery = req.query;
    const { brandId, storeId, tenantId } = normalizeQueryParams(rawQuery, [
      "brandId",
      "storeId",
      "tenantId",
    ]);

    logger.info(
      `Normalized query params -> brandId: ${brandId}, storeId: ${storeId}, tenantId: ${tenantId}`
    );

    const validationErrors = validateCustomerUserQuery(
      brandId,
      storeId,
      tenantId
    );
    logger.info(`Validation error count = ${validationErrors.length}`);

    if (validationErrors.length > 0) {
      logger.info(
        `Returning 400 with errors: ${JSON.stringify(validationErrors)}`
      );
      return res.status(400).json({
        status: false,
        errors: validationErrors,
        message: "Validation failed",
      });
    }
    logger.info(
      `Calling getCustomerUsersService with brandId=${brandId}, storeId=${storeId}, tenantId=${tenantId}`
    );
    const result = await getCustomerUsersService(
      req,
      brandId,
      storeId,
      tenantId
    );

    logger.info(`getCustomerUsersService result: ${JSON.stringify(result)}`);
    const pascalCaseResponse = toPascalCaseJson(result);

    return res.status(result.status ? 200 : 400).json(pascalCaseResponse);
  } catch (err) {
    logger.error("getCustomerUsers error: ", err as Error);
    return res.status(500).json({
      status: false,
      message: "Internal server error",
    });
  }
}
