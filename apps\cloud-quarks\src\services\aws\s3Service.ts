import AWS from 'aws-sdk';
import logger from "../../utils/logger";
import { S3Credentials } from "../../types/responses/customResponse";

// Helper function for delay (similar to <PERSON> retry delay)
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

/**
 * Get S3 Object content as string (without retry logic)
 * Matches .NET GetS3ObjectAsync method
 */
export async function getS3ObjectService(
  s3Credentials: S3Credentials,
  fileName: string
): Promise<string> {
  logger.info(`Entered into BL GetS3ObjectAsync method with fileName: ${fileName}`);

  try {
    // Configure S3 client with credentials
    const s3Client = new AWS.S3({
      accessKeyId: s3Credentials.AWS_ACCESS_KEY_ID,
      secretAccessKey: s3Credentials.AWS_SECRET_ACCESS_KEY,
      region: s3Credentials.AWS_REGION,
    });

    const request = {
      Bucket: s3Credentials.AWS_BUCKETNAME,
      Key: fileName
    };

    logger.info(`Going to s3Client.GetObjectAsync with request: ${JSON.stringify(request)}`);

    const response = await s3Client.getObject(request).promise();
    logger.info(`Got Successful response from s3Client.GetObjectAsync`);

    // Convert response body to string
    let responseObject = "";
    if (response.Body) {
      if (response.Body instanceof Buffer) {
        responseObject = response.Body.toString('utf-8');
      } else if (typeof response.Body === 'string') {
        responseObject = response.Body;
      } else {
        // Handle other types (Uint8Array, etc.)
        responseObject = response.Body.toString();
      }
    }

    logger.info(`Going to return response as ${responseObject}`);
    return responseObject;
  } catch (error: any) {
    logger.error(`Error in GetS3ObjectAsync: ${error.message}, stackTrace: ${error.stack}`);
    throw error;
  }
}

/**
 * Get S3 Object content as string with retry logic
 * Matches .NET GetS3ObjectAsync method with Polly retry policy
 * @param s3Credentials - AWS S3 credentials
 * @param fileName - S3 object key/filename
 * @param configModule - Configuration module for retry settings
 */
export async function getS3ObjectServiceWithRetry(
  s3Credentials: S3Credentials,
  fileName: string,
  configModule?: any
): Promise<string> {
  logger.info(`Entered into BL GetS3ObjectAsync method with fileName: ${fileName}`);

  // Get retry configuration from config module or use defaults
  const maxRetries = parseInt(configModule?.projectConfig?.maxRetries || "3");
  const delaySeconds = parseInt(
    configModule?.projectConfig?.incrementalRetryDelayInSeconds || "1"
  );

  logger.info(`S3 GetObject MaxRetries: ${maxRetries}, RetryDelay: ${delaySeconds}s`);

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      logger.info(`[Attempt ${attempt}] Getting S3 object: ${fileName}`);

      // Configure S3 client with credentials
      const s3Client = new AWS.S3({
        accessKeyId: s3Credentials.AWS_ACCESS_KEY_ID,
        secretAccessKey: s3Credentials.AWS_SECRET_ACCESS_KEY,
        region: s3Credentials.AWS_REGION,
      });

      const request = {
        Bucket: s3Credentials.AWS_BUCKETNAME,
        Key: fileName
      };

      logger.info(`[Attempt ${attempt}] Going to s3Client.GetObjectAsync with request: ${JSON.stringify(request)}`);

      const start = Date.now();
      const response = await s3Client.getObject(request).promise();
      const duration = Date.now() - start;

      logger.info(`[Attempt ${attempt}] Got Successful response from s3Client.GetObjectAsync in ${duration}ms`);

      // Convert response body to string
      let responseObject = "";
      if (response.Body) {
        if (response.Body instanceof Buffer) {
          responseObject = response.Body.toString('utf-8');
        } else if (typeof response.Body === 'string') {
          responseObject = response.Body;
        } else {
          // Handle other types (Uint8Array, etc.)
          responseObject = response.Body.toString();
        }
      }

      logger.info(`[Attempt ${attempt}] Going to return response as ${responseObject}`);
      return responseObject;

    } catch (error: any) {
      logger.error(`[Attempt ${attempt}] S3 GetObject failed: ${error.message}`);
      logger.error(`[Attempt ${attempt}] Stack: ${error.stack}`);

      // If this is the last attempt, throw the error
      if (attempt === maxRetries) {
        logger.error(`Max retries reached for S3 GetObject. Throwing error.`);
        throw error;
      }

      // Calculate delay for next retry (incremental delay)
      const retryDelay = attempt * delaySeconds * 1000;
      logger.warn(`Retrying S3 GetObject in ${retryDelay / 1000}s before next attempt...`);
      await delay(retryDelay);
    }
  }

  // This should never be reached, but included for completeness
  throw new Error("Unknown error after retries in S3 GetObject");
}
