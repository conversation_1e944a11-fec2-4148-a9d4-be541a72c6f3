import { model } from '@medusajs/framework/utils';
import RolePage from './role-page';
import Permission from './permission';

const RolePagePermission = model.define('cq_role_page_permission', {
  id: model.id().primaryKey(),
  role_page: model.belongsTo(() => RolePage, { mappedBy: 'permissions' }),
  permission: model.belongsTo(() => Permission, { mappedBy: 'role_pages' }),
});

export default RolePagePermission;
