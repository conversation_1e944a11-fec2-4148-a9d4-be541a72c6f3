import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import {
  ErrorResponseModel,
  GoogleCustomerDetailsModel,
} from "../../../../types/responses/customResponse";
import { validateGetGoogleCustomerDetailsRequest } from "../../../../validators/google/getGoogleCustomerDetailsValidator";
import { getGoogleCustomerDetailsService } from "../../../../services/googleCustomer/getGoogleCustomerDetailsService";

/**
 * @openapi
 * /v1/googleCustomer/getGoogleCustomerDetails:
 *   get:
 *     summary: Get Google Customer Details
 *     description: Retrieves Google customer details by customer ID (custId)
 *     tags:
 *       - GoogleCustomer
 *     parameters:
 *       - in: query
 *         name: custId
 *         required: true
 *         schema:
 *           type: string
 *         description: Magento Customer ID
 *         example: "customer123"
 *     responses:
 *       200:
 *         description: Successfully retrieved Google customer details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 ChannelPartnerId:
 *                   type: string
 *                   example: "C02a7qnez"
 *                 CloudIdentityId:
 *                   type: string
 *                   example: "C04g3z3oh"
 *                 Domain:
 *                   type: string
 *                   example: "goog-test.rilcsp.in.18Jul2025-1.com"
 *                 CustomerType:
 *                   type: string
 *                   example: "DOMAIN"
 *                 CompanyName:
 *                   type: string
 *                   example: "Google 3 GWF P0T0"
 *                 FirstName:
 *                   type: string
 *                   example: "Google 3 GWF P0T0"
 *                 LastName:
 *                   type: string
 *                   example: "fg"
 *                 Email:
 *                   type: string
 *                   example: "<EMAIL>"
 *                 AlternateEmail:
 *                   type: string
 *                   example: "<EMAIL>"
 *                 RegionCode:
 *                   type: string
 *                   example: "IN"
 *                 AdministrativeArea:
 *                   type: string
 *                   example: "Maharashtra"
 *                 Locality:
 *                   type: string
 *                   example: "Pune"
 *                 AddressLine1:
 *                   type: string
 *                   example: "gdfgfd"
 *                 AddressLine2:
 *                   type: string
 *                   nullable: true
 *                   example: null
 *                 AddressLine3:
 *                   type: string
 *                   nullable: true
 *                   example: null
 *                 PostalCode:
 *                   type: string
 *                   example: "411057"
 *                 InstituteType:
 *                   type: string
 *                   nullable: true
 *                   example: null
 *                 InstituteSize:
 *                   type: string
 *                   nullable: true
 *                   example: null
 *                 Website:
 *                   type: string
 *                   nullable: true
 *                   example: null
 *                 PhoneNumber:
 *                   type: string
 *                   example: "+917867868686"
 *       400:
 *         description: Bad request - validation errors
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: string
 *                   example: ["CustId can't be null or empty"]
 *                 statusCode:
 *                   type: integer
 *                   example: 400
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: string
 *                   example: ["An error occurred: internal service failure"]
 *                 statusCode:
 *                   type: integer
 *                   example: 500
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req);

  logger.info(
    `Entered into GetGoogleCustomerDetails API with CorrelationId: ${correlationId}`
  );

  try {
    // Extract route parameter (matching .NET [FromRoute] string custId)
    const { custId } = req.query as { custId: string };

    logger.info(
      `Entered into GetGoogleCustomerDetails API with details: custId ${custId}`
    );

    // Validate request parameters (matching .NET validation)
    const validationErrors = await validateGetGoogleCustomerDetailsRequest(
      custId
    );
    logger.info(
      `Validation errors count = ${
        validationErrors.length
      }; Errors : ${JSON.stringify(validationErrors)}`
    );

    if (validationErrors.length > 0) {
      logger.info(
        `Going to return Bad request response due to Bad query paramaters in request ${JSON.stringify(
          validationErrors
        )}`
      );

      const errorResponse: ErrorResponseModel = {
        errors: validationErrors,
        statusCode: 400,
      };

      return res.status(400).json(errorResponse);
    }

    // Call the service method (matching .NET service call)
    const response: GoogleCustomerDetailsModel =
      await getGoogleCustomerDetailsService(custId);

    logger.info(`Going to return response`);
    return res.status(200).json(response);
  } catch (exp: any) {
    logger.error(
      `Error in GetGoogleCustomerDetails API method. Message : ${exp.message}, StackTrace: ${exp.stack}`
    );

    const errorResponse: ErrorResponseModel = {
      errors: [`An error occurred: ${exp.message}`],
      statusCode: 500,
    };

    return res.status(500).json(errorResponse);
  }
}
