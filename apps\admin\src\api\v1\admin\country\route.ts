import {
  AuthenticatedMedusaRequest,
  MedusaRequest,
  MedusaResponse,
} from '@medusajs/framework';
import { ContainerRegistrationKeys, Modules } from '@medusajs/framework/utils';
import { stringConstants } from '@org/utils';
import MasterModuleService from '../../../../modules/master/service';
import axios, { HttpStatusCode } from 'axios';
import { z } from 'zod';
import { countrySchema, toDisplayName } from './validators';

type PostCountryCustomSchemaType = z.infer<typeof countrySchema>;

/**
 * @openapi
 * components:
 *  securitySchemes:
 *     bearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 * /v1/admin/country:
 *   post:
 *     summary: Create a new country
 *     tags:
 *       - Country
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - region_id
 *               - name
 *               - country_flag
 *               - num_code
 *               - iso_3
 *               - iso_2
 *               - country_code
 *             properties:
 *               region_id:
 *                 type: string
 *                 example: "reg_01JY0Q1Z9W67FY9C6X4E128GF1"
 *               name:
 *                 type: string
 *                 example: "UNITED STATES"
 *               country_flag:
 *                 type: string
 *                 example: "dummy.pdf"
 *               num_code:
 *                 type: string
 *                 example: "840"
 *               iso_3:
 *                 type: string
 *                 example: "usa"
 *               iso_2:
 *                 type: string
 *                 example: "us"
 *               country_code:
 *                 type: string
 *                 example: "+1"
 *     responses:
 *       200:
 *         description: Country created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Country created successfully.
 *       404:
 *         description: Region not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Region does not exist.
 *       409:
 *         description: Country already exists
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Country already exists.
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Internal server error. Please try again later.
 */

export async function POST(
  req: MedusaRequest<PostCountryCustomSchemaType>,
  res: MedusaResponse
): Promise<void> {
  try {
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

    const { name, region_id, iso_2, country_code, country_flag } =
      req.validatedBody;

    (req.validatedBody as any)['display_name'] = toDisplayName(name);

    const regionModuleService = req.scope.resolve(Modules.REGION);
    const masterModuleService: MasterModuleService =
      req.scope.resolve('master');
    const userModuleService: any = req.scope.resolve('cq_user');
    const link = req.scope.resolve(ContainerRegistrationKeys.LINK);

    const s3Url = process.env.S3_SERVICE_URL as string;

    try {
      await regionModuleService.retrieveRegion(region_id);
    } catch (error) {
      res.status(404).json({
        success: false,
        message: stringConstants.NOT_FOUND('Region'),
      });
      return;
    }

    const { data: checkCountry } = await query.graph({
      entity: 'country',
      fields: ['id'],
      filters: {
        iso_2,
      },
    });

    if (checkCountry?.length > 0) {
      res.status(200).json({
        success: false,
        message: stringConstants.CONFLICT('Country'),
      });
      return;
    }

    let response;

    try {
      response = await axios.post(s3Url, {
        folder_name: 'cq-country-flag',
        image_name: country_flag,
      });
    } catch (error) {
      res.status(508).json({
        success: false,
        message: stringConstants.SERVICE_UNAVAILABLE('S3'),
      });
      return;
    }

    req.validatedBody.country_flag = response.data.link;

    await masterModuleService.InsertCountry(req.validatedBody as any);

    const countryExtension = await userModuleService.createCqCountryExtensions({
      country_code,
      country_flag: response.data.link,
    });

    await link.create({
      [Modules.REGION]: { country_iso_2: iso_2 },
      cq_user: { cq_country_extension_id: countryExtension.id },
    });

    res.status(200).json({
      success: true,
      message: stringConstants.CREATED_SUCCESSFULLY('Country'),
    });
  } catch (error) {
    console.error('Error fetching phone code:', error);
    res.status(500).json({
      success: false,
      message: stringConstants.INTERNAL_SERVER_ERROR(),
    });
  }
}
