import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import MasterModuleService from "../../../../../modules/master/service"
import { StateCreateSchema, StateCreateRequest } from "./validators"
import { z } from "zod"
import { Modules } from "@medusajs/framework/utils"
import type { IRegionModuleService } from "@medusajs/framework/types"

export const POST = async (req: MedusaRequest<StateCreateRequest>, res: MedusaResponse) => {
  try {
   
    const validatedData = StateCreateSchema.parse(req.body)
    const { state_name, action, region_country_id, metadata } = validatedData

    const masterModuleService: MasterModuleService = req.scope.resolve("master")
    const regionService = req.scope.resolve(Modules.REGION) as IRegionModuleService
    let country= null
    try {
      country = await regionService.retrieveCountry(region_country_id)
    } catch{
      // If not found, Medusa throws, so we catch and handle below
    }
    if (!country) {
      return res.status(400).json({
        message: `Region country ID '${region_country_id}' does not exist`,
        error: "INVALID_REGION_COUNTRY_ID"
      })
    }

    const existingStates = await masterModuleService.listStates({
      region_country_id,
    })

    const stateExists = existingStates.some(
      (state) => state.state_name.toLowerCase() === state_name.toLowerCase()
    )

    if (stateExists) {
      return res.status(409).json({
        message: `State '${state_name}' already exists for the specified region/country`,
        error: "DUPLICATE_STATE"
      })
    }


    const state = await masterModuleService.createStates({ 
      state_name,
      action,
      region_country_id,
      metadata,
    })

    return res.status(201).json({ 
      state,
      message: "State created successfully"
    })
  } catch (err: unknown) {
    console.log("Error creating state:", err)

    if (err instanceof z.ZodError) {
      const validationErrors = err.errors.map(error => ({
        field: error.path.join('.'),
        message: error.message
      }))
      
      return res.status(400).json({
        message: "Validation failed",
        errors: validationErrors,
        error: "VALIDATION_ERROR"
      })
    }
    

    if (err instanceof Error) {
      if (err.name === "ValidationError") {
        return res.status(400).json({ 
          message: err.message,
          error: "VALIDATION_ERROR"
        })
      }
      
      if (err.message.includes("duplicate") || err.message.includes("unique")) {
        return res.status(409).json({
          message: "State already exists",
          error: "DUPLICATE_STATE"
        })
      }
    }
    
    return res.status(500).json({ 
      message: "Internal server error",
      error: "INTERNAL_SERVER_ERROR"
    })
  }
}