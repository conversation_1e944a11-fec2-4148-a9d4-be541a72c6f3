import { model } from "@medusajs/framework/utils";
import Answer from "./answer";
import AnswerType from "./answer-type";
import { ProfileInfo } from "./profile-info";

const CqQuestion = model.define("cq_question", {
	id: model.id().primaryKey(),
	question: model.text(),
	answers: model.hasMany(() => Answer, { mappedBy: "questions" }),
	order_id: model.autoincrement(),
	region_id: model.text(),
	profile_info: model.hasMany(() => ProfileInfo, { mappedBy: "questions" }),
	answer_type: model.belongsTo(() => AnswerType, { mappedBy: "questions" }),
});

export default CqQuestion;
