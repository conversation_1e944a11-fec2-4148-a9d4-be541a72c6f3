import { getStoreDetails } from "../token/microsoft/getStoreDetailsService";
import {
  MicrosoftEndpoints,
  prepareCustomerValidationHeaders,
} from "../../config/microsoftEndpoints";
import { getMethod } from "../externalService/externalEndPointService";
import { getMsToken } from "../token/microsoft/getTokenService";
import logger from "../../utils/logger";
import {
  GetCustomerByIdMaster,
  GetCustomerByIdResponse,
} from "../../types/responses/customResponse";
import { createGetCustomersFilter } from "../../types/responses/customResponse";
import { ComparableString } from "../../validators/customer/customCustomerValidator";

export async function getCustomerByIdService(
  req: any,
  type: string,
  storeId: string,
  brandId: string,
  tenantId: string
): Promise<GetCustomerByIdMaster> {
  const errors: string[] = [];
  logger.info(`Calling Get Customer By Id Service`);
  if (!type) errors.push("type is required");
  if (!storeId) errors.push("storeId is required");
  if (!brandId) errors.push("brandId is required");
  if (!tenantId) errors.push("tenantId is required");

  const getCustomerByIdMaster: GetCustomerByIdMaster = {
    GetCustomerById: {
      Status: "FAIL",
      Message: "Not Processed",
    },
  };

  logger.info(
    `Calling GetStoreDetails for storeId=${storeId}, brandId=${brandId}`
  );
  const storeData = await getStoreDetails({ storeId, brandId });
  if (ComparableString(storeData.brand) === "MICROSOFT") {
    // Log token request
    logger.info(
      `Preparing token request with storeData: ${JSON.stringify(storeData)}`
    );

    const token = await getMsToken({
      brand: storeData.brand || "",
      client_id: storeData.clientid || "",
      client_secret: storeData.clientsecret || "",
      grant_type: storeData.granttype || "",
      markValue: storeData.markvalue?.toString() || "",
      redirect_uri: storeData.redirecturi || "",
      refresh_token: storeData.token || "",
      resource: storeData.resource || "",
      store_domain: storeData.storedomain || "",
    });

    if (ComparableString(type) === "VALIDATE-ID") {
      const completeUrlWithParams =
        MicrosoftEndpoints.getCustomerByIdUrl(tenantId);

      logger.info(`Final GetCustomerById URL: ${completeUrlWithParams}`);

      const headerList = prepareCustomerValidationHeaders(token.access_token);
      logger.info(
        `Prepared HeaderList for Microsoft call: ${JSON.stringify(headerList)}`
      );
      logger.info(
        `Hitting external endpoint with URL: ${completeUrlWithParams}`
      );

      const configModule = req.scope.resolve("configModule");

      const getByIdResponse = await getMethod(
        {
          url: completeUrlWithParams,
          headers: headerList,
          isVendorHit: true,
          module: "getCustomerById",
        },
        configModule
      );

      logger.info(
        `Response from external Method getByIdResponse API: ${JSON.stringify(
          getByIdResponse
        )}`
      );
      if (getByIdResponse.isSuccessStatusCode && getByIdResponse.content) {
        // const parsed: GetCustomerByIdResponse = JSON.parse(
        //   getByIdResponse.content
        // );

        const data = JSON.parse(getByIdResponse.content);

        console.log("data----------->", data);

        const parsed: GetCustomerByIdResponse = {
          Id: data.id || "",
          CompanyProfile: {
            ...data.companyProfile,
            Domain: data.companyProfile?.domain || "",
          },
          RelationShipToPartner: data.relationshipToPartner || "",
          Links: {...data.links},
          Atrributes: {...data.attributes},
          CommerceId: data.commerceId || "",
          BillingProfile: {
            ...data.billingProfile,
          },
          AllowDelegatedAccess: data.allowDelegatedAccess || false,
          CustomDomains: [... data.customDomains],
          Code: 0,
          Description: "",
          UserCredentials: data.userCredentials || null,
        };

        if (ComparableString(parsed.Id) === ComparableString(tenantId)) {
          logger.info(`Tenant ID matched. Returning success.`);

          getCustomerByIdMaster.GetCustomerById = {
            Status: "1",
            Message: parsed.CompanyProfile?.Domain || "",
          };
        } else {
          logger.info(`Tenant ID mismatch. Returning failure.`);

          getCustomerByIdMaster.GetCustomerById = {
            Status: "0",
            Message: parsed.Description || "Tenant mismatch",
          };
        }
      } else {
        logger.info(`API call failed. Building error response.`);

        getCustomerByIdMaster.Error = {
          errors: [
            `${getByIdResponse.httpStatusCode} ${
              getByIdResponse.errorMessage || "Unknown error"
            }`,
          ],
          statusCode: 500,
        };
      }
      return getCustomerByIdMaster;
    } else if (ComparableString(type) === "VALIDATE-DOMAIN") {
      logger.info(`Entered VALIDATE-DOMAIN block to check if customer exists`);

      const filterObject = createGetCustomersFilter(tenantId);
      const filterJson = JSON.stringify(filterObject);

      logger.info(`Constructed filter for GetCustomers API: ${filterJson}`);

      const getCustomersUrl = MicrosoftEndpoints.getCustomersUrl(filterJson);

      const headerList = prepareCustomerValidationHeaders(token.access_token);
      logger.info(`Final GetCustomers URL: ${getCustomersUrl}`);
      logger.info(
        `Hitting external endpoint with URL: ${getCustomersUrl} and headers: ${JSON.stringify(
          headerList
        )}`
      );
      const configModule = req.scope.resolve("configModule");
      const getCustomersResponse = await getMethod(
        {
          url: getCustomersUrl,
          headers: headerList,
          isVendorHit: true,
          module: "getCustomerUsers",
        },
        configModule
      );

      logger.info(
        `Response from GetCustomers endpoint: ${JSON.stringify(
          getCustomersResponse
        )}`
      );

      if (
        getCustomersResponse.isSuccessStatusCode &&
        getCustomersResponse.content
      ) {
        const responseObject = JSON.parse(getCustomersResponse.content) as {
          totalCount: number;
          items: { id: string }[];
        };

        console.log("responseObject------>",responseObject);

        if (responseObject.totalCount > 0) {
          logger.info(`Customer(s) exist. Assigning values to response model`);

          getCustomerByIdMaster.GetCustomerById = {
            Status: "1",
            Message: responseObject.items[0]?.id || "",
          };
        }
      }

      return getCustomerByIdMaster;
    }
  }
  return getCustomerByIdMaster;
}

