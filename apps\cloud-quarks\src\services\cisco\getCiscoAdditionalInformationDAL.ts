import logger from "../../utils/logger";
import sql from "mssql";
import { getSqlServerConnection } from "../../utils/sqlServerClient";
import {
  CiscoAdditionalInformationResponse,
  CiscoProvisioningAttributes,
  CiscoOrderDetail
} from "../../types/responses/customResponse";

/**
 * Get Cisco Additional Information from database
 * Matches .NET CiscoDAL.GetCiscoAdditionalInformation method
 */
export async function getCiscoAdditionalInformationDAL(
  subscriptionIds: string[]
): Promise<CiscoAdditionalInformationResponse> {
  logger.info(`Entered into Cisco DAL GetCiscoAdditionalInformation method with subscriptionIds-${JSON.stringify(subscriptionIds)}`);

  const ciscoAdditionalInformation: CiscoAdditionalInformationResponse = {
    ProvisioningAttributes: [],
    CiscoOrderDetails: []
  };

  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();

    const procedure = "Usp_GetCiscoInformationForSubscriptionIds"; // Matches .NET Procedures.Usp_GetCiscoInformationForSubscriptionIds

    // Create table-valued parameter (matching .NET DataTable logic)
    const table = new sql.Table('dbo.StringList');
    table.columns.add('SubscriptionID', sql.VarChar(255));

    // Add subscription IDs to table (matching .NET: subscriptionIds.ForEach(x => table.Rows.Add(x)))
    subscriptionIds.forEach(id => {
      table.rows.add(id);
    });

    request.input("list", table);

    logger.info(`Going to hit DB with proc name : ${procedure} and parameters ${JSON.stringify({ subscriptionIds })}`);

    const result = await request.execute(procedure);

    // Read multiple result sets (matching .NET QueryMultipleAsync logic)
    logger.info(`Reading ProvisioningAttributes from response`);
    ciscoAdditionalInformation.ProvisioningAttributes = (Array.isArray(result.recordsets) ? result.recordsets[0] : []) || [];
    logger.info(`ProvisioningAttributes : ${JSON.stringify(ciscoAdditionalInformation.ProvisioningAttributes)}`);

    logger.info(`Reading CiscoOrderDetail from response`);
    ciscoAdditionalInformation.CiscoOrderDetails = (Array.isArray(result.recordsets) ? result.recordsets[1] : []) || [];
    logger.info(`CiscoOrderDetail : ${JSON.stringify(ciscoAdditionalInformation.CiscoOrderDetails)}`);

    logger.info(`Proc-${procedure} ran successfully, response ${JSON.stringify(ciscoAdditionalInformation)}`);
    return ciscoAdditionalInformation;

  } catch (error: any) {
    logger.error(`Error in getCiscoAdditionalInformationDAL: ${error.message}`);

    // Return empty response on error
    return ciscoAdditionalInformation;
  }
}
