import logger from "../../utils/logger";
import sql from "mssql";
import { getSqlServerConnection } from "../../utils/sqlServerClient";
import { GetSubscriptionsByCustomerIdAndMaterialIdResponse } from "../../types/responses/customResponse";

export async function getSubscriptionsByCustomerIdAndMaterialIdDAL(
  customerId: string,
  materialId: string
): Promise<GetSubscriptionsByCustomerIdAndMaterialIdResponse[]> {
  logger.info(`Entered into DAL GetSubscriptionsByCustomerIdAndMaterialId method with customerId-${customerId}, materialId-${materialId}`);

  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();

    const procedure = "spGetSubscriptionsByCustomerIdAndMaterialId";

    // Add parameters
    request.input("CustomerId", sql.Var<PERSON>har(255), customerId);
    request.input("MaterialId", sql.<PERSON>ar<PERSON><PERSON>(255), materialId);

    logger.info(`Going to hit DB with proc name : ${procedure} and parameters CustomerId: ${customerId}, MaterialId: ${materialId}`);

    // Execute stored procedure
    const result = await request.execute<GetSubscriptionsByCustomerIdAndMaterialIdResponse>(procedure);
    console.log("result---------------------->",result);
    const response = result.recordset || [];

    logger.info(`Proc ran successfully, response ${JSON.stringify(response)}`);
    return response;

  } catch (error: any) {
    logger.error(`Error in getSubscriptionsByCustomerIdAndMaterialIdDAL: ${error.message}`);
    throw error;
  }
}
