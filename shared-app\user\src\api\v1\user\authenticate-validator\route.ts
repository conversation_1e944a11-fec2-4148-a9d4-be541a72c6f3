import {
  MedusaRequest as OriginalMedusaRequest,
  MedusaResponse,
} from '@medusajs/framework/http';
import { masterDB } from '../../../../utils/master-db';
import jwt from 'jsonwebtoken';
import { stringConstants } from '@org/utils';

interface AuthContext {
  actor_id: string;
  actor_type: string;
  user_id: string;
  authenticated: boolean;
}

interface MedusaRequest extends OriginalMedusaRequest {
  auth_context?: AuthContext;
}

interface SessionData {
  token: string;
  actor_id: string;
  user_id: string;
  actor_type: string;
  created_at: string;
  expires_at: string;
}

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    let userId: string | null = null;
    let actorId: string | null = null;
    let actorType: string = 'cq_user';

    const customSession = req.cookies.custom_auth_session;
    const parsedSession = JSON.parse(customSession);

    if (req.session) {
      userId = parsedSession.actor_id;
      actorId = req.session.actorId;
      actorType = req.session.actorType || 'cq_user';
    }

    if (!userId && req.cookies?.custom_auth_session) {
      try {
        const cookieValue = decodeURIComponent(req.cookies.custom_auth_session);
        const sessionData: SessionData = JSON.parse(cookieValue);

        if (
          sessionData.expires_at &&
          new Date(sessionData.expires_at) < new Date()
        ) {
          return res.status(401).json({
            message: stringConstants.SESSION_EXPIRED(),
          });
        }

        userId = sessionData.user_id;
        actorId = sessionData.actor_id;
        actorType = sessionData.actor_type;
      } catch (cookieError) {
        console.error('Error parsing auth cookie:', cookieError);
      }
    }

    if (!userId) {
      const authHeader =
        req.headers['authorization'] || req.headers['Authorization'];

      if (
        authHeader &&
        typeof authHeader === 'string' &&
        authHeader.startsWith('Bearer ')
      ) {
        const token = authHeader.slice(7);

        try {
          const decoded: any = jwt.decode(token);
          if (decoded) {
            userId =
              decoded?.user_id ||
              decoded?.sub ||
              decoded?.app_metadata?.cq_user_id;
            actorId = decoded?.actor_id || userId;
          }
        } catch (jwtError) {
          console.error('JWT decode error:', jwtError);
        }
      }
    }

    if (!userId) {
      return res.status(401).json({
        message: stringConstants.UNAUTHORIZED(),
      });
    }

    try {
      const dbResult = await masterDB.query(
        'SELECT * FROM cq_user WHERE id = $1',
        [userId]
      );

      const user = dbResult?.rows?.[0];

      if (!user) {
        return res.status(404).json({
          message: stringConstants.USER_NOT_FOUND(userId),
        });
      }

      // Set auth context
      req.auth_context = {
        actor_id: actorId || userId,
        actor_type: actorType,
        user_id: userId,
        authenticated: true,
      };

      return res.status(200).json({
        message: stringConstants.AUTHENTICATION_SUCCESSFUL(),
        data: {
          ...req.auth_context,
        },
        authenticated: true,
      });
    } catch (dbError) {
      return res.status(500).json({
        message: stringConstants.DATABASE_ERROR(),
      });
    }
  } catch (err: any) {
    const errorMessage = err instanceof Error ? err.message : String(err);

    return res.status(500).json({
      message: stringConstants.INTERNAL_SERVER_ERROR(),
      error: errorMessage,
    });
  }
}
