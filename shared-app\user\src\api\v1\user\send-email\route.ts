import { Request, Response } from 'express';
import { MyNotificationProviderService } from '@org/ses';
import { z } from 'zod';
import { EmailPayloadSchema } from './validators';

type EmailPayloadSchemaType = z.infer<typeof EmailPayloadSchema>;

export async function POST(req: Request, res: Response): Promise<void> {
  try {
    const validatedData: EmailPayloadSchemaType = req.body;

    // Initialize SES service
    const sesService = new MyNotificationProviderService({
      region: process.env.AWS_REGION as string,
      accessKeyId: process.env.AWS_ACCESS_KEY_ID as string,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY as string,
    });

    // Send email
    const result = await sesService.sendEmail(validatedData);

    res.json({
      success: true,
      message: 'Email sent successfully',
      data: result,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      res.status(400).json({
        success: false,
        message: 'Invalid request data',
        errors: error.errors,
      });
      return;
    }

    console.error('Email sending error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send email',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
