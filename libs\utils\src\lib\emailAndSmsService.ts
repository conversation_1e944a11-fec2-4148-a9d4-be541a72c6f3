import {
    TransactionalEmailsApi,
    SendSmtpEmail,
    TransactionalEmailsApiApiKeys,
    TransactionalSMSApi,
    SendTransacSms,
    TransactionalSMSApiApiKeys,
} from '@getbrevo/brevo';

interface SendTransactionalEmailOptions {
    toEmail: string;
    templateId?: number;
    subjectTemplate?: string;
    htmlContentTemplate?: string;
    params?: Record<string, unknown>;
    senderEmail: string;
    senderName: string;
}

interface SendTransactionalSmsOptions {
    recipient: string;
    content: string;
    sender: string;
}
if (!process.env.BREVO_API_KEY) {
    throw new Error('Brevo API KEY environment variable is required');
}
const emailApiInstance = new TransactionalEmailsApi();
emailApiInstance.setApiKey(
    TransactionalEmailsApiApiKeys.apiKey,
    process.env.BREVO_API_KEY || ''
);

const smsApiInstance = new TransactionalSMSApi();
smsApiInstance.setApiKey(
    TransactionalSMSApiApiKeys.apiKey,
    process.env.BREVO_API_KEY || ''
);

export async function sendTransactionalEmail({
    toEmail,
    templateId,
    subjectTemplate,
    htmlContentTemplate,
    senderEmail,
    senderName,
}: SendTransactionalEmailOptions): Promise<any> {
    const email = new SendSmtpEmail();
    email.to = [{ email: toEmail }];
    email.sender = { email: senderEmail, name: senderName };

    if (templateId) {
        email.templateId = templateId;
    } else {
        if (!subjectTemplate || !htmlContentTemplate) {
            throw new Error('subjectTemplate and htmlContentTemplate are required if no templateId');
        }
        email.subject = subjectTemplate;
        email.htmlContent = htmlContentTemplate;
    }
    try {
        const response = await emailApiInstance.sendTransacEmail(email);
        console.log('Email sent successfully');
        return response;
    } catch (err) {
        console.error('Error sending transactional email:', err);
        throw err;
    }
}

export async function sendTransactionalSms({
    recipient,
    content,
    sender,
}: SendTransactionalSmsOptions): Promise<any> {
    const sms = new SendTransacSms();
    sms.sender = sender;
    sms.recipient = recipient;
    sms.content = content;

    try {
        const response = await smsApiInstance.sendTransacSms(sms);
        console.log('SMS sent successfully');
        return response;
    } catch (err) {
        console.error('Error sending transactional SMS:', err);
        throw err;
    }
}