import { ExecArgs } from "@medusajs/framework/types";
import { CQUser_MODULE } from "../modules/cq-user";
import CQUserService from "../modules/cq-user/service";

export default async function seedCompanyTypes({ container }: ExecArgs) {
	const userService: CQUserService = container.resolve(CQUser_MODULE);

	const predefinedCompanyTypes = [
		{ name: "Partner", key: "partner" },
		{ name: "Direct End Customer", key: "directEndCustomer" },
	];

	const existingTypes = await userService.listCompanyTypes({});
	const existingKeys = new Set(
		existingTypes.map((type: any) => type.key.toLowerCase())
	);

	const typesToCreate = predefinedCompanyTypes
		.filter((t) => {
			if (!t.key || !t.name) {
				console.warn(`Invalid company type skipped: ${JSON.stringify(t)}`);
				return false;
			}
			return !existingKeys.has(t.key.toLowerCase());
		})
		.map((t) => ({ ...t, metadata: {} }));

	if (typesToCreate.length > 0) {
		await Promise.all(
			typesToCreate.map((type) => userService.createCompanyTypes(type))
		);
	}
}
