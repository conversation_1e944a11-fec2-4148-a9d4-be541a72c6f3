import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { stringConstants } from '@org/utils';
import { COMPANY_REGISTRATION_MODULE } from '../../../../../modules/company';
import PartnerRegistrationModuleService from '../../../../../modules/company/service';

// /**
//  * @openapi
//  * /v1/company/company-registration/brand-brand-category:
//  *   get:
//  *     summary: Get Brand Category Preferences List
//  *     tags:
//  *       - Brand Category Preferences
//  *     responses:
//  *       200:
//  *         description: Successfully fetched brand category preferences
//  *         content:
//  *           application/json:
//  *             schema:
//  *               type: object
//  *               properties:
//  *                 data:
//  *                   type: array
//  *                   items:
//  *                     type: object
//  *                     # Define properties of each brand category preference object here
//  *                     properties:
//  *                       id:
//  *                         type: string
//  *                         example: bcp_12345
//  *                       brand_id:
//  *                         type: string
//  *                         example: brand_123
//  *                       category_id:
//  *                         type: string
//  *                         example: cat_456
//  *                       preference_level:
//  *                         type: integer
//  *                         example: 1
//  *                 message:
//  *                   type: string
//  *                   example: Brand Category Preferences fetched successfully
//  *       404:
//  *         description: No brand category preferences found
//  *         content:
//  *           application/json:
//  *             schema:
//  *               type: object
//  *               properties:
//  *                 data:
//  *                   type: array
//  *                   items: {}
//  *                 message:
//  *                   type: string
//  *                   example: Brand Category Preferences not found
//  *       500:
//  *         description: Internal server error
//  *         content:
//  *           application/json:
//  *             schema:
//  *               type: object
//  *               properties:
//  *                 message:
//  *                   type: string
//  *                   example: Internal server error. Please try again later.
//  */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const brandBrandCategoryService: PartnerRegistrationModuleService =
    req.scope.resolve(COMPANY_REGISTRATION_MODULE);

  try {
    const brandCategoryPreferences =
      await brandBrandCategoryService.listBrandBrandCategories();

    if (!brandCategoryPreferences || brandCategoryPreferences?.length === 0) {
      return res.status(404).json({
        data: [],
        message: stringConstants.NOT_FOUND('Brand Category Preferences'),
      });
    }

    return res.status(200).json({
      data: brandCategoryPreferences,
      message: stringConstants.FETCHED_SUCCESSFULLY(
        'Brand Category Preferences'
      ),
    });
  } catch (err: any) {
    return res.status(500).json({
      message: stringConstants.INTERNAL_SERVER_ERROR(),
    });
  }
}

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const brandBrandCategoryService: PartnerRegistrationModuleService =
    req.scope.resolve(COMPANY_REGISTRATION_MODULE);

  const brandCategoryPreferencesData = req.body as {
    brand_id?: string;
    brand_category_id?: string;
    metadata?: Record<string, unknown>;
  };

  try {
    const brandCategoryPreferences =
      await brandBrandCategoryService.createBrandBrandCategories(
        brandCategoryPreferencesData
      );

    return res.status(200).json({ data: brandCategoryPreferences });
  } catch (err: any) {
    return res
      .status(500)
      .json({ message: stringConstants.INTERNAL_SERVER_ERROR() });
  }
}
