import { z } from "zod";

// Azure Billed Usage DTO validation schema (camelCase to match request body)
export const AzureBilledUsageSchema = z.object({
  storeId: z.string().min(1, "StoreId cannot be null or empty"),
  brandId: z.string().min(1, "BrandId cannot be null or empty"),
  invoiceId: z.string().min(1, "InvoiceId cannot be null or empty"),
  attributeSet: z.string().min(1, "AttributeSet cannot be null or empty"),
});

// PascalCase version for service layer (matching .NET)
export const AzureBilledUsagePascalSchema = z.object({
  StoreId: z.string().min(1, "StoreId cannot be null or empty"),
  BrandId: z.string().min(1, "BrandId cannot be null or empty"),
  InvoiceId: z.string().min(1, "InvoiceId cannot be null or empty"),
  AttributeSet: z.string().min(1, "AttributeSet cannot be null or empty"),
});

// Request schema for service layer (only InvoiceId and AttributeSet)
export const AzureBilledUsageRequestSchema = z.object({
  InvoiceId: z.string().min(1, "InvoiceId cannot be null or empty"),
  AttributeSet: z.string().min(1, "AttributeSet cannot be null or empty"),
});

// Type definitions for TypeScript
export type AzureBilledUsageType = z.infer<typeof AzureBilledUsageSchema>;
export type AzureBilledUsagePascalType = z.infer<typeof AzureBilledUsagePascalSchema>;
export type AzureBilledUsageRequestType = z.infer<typeof AzureBilledUsageRequestSchema>;

// Validation function for store and brand (reusing existing validators)
export function validateAzureBilledUsageStoreAndBrand(storeId: string, brandId: string): string[] {
  const errors: string[] = [];
  
  if (!storeId || storeId.trim() === "") {
    errors.push("The 'StoreId' parameter is missing in the request.");
  }
  
  if (!brandId || brandId.trim() === "") {
    errors.push("The 'BrandId' parameter is missing in the request.");
  }
  
  return errors;
}
