import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import { GetNCEProductUpgradesResponse } from "../../../../types/responses/customResponse";
import {
  validateGetNCEProductUpgradesRequest,
  GetNCEProductUpgradesQueryType
} from "../../../../validators/transition/getNCEProductUpgradesValidator";
import { getNCEProductUpgradesService } from "../../../../services/transition/getNCEProductUpgradesService";

/**
 * @openapi
 * /v1/transition/getNCEProductUpgrades:
 *   get:
 *     summary: Get NCE Product Upgrade Recommendations
 *     tags:
 *       - Transition
 *     parameters:
 *       - in: query
 *         name: customerId
 *         schema:
 *           type: string
 *         required: true
 *         description: Unique customer ID
 *       - in: query
 *         name: subscriptionId
 *         schema:
 *           type: string
 *         required: true
 *         description: Subscription ID for upgrade
 *       - in: query
 *         name: storeId
 *         schema:
 *           type: integer
 *         required: true
 *         description: Store ID associated with the request
 *       - in: query
 *         name: eligibilityType
 *         schema:
 *           type: string
 *           enum: [immediate, scheduled]
 *         required: false
 *         description: Eligibility type for product upgrade
 *     responses:
 *       200:
 *         description: Successfully retrieved product upgrade options
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: boolean
 *                   example: true
 *                 Message:
 *                   type: string
 *                   example: Product upgrade recommendations retrieved successfully
 *                 Items:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       ProductId:
 *                         type: string
 *                       ProductName:
 *                         type: string
 *                       UpgradeType:
 *                         type: string
 *                       EligibleFrom:
 *                         type: string
 *                         format: date
 *                       EligibleTo:
 *                         type: string
 *                         format: date
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: string
 *                 example: "customerId is required"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: boolean
 *                   example: false
 *                 Message:
 *                   type: string
 *                   example: Internal server error
 *                 Items:
 *                   type: array
 *                   items:
 *                     type: object
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req);

  logger.info(`[${correlationId}] Entered into Transition/GetNCEProductUpgrades API with customerId-${req.query.customerId}, subscriptionId-${req.query.subscriptionId}, storeId-${req.query.storeId}`);

  try {
    // Validate the query parameters
    logger.info(`[${correlationId}] Going to validate request items`);
    const validationErrors = validateGetNCEProductUpgradesRequest(req.query);
    logger.info(`[${correlationId}] Got validation messages from validate method: ${JSON.stringify(validationErrors)}`);

    if (validationErrors.length > 0) {
      logger.info(`[${correlationId}] Going to return Badrequest with validationErrors in GetNCEProductUpgrades method ${JSON.stringify(validationErrors)}`);
      return res.status(400).json(validationErrors);
    }

    const queryParams: GetNCEProductUpgradesQueryType = req.query as GetNCEProductUpgradesQueryType;

    console.log("query params------------->",queryParams)

    // Call business logic service
    logger.info(`[${correlationId}] Going to hit BL GetNCEProductUpgrades method with customerId-${queryParams.customerId}, subscriptionId-${queryParams.subscriptionId}, storeId-${queryParams.storeId}`);
    const response = await getNCEProductUpgradesService(
      req,
      queryParams.customerId,
      queryParams.subscriptionId,
      queryParams.storeId,
      queryParams.eligibilityType || "immediate"
    );
    logger.info(`[${correlationId}] Result from BL GetNCEProductUpgrades full process is ${JSON.stringify(response)}`);

    return res.status(200).json(response);

  } catch (error: any) {
    // Log error
    logger.error(`[${correlationId}] Error in GetNCEProductUpgrades, errorMessage: ${error.message}, StackTrace: ${error.stack}`);

    // Return internal server error
    return res.status(500).json({
      Status: false,
      Message: error.message || "Internal server error",
      Items: []
    } as GetNCEProductUpgradesResponse);
  }
}