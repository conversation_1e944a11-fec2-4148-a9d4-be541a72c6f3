import { validateCloudIdentityAccountDto } from "../verifyCloudIdentityAccountsValidator";

describe("verifyCloudIdentityAccountsValidator", () => {
  it("should validate a valid CloudIdentityAccountDto", () => {
    // Arrange
    const validDto = {
      storeId: "store123",
      domain: "example.com",
      isExisting: true,
      channelPartnerId: "partner123"
    };

    // Act
    const result = validateCloudIdentityAccountDto(validDto);

    // Assert
    expect(result.isValid).toBe(true);
    expect(result.errors).toHaveLength(0);
  });

  it("should validate a valid CloudIdentityAccountDto without optional channelPartnerId", () => {
    // Arrange
    const validDto = {
      storeId: "store123",
      domain: "example.com",
      isExisting: false
    };

    // Act
    const result = validateCloudIdentityAccountDto(validDto);

    // Assert
    expect(result.isValid).toBe(true);
    expect(result.errors).toHaveLength(0);
  });

  it("should return error when storeId is missing", () => {
    // Arrange
    const invalidDto = {
      domain: "example.com",
      isExisting: true
    };

    // Act
    const result = validateCloudIdentityAccountDto(invalidDto);

    // Assert
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain("StoreId cannot be null or empty");
  });

  it("should return error when storeId is empty", () => {
    // Arrange
    const invalidDto = {
      storeId: "",
      domain: "example.com",
      isExisting: true
    };

    // Act
    const result = validateCloudIdentityAccountDto(invalidDto);

    // Assert
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain("StoreId cannot be null or empty");
  });

  it("should return error when domain is missing", () => {
    // Arrange
    const invalidDto = {
      storeId: "store123",
      isExisting: true
    };

    // Act
    const result = validateCloudIdentityAccountDto(invalidDto);

    // Assert
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain("Domain cannot be null or empty");
  });

  it("should return error when domain is empty", () => {
    // Arrange
    const invalidDto = {
      storeId: "store123",
      domain: "",
      isExisting: true
    };

    // Act
    const result = validateCloudIdentityAccountDto(invalidDto);

    // Assert
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain("Domain cannot be null or empty");
  });

  it("should return error when domain is too long", () => {
    // Arrange
    const longDomain = "a".repeat(254) + ".com"; // 258 characters, exceeds 253 limit
    const invalidDto = {
      storeId: "store123",
      domain: longDomain,
      isExisting: true
    };

    // Act
    const result = validateCloudIdentityAccountDto(invalidDto);

    // Assert
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain("Domain is not valid");
  });

  it("should return error when isExisting is missing", () => {
    // Arrange
    const invalidDto = {
      storeId: "store123",
      domain: "example.com"
    };

    // Act
    const result = validateCloudIdentityAccountDto(invalidDto);

    // Assert
    expect(result.isValid).toBe(false);
    expect(result.errors.length).toBeGreaterThan(0);
  });

  it("should return error when dto is null", () => {
    // Act
    const result = validateCloudIdentityAccountDto(null);

    // Assert
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain("Invalid data structure");
  });

  it("should return error when dto is undefined", () => {
    // Act
    const result = validateCloudIdentityAccountDto(undefined);

    // Assert
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain("Invalid data structure");
  });

  it("should return multiple errors for multiple validation failures", () => {
    // Arrange
    const invalidDto = {
      storeId: "",
      domain: "",
      // isExisting missing
    };

    // Act
    const result = validateCloudIdentityAccountDto(invalidDto);

    // Assert
    expect(result.isValid).toBe(false);
    expect(result.errors.length).toBeGreaterThan(1);
    expect(result.errors).toContain("StoreId cannot be null or empty");
    expect(result.errors).toContain("Domain cannot be null or empty");
  });
});
