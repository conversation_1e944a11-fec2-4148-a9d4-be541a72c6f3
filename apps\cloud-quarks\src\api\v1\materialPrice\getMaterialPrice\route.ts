import { MedusaRequest, MedusaResponse } from "@medusajs/framework"
import logger from "../../../../utils/logger"
import { getCorrelationId } from "../../../../config/microsoftEndpoints"
import { toPascalCase<PERSON>son } from "../../../../utils/mappers/getCustomerUsers"
import { getMaterialPriceService } from "../../../../services/materialPrice/getMaterialPriceService"

/**
 * @openapi
 * /v1/materialPrice/getMaterialPrice:
 *   get:
 *     summary: Get material pricing details
 *     tags:
 *       - MaterialPrice
 *     parameters:
 *       - in: query
 *         name: storeId
 *         schema:
 *           type: string
 *         required: true
 *         description: Store ID
 *       - in: query
 *         name: materialId
 *         schema:
 *           type: string
 *         required: true
 *         description: Material ID
 *       - in: query
 *         name: brandName
 *         schema:
 *           type: string
 *         required: true
 *         description: Brand Name
 *       - in: query
 *         name: billType
 *         schema:
 *           type: string
 *         required: true
 *         description: Bill Type
 *       - in: query
 *         name: segment
 *         schema:
 *           type: string
 *         required: true
 *         description: Segment
 *       - in: query
 *         name: term
 *         schema:
 *           type: string
 *         required: true
 *         description: Term
 *     responses:
 *       200:
 *         description: Successfully fetched material price
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                 Message:
 *                   type: string
 *                 CostPrice:
 *                   type: number
 *                 ListPrice:
 *                   type: number
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                   example: "0"
 *                 Message:
 *                   type: string
 *                   example: "Internal server error"
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req)
  const {
    storeId = "",
    materialId = "",
    brandName = "",
    billType = "",
    segment = "",
    term = ""
  } = req.query as Record<string, string>

  logger.debug(
    `Entered into GetMaterialPrice API | correlationId: ${correlationId} | storeId: ${storeId}, materialId: ${materialId}, brandName: ${brandName}, billType: ${billType}, segment: ${segment}, term: ${term}`
  )

  try {
    const result = await getMaterialPriceService(
      storeId,
      materialId,
      brandName,
      billType,
      segment,
      term
    )

    logger.debug(
      `Returning from GetMaterialPrice API | correlationId: ${correlationId} | response: ${JSON.stringify(
        result
      )}`
    )

    const pascalCaseResponse = toPascalCaseJson(result)
    return res.status(200).json(pascalCaseResponse)
  } catch (err) {
    logger.error({
      message: "GetMaterialPrice API failed",
      correlationId,
      error: (err as Error)?.message,
      stack: (err as Error)?.stack,
    })
    return res.status(500).json({
      Status: "0",
      Message: "Internal server error",
    })
  }
}
