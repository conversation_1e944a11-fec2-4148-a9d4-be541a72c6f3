import { model } from '@medusajs/framework/utils';
import CQUser from './cq_user';

const CQStore = model.define('cq_store', {
  id: model.id().primaryKey(),
  name: model.text(),
  region_country_id: model.text(),
  default: model.boolean().default(false),
  metadata: model.json().nullable(),
  created_by: model.belongsTo(() => CQUser, { mappedBy: undefined }).nullable(),
  updated_by: model.belongsTo(() => CQUser, { mappedBy: undefined }).nullable(),
  deleted_by: model.belongsTo(() => CQUser, { mappedBy: undefined }).nullable(),

  // users: model.hasMany(() => CQUser),
});

export default CQStore;
