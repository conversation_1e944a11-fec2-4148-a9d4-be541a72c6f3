import { ExecArgs } from '@medusajs/framework/types';
import { CQUser_MODULE } from '../modules/cq-user';
import CQUserService from '../modules/cq-user/service';

export default async function seedPersona({ container }: ExecArgs) {
	const guestUserService: CQUserService = container.resolve(CQUser_MODULE);

	const targetPersonas = [
		{ persona: 'Partner', key: 'partner', order_id: 1 },
		{ persona: 'Direct End Customer', key: 'directEndCustomer', order_id: 2 },
		{ persona: 'Vendor', key: 'vendor', order_id: 3 },
		{ persona: 'Redington Team', key: 'redingtonTeam', order_id: 4 },
	];

	const existingPersonas = (await guestUserService.listCqPersonas?.({})) || [];

	const existingKeys = new Set(
		existingPersonas.map((d) => d.key.toLowerCase())
	);

	const personasToCreate = targetPersonas
		.filter((d) => {
			if (!d.key || !d.persona) {
				console.warn(`Invalid Persona skipped: ${JSON.stringify(d)}`);
				return false;
			}
			return !existingKeys.has(d.key.toLowerCase());
		})
		.map((d) => ({ ...d, metadata: {} }));

	if (personasToCreate.length > 0) {
		await Promise.all(
			personasToCreate.map((val) => guestUserService.createCqPersonas(val))
		);
	}

	console.log(`Seeded ${personasToCreate.length} persona(s).`);
}
