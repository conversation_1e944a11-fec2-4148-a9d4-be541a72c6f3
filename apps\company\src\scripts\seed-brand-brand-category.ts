import { ExecArgs } from '@medusajs/framework/types';
import PartnerRegistrationModuleService from '../modules/company/service';
import { COMPANY_REGISTRATION_MODULE } from '../modules/company';

export default async function seedBrandBrandCategory({ container }: ExecArgs) {
  const brandService: PartnerRegistrationModuleService = container.resolve(
    COMPANY_REGISTRATION_MODULE
  );

  const allBrands = await brandService.listBrands({});
  const allCategories = await brandService.listBrandCategories({});

  let createdLinks = 0;

  for (const brand of allBrands) {
    for (const category of allCategories) {
      // Check if link already exists
      const existing = await brandService.listBrandBrandCategories({
        brand_id: brand.id,
        brand_category_id: category.id,
      });

      if (existing.length > 0) {
        continue;
      }

      await brandService.createBrandBrandCategories({
        brand_id: brand.id,
        brand_category_id: category.id,
        metadata: {},
      });

      createdLinks++;
    }
  }

  console.log(`Seeded ${createdLinks} brand ↔ brand_brand_category links.`);
}
