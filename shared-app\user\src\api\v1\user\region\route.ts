import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { Modules } from '@medusajs/framework/utils';
import { IRegionModuleService } from '@medusajs/framework/types';
import { stringConstants } from '@org/utils';

/**
 * @openapi
 * /v1/user/region:
 *   get:
 *     summary: Get Regions List
 *     tags:
 *       - Region
 *     responses:
 *       200:
 *         description: Successfully fetched regions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                        type: string
 *                        example: reg_01JZ2RG968V8N98E6FRTEW0AAT
 *                       name:
 *                         type: string
 *                         example: North America
 *                 message:
 *                   type: string
 *                   example: Region fetched successfully
 *       404:
 *         description: No regions found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items: {}
 *                 message:
 *                   type: string
 *                   example: Region not found
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error. Please try again later.
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const regionService: IRegionModuleService = req.scope.resolve(Modules.REGION);

  try {
    const regions = await regionService.listRegions({}, { select: ['name'] });

    if (!regions || regions?.length === 0) {
      return res.status(404).json({
        data: [],
        message: stringConstants.NOT_FOUND('Region'),
      });
    }

    return res.status(200).json({
      data: regions,
      message: stringConstants.FETCHED_SUCCESSFULLY('Region'),
    });
  } catch (err: any) {
    return res.status(500).json({});
  }
}
