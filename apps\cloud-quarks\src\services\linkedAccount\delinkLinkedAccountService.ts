import { getSqlServerConnection } from "../../utils/sqlServerClient";
import logger from "../../utils/logger";

export interface StatusResponse {
  Status: string;
  Message: string;
}

export async function delinkLinkedAccountService(
  linkedAccountId: string
): Promise<StatusResponse> {
  const pool = await getSqlServerConnection();
  const request = pool.request();

  logger.info(`Calling spDelinkLinkedAccount with linkedAccountId=${linkedAccountId}`);

  request.input("iLinkedAccountId", linkedAccountId);

  const result = await request.execute<StatusResponse>("spDelinkLinkedAccount");
  const response = result.recordset?.[0];

  logger.info(`DelinkLinkedAccount DB Response: ${JSON.stringify(response)}`);

  return response ?? { Status: "FAIL", Message: "No response from <PERSON>" };
}
