import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import { getStoreDetails } from "../token/microsoft/getStoreDetailsService";
import { getMsToken } from "../token/microsoft/getTokenService";
import { getMethod } from "../externalService/externalEndPointService";
import {
  getMicrosoftRequestHeader,
  MicrosoftEndpoints,
} from "../../config/microsoftEndpoints";
import logger from "../../utils/logger";
import { v4 as uuidv4 } from "uuid";
import { GetCustomerUsersResponse } from "../../types/responses/customResponse";

export async function getCustomerUsersService(
  req: MedusaRequest,
  brandId: string,
  storeId: string,
  tenantId: string
): Promise<GetCustomerUsersResponse> {
  const errors: string[] = [];
  logger.info(`Calling getCustomerUsersService service`);
  if (!brandId) errors.push("brandId is required");
  if (!storeId) errors.push("storeId is required");
  if (!tenantId) errors.push("tenantId is required");

  if (errors.length > 0) {
    return {
      TotalCount: 0,
      Items: [],
      Links: null,
      Attributes: null,
      Code: 400,
      Description: errors.join(", "),
      status: false
    };
  }

  logger.info(
    `Calling GetStoreDetails for storeId=${storeId}, brandId=${brandId}`
  );
  const storeData = await getStoreDetails({ storeId, brandId });

  // Brand check
  const brandComparable = (storeData.brand || "")
    .replace(/\s+/g, "")
    .toUpperCase();
  if (brandComparable !== "MICROSOFT") {
    logger.info(
      `Brand is not MICROSOFT (${brandComparable}), skipping MS call`
    );
    return {
      TotalCount: 0,
      Items: [],
      Links: null,
      Attributes: null,
      Code: -1,
      Description: "Not Processed",
      status: false
    };
  }

  // Log token request
  logger.info(
    `Preparing token request with storeData: ${JSON.stringify(storeData)}`
  );

  const token = await getMsToken({
    brand: storeData.brand || "",
    client_id: storeData.clientid || "",
    client_secret: storeData.clientsecret || "",
    grant_type: storeData.granttype || "",
    markValue: storeData.markvalue?.toString() || "",
    redirect_uri: storeData.redirecturi || "",
    refresh_token: storeData.token || "",
    resource: storeData.resource || "",
    store_domain: storeData.storedomain || "",
  });

  logger.info(`Token received: ${JSON.stringify(token)}`);

  if (!token || !token.access_token) {
    logger.error(`Token generation failed`);
    return {
      TotalCount: 0,
      Items: [],
      Links: null,
      Attributes: null,
      Code: -1,
      Description: "Get Token Error",
      status: false
    };
  }

  const completeUrlWithParams =
    MicrosoftEndpoints.getCustomerUsersUrl(tenantId);

  // ✅ Generate Microsoft headers
  const correlationId = uuidv4();
  const requestId = uuidv4();
  const headerList = getMicrosoftRequestHeader(
    token.access_token,
    correlationId,
    requestId
  );

  logger.info(
    `Prepared HeaderList for Microsoft call: ${JSON.stringify(headerList)}`
  );
  logger.info(`Hitting external endpoint with URL: ${completeUrlWithParams}`);

  const configModule = req.scope.resolve("configModule");

  const response = await getMethod(
    {
      url: completeUrlWithParams,
      headers: headerList,
      isVendorHit: true,
      module: "getCustomerUsers",
    },
    configModule
  );

  logger.info(`Response from Microsoft: ${JSON.stringify(response)}`);
  logger.info(`Deserializing response.content into GetCustomerUsersResponse`);

  const data = JSON.parse(response.content);

  const parsed: GetCustomerUsersResponse = {
    TotalCount: data.TotalCount ?? 0,
    Items: data.Items ?? [],
    Links: data.Links ?? null,
    Attributes: data.Attributes ?? null,
    Code: data.TotalCount > 0 ? 1 : 0,
    Description: data.TotalCount > 0 ? "Success" : "No users found",
    status: true
  };

  return parsed;
}
