import swagger<PERSON>SDoc from 'swagger-jsdoc';
import yaml from 'js-yaml';
import fs from 'fs';
import path from 'path';

const options = {
  definition: {
    openapi: '3.0.1',
    info: {
      title: 'CloudQuarks Service Provider',
      version: '1.0.0',
      description: 'CloudQuarks - Business validations API,s',
    },
    servers: [
      {
        url: 'http://localhost:8086',
        description: 'Local development server',
      },
    ],
  },
  apis: ['src/api/**/*.ts', 'src/api/**/*.js'], // Adjust path if needed
};

const swaggerSpec = swaggerJSDoc(options) as {
  paths: Record<string, any>
}
const yamlStr = yaml.dump(swaggerSpec);

console.log("Swagger paths generated:", Object.keys(swaggerSpec.paths))
const outputPath = path.join(__dirname, '../docs/swagger.yml');
fs.mkdirSync(path.dirname(outputPath), { recursive: true });
fs.writeFileSync(outputPath, yamlStr, 'utf8');

console.log(`✅ Generated Swagger YAML at: ${outputPath}`);