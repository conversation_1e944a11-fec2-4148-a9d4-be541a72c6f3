import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import { PostMethodWithRequestJsonBodyAndHeaders400Handle } from "../externalService/externalEndPointService";
import { MicrosoftEndpoints } from "../../config/microsoftEndpoints";
import { SubscriptionStatusResponse, ResponseError } from "../../types/responses/customResponse";
import { v4 as uuidv4 } from "uuid";

/**
 * Register Subscription Status
 * Matches .NET RegisterSubscriptionStatus method
 */
export async function registerSubscriptionStatusService(
  req: MedusaRequest,
  customerId: string,
  subscriptionId: string,
  token: string
): Promise<SubscriptionStatusResponse> {
  logger.info(`Entered into RegisterSubscriptionStatus Service method with customerId ${customerId}, subscriptionId ${subscriptionId}`);

  try {
    // Construct the URL (matching .NET: string.Format(getSubscriptionStatus, customerId, subscriptionId))
    const registerSubscriptionUrl = MicrosoftEndpoints.registerSubscriptionUrl(customerId, subscriptionId);
    
    logger.info(`Request initiated to RegisterSubscriptionStatus ${registerSubscriptionUrl}`);

    // Prepare headers (matching .NET headers)
    const headerList = {
      Authorization: `Bearer ${token}`,
      Accept: "application/json",
      "Content-Type": "application/json",
      "MS-RequestId": uuidv4(), // Matches .NET: client.DefaultRequestHeaders.Add("MS-RequestId", new Guid().ToString())
    };

    // Prepare empty body (matching .NET: JsonConvert.SerializeObject(new Object()))
    const requestBody = {};

    const configModule = req.scope.resolve("configModule");

    // Make the POST request (matching .NET: client.PostAsync(getSubscriptionStatus, null).Result)
    const response = await PostMethodWithRequestJsonBodyAndHeaders400Handle(
      {
        url: registerSubscriptionUrl,
        body: requestBody,
        headers: headerList,
        isVendorHit: true,
        module: "RegisterSubscriptionStatus",
      },
      configModule
    );

    logger.info(`Response from RegisterSubscriptionStatus ${response.content}`);

    if (response.isSuccessStatusCode) {
      // Create response object (matching .NET: new SubscriptionStatusResponse() { SubscriptionId = subscriptionId, Status = "ACCEPTED" })
      const subscriptionStatusResponse: SubscriptionStatusResponse = {
        SubscriptionId: subscriptionId,
        Status: "ACCEPTED"
      };
      
      logger.info(`Returning resp ${JSON.stringify(subscriptionStatusResponse)}`);
      return subscriptionStatusResponse;
    } else {
      // Handle error response (matching .NET: throw new Exception("Error occured while calling RegisterSubscription"))
      logger.info(`Returning Exception - Error occured while calling RegisterSubscription`);
      throw new Error("Error occured while calling RegisterSubscription");
    }
  } catch (error: any) {
    logger.error(`Error in registerSubscriptionStatusService: ${error.message}`);
    throw error;
  }
}
