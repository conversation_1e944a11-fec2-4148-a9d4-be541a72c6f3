import { model } from '@medusajs/framework/utils';
import UserColumnPreference from './user-column-preference';
import Page from './page';

const ColumnMaster = model.define('cq_column_master', {
  id: model.id().primaryKey(),
  column_name: model.text(),
  column_slug: model.text().nullable(),
  column_type: model.text().nullable(),
  sort_order: model.number().default(0),
  created_by: model.text().nullable(),
  updated_by: model.text().nullable(),
  preferences: model.hasMany(() => UserColumnPreference, {
    mappedBy: 'column_master',
  }),
  table_master: model.belongsTo(() => Page, { mappedBy: 'columns' }),
});

export default ColumnMaster;
