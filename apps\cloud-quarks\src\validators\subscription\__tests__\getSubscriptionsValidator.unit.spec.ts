import { 
  emptyValuelessParameterInRequest, 
  validateGetSubscriptionRequest 
} from "../getSubscriptionsValidator";

describe("getSubscriptionsValidator", () => {
  describe("emptyValuelessParameterInRequest", () => {
    it("should handle normal parameters without changes", () => {
      // Arrange
      const queryParams = {
        storeId: "store123",
        partnerId: "partner123",
        customerId: "customer123",
        brandId: "brand123"
      };

      // Act
      const result = emptyValuelessParameterInRequest(queryParams);

      // Assert
      expect(result).toEqual({
        storeId: "store123",
        partnerId: "partner123",
        customerId: "customer123",
        brandId: "brand123"
      });
    });

    it("should convert null values to empty strings", () => {
      // Arrange
      const queryParams = {
        storeId: null,
        partnerId: "partner123",
        customerId: null,
        brandId: "brand123"
      };

      // Act
      const result = emptyValuelessParameterInRequest(queryParams);

      // Assert
      expect(result).toEqual({
        storeId: "",
        partnerId: "partner123",
        customerId: "",
        brandId: "brand123"
      });
    });

    it("should convert undefined values to empty strings", () => {
      // Arrange
      const queryParams = {
        storeId: undefined,
        partnerId: "partner123",
        customerId: undefined,
        brandId: "brand123"
      };

      // Act
      const result = emptyValuelessParameterInRequest(queryParams);

      // Assert
      expect(result).toEqual({
        storeId: "",
        partnerId: "partner123",
        customerId: "",
        brandId: "brand123"
      });
    });

    it("should convert whitespace-only values to empty strings", () => {
      // Arrange
      const queryParams = {
        storeId: "   ",
        partnerId: "partner123",
        customerId: "\t\n",
        brandId: "brand123"
      };

      // Act
      const result = emptyValuelessParameterInRequest(queryParams);

      // Assert
      expect(result).toEqual({
        storeId: "",
        partnerId: "partner123",
        customerId: "",
        brandId: "brand123"
      });
    });

    it("should handle case-insensitive parameter names", () => {
      // Arrange
      const queryParams = {
        StoreId: null,
        PartnerId: "partner123",
        CustomerId: undefined,
        BrandId: "brand123"
      };

      // Act
      const result = emptyValuelessParameterInRequest(queryParams);

      // Assert
      expect(result).toEqual({
        storeId: "",
        partnerId: "partner123",
        customerId: "",
        brandId: "brand123"
      });
    });
  });

  describe("validateGetSubscriptionRequest", () => {
    it("should return no errors for valid parameters", () => {
      // Arrange
      const storeId = "store123";
      const partnerId = "partner123";
      const customerId = "customer123";
      const brandId = "brand123";

      // Act
      const result = validateGetSubscriptionRequest(storeId, partnerId, customerId, brandId);

      // Assert
      expect(result).toEqual([]);
    });

    it("should return error when storeId is null", () => {
      // Arrange
      const storeId = null as any;
      const partnerId = "partner123";
      const customerId = "customer123";
      const brandId = "brand123";

      // Act
      const result = validateGetSubscriptionRequest(storeId, partnerId, customerId, brandId);

      // Assert
      expect(result).toContain("The 'StoreId' URL parameter is missing in the request.");
    });

    it("should return error when partnerId is null", () => {
      // Arrange
      const storeId = "store123";
      const partnerId = null as any;
      const customerId = "customer123";
      const brandId = "brand123";

      // Act
      const result = validateGetSubscriptionRequest(storeId, partnerId, customerId, brandId);

      // Assert
      expect(result).toContain("The 'PartnerId' URL parameter is missing in the request.");
    });

    it("should return error when customerId is null", () => {
      // Arrange
      const storeId = "store123";
      const partnerId = "partner123";
      const customerId = null as any;
      const brandId = "brand123";

      // Act
      const result = validateGetSubscriptionRequest(storeId, partnerId, customerId, brandId);

      // Assert
      expect(result).toContain("The 'CustomerId' URL parameter is missing in the request.");
    });

    it("should return error when brandId is null", () => {
      // Arrange
      const storeId = "store123";
      const partnerId = "partner123";
      const customerId = "customer123";
      const brandId = null as any;

      // Act
      const result = validateGetSubscriptionRequest(storeId, partnerId, customerId, brandId);

      // Assert
      expect(result).toContain("The 'BrandId' URL parameter is missing in the request.");
    });

    it("should return multiple errors when multiple parameters are null", () => {
      // Arrange
      const storeId = null as any;
      const partnerId = null as any;
      const customerId = "customer123";
      const brandId = "brand123";

      // Act
      const result = validateGetSubscriptionRequest(storeId, partnerId, customerId, brandId);

      // Assert
      expect(result).toHaveLength(2);
      expect(result).toContain("The 'StoreId' URL parameter is missing in the request.");
      expect(result).toContain("The 'PartnerId' URL parameter is missing in the request.");
    });

    it("should return all errors when all parameters are null", () => {
      // Arrange
      const storeId = null as any;
      const partnerId = null as any;
      const customerId = null as any;
      const brandId = null as any;

      // Act
      const result = validateGetSubscriptionRequest(storeId, partnerId, customerId, brandId);

      // Assert
      expect(result).toHaveLength(4);
      expect(result).toContain("The 'StoreId' URL parameter is missing in the request.");
      expect(result).toContain("The 'PartnerId' URL parameter is missing in the request.");
      expect(result).toContain("The 'CustomerId' URL parameter is missing in the request.");
      expect(result).toContain("The 'BrandId' URL parameter is missing in the request.");
    });

    it("should allow empty strings (not null/undefined)", () => {
      // Arrange
      const storeId = "";
      const partnerId = "";
      const customerId = "";
      const brandId = "";

      // Act
      const result = validateGetSubscriptionRequest(storeId, partnerId, customerId, brandId);

      // Assert
      expect(result).toEqual([]);
    });

    it("should handle undefined parameters", () => {
      // Arrange
      const storeId = undefined as any;
      const partnerId = "partner123";
      const customerId = undefined as any;
      const brandId = "brand123";

      // Act
      const result = validateGetSubscriptionRequest(storeId, partnerId, customerId, brandId);

      // Assert
      expect(result).toHaveLength(2);
      expect(result).toContain("The 'StoreId' URL parameter is missing in the request.");
      expect(result).toContain("The 'CustomerId' URL parameter is missing in the request.");
    });
  });
});
