import { ExecArgs } from '@medusajs/framework/types';
import PartnerRegistrationModuleService from '../modules/company/service';
import { COMPANY_REGISTRATION_MODULE } from '../modules/company';
import { Modules } from '@medusajs/framework/utils';
import { masterDB } from '../utils/master-db';

export default async function seedBrandCategories({ container }: ExecArgs) {
  const brandCategoryService: PartnerRegistrationModuleService =
    container.resolve(COMPANY_REGISTRATION_MODULE);

  const targetCategories = [
    { category_name: 'Luxury' },
    { category_name: 'Electric' },
    { category_name: 'SUV' },
    { category_name: 'Sedan' },
    { category_name: 'Compact' },
    { category_name: 'Pickup Truck' },
  ];

  const targetRegionNames = ['India', 'Turkey', 'MEA'];

  // Step 1: Get all vendors and map by name
  const allBrands = await brandCategoryService.listBrands();
  const brandMap = new Map(
    allBrands.map((v: any) => [v.brand_name.toLowerCase(), v.id])
  );

  // Step 2: Get all regions and map by name
  const { rows: allRegions } = await masterDB.query('SELECT id, name FROM region');
  const regionMap = new Map(
    allRegions.map((r: any) => [r.name.toLowerCase(), r.id])
  );

  const targetRegionIds = targetRegionNames
    .map((name) => regionMap.get(name.toLowerCase()))
    .filter(Boolean);

  if (targetRegionIds.length !== targetRegionNames.length) {
    console.warn('Some region names were not found in the Region table.');
  }

  // Step 3: Fetch existing categories
  const existingCategories = await brandCategoryService.listBrandCategories({});
  const existingNames = new Set(
    existingCategories.map((c) => c.category_name.toLowerCase())
  );

  const categoriesToCreate: any[] = [];

  for (const [brandName, brandId] of brandMap.entries()) {
    for (const c of targetCategories) {
      const categoryNameLower = c.category_name?.toLowerCase();

      if (!categoryNameLower || existingNames.has(categoryNameLower)) continue;

      categoriesToCreate.push({
        category_name: c.category_name,
        brand_id: brandId,
        metadata: {},
      });
    }
  }

  let createdCount = 0;

  for (const brandCategory of categoriesToCreate) {
    if (!brandCategory) {
      continue;
    }
    const createdBrandCategory =
      await brandCategoryService.createBrandCategories(brandCategory);
    await Promise.all(
      targetRegionIds.map((region_id) =>
        brandCategoryService.createRegionBrandCategories({
          brand_category_id: createdBrandCategory.id,
          region_id,
          metadata: {},
        })
      )
    );
    createdCount++;
  }

  console.log(
    `Seeded ${createdCount} brand categor(y|ies) with region assignments.`
  );
}
