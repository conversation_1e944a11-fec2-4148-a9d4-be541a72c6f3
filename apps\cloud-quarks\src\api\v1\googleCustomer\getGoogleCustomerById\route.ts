import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import { GoogleAPIResponse } from "../../../../types/responses/customResponse";
import { validateGetGoogleCustomerByIdRequest } from "../../../../validators/google/getGoogleCustomerByIdValidator";
import { getGoogleCustomerByIdService } from "../../../../services/googleCustomer/getGoogleCustomerByIdService";

/**
 * @openapi
 * /v1/googleCustomer/getGoogleCustomerById:
 *   get:
 *     summary: Get Google Customer By ID
 *     description: Retrieves Google customer details by store ID and Google customer ID
 *     tags:
 *       - GoogleCustomer
 *     parameters:
 *       - in: query
 *         name: storeId
 *         required: true
 *         schema:
 *           type: string
 *         description: Store identifier
 *         example: "AE-EN"
 *       - in: query
 *         name: googleCustomerId
 *         required: true
 *         schema:
 *           type: string
 *         description: Google customer identifier
 *         example: "customers/C12345678"
 *     responses:
 *       200:
 *         description: Successfully retrieved Google customer details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Message:
 *                   type: string
 *                   example: "Success"
 *                 Data:
 *                   type: object
 *                   description: Google customer details
 *                 StatusCode:
 *                   type: integer
 *                   example: 200
 *                 IsError:
 *                   type: boolean
 *                   example: false
 *       400:
 *         description: Bad request - validation errors
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Message:
 *                   type: string
 *                   example: "StoreId can't be null or empty"
 *                 IsError:
 *                   type: boolean
 *                   example: true
 *                 StatusCode:
 *                   type: integer
 *                   example: 400
 *       422:
 *         description: Unprocessable entity - service error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Message:
 *                   type: string
 *                   example: "An error occurred: [error details]"
 *                 IsError:
 *                   type: boolean
 *                   example: true
 *                 StatusCode:
 *                   type: integer
 *                   example: 422
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req);

  logger.info(`Entered into GetGoogleCustomerById API with CorrelationId: ${correlationId}`);

  let response: GoogleAPIResponse = {
    Message: "Success",
    StatusCode: 200,
    IsError: false
  };

  try {
    // Extract query parameters
    const { storeId, googleCustomerId } = req.query as {
      storeId?: string;
      googleCustomerId?: string;
    };

    logger.info(`Entered into GetGoogleCustomerById API with details: storeId ${storeId}, googleCustomerId: ${googleCustomerId}`);

    // Validate request parameters (matching .NET validation)
    const validationErrors = await validateGetGoogleCustomerByIdRequest(
      storeId || "",
      googleCustomerId || ""
    );

    logger.info(`Validation errors count = ${validationErrors.length}; Errors : ${JSON.stringify(validationErrors)}`);

    if (validationErrors.length > 0) {
      response.Message = validationErrors.join(', ');
      response.IsError = true;
      response.StatusCode = 400;

      return res.status(400).json(response);
    }

    // Call the service method
    response = await getGoogleCustomerByIdService(storeId!, googleCustomerId!, req);

  } catch (exp: any) {
    logger.error(`Error in GetGoogleCustomerById API method. Message : ${exp.message}, StackTrace: ${exp.stack}`);

    response.Message = `An error occurred: ${exp.message}`;
    response.IsError = true;
    response.StatusCode = 422; // UnprocessableEntity
  }

  logger.info(`Going to return GetGoogleCustomerById API response with StatusCode: ${response.StatusCode}`);

  // Return ContentResult equivalent (matching .NET return pattern)
  return res.status(response.StatusCode).json(response);
}