import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { CQUser_MODULE } from '@org/guest-user';
import guestUserService from '@org/guest-user-service';

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const guestUserService: guestUserService = req.scope.resolve(CQUser_MODULE);
    const query = req.query as Record<string, string>;

    const {
      sort_by = 'id',
      sort_order = 'DESC',
      limit = '25',
      offset = '0',
    } = query;

    // Allowed fields for filtering and sorting
    const allowedFilters = [
      'id',
      'first_name',
      'last_name',
      'email',
      'company_name',
      'country_id',
      'country_code',
      'user_type',
      'created_by',
      'is_active',
      'source',
    ];

    const allowedSortFields = [...allowedFilters]; // you can expand if needed

    // Dynamically build filters from query
    const filters: Record<string, any> = {};
    for (const key of allowedFilters) {
      if (query[key] !== undefined) {
        filters[key] = key === 'is_active' ? query[key] === 'true' : query[key];
      }
    }

    // Validate sort field and order
    const validatedSortBy = allowedSortFields.includes(sort_by)
      ? sort_by
      : 'id';
    const normalizedSortOrder =
      sort_order.toUpperCase() === 'DESC' ? 'DESC' : 'ASC';

    const order: Record<string, 'ASC' | 'DESC'> = {
      [validatedSortBy]: normalizedSortOrder,
    };

    const findConfig = {
      order,
      skip: parseInt(offset, 10),
      take: parseInt(limit, 10),
    };

    const [data, count] = await guestUserService.listAndCountCqUsers(
      filters,
      findConfig
    );

    const sanitizedData = data.map((user) => {
      const {
        designation_id,
        designation,
        mobile_number,
        redington_sales_reference,
        profile_pic_file_name,
        is_active,
        registered_country_id,
        is_password_set,
        has_sales_reference,
        country_code,
        source,
        // created_by,
        updated_at,
        deleted_at,
        ...rest
      } = user;
      return rest;
    });

    return res.status(200).json({
      data: sanitizedData,
      count,
      limit: findConfig.take,
      offset: findConfig.skip,
    });
  } catch (err) {
    console.error('Admin GET /guest-user error:', err);
    const error = err instanceof Error ? err.message : 'Unknown error';
    return res.status(500).json({ error });
  }
}