import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import { GetSubscriptionResponse } from "../../../../types/responses/customResponse";
import {
  validateRenewalRequestMSDto,
  RenewalRequestMSDto
} from "../../../../validators/renewal/renewalRequestMSValidator";
import { createRenewalRequestMC } from "../../../../services/renewal/createRenewalRequestMSService";
import { RenewalRequestMSMasterModel } from "../../../../services/renewal/patchSubscriptionAsync";
import { ConstantValue, Message, appConfig } from "../../../../validators/renewal/customRenewalValidator";

/**
 * Get Header List For Renewal
 * Matches .NET GetHeaderListForRenewal method
 */
function getHeaderListForRenewal(req: MedusaRequest): Record<string, string> {
  const headerList: Record<string, string> = {};

  // Extract headers from request (matching .NET Request.Headers pattern)
  const correlationId = req.headers["correlationid"] as string;
  const msRequestId = req.headers["ms-requestid"] as string;
  const msCorrelationId = req.headers["ms-correlationid"] as string;
  const xRegion = req.headers["x-region"] as string;

  if (correlationId) headerList["correlationId"] = correlationId;
  if (msRequestId) headerList["MS-RequestId"] = msRequestId;
  if (msCorrelationId) headerList["MS-CorrelationId"] = msCorrelationId;
  if (xRegion) headerList["x-region"] = xRegion;

  return headerList;
}

/**
 * @openapi
 * /v1/renewal/createRenewalRequestMS:
 *   post:
 *     summary: Create renewal request for Microsoft subscriptions
 *     tags:
 *       - Renewal
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - skey
 *               - brandId
 *               - storeId
 *             properties:
 *               skey:
 *                 type: integer
 *                 description: Subscription key identifier
 *                 example: 12345
 *               brandId:
 *                 type: string
 *                 description: Brand identifier
 *                 example: "1002"
 *               storeId:
 *                 type: string
 *                 description: Store identifier
 *                 example: "STORE001"
 *     responses:
 *       200:
 *         description: Renewal request created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                   example: "PASS"
 *                 MSResponse:
 *                   type: object
 *                   description: Microsoft subscription response
 *                 MSErrorResponse:
 *                   type: object
 *                   description: Microsoft error response if any
 *                 ErrorMessage:
 *                   type: string
 *                   description: Error message if any
 *       400:
 *         description: Bad request - validation errors
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                   example: "FAIL"
 *                 ErrorMessage:
 *                   type: string
 *                   example: "Validation errors"
 *       500:
 *         description: Internal server error
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req);

  logger.info(`[${correlationId}] Entered into CreateRenewalRequestMC API with details ${JSON.stringify(req.body)}`);

  try {
    const renewalRequestMSDto = req.body as RenewalRequestMSDto;

    // Validate if renewalRequestMSDto is null (matching .NET null check)
    logger.info("Going to validate the renewal dto object");
    if (!renewalRequestMSDto) {
      const response: GetSubscriptionResponse = {
        Status: ConstantValue.FAIL,
        ErrorMessage: Message.INVALID_MODEL
      };
      return res.status(400).json(response);
    }

    // Validate the renewal MS DTO object (matching .NET RenewalRequestMSValidator)
    logger.info("Going to validate the renewal ms dto object");
    const validationErrors = validateRenewalRequestMSDto(renewalRequestMSDto);
    logger.info(`Validations error count on renewalms dto object is ${validationErrors.length}`);

    if (validationErrors.length > 0) {
      logger.info("Going to extract errors from validationResult object");
      const validationError = validationErrors.join(",");
      logger.info(`validation errors are ${validationError}`);

      return res.status(400).json({
        Status: ConstantValue.FAIL,
        ErrorMessage: validationError
      } as GetSubscriptionResponse);
    } else {
      logger.info(`Going to map renewal dto to model. renewalDto: ${JSON.stringify(renewalRequestMSDto)}`);

      // Get header list for renewal (matching .NET GetHeaderListForRenewal)
      const headerList = getHeaderListForRenewal(req);

      // Map to master model (matching .NET AutoMapper functionality)
      const renewalMasterModel: RenewalRequestMSMasterModel = {
        Skey: renewalRequestMSDto.skey,
        BrandId: renewalRequestMSDto.brandId,
        StoreId: renewalRequestMSDto.storeId,
        SSBrandId: appConfig.Brands.SSBrandId.toString(), // Matching .NET configuration
        HeaderList: headerList
      };

      logger.info(`Renewal model after mapping : ${JSON.stringify(renewalMasterModel)}`);

      // Call business logic (matching .NET _renewalFactory.CreateRenewalRequestMC)
      logger.info(`Going to hit BL UpdateRenewalDataMS method with details : ${JSON.stringify(renewalMasterModel)}`);
      const response = await createRenewalRequestMC(req, renewalMasterModel);

      return res.status(200).json(response);
    }

  } catch (error: any) {
    logger.error(`[${correlationId}] Error in CreateRenewalRequestMS: ${error.message}`);

    const errorResponse: GetSubscriptionResponse = {
      Status: ConstantValue.FAIL,
      ErrorMessage: error.message || "An error occurred while processing the renewal request"
    };

    return res.status(500).json(errorResponse);
  }
}