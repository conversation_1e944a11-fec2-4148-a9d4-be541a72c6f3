import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { formatPagination, stringConstants } from '@org/utils';
import { COMPANY_REGISTRATION_MODULE } from '../../../../../modules/company';
import CompanyRegistrationModuleService from '../../../../../modules/company/service';

/**
 * @openapi
 * /v1/company/company-registration/document:
 *   get:
 *     summary: Get a list of company registration documents
 *     tags:
 *       - Company Registration
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           example: 1
 *         required: false
 *         description: Optional page number for pagination (default is 1)
 *       - in: query
 *         name: page_size
 *         schema:
 *           type: integer
 *           example: 10
 *         required: false
 *         description: Optional number of results per page (default is 10)
 *     responses:
 *       200:
 *         description: List of documents fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         example: "01JYG9TF3JCW4DF9ESGJQHKW2V"
 *                       document_link:
 *                         type: string
 *                         example: "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                       example: 1
 *                     pageSize:
 *                       type: integer
 *                       example: 10
 *                     totalItems:
 *                       type: integer
 *                       example: 2
 *                     totalPages:
 *                       type: integer
 *                       example: 1
 *                     hasNextPage:
 *                       type: boolean
 *                       example: false
 *                     hasPrevPage:
 *                       type: boolean
 *                       example: false
 *                 message:
 *                   type: string
 *                   example: Document fetched successfully
 *       404:
 *         description: No documents found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items: {}
 *                 message:
 *                   type: string
 *                   example: Documents not found
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error
 *                 error:
 *                   type: string
 *                   example: Internal server error. Please try again later.
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
	try {
		const documentService: CompanyRegistrationModuleService = req.scope.resolve(
			COMPANY_REGISTRATION_MODULE
		);

		let page = parseInt(req.query.page as string, 10);
		let pageSize = parseInt(req.query.page_size as string, 10);

		if (isNaN(page) || page < 1) page = 1;
		if (isNaN(pageSize) || pageSize < 1) pageSize = 10;

		const skip = (page - 1) * pageSize;

		const [documents, count] =
			await documentService.listAndCountCompanyDocuments();

		const filteredDocuments = documents.map((doc) => ({
			id: doc.id,
			document_link: doc.document_uploaded_link,
		}));

		const totalItems = count ?? 0;

		return res.status(200).json({
			data: filteredDocuments,
			pagination: formatPagination({ page, pageSize, totalItems }),
			message: stringConstants.FETCHED_SUCCESSFULLY('Document'),
		});
	} catch (err) {
		return res.status(500).json({
			message: stringConstants.INTERNAL_SERVER_ERROR(),
		});
	}
}
