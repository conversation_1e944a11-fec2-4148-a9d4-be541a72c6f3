import AWS from 'aws-sdk';
import * as dotenv from 'dotenv';
import type { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { stringConstants } from '@org/utils';
dotenv.config();

// Configure AWS S3
const s3 = new AWS.S3({
  region: process.env.S3_REGION || 'us-east-1',
  accessKeyId: process.env.S3_ACCESS_KEY_ID,
  secretAccessKey: process.env.S3_SECRET_ACCESS_KEY,
});

interface S3TransferOptions {
  folder_name: string;
  image_name: string;
  source_bucket?: string;
  destination_bucket?: string;
  delete_source?: boolean;
}

/**
 * POST endpoint for S3 file transfer
 * Endpoint: POST /s3
 *
 * Request body:
 * {
 *   "folder_name": "documents",
 *   "image_name": "example.pdf",
 *   "source_bucket": "optional-source-bucket",
 *   "destination_bucket": "optional-destination-bucket",
 *   "delete_source": false
 * }
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  console.log('Starting S3 transfer POST endpoint...');

  try {
    // Extract and validate request body
    const { folder_name, image_name, delete_source } =
      req.body as S3TransferOptions;

    // Validate required parameters
    if (!folder_name || !image_name) {
      return res.status(400).json({
        success: false,
        message:
          'Missing required parameters: folder_name and image_name are required',
        error: 'MISSING_PARAMETERS',
      });
    }

    // Default bucket names - you can set these as environment variables
    const s3Url = process.env.S3_FILE_URL;
    const sourceBucket = process.env.S3_BUCKET_TEMP || '';
    const destinationBucket = process.env.S3_BUCKET || '';

    // Construct the S3 object keys
    const sourceKey = `${folder_name}/${image_name}`;

    const [name, ext] = image_name.split(/\.(?=[^\.]+$)/);
    const suffix = Math.floor(Math.random() * 1000);
    const newImageName = `${name}${suffix}.${ext}`;

    const destinationKey = `${folder_name}/${newImageName}`;

    // Check if source object exists
    try {
      await s3
        .headObject({
          Bucket: sourceBucket,
          Key: sourceKey,
        })
        .promise();
    } catch (headError: any) {
      if (headError.code === 'NotFound') {
        return res.status(404).json({
          success: false,
          message: `Source file not found: ${sourceBucket}/${sourceKey}`,
          error: 'FILE_NOT_FOUND',
        });
      }
      throw headError;
    }

    // Copy object from source to destination bucket
    const copyParams = {
      Bucket: destinationBucket,
      CopySource: `${sourceBucket}/${sourceKey}`,
      Key: destinationKey,
      MetadataDirective: 'COPY' as const,
    };

    const copyResult = await s3.copyObject(copyParams).promise();

    // Delete source file if requested (move operation)
    const shouldDeleteSource =
      delete_source !== undefined ? delete_source : true;
    if (shouldDeleteSource) {
      await s3
        .deleteObject({
          Bucket: sourceBucket,
          Key: sourceKey,
        })
        .promise();
      console.log(
        `Successfully deleted source file: ${sourceBucket}/${sourceKey}`
      );
    }

    // Return success response
    return res.status(200).json({
      link: `${destinationKey}`,
    });
  } catch (err: any) {
    console.error('Error during partner registration:', {
      message: err?.message,
      stack: err?.stack,
    });

    return res.status(500).json({
      message: stringConstants.INTERNAL_SERVER_ERROR(),
    });
  }
}
