import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import {
  ErrorResponseModel
} from "../../../../types/responses/customResponse";
import {
  validateGetSubscriptionByIdRequest,
  GetSubscriptionByIdQueryType
} from "../../../../validators/subscription/getSubscriptionByIdValidator";
import { getStoreDetails } from "../../../../services/token/microsoft/getStoreDetailsService";
import { getMsToken } from "../../../../services/token/microsoft/getTokenService";
import { getSubscriptionById } from "../../../../services/entitlement/subscriptionService";

/**
 * GET /v1/subscription/getSubscriptionById
 * Get Subscription By Id API - matches .NET GetSubscriptionById controller method
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req);

  logger.info(`[${correlationId}] Entered into GetSubscriptionById API with customerId-${req.query.customerId},subscriptionId-${req.query.subscriptionId},storeId-${req.query.storeId},brandId-${req.query.brandId} and going to call GetSubscriptionById service method`);

  try {
    // Validate the query parameters
    const validationErrors = validateGetSubscriptionByIdRequest(req.query);

    if (validationErrors.length > 0) {
      logger.error(`[${correlationId}] Validation failed: ${JSON.stringify(validationErrors)}`);

      const errorResponse: ErrorResponseModel = {
        errors: validationErrors,
        statusCode: 400,
      };

      return res.status(400).json(errorResponse);
    }

    const queryParams: GetSubscriptionByIdQueryType = req.query as GetSubscriptionByIdQueryType;

    logger.info(`[${correlationId}] Query parameter validation passed, proceeding with subscription retrieval`);

    // Get store details (matching .NET: TokenRequest tokenRequest = _storeFactory.GetStoreDetails(storeId, brandId))
    const storeData = await getStoreDetails({
      storeId: queryParams.storeId,
      brandId: queryParams.brandId
    });

    logger.info(`[${correlationId}] Store details retrieved successfully`);

    // Get token from Microsoft (matching .NET: var token = BCommon.GetToken(tokenRequest, getToken))
    const token = await getMsToken({
      brand: storeData.brand || "",
      client_id: storeData.clientid || "",
      client_secret: storeData.clientsecret || "",
      grant_type: storeData.granttype || "",
      markValue: storeData.markvalue?.toString() || "0",
      redirect_uri: storeData.redirecturi || "http://localhost",
      refresh_token: storeData.token || "",
      resource: storeData.resource || "",
      store_domain: storeData.storedomain || "",
    });

    if (!token || !token.access_token) {
      logger.error(`[${correlationId}] Token generation failed`);
      return res.status(500).json(null);
    }

    logger.info(`[${correlationId}] Token generated successfully, calling GetSubscriptionById service`);

    // Call GetSubscriptionById service (matching .NET: return _subscriptionFactory.GetSubscriptionById(customerId, subscriptionId, token))
    const subscriptionResponse = await getSubscriptionById(
      req,
      queryParams.customerId,
      queryParams.subscriptionId,
      token.access_token
    );

    logger.info(`[${correlationId}] GetSubscriptionById service completed successfully: ${JSON.stringify(subscriptionResponse)}`);

    return res.status(200).json(subscriptionResponse);

  } catch (error: any) {
    // Log error (matching .NET exception handling)
    logger.error(`[${correlationId}] Error in GetSubscriptionById|ErrorDetail, errorMessage: ${error.message}, StackTrace: ${error.stack}`);

    // Return null (matching .NET: return null)
    return res.status(500).json(null);
  }
}