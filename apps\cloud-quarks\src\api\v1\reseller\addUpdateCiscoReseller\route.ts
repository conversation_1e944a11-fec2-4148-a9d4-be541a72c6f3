import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import { AddUpdateCiscoResellerValidator, CiscoConstants } from "../../../../validators/reseller/addUpdateCiscoResellerValidator";
import { addUpdateCiscoResellerService } from "../../../../services/reseller/addUpdateCiscoResellerService";
import { ResellerDto } from "../../../../validators/reseller/addUpdateCiscoResellerValidator";

/**
 * @openapi
 * /v1/reseller/addUpdateCiscoReseller:
 *   post:
 *     summary: Add or update a Cisco Reseller
 *     description: This API adds a new reseller or updates an existing one based on the input.
 *     tags:
 *       - Reseller
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - storeId
 *               - partnerId
 *               - partyName
 *               - addressLine1
 *               - city
 *               - state
 *               - postalCode
 *               - country
 *               - businessContactName
 *               - businessContactEmail
 *               - businessContactNumber
 *               - accountNumber
 *               - isActive
 *             properties:
 *               storeId:
 *                 type: string
 *               partnerId:
 *                 type: string
 *               partyName:
 *                 type: string
 *               addressLine1:
 *                 type: string
 *               addressLine2:
 *                 type: string
 *               city:
 *                 type: string
 *               state:
 *                 type: string
 *               postalCode:
 *                 type: string
 *               country:
 *                 type: string
 *               businessContactName:
 *                 type: string
 *               businessContactEmail:
 *                 type: string
 *               businessContactNumber:
 *                 type: string
 *               accountNumber:
 *                 type: string
 *               isActive:
 *                 type: boolean
 *           example:
 *             storeId: "1234"
 *             partnerId: "5678"
 *             partyName: "Cisco Partner"
 *             addressLine1: "123 Tech Park"
 *             addressLine2: "Suite 456"
 *             city: "San Jose"
 *             state: "CA"
 *             postalCode: "95134"
 *             country: "USA"
 *             businessContactName: "John Doe"
 *             businessContactEmail: "<EMAIL>"
 *             businessContactNumber: "+1-408-555-1212"
 *             accountNumber: "ACCT123456"
 *             isActive: true
 *     responses:
 *       201:
 *         description: Reseller created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                 Message:
 *                   type: string
 *                 ActionType:
 *                   type: string
 *       200:
 *         description: Reseller updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                 Message:
 *                   type: string
 *                 ActionType:
 *                   type: string
 *       400:
 *         description: Validation failed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: FAIL
 *                 message:
 *                   type: string
 *                   example: "Validation error details"
 *       422:
 *         description: Business logic failure
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                   example: FAIL
 *                 Message:
 *                   type: string
 *                   example: "Unable to insert/update reseller"
 *       500:
 *         description: Internal server error
 */

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req);

  logger.info(
    `Entered into AddUpdateCiscoReseller API | CorrelationId: ${correlationId} | Request: ${JSON.stringify(
      req.body
    )}`
  );

  const validation = AddUpdateCiscoResellerValidator.safeParse(req.body);

  if (!validation.success) {
    const errors = validation.error.errors.map((e) => e.message).join(", ");
    logger.warn(
      `Validation failed | CorrelationId: ${correlationId} | Errors: ${errors}`
    );
    return res.status(400).json({
      status: "FAIL",
      message: errors,
    });
  }

  const input = validation.data;

  const resellerDto: ResellerDto = {
    StoreId: input.storeId,
    PartnerId: input.partnerId,
    PartyName: input.partyName,
    AddressLine1: input.addressLine1,
    AddressLine2: input.addressLine2,
    City: input.city,
    State: input.state,
    PostalCode: input.postalCode,
    Country: input.country,
    BusinessContactName: input.businessContactName,
    BusinessContactEmail: input.businessContactEmail,
    BusinessContactNumber: input.businessContactNumber,
    AccountNumber: input.accountNumber,
    IsActive: input.isActive,
  };

  const result = await addUpdateCiscoResellerService(
    resellerDto,
    correlationId
  );

  logger.info(`Result from Reseller BL is ${JSON.stringify(result)}`);

  if (
    result?.Status?.toString().trim().toUpperCase() ===
    CiscoConstants.StatusPass.toUpperCase()
  ) {
    if (
      result?.ActionType?.toString().trim().toUpperCase() ===
      CiscoConstants.DbActionInsert.toUpperCase()
    ) {
      logger.info(
        `Going to return created status with result : ${JSON.stringify(result)}`
      );
      return res.status(201).json(result);
    }

    if (
      result?.ActionType?.toString().trim().toUpperCase() ===
      CiscoConstants.DbActionUpdate.toUpperCase()
    ) {
      logger.info(
        `Going to return ok status with result : ${JSON.stringify(result)}`
      );
      return res.status(200).json(result);
    }
  }

  if (
    result?.Status?.toString().trim().toUpperCase() ===
    CiscoConstants.StatusFail.toUpperCase()
  ) {
    logger.info(
      `Going to return UnprocessableEntity status from controller with result : ${JSON.stringify(
        result
      )}`
    );
    return res.status(422).json(result);
  }

  logger.info(`Going to return OK status from controller`);
  return res.status(200).json(result);
};
