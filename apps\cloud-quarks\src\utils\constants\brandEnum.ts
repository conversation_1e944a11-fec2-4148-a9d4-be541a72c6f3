// Brand Enum (matching .NET BrandEnum)
export enum BrandEnum {
  CSP = 1,
  Azure = 2,
  AzureRI = 12,
  Subscription = 13,
  Perpetual = 14,
  AzurePlan = 15,
  NCE = 16,
  MarketPlace = 23,
}

// Subscription Status Enum
export enum SubStatus {
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE",
  SUSPENDED = "SUSPENDED",
  CANCELLED = "CANCELLED",
}

// Helper function for case-insensitive string comparison
export function comparableString(str: string | null | undefined): string {
  return (str || "").trim().toUpperCase();
}
