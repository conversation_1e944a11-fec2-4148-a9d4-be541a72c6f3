import { Migration } from '@mikro-orm/migrations';

export class Migration20250710132951 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table if exists "cq_user" drop constraint if exists "cq_user_source_check";`);

    this.addSql(`alter table if exists "cq_country_extension" add column if not exists "is_custom" boolean not null default true;`);

    this.addSql(`alter table if exists "cq_user" alter column "source" drop default;`);
    this.addSql(`alter table if exists "cq_user" alter column "source" type text using ("source"::text);`);
    this.addSql(`alter table if exists "cq_user" alter column "region_id" type text using ("region_id"::text);`);
    this.addSql(`alter table if exists "cq_user" alter column "region_id" set not null;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table if exists "cq_country_extension" drop column if exists "is_custom";`);

    this.addSql(`alter table if exists "cq_user" alter column "region_id" type text using ("region_id"::text);`);
    this.addSql(`alter table if exists "cq_user" alter column "region_id" drop not null;`);
    this.addSql(`alter table if exists "cq_user" add constraint "cq_user_source_check" check("source" in ('Partner', 'Direct End Customer', 'Vendor', 'Redington Admin'));`);
  }

}
