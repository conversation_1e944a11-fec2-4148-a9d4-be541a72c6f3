import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import {
  getPayerAccountIdsByRequestId,
  createAwsPayerAccountListFromRecords,
  extractPayerAccountIds
} from "./pullCsvInvoicesBackgroundDAL";

/**
 * Pull CSV Invoices In Background Service
 * Matches .NET AwsBackgroundFactory.PullCsvInvoicesInBackground method
 */
export async function pullCsvInvoicesInBackgroundService(
  req: MedusaRequest,
  requestId: string,
  username: string,
  month: number,
  year: number,
  storeKeyInput: string
): Promise<string> {
  logger.info(`Entered PullCsvInvoicesInBackground method with request:${requestId}, username: ${username}, month:${month}, year:${year}, storeKeyInput:${storeKeyInput}`);

  let requestStatus = "FAILED";
  let UserName = username;

  if (!username || username.trim() === "") {
    logger.info("Username is empty, using default");
  } else {
    UserName = username;
  }

  try {
    logger.info(`Going to hit GetPayerAccountIdsByRequestId with requestid:${requestId}, month:${month}, year:${year}`);

    // Get payer account data from database (matching .NET DataTable dt = GetPayerAccountIdsByRequestId)
    const payerAccountRecords = await getPayerAccountIdsByRequestId(requestId, month, year);
    logger.info(`Payer accounts datatable is ${JSON.stringify(payerAccountRecords)}`);

    // Convert to list and extract payer account IDs (matching .NET Utility.CreateListFromTable<AwsPayerAccountRequest>(dt).Select(k => k.PayerAccountId).ToList())
    const awsPayerAccountRequests = createAwsPayerAccountListFromRecords(payerAccountRecords);
    const awsPayerAccountIds = extractPayerAccountIds(awsPayerAccountRequests);
    logger.info(`awsPayerAccountIds in list format is ${JSON.stringify(awsPayerAccountIds)}`);

    // Get token configuration (matching .NET _configuration.GetSection("AppSettings:URL:GetToken")?.Value)
    const configModule = req.scope.resolve("configModule");
    const getToken = configModule?.projectConfig?.getTokenUrl || "";

    // Fire background task (matching .NET Task.Run)
    logger.info(`Number of payers is ${awsPayerAccountIds.length}`);
    logger.info(`Going to hit PullAwsInvoices method with Year:${year}, Month:${month}, RequestId:${requestId}, AWSPayerAccountIds:${JSON.stringify(awsPayerAccountIds)}, storeKeyInput:${storeKeyInput}`);
    
    if (awsPayerAccountIds.length > 0) {
      // Start background task (matching .NET Task.Run with delay)
      setImmediate(async () => {
        try {
          // Add delay (matching .NET await Task.Delay(TimeSpan.FromSeconds(4)))
          await new Promise(resolve => setTimeout(resolve, 4000));
          
          // Call PullAwsInvoices (this would be implemented separately)
          await pullAwsInvoices(awsPayerAccountIds, year, month, requestId, storeKeyInput);
        } catch (error) {
          logger.error(`Error in background task: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      });
    }

    // Comment out token generation as requested
    // string token = BCommon.GetToken(tokenRequest, getToken);

    logger.info(`Going to make the return status as SUBMITTED`);
    requestStatus = "SUBMITTED";

  } catch (error: any) {
    logger.error(`Got error in PullCsvInvoicesInBackground method with details, RequestId:${requestId}, Username:${username}, Year:${year}, Month:${month}, Message:${error.message}, Stacktrace:${error.stack}`);
    logger.info(`Going to return FAILED`);
    return "FAILED";
  }

  logger.info(`Going to return status as ${requestStatus}`);
  return requestStatus;
}

/**
 * Pull AWS Invoices (placeholder implementation)
 * This would match the .NET PullAwsInvoices method
 */
async function pullAwsInvoices(
  awsPayerAccountIds: string[],
  year: number,
  month: number,
  requestId: string,
  storeKeyInput: string
): Promise<void> {
  logger.info(`PullAwsInvoices called with payerAccountIds:${JSON.stringify(awsPayerAccountIds)}, year:${year}, month:${month}, requestId:${requestId}, storeKeyInput:${storeKeyInput}`);
  
  // TODO: Implement the actual AWS invoice pulling logic
  // This would match the .NET PullAwsInvoices implementation
  logger.info("PullAwsInvoices implementation pending");
}
