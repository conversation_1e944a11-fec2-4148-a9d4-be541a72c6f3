import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import jwt from "jsonwebtoken";
import crypto from "crypto";
import fs from "fs";
import axios from "axios";
import path from "path";
import {
  DocuSignOAuthToken,
  DocuSignTokenServiceResponse
} from "../../types/responses/customResponse";

// DocuSign OAuth scopes
const DOCUSIGN_SCOPES = {
  SIGNATURE: "signature",
  IMPERSONATION: "impersonation",
};
import https from "https";

// JWT Grant type
const JWT_GRANT_TYPE = "urn:ietf:params:oauth:grant-type:jwt-bearer";

/**
 * Create RSA key from PEM string
 */
function createRSAKeyFromPem(pemKey: string): crypto.KeyObject {
  try {
    return crypto.createPrivateKey({
      key: pemKey,
      format: "pem",
    });
  } catch (error) {
    logger.error("Error creating RSA key from PEM:", error);
    throw new Error("Invalid private key format");
  }
}

/**
 * Request JWT User Token from DocuSign
 * Matches .NET signature: RequestJWTUserToken(clientId, userId, oauthBasePath, privateKeyBytes, expiresInHours, keyFilePath, scopes)
 */
// async function requestJWTUserToken(
//   clientId: string,
//   userId: string,
//   oauthBasePath: string,
//   privateKeyBytes: Buffer,
//   expiresInHours: number = 1,
//   keyFilePath: string,
//   scopes: string[] = [DOCUSIGN_SCOPES.SIGNATURE, DOCUSIGN_SCOPES.IMPERSONATION]
// ): Promise<DocuSignOAuthToken> {
//   try {
//     // Read private key from file (matching .NET behavior)
//     // In .NET: string key = File.ReadAllText(pemFilePath); string privateKey = key;
//     if (!fs.existsSync(keyFilePath)) {
//       throw new Error(`Private key file not found: ${keyFilePath}`);
//     }

//     const privateKey = fs.readFileSync(keyFilePath, "utf8");

//     console.log("privateKey-----requestJWTUserToken------->",privateKey);

//     logger.info("Private key loaded successfully from file");

//     // Note: In .NET, privateKeyBytes parameter contains base64-encoded key bytes,
//     // but the actual private key is read from keyFilePath. We follow the same pattern.

//     // Validate required parameters
//     if (!clientId) {
//       throw new Error("Client ID not supplied or is invalid!");
//     }
//     if (!userId) {
//       throw new Error("User ID not supplied or is invalid!");
//     }
//     if (!privateKey) {
//       throw new Error("Private key not supplied or is invalid!");
//     }

//     // Create JWT payload
//     const now = Math.floor(Date.now() / 1000);
//     const payload = {
//       iss: clientId, // Issuer (client ID)
//       sub: userId, // Subject (user ID)
//       aud: oauthBasePath, // Audience (OAuth base path)
//       iat: now, // Issued at
//       exp: now + (expiresInHours * 3600), // Expires in
//       scope: scopes.join(" "), // Scopes
//     };

//     logger.info(`JWT payload created: ${JSON.stringify(payload)}`);

//     // Create RSA key
//     const rsaKey = createRSAKeyFromPem(privateKey);

//     // Sign JWT token
//     const jwtToken = jwt.sign(payload, rsaKey, {
//       algorithm: "RS256",
//       header: {
//         typ: "JWT",
//         alg: "RS256",
//       },
//     });

//     logger.info("JWT token created successfully");

//     // Prepare OAuth request
//     // const baseUri = `https://${oauthBasePath}/`;
//     // const tokenUrl = `${baseUri}oauth/token`;
//     const tokenUrl = `https://account-d.docusign.com/oauth/token`;

//     const formData = new URLSearchParams();
//     formData.append("grant_type", JWT_GRANT_TYPE);
//     formData.append("assertion", jwtToken);

//     const headers = {
//       "Content-Type": "application/x-www-form-urlencoded",
//       "Cache-Control": "no-store",
//       "Pragma": "no-cache",
//     };

//     logger.info(`Making OAuth request to: ${tokenUrl}`);

//     // Make OAuth request
//     const response = await axios.post(tokenUrl, formData.toString(), {
//       headers,
//       timeout: 20000,
//       validateStatus: (status) => status >= 200 && status < 400,
//     });

//     logger.info(`OAuth response status: ${response.status}`);
//     logger.info(`OAuth response data: ${JSON.stringify(response.data)}`);

//     const tokenInfo: DocuSignOAuthToken = response.data;

//     if (!tokenInfo.access_token) {
//       throw new Error("No access token received from DocuSign");
//     }

//     return tokenInfo;
//   } catch (error) {
//     if (axios.isAxiosError(error)) {
//       const errorMessage = `OAuth request failed: ${error.response?.status} - ${error.response?.statusText}`;
//       const errorContent = error.response?.data ? JSON.stringify(error.response.data) : "No response content";
//       logger.error(`${errorMessage} | Response: ${errorContent}`);
//       throw new Error(`${errorMessage} | Response: ${errorContent}`);
//     } else {
//       logger.error("JWT User Token request error:", error);
//       throw error;
//     }
//   }
// }

async function requestJWTUserToken(
  clientId: string,
  userId: string,
  oauthBasePath: string,
  privateKeyBytes: Buffer,
  expiresInHours: number = 1,
  keyFilePath: string,
  scopes: string[] = [DOCUSIGN_SCOPES.SIGNATURE, DOCUSIGN_SCOPES.IMPERSONATION]
): Promise<DocuSignOAuthToken> {
  try {
    if (!fs.existsSync(keyFilePath)) {
      throw new Error(`Private key file not found: ${keyFilePath}`);
    }

    const privateKey = fs.readFileSync(keyFilePath, "utf8");

    console.log("privateKey=================>",privateKey);

    logger.info("Private key loaded successfully from file");

    if (!clientId) throw new Error("Client ID not supplied or is invalid!");
    if (!userId) throw new Error("User ID not supplied or is invalid!");
    if (!privateKey) throw new Error("Private key not supplied or is invalid!");

    const now = Math.floor(Date.now() / 1000);
    const payload = {
      iss: clientId,
      sub: userId,
      aud: `account-d.docusign.com`, // ✅ FIXED: include https://
      iat: now,
      exp: now + (expiresInHours * 3600),
      scope: "signature impersonation",
    };

    logger.info(`JWT payload created: ${JSON.stringify(payload)}`);

    const rsaKey = createRSAKeyFromPem(privateKey);

    console.log("rsaKey-------------->",rsaKey)

    const jwtToken = jwt.sign(payload, rsaKey, {
      algorithm: "RS256",
      header: {
        typ: "JWT",
        alg: "RS256",
      },
    });

    console.log("jswToken------------->",jwtToken);

    logger.info("JWT token created successfully");

    const tokenUrl = `https://account-d.docusign.com/oauth/token`;

    const formData = new URLSearchParams();
    formData.append("grant_type", "urn:ietf:params:oauth:grant-type:jwt-bearer");
    formData.append("assertion", jwtToken);

    const headers = {
      "Content-Type": "application/x-www-form-urlencoded",
      "Cache-Control": "no-store",
      "Pragma": "no-cache",
    };

    logger.info(`Making OAuth request to: ${tokenUrl}`);

    const httpsAgent = new https.Agent({ rejectUnauthorized: false });

    const response = await axios.post(tokenUrl, formData.toString(), {
      headers,
      timeout: 20000,
      httpsAgent,
      validateStatus: (status) => status >= 200 && status < 400,
    });

    logger.info(`OAuth response status: ${response.status}`);
    logger.info(`OAuth response data: ${JSON.stringify(response.data)}`);

    const tokenInfo: DocuSignOAuthToken = response.data;

    if (!tokenInfo.access_token) {
      throw new Error("No access token received from DocuSign");
    }

    return tokenInfo;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      logger.error("Axios error details:", {
        message: error.message,
        code: error.code,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
      });

      const errorMessage = `OAuth request failed: ${error.response?.status} - ${error.response?.statusText}`;
      const errorContent = error.response?.data ? JSON.stringify(error.response.data) : "No response content";
      throw new Error(`${errorMessage} | Response: ${errorContent}`);
    } else {
      logger.error("Unexpected JWT Token request error:", error);
      throw error;
    }
  }
}

/**
 * Main service function to get DocuSign access token
 */
export async function getDocuSignAccessTokenService(
  _req?: MedusaRequest
): Promise<DocuSignTokenServiceResponse> {
  try {
    logger.info("Entered into getDocuSignAccessTokenService");

    // DocuSign configuration from environment variables
    const clientId = process.env.DOCUSIGN_CLIENT_ID;
    const userId = process.env.DOCUSIGN_USER_ID;
    const oauthBasePath = process.env.DOCUSIGN_OAUTH_BASE_PATH || "account-d.docusign.com";
    const keyFilePath = process.env.DOCUSIGN_RSA_PEM_FILE_PATH;

    logger.info(`DocuSign config - ClientId: ${clientId ? "***" : "missing"}, UserId: ${userId ? "***" : "missing"}, OAuthBasePath: ${oauthBasePath}, KeyFilePath: ${keyFilePath}`);

    // Validate configuration
    if (!clientId) {
      throw new Error("DocuSign Client ID not configured");
    }
    if (!userId) {
      throw new Error("DocuSign User ID not configured");
    }
    if (!keyFilePath) {
      throw new Error("DocuSign RSA PEM file path not configured");
    }

    // Resolve absolute path for key file
    const absoluteKeyPath = path.isAbsolute(keyFilePath) ? keyFilePath : path.resolve(process.cwd(), keyFilePath);

    console.log("absoluteKeyPath----------->",absoluteKeyPath);

    logger.info(`Using key file path: ${absoluteKeyPath}`);

    console.log("clientId------------>",clientId)
    console.log("userId------------>",userId)
    console.log("oauthBasePath------------>",oauthBasePath)
    console.log("keyFilePath------------>",keyFilePath)
    console.log("absoluteKeyPath------------>",absoluteKeyPath)
    // Create private key bytes (matching .NET behavior)
    // In .NET: var privateKeyBytes = Encoding.ASCII.GetBytes("base64-encoded-key");
    // For now, we'll create a placeholder buffer since the actual key is read from file
    const privateKeyBytes = Buffer.from("MjY3Yzg3NDgtMzRjNy00ODJlLWE0NjAtY2JjMzk2MjRhMDkxOmY1ZWNlNWZkLWI3YmEtNGQzNC05MjRjLTZhMTU5YTFhOGM2MQ==", "ascii");

    console.log("privateKeyBytes--------->",privateKeyBytes)

    const fs = require("fs");
console.log(fs.existsSync("D://docusignPrivateKey.pem")); // must be true

    // Request JWT token (matching .NET signature)
    const tokenInfo = await requestJWTUserToken(
      clientId,
      userId,
      oauthBasePath,
      privateKeyBytes,
      1, // 1 hour expiration
      absoluteKeyPath
    );

    logger.info("DocuSign access token retrieved successfully");

    return {
      success: true,
      data: tokenInfo,
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
    logger.error("GetDocuSignAccessToken Service Error:", error);
    
    return {
      success: false,
      error: errorMessage,
    };
  }
}
