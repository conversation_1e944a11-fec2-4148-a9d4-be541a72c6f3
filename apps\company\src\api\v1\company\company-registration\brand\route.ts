import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { stringConstants } from '@org/utils';
import { COMPANY_REGISTRATION_MODULE } from '../../../../../modules/company';
import CompanyRegistrationModuleService from '../../../../../modules/company/service';

/**
 * @openapi
 * /v1/company/company-registration/brand:
 *   get:
 *     summary: Get Brands by Region and Vendor
 *     tags:
 *       - Company Registration
 *     parameters:
 *       - in: query
 *         name: region_id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the region to fetch brands for.
 *       - in: query
 *         name: vendor_id
 *         required: true
 *         schema:
 *           type: string
 *         description: Filter brands by vendor ID.
 *     responses:
 *       200:
 *         description: Brands fetched successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Brand fetched successfully
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         example: 01JW80ZPCN03RTBDHREG9R79TJ
 *                       brand_name:
 *                         type: string
 *                         example: Toyota
 *       400:
 *         description: Bad request - region_id or vendor_id is missing.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: region_id and vendor_id parameter is missing
 *       404:
 *         description: No brands found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   example: []
 *                 message:
 *                   type: string
 *                   example: Brand not found
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const brandService: CompanyRegistrationModuleService = req.scope.resolve(
      COMPANY_REGISTRATION_MODULE
    );

    const { region_id, vendor_id } = req.query as {
      region_id?: string;
      vendor_id?: string;
      // brand_category_id?: string;
    };

    if (!region_id || !vendor_id) {
      return res.status(400).json({
        message: stringConstants.PARAMETER_MISSING('region_id and vendor_id'),
      });
    }

    const regionBrands = await brandService.listRegionBrands({ region_id });
    const regionBrandIds = regionBrands.map((rb) => rb.brand_id);

    let validBrandIds = regionBrandIds;

    const filter: any = {
      id: validBrandIds.map((item) => item.id),
    };

    if (vendor_id) filter.vendor_id = vendor_id;

    const brands = await brandService.listBrands(filter);

    if (!brands.length) {
      return res.status(404).json({
        message: stringConstants.NOT_FOUND('Brand'),
      });
    }

    const filteredBrands = brands.map((brand) => ({
      id: brand.id,
      brand_name: brand.brand_name,
    }));

    return res.status(200).json({
      data: filteredBrands,
      message: stringConstants.FETCHED_SUCCESSFULLY('Brand'),
    });
  } catch (err) {
    return res.status(500).json({
      message: stringConstants.INTERNAL_SERVER_ERROR(),
    });
  }
}
