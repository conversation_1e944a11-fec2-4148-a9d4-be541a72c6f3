import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import {
  GoogleAPIResponse,
  GoogleCreateCustomerRequest,
  GoogleEndCustomerVerifyDomainResponse
} from "../../types/responses/customResponse";
import { handleCustomerProvisioningAndSavingInDB } from "./handleAvailableDomain";

/**
 * Handle Unavailable Domain
 * Matches .NET HandleUnavailableDomain method
 */
export async function handleUnavailableDomain(
  req: MedusaRequest,
  isExistingCustomer: boolean,
  custId: string,
  storeId: string,
  brandIds: string[],
  partnerId: string,
  createCustomerRequest: GoogleCreateCustomerRequest,
  verifyDomainResponse: GoogleEndCustomerVerifyDomainResponse
): Promise<GoogleAPIResponse> {
  logger.info(`Entered into HandleUnavailableDomain service method with isExistingCustomer ${isExistingCustomer},custId ${custId}, storeId ${storeId}, brandIds ${JSON.stringify(brandIds)}, partnerId ${partnerId}, GoogleCreateCustomerRequest ${JSON.stringify(createCustomerRequest)}, GoogleEndCustomerVerifyDomainResponse ${JSON.stringify(verifyDomainResponse)}.`);

  const cloudIdentityAccountInfo = verifyDomainResponse.Data?.cloudIdentityAccounts?.[0];
  logger.info(`Fetched cloudIdentityAccountInfo from verifyDomainResponse ${JSON.stringify(cloudIdentityAccountInfo)}`);

  if (cloudIdentityAccountInfo?.Existing === true && cloudIdentityAccountInfo?.Owned === true) {
    logger.info(`Entered in section where domain exists and owned.`);

    // Extract customer ID from CustomerName field (matching .NET: CustomerName?.Split('/')?.Last())
    const customerID = cloudIdentityAccountInfo?.CustomerName?.split('/').pop();
    logger.info(`Fetched Google customer id ${customerID}.`);

    return await handleCustomerProvisioningAndSavingInDB(
      req,
      isExistingCustomer,
      custId,
      storeId,
      customerID || '',
      brandIds,
      partnerId,
      createCustomerRequest
    );
  }

  return {
    Message: verifyDomainResponse?.Message || "Domain is not available",
    Data: null,
    StatusCode: verifyDomainResponse.StatusCode,
    IsError: true
  };
}