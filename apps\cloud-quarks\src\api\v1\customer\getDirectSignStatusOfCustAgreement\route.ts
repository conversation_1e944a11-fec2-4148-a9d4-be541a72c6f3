import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import { validateGetDirectSignStatusOfCustAgreement } from "../../../../validators/customer/customCustomerValidator";
import { getDirectSignStatusOfCustAgreementService } from "../../../../services/index";


/**
 * @openapi
 * /v1/customer/getDirectSignStatusOfCustAgreement:
 *   get:
 *     summary: Get Direct Sign Status of Customer Agreement
 *     tags:
 *       - Customer
 *     parameters:
 *       - in: query
 *         name: storeId
 *         required: true
 *         schema:
 *           type: string
 *         description: Unique identifier of the store
 *       - in: query
 *         name: custTenantId
 *         required: true
 *         schema:
 *           type: string
 *         description: Tenant ID of the customer whose agreement status needs to be verified
 *     responses:
 *       200:
 *         description: Successfully retrieved agreement sign status
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                   example: "1"
 *                 Message:
 *                   type: string
 *                   example: Agreement is signed
 *                 isSigned:
 *                   type: boolean
 *                   example: true
 *       400:
 *         description: Validation error on missing or invalid parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                   example: "0"
 *                 Message:
 *                   type: string
 *                   example: The 'storeId' URL parameter is missing in the request.
 *       500:
 *         description: Internal server error during processing
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                   example: "0"
 *                 Message:
 *                   type: string
 *                   example: Internal server error
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    logger.info(":: Get Direct Sign Status Of Cust Agreement API Hits ::");
    const correlationId = getCorrelationId(req); // mimic .NET's LogContext.PushProperty

    const storeId = req.query.storeId as string;
    const custTenantId = req.query.custTenantId as string;

    logger.info(
      `Entered into Get Direct Sign Status Of Cust Agreement with storeId-${storeId}, custTenantId-${custTenantId}, CorrelationId-${correlationId}`
    );

    const validationErrors = validateGetDirectSignStatusOfCustAgreement(req);

    if (validationErrors.length > 0) {
      logger.info(
        `Going to return BadRequest with validation errors: ${JSON.stringify(
          validationErrors
        )}`
      );
      return res.status(400).json({
        Status: "0",
        Message: validationErrors.join(", "),
      });
    }

    logger.info(
      `Going to hit BL getDirectSignStatusOfCustAgreement method with storeId-${storeId}, custTenantId-${custTenantId}`
    );
    const response = await getDirectSignStatusOfCustAgreementService(
      req,
      storeId,
      custTenantId
    );

    logger.info(
      `Result from BL getDirectSignStatusOfCustAgreement: ${JSON.stringify(
        response
      )}`
    );

    return res.status(200).json(response);
  } catch (err) {
    logger.error("Get Direct Sign Status Of Cust Agreement error:", err);
    return res.status(500).json({
      Status: "0",
      Message: "Internal server error",
    });
  }
}
