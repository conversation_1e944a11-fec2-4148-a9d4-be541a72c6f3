import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import CQUserService from '../../../../../modules/cq-user/service';
import { CQUser_MODULE } from '../../../../../modules/cq-user';
import {
  stringConstants,
  sendTransactionalEmail,
  renderOtpEmail,
} from '@org/utils';

/**
 * @openapi
 * /v1/user/signup/email-send:
 *   post:
 *     summary: Send or update email OTP for verification
 *     tags:
 *       - Signup - Email
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 example: <EMAIL>
 *     responses:
 *       200:
 *         description: OTP sent or updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Email verification code sent successfully
 *       400:
 *         description: Bad request (e.g., email already registered or missing)
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Email is required
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error. Please try again later.
 */

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const emailOtpService: CQUserService = req.scope.resolve(CQUser_MODULE);

  interface RequestBody {
    email: string;
  }

  try {
    const { email } = req.body as RequestBody;
    const otp_code = Math.floor(100000 + Math.random() * 900000).toString();
    const expires_at = new Date(Date.now() + 60 * 1000);
    const htmlBody = await renderOtpEmail(otp_code);
    if (email) {
      const existingEntries = await emailOtpService.listEmailOtpVerifications({
        email: email.toLowerCase().trim(),
      });
      const sendEmail = async () => {
        await sendTransactionalEmail({
          toEmail: email.toLowerCase().trim(),
          subjectTemplate: 'Email Verification Code for Cloudquarks',
          htmlContentTemplate: htmlBody,
          senderEmail: '<EMAIL>',
          senderName: 'Redington',
        });
      };

      if (existingEntries && existingEntries.length > 0) {
        const entry = existingEntries[0];

        if (!entry.is_registered) {
          await emailOtpService.updateEmailOtpVerifications({
            id: entry.id,
            otp_code,
            is_verified: false,
            expires_at,
          });
          await sendEmail();

          return res.status(200).json({
            message: stringConstants.VERIFICATION('Email verification'),
          });
        } else {
          return res.status(400).json({
            message: stringConstants.ALREADY_EXISTS('Email'),
          });
        }
      }

      const result = await emailOtpService.createEmailOtpVerifications({
        email: email.toLowerCase().trim(),
        mobile_number: null,
        otp_code,
        expires_at,
      });
      if (result) {
        await sendEmail();
      }
      return res.status(200).json({
        message: stringConstants.VERIFICATION('Email verification'),
      });
    }

    return res.status(400).json({
      message: stringConstants.REQUIRED('Email'),
    });
  } catch (err: any) {
    return res.status(500).json({});
  }
}
