import { validateSearchCiscoCustomerDto } from "../searchCiscoCustomerValidator";

describe("validateSearchCiscoCustomerDto", () => {
  it("should return no errors for valid DTO", () => {
    // Arrange
    const validDto = {
      countryCode: "US",
      name: "Example Corp",
      storeId: "store123",
      pageSize: 100
    };

    // Act
    const result = validateSearchCiscoCustomerDto(validDto);

    // Assert
    expect(result.isValid).toBe(true);
    expect(result.errors).toEqual([]);
  });

  it("should return error when DTO is null", () => {
    // Arrange
    const nullDto = null;

    // Act
    const result = validateSearchCiscoCustomerDto(nullDto);

    // Assert
    expect(result.isValid).toBe(false);
    expect(result.errors).toEqual(["Invalid data structure"]);
  });

  it("should return error when DTO is undefined", () => {
    // Arrange
    const undefinedDto = undefined;

    // Act
    const result = validateSearchCiscoCustomerDto(undefinedDto);

    // Assert
    expect(result.isValid).toBe(false);
    expect(result.errors).toEqual(["Invalid data structure"]);
  });

  it("should return error when countryCode is empty", () => {
    // Arrange
    const dtoWithEmptyCountryCode = {
      countryCode: "",
      name: "Example Corp",
      storeId: "store123",
      pageSize: 100
    };

    // Act
    const result = validateSearchCiscoCustomerDto(dtoWithEmptyCountryCode);

    // Assert
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain("Country code cannot be null or empty.");
  });

  it("should return error when name is empty", () => {
    // Arrange
    const dtoWithEmptyName = {
      countryCode: "US",
      name: "",
      storeId: "store123",
      pageSize: 100
    };

    // Act
    const result = validateSearchCiscoCustomerDto(dtoWithEmptyName);

    // Assert
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain("Name cannot be null or empty.");
  });

  it("should return error when storeId is empty", () => {
    // Arrange
    const dtoWithEmptyStoreId = {
      countryCode: "US",
      name: "Example Corp",
      storeId: "",
      pageSize: 100
    };

    // Act
    const result = validateSearchCiscoCustomerDto(dtoWithEmptyStoreId);

    // Assert
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain("Store Id cannot be null or empty.");
  });

  it("should return error when pageSize is negative", () => {
    // Arrange
    const dtoWithNegativePageSize = {
      countryCode: "US",
      name: "Example Corp",
      storeId: "store123",
      pageSize: -1
    };

    // Act
    const result = validateSearchCiscoCustomerDto(dtoWithNegativePageSize);

    // Assert
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain("Page size must be a non-negative integer.");
  });

  it("should allow pageSize to be zero", () => {
    // Arrange
    const dtoWithZeroPageSize = {
      countryCode: "US",
      name: "Example Corp",
      storeId: "store123",
      pageSize: 0
    };

    // Act
    const result = validateSearchCiscoCustomerDto(dtoWithZeroPageSize);

    // Assert
    expect(result.isValid).toBe(true);
    expect(result.errors).toEqual([]);
  });

  it("should return multiple errors when multiple fields are invalid", () => {
    // Arrange
    const dtoWithMultipleErrors = {
      countryCode: "",
      name: "",
      storeId: "",
      pageSize: -5
    };

    // Act
    const result = validateSearchCiscoCustomerDto(dtoWithMultipleErrors);

    // Assert
    expect(result.isValid).toBe(false);
    expect(result.errors).toHaveLength(4);
    expect(result.errors).toContain("Country code cannot be null or empty.");
    expect(result.errors).toContain("Name cannot be null or empty.");
    expect(result.errors).toContain("Store Id cannot be null or empty.");
    expect(result.errors).toContain("Page size must be a non-negative integer.");
  });

  it("should return error when pageSize is not a number", () => {
    // Arrange
    const dtoWithInvalidPageSize = {
      countryCode: "US",
      name: "Example Corp",
      storeId: "store123",
      pageSize: "invalid" as any
    };

    // Act
    const result = validateSearchCiscoCustomerDto(dtoWithInvalidPageSize);

    // Assert
    expect(result.isValid).toBe(false);
    expect(result.errors.length).toBeGreaterThan(0);
  });

  it("should return error when required fields are missing", () => {
    // Arrange
    const incompleteDto = {
      countryCode: "US"
      // Missing name, storeId, pageSize
    };

    // Act
    const result = validateSearchCiscoCustomerDto(incompleteDto);

    // Assert
    expect(result.isValid).toBe(false);
    expect(result.errors.length).toBeGreaterThan(0);
  });
});
