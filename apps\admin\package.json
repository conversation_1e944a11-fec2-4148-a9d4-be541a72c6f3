{"name": "admin", "version": "0.0.1", "description": "A starter for Medusa projects.", "author": "Medusa (https://medusajs.com)", "license": "MIT", "keywords": ["sqlite", "postgres", "typescript", "ecommerce", "headless", "medusa"], "scripts": {"build": "medusa build", "seed": "medusa exec ./src/scripts/seed.ts", "start": "medusa start", "dev": "medusa develop --port", "test:integration:http": "TEST_TYPE=integration:http NODE_OPTIONS=--experimental-vm-modules jest --silent=false --runInBand --forceExit", "test:integration:modules": "TEST_TYPE=integration:modules NODE_OPTIONS=--experimental-vm-modules jest --silent --runInBand --forceExit", "test:unit": "TEST_TYPE=unit NODE_OPTIONS=--experimental-vm-modules jest --silent --runInBand --forceExit", "migrate:master": "cross-env DATABASE_URL=postgres://postgres:SmartWork%40123@localhost/master DATABASE_TYPE=master medusa db:migrate", "migrate:india": "cross-env DATABASE_URL=postgres://postgres:SmartWork%40123@localhost/india DATABASE_TYPE=india medusa db:migrate", "migrate:mea": "cross-env DATABASE_URL=postgres://postgres:SmartWork%40123@localhost/mea DATABASE_TYPE=mea medusa db:migrate", "migrate:turkey": "cross-env DATABASE_URL=postgres://postgres:SmartWork%40123@localhost/turkey DATABASE_TYPE=turkey medusa db:migrate", "migrate:all": "npm run migrate:master &&npm run migrate:india && npm run migrate:mea", "generate:master": "cross-env DATABASE_TYPE=master medusa db:generate", "generate:india": "cross-env DATABASE_TYPE=india medusa db:generate", "generate:mea": "cross-env DATABASE_TYPE=mea medusa db:generate", "generate:turkey": "cross-env DATABASE_TYPE=turkey medusa db:generate"}, "dependencies": {"@medusajs/admin-sdk": "2.8.3", "@medusajs/cli": "2.8.3", "@medusajs/framework": "2.8.3", "@medusajs/medusa": "2.8.3", "@mikro-orm/core": "6.4.3", "@mikro-orm/knex": "6.4.3", "@mikro-orm/migrations": "6.4.3", "@mikro-orm/postgresql": "6.4.3", "awilix": "^8.0.1", "pg": "^8.13.0"}, "devDependencies": {"@medusajs/test-utils": "2.8.3", "@mikro-orm/cli": "6.4.3", "@nx/eslint": "^20.8.0", "@swc/core": "1.5.7", "@swc/jest": "^0.2.36", "@types/jest": "^29.5.13", "@types/node": "^20.0.0", "@types/react": "^18.3.2", "@types/react-dom": "^18.2.25", "eslint": "^9.25.1", "jest": "^29.7.0", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "ts-node": "^10.9.2", "typescript": "^5.6.2", "vite": "^5.2.11", "yalc": "^1.0.0-pre.53"}, "engines": {"node": ">=20"}}