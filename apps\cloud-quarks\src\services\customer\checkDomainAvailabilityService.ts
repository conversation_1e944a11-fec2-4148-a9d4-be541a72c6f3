import { getStoreDetails } from "../token/microsoft/getStoreDetailsService";
import { getMsToken } from "../token/microsoft/getTokenService";
import logger from "../../utils/logger";
import { CheckDomainResponse } from "../../types/responses/customResponse";
import { MicrosoftEndpoints } from "../../config/microsoftEndpoints";
import { checkMicrosoftDomainExists } from "../externalService/externalEndPointService";

export async function checkDomainAvailabilityService(
  req: any,
  brandId: string,
  storeId: string,
  domainId: string
): Promise<CheckDomainResponse> {
  logger.info("Entered into CheckDomainAvailability service method");

  let checkDomainResponse: CheckDomainResponse = {
    Status: "0",
    Message: "",
  };

  logger.info(
    `Going to hit GetStoreDetails method with storeId: ${storeId} and brandId: ${brandId}`
  );
  const storeData = await getStoreDetails({ storeId, brandId });
  logger.info(`Store details: ${JSON.stringify(storeData)}`);

  const token = await getMsToken({
    brand: storeData.brand || "",
    client_id: storeData.clientid || "",
    client_secret: storeData.clientsecret || "",
    grant_type: storeData.granttype || "",
    markValue: storeData.markvalue?.toString() || "",
    redirect_uri: storeData.redirecturi || "",
    refresh_token: storeData.token || "",
    resource: storeData.resource || "",
    store_domain: storeData.storedomain || "",
  });

  console.log("token ---->", token);
  logger.info("Token generated successfully", token);
  if (token) {
    try {
      const checkDomainUrl = MicrosoftEndpoints.checkDomainUrl(domainId);
      logger.info(`Going to hit checkDomainUrl: ${checkDomainUrl}`);

      const result = await checkMicrosoftDomainExists(
        checkDomainUrl,
        token.access_token
      );
      logger.info(`Final checkDomain result: ${JSON.stringify(result)}`);
      checkDomainResponse = result;
      return checkDomainResponse;

      // const url = new URL(checkDomainUrl);

      // const options = {
      //   method: "HEAD",
      //   hostname: url.hostname,
      //   path: url.pathname + url.search,
      //   headers: {
      //     Authorization: `Bearer ${token}`,
      //   },
      //   rejectUnauthorized: false, // for local/dev self-signed certs
      // };

      // const statusCode: number = await new Promise((resolve, reject) => {
      //   const req = https.request(options, (res: { statusCode: any; }) => {
      //     logger.info(`HEAD request status code: ${res.statusCode}`);
      //     resolve(res.statusCode || 0);
      //   });

      //   req.on("error", (e: any) => {
      //     reject(e);
      //   });

      //   req.end();
      // });

      // // Final response based on status code
      // if (statusCode === 200) {
      //   logger.info(`Domain exists. Returning success.`);
      //   checkDomainResponse.Status = "1";
      //   checkDomainResponse.Message = "Domain exist";
      // } else if (statusCode === 404) {
      //   logger.info(`Domain not found (404).`);
      //   checkDomainResponse.Status = "0";
      //   checkDomainResponse.Message = "Domain doesn't exist";
      // } else if (statusCode === 401) {
      //   logger.warn(`Unauthorized (401) — token may be invalid or expired.`);
      //   checkDomainResponse.Status = "0";
      //   checkDomainResponse.Message = "Unauthorized. Token might be invalid.";
      // } else {
      //   logger.warn(`Unexpected status code: ${statusCode}`);
      //   checkDomainResponse.Status = "0";
      //   checkDomainResponse.Message = `Unexpected response: ${statusCode}`;
      // }
    } catch (err: any) {
      logger.error(`HEAD request failed`, err);

      checkDomainResponse.Status = "0";
      checkDomainResponse.Message =
        err?.message || "Domain check failed due to unknown error";
    }
  } else {
    checkDomainResponse.Message =
      "Token not found due to invalid BrandId or StoreId";
  }
  // if (token) {
  //   try {
  //     const checkDomainUrl = MicrosoftEndpoints.checkDomainUrl(domainId);
  //   logger.info(`Going to hit checkDomainUrl: ${checkDomainUrl}`);

  //   const url = new URL(checkDomainUrl);

  //   const options = {
  //     method: "HEAD",
  //     hostname: url.hostname,
  //     path: url.pathname + url.search,
  //     headers: {
  //       Authorization: `Bearer ${token}`,
  //     },
  //     rejectUnauthorized: false,
  //   };

  //   const responseContent = await new Promise<string>((resolve, reject) => {
  //     const req = https.request(options, (res) => {
  //       logger.info(`HEAD request status code: ${res.statusCode}`);

  //       // Collect response data (even though HEAD usually has no body)
  //       let data = "";
  //       res.on("data", (chunk) => {
  //         data += chunk;
  //       });
  //       res.on("end", () => {
  //         resolve(data || "");
  //       });
  //     });

  //     req.on("error", (e) => {
  //       reject(e);
  //     });

  //     req.end();
  //   });

  //   logger.info(
  //     `Success code is true. So, Response is ${responseContent}`
  //   );

  //   checkDomainResponse.Status = "1";
  //   checkDomainResponse.Message = "Domain exist";

  //     // const configModule = req.scope.resolve("configModule");

  //     // const response = await getMethod(
  //     //   {
  //     //     url: checkDomainUrl,
  //     //     headers: {
  //     //       Authorization: `Bearer ${token}`,
  //     //     },
  //     //     isVendorHit: true,
  //     //     module: "checkDomainAvailability"
  //     //   },
  //     //   configModule
  //     // );

  //     // logger.info(`HEAD request success: ${response.isSuccessStatusCode}`);

  //     // if (response.isSuccessStatusCode) {
  //     //   checkDomainResponse.Status = "1";
  //     //   checkDomainResponse.Message = "Domain exist";
  //     // } else {
  //     //   // Handle failure (e.g., 404 = domain doesn't exist)
  //     //   let reason = "Unknown error";
  //     //   try {
  //     //     const parsed = JSON.parse(response.content);
  //     //     reason = parsed?.message || parsed?.Description || reason;
  //     //   } catch (_) {
  //     //     reason = response.errorMessage || reason;
  //     //   }

  //     //   checkDomainResponse.Status = "0";
  //     //   checkDomainResponse.Message =
  //     //     response.httpStatusCode === 404 ? "Domain doesn't exist" : reason;
  //     // }
  //   } catch (err: any) {

  //     logger.error(`HEAD request failed`, err);

  //   checkDomainResponse.Status = "0";
  //   checkDomainResponse.Message =
  //     err?.message || "Domain check failed due to unknown error";

  //     // logger.error("HEAD request failed", {
  //     //   message: err?.message,
  //     //   code: err?.code,
  //     //   response: err?.response,
  //     //   stack: err?.stack,
  //     // });

  //     // const statusCode = err?.response?.status;
  //     // const reason =
  //     //   err?.response?.statusText || err?.message || "Unknown error";

  //     // if (statusCode === 404) {
  //     //   checkDomainResponse.Status = "0";
  //     //   checkDomainResponse.Message = "Domain doesn't exist";
  //     // } else {
  //     //   checkDomainResponse.Status = "0";
  //     //   checkDomainResponse.Message = reason;
  //     // }
  //   }
  // } else {
  //   checkDomainResponse.Message =
  //     "Token not found due to invalid BrandId or StoreId";
  // }

  logger.info(
    `Going to return response from service method as : ${JSON.stringify(
      checkDomainResponse
    )}`
  );
  return checkDomainResponse;
}
