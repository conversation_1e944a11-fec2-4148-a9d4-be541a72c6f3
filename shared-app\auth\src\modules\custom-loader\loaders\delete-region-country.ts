import { LoaderOptions } from '@medusajs/framework/types';

import { Client } from 'pg';

const client = new Client({
  connectionString: process.env.DATABASE_URL,
});

export default async function deleteRegionCountryLoader({
  container,
}: LoaderOptions) {
  const logger = container.resolve('logger');
  try {
    await client.connect();

    const iso2Res = await client.query(`
      SELECT DISTINCT rci.country_iso_2
      FROM cq_country_extension cce
      JOIN region_country_iso_2_cq_user_cq_country_extension rci
        ON cce.id = rci.cq_country_extension_id
      WHERE cce.is_custom = true
    `);

    const allowedIso2List = iso2Res.rows.map((row) => row.country_iso_2);

    if (allowedIso2List.length >= 0) {
      const deleteQuery = `
      DELETE FROM region_country
      WHERE region_id IS NULL
      AND iso_2 <> ALL($1)
    `;

      const deleteRes = await client.query(deleteQuery, [allowedIso2List]);

      logger.log(
        `Deleted ${deleteRes.rowCount} records from region_country where region_id IS NULL and iso_2 is not in the allowed list.`
      );
    }

    logger.log(`Deleted all records from region_country.`);
  } catch (err: unknown) {
    if (err instanceof Error) {
      logger.error(`Error executing delete query:', ${err.stack}`);
    } else {
      logger.error(`Error executing delete query:, ${err} `);
    }
  } finally {
    await client.end();
  }
}
