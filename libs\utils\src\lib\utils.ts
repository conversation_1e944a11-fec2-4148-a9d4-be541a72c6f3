import template from 'string-template';

const stringConstants: any = {
  // Auth constants
  INVALID: (value: string) => template('Invalid {0}', value),
  PARAMETER_MISSING: (value: string) =>
    template('Parameter {0} is missing', value),
  PARAMETER_INVALID: (value: string) =>
    template('Parameter {0} is invalid', value),
  NOT_FOUND: (value: string) => template('{0} does not exist', value),
  VERIFICATION: (value: string) =>
    template('{0} code sent successfully', value),
  ALREADY_EXISTS: (value: string) =>
    template(
      '{0} already in use. Please try again with a different {0}.',
      value
    ),
  OTP_RECORD_NOT_FOUND: (value: string) =>
    template('No OTP record found for the given {0}.', value),
  OTP_EXPIRED: () => 'OTP is invalid or expired.',
  OTP_INVALID: () => 'Invalid OTP code.',
  OTP_VERIFIED: () => 'OTP verification was successful.',
  OTP_RESEND: () => 'OTP resent successfully.',
  INVALID_PASSWORD: () => 'Invalid old password',
  PASSWORD_NOT_TO_SAME: () =>
    'New password and old password should not be same',
  UNAUTHORIZED: () => 'Unauthorized user',
  REQUIRED: (value: string) => template('{0} is required', value),
  MOBILE_VERIFY: () => 'Mobile number and OTP code are required',
  EMAIL_VERIFY: () => 'Email and OTP code are required',
  EMAIL_PASSWORD_VERIFY: () => 'Email and Password are required',
  SESSION_ESTABLISHED_VERIFY: () => 'Session established successfully',
  SESSION_SERVICE_ERROR: () => 'Error from session service',
  SESSION_RESSPONE_ERROR: () => 'No response from session service',
  SESSION_SETUP_ERROR: () => 'Error setting up session service request',
  SESSION_EXPIRED: () => 'Session expired. Please login again.',
  USER_NOT_FOUND: (value: string) =>
    template('User with ID {0} was not found in the database.', value),
  AUTHENTICATION_SUCCESSFUL: () => 'Authentication successful',
  DATABASE_ERROR: () => 'Database error during authentication',
  AUTHENTICATION_FAILED: () => 'Authentication failed',
  AUTHENTICATE_VALIDATOR: () => 'authenticate-validator',
  AUTHENTICATION_SERVICE_ERROR: () => 'Authentication service error',
  AUTHENTICATION_REQUIRED: () => 'Authentication required',
  INVALID_CREDENTIALS: () => 'Invalid email or password',
  LOGOUT_SUCCESSFUL: () => 'Logged out successfully',
  LOGOUT_ERROR: () => 'Error during logout',
  // Common constants
  REGISTERED_SUCCESSFULLY: (value: string) =>
    template('{0} registered successfully', value),
  CREATED_SUCCESSFULLY: (value: string) =>
    template('{0} created successfully', value),
  FETCHED_SUCCESSFULLY: (value: string) =>
    template('{0} fetched successfully', value),
  UPLOADED_SUCCESSFULLY: (value: string) =>
    template('{0} uploaded successfully', value),
  UPDATED_SUCCESSFULLY: (value: string) =>
    template('{0} updated successfully', value),
  DELETED_SUCCESSFULLY: (value: string) =>
    template('{0} deleted successfully', value),
  SAVED_SUCCESSFULLY: (value: string) =>
    template('{0} saved successfully', value),
  CONFLICT: (value: string) =>
    template('{0} already exists with the same details.', value),
  INTERNAL_SERVER_ERROR: () => 'Internal server error. Please try again later.',
  PASSWORD_RESET: () => 'Password reset link sent to your email',
  ERROR_SENDING_EMAIL: () => 'Error sending email',
  TOKEN_EXPIRED: () => 'Token expired',
  INVALID_JWT_TOKEN: () => 'Invalid JWT token',
  INVALID_TOKEN_FORMAT: () => 'Invalid token format',
  AUTHORIZATION_TOKEN_MISSING: () =>
    'Authorization token must be provided in the Bearer format.',
  // Registration:
  REGISTRATION_SUCCESS: () =>
    'Company Registration request has been submitted successfully.',
  ALREADY_REGISTERED_AND_ONBOARDED: (adminName: string, adminEmail: string) =>
    template(
      'The company is already registered as a Partner and is onboarded in CloudQuarks.  Please contact your company administrator, {0} - {1}, to add you to this company account.',
      adminName,
      adminEmail
    ),
  PENDING_REGISTRATION_AND_ONBOARDING: (
    adminName: string,
    adminEmail: string
  ) =>
    template(
      'The Company’s partner registration and CloudQuarks onboarding request have already been submitted and are under review. The request was submitted by {0} - {1},',
      adminName,
      adminEmail
    ),
  ALREADY_REGISTERED_PENDING_ONBOARD: (adminName: string, adminEmail: string) =>
    template(
      'The Company’s partner registration and CloudQuarks onboarding request have already been submitted and are under review. The request was submitted by {0} - {1},',
      adminName,
      adminEmail
    ),
  COMPANY_ONBOARDING_PENDING: () =>
    'Company onboarding is pending. Approval from Redington Admin is required.',
  NO_COMPANY_MATCHING_SAP_ID: () =>
    'No existing company found for the entered SAP ID.',
  COMPANY_ONBOARDED: () =>
    'The company is already onboarded for the specified SAP ID.',
  ONBOARDING_SUCCESS: () => 'Company Onboarding request has been submitted.',
  GET_ALL_SUCCESS: (entity: string) =>
    `All ${entity} data retrieved successfully.`,
  GET_ONE_SUCCESS: (entity: string) =>
    `${entity} details retrieved successfully.`,
  LIST_SUCCESS: (entity: string) => `${entity} list fetched successfully.`,
  FETCH_SUCCESS: (entity: string) => `${entity} fetched successfully.`,
  OFFLINE_REGISTRATION_AND_ONBOARDING_SUCCESS: (
    adminName: string,
    adminEmail: string
  ) =>
    template(
      'The Company is already registered as a partner and is onboarded in CloudQuarks.  Please contact your company administrator, {0} - {1}, to add you to this company account.',
      adminName,
      adminEmail
    ),
  VERIFY_REDINGTON_ACCOUNT: () =>
    'Please enter a valid Redington Account Number.',
  ALREADY_ONBOARDED: (adminName: string, adminEmail: string) =>
    template(
      'The Redington Account Number (company) is already onboarded in CloudQuarks. Please contact your company administrator, {0} - {1}, to add you to this company account.',
      adminName,
      adminEmail
    ),
  PROCEED_ONBOARDING_FORM: () =>
    'Proceed with Company onboarding Form and get the company details from SAP ',
  CHECK_COMPANY_STATUS: (adminEmail: string) =>
    template(
      ' The Redington Account (company) is Inactive, please contact {0} for further assistance',
      adminEmail
    ),
  TIMEOUT: (service: string) =>
    template('{0} service timed out. Please try again later.', service),
  SERVICE_UNAVAILABLE: (service: string) =>
    `${service} service is currently unavailable. Please try again later.`,
};

interface PaginationMetadataInput {
  page: number;
  pageSize: number;
  totalItems: number;
}

function formatPagination({
  page,
  pageSize,
  totalItems,
}: PaginationMetadataInput): PaginationResult {
  const totalPages = Math.ceil(totalItems / pageSize);

  return {
    page,
    pageSize,
    totalItems,
    totalPages,
    hasNextPage: page < totalPages,
    hasPrevPage: page > 1,
  };
}

interface PaginationResult {
  page: number;
  pageSize: number;
  totalItems: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export { stringConstants, formatPagination };
