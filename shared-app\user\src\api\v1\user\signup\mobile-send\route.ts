import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import CQUserService from '../../../../../modules/cq-user/service';
import { CQUser_MODULE } from '../../../../../modules/cq-user';
import { stringConstants } from '@org/utils';

/**
 * @openapi
 * /v1/user/signup/mobile-send:
 *   post:
 *     summary: Send OTP to mobile number
 *     tags:
 *       - Signup - Mobile
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - mobile_number
 *             properties:
 *               mobile_number:
 *                 type: string
 *                 example: "+11234567890"
 *     responses:
 *       200:
 *         description: OTP sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Mobile verification OTP sent successfully.
 *       400:
 *         description: Mobile number missing or already registered
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Mobile number already exists
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error
 */

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const emailOtpService: CQUserService = req.scope.resolve(CQUser_MODULE);

  interface RequestBody {
    mobile_number: string;
  }

  try {
    const { mobile_number } = req.body as RequestBody;
    const otp_code = '123456';

    if (mobile_number) {
      const existingEntries = await emailOtpService.listEmailOtpVerifications({
        mobile_number,
      });

      if (existingEntries && existingEntries.length > 0) {
        const entry = existingEntries[0];

        if (!entry.is_registered) {
          const expires_at = new Date(Date.now() + 60 * 1000); // Add 1 minute to current time

          await emailOtpService.updateEmailOtpVerifications({
            id: entry.id,
            otp_code: '123456',
            is_verified: false,
            expires_at,
          });
          return res.status(200).json({
            message: stringConstants.VERIFICATION('Mobile verification'),
          });
        } else {
          return res.status(400).json({
            message: stringConstants.ALREADY_EXISTS('Mobile number'),
          });
        }
      }

      const expires_at = new Date(Date.now() + 60 * 1000); // 1 minute expiration

      await emailOtpService.createEmailOtpVerifications({
        email: null,
        mobile_number,
        otp_code,
        expires_at,
      });

      return res.status(200).json({
        message: stringConstants.VERIFICATION('Mobile verification'),
      });
    }

    return res.status(400).json({
      message: 'Mobile number is required',
    });
  } catch (err: any) {
    return res.status(500).json({});
  }
}
