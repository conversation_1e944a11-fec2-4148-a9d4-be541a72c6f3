import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import {
  GoogleAPIResponse,
  GoogleCreateCustomerRequest
} from "../../types/responses/customResponse";
import { handleExistingCustomerAndSavingInDB } from "./handleExistingCustomerAndSavingInDB";
import { verifyDomainExists } from "./verifyDomainExists";
import { handleAvailableDomain } from "./handleAvailableDomain";
import { handleUnavailableDomain } from "./handleUnavailableDomain";

/**
 * Create Customer Service
 * Matches .NET GoogleCustomerService.CreateCustomer method
 */
export async function createCustomerService(
  req: MedusaRequest,
  custId: string,
  partnerId: string,
  storeId: string,
  brandIds: string[],
  isExistingCustomer: boolean,
  googleCustomerId: string,
  cloudIdentityId: string,
  createCustomerRequest: GoogleCreateCustomerRequest
): Promise<GoogleAPIResponse> {
  const response: GoogleAPIResponse = {
    Message: "Success",
    Data: null,
    StatusCode: 200,
    IsError: false
  };

  logger.info(`Entered into CreateCustomer service method with custId ${custId}, partnerId ${partnerId}, storeid ${storeId}, brandIds ${JSON.stringify(brandIds)},isExistingCustomer ${isExistingCustomer}, googleCustomerId ${googleCustomerId},cloudIdentityId ${cloudIdentityId}, GoogleCreateCustomerRequest:${JSON.stringify(createCustomerRequest)}`);

  if (isExistingCustomer) {
    return await handleExistingCustomerAndSavingInDB(
      req,
      isExistingCustomer,
      custId,
      storeId,
      googleCustomerId,
      brandIds,
      partnerId,
      createCustomerRequest,
      cloudIdentityId
    );
  }

  logger.info(`Going to call service method VerifyDomainExists with domain:${createCustomerRequest?.Domain}, storeId ${storeId}, isExistingCustomer ${isExistingCustomer}`);
  const verifyDomainResponse = await verifyDomainExists(
    req,
    createCustomerRequest?.Domain || '',
    storeId,
    isExistingCustomer
  );
  logger.info(`Got response from VerifyDomainExists ${JSON.stringify(verifyDomainResponse)}`);

  if (verifyDomainResponse.IsAvailable) {
    logger.info(`Entered into section where domain is available.`);
    return await handleAvailableDomain(
      req,
      isExistingCustomer,
      custId,
      storeId,
      brandIds,
      partnerId,
      createCustomerRequest
    );
  } else {
    logger.info(`Entered into section where domain is not available.`);
    return await handleUnavailableDomain(
      req,
      isExistingCustomer,
      custId,
      storeId,
      brandIds,
      partnerId,
      createCustomerRequest,
      verifyDomainResponse
    );
  }
}
