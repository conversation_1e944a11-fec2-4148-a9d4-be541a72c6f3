import { MedusaRequest } from "@medusajs/framework";
import { createGoogleRequestHeaders } from "../../config/microsoftEndpoints";
import { getGoogleTokenService } from "../token/google/getGoogleTokenService";
import logger from "../../utils/logger";
import { GoogleEndpoints } from "../../config/googleEndpoints";
import { getMethod } from "../externalService/externalEndPointService";
import { getSqlServerConnection } from "../../utils/sqlServerClient";
import {
  GoogleAPIResponse,
  GooglePurchasableOffersModel,
  PurchasableOffer,
  GoogleErrorResponse
} from "../../types/responses/customResponse";

export interface GoogleBillingAccountMappingModel {
  StoreId: string;
  BrandId: string;
  RedingtonGoogleCloudId: string;
  BillingAccountId: string;
  UpdatedBy: string;
  IsDeleted: boolean | null;
}

export async function getGooglePurchasableOfferService(
  req: MedusaRequest,
  storeId: string,
  googleCustomerId: string,
  productId: string,
  skuId: string,
  offerId?: string
): Promise<GoogleAPIResponse> {
  logger.info(`Entered into GetGooglePurchasableOffer service method with storeId: ${storeId}, googleCustomerId: ${googleCustomerId}, productId: ${productId}, skuId: ${skuId}, offerId: ${offerId}`);

  // Call GetGooglePurchasableOffers to get all offers (matching .NET logic)
  const apiResponse = await getGooglePurchasableOffersService(req, storeId, googleCustomerId, productId, skuId);

  if (!apiResponse.IsError && offerId) {
    // Find specific offer by ID (matching .NET: .Find(x => x.offer.name.Split('/').LastOrDefault() == offerId))
    const purchasableOffers = apiResponse.Data as PurchasableOffer[];
    const specificOffer = purchasableOffers?.find(x =>
      x.offer?.name?.split('/').pop() === offerId
    );

    apiResponse.Data = specificOffer || null;
  }

  logger.info(`Going to return GetGooglePurchasableOffer API response- ${JSON.stringify(apiResponse)}`);
  return apiResponse;
}

/**
 * Get Google Purchasable Offers (all offers with pagination)
 * Matches .NET GoogleOfferService.GetGooglePurchasableOffers method
 */
export async function getGooglePurchasableOffersService(
  req: MedusaRequest,
  storeId: string,
  googleCustomerId: string,
  productId: string,
  skuId: string
): Promise<GoogleAPIResponse> {
  logger.info(`Entered into GetGooglePurchasableOffers service method with storeId: ${storeId}, googleCustomerId: ${googleCustomerId}, productId: ${productId}, skuId: ${skuId}`);

  let apiResponse: GoogleAPIResponse;
  let offerResponse: GooglePurchasableOffersModel = {};
  const purchasableOffers: PurchasableOffer[] = [];

  try {
    // Get Google Account ID (matching .NET: _googlePartnerManagementDAL.GetGoogleRedingtonAccountId(storeId))
    logger.info(`Going to call DAL method GetGoogleRedingtonAccountId with StoreId ${storeId}`);
    const googleAccountId = await getGoogleAccountIdFromStore(storeId);
    logger.info(`Got googleAccountId ${googleAccountId}`);

    // Build the purchasable offers URL (matching .NET URL construction)
    const getGooglePurchasableOffersUrl = GoogleEndpoints.getGooglePurchasableOffersUrl(
      googleAccountId,
      googleCustomerId,
      productId,
      skuId
    );
    logger.info(`GetGooglePurchasableOffers URL: ${getGooglePurchasableOffersUrl}`);

    // Get Google token (matching .NET: _tokenService.GetGoogleToken(storeId))
    logger.info(`Going to hit GetGoogleToken method with storeId-${storeId}`);
    const token = await getGoogleTokenService(req, storeId);
    logger.info(`Got token from BL TokenService method.`);

    // Create Google request headers (matching .NET: _externalEndpointRequestHeaders.CreateGoogleRequestHeaders(token, storeId))
    const headerList = req
      ? createGoogleRequestHeaders(req, token, storeId)
      : {
          Authorization: `Bearer ${token}`,
          "x-region": storeId,
        };

    const configModule = req?.scope?.resolve("configModule");
    let currentUrl = getGooglePurchasableOffersUrl;

    // Pagination loop (matching .NET do-while loop)
    do {
      logger.info(`Going to hit ExternalEndPoint GetMethod method with url-${currentUrl} and headerlist-${JSON.stringify(headerList)}`);

      // Call Google API (matching .NET: GetMethod)
      const response = await getMethod(
        {
          url: currentUrl,
          headers: headerList,
          isVendorHit: true,
          module: "GetGooglePurchasableOffers",
        },
        configModule
      );

      logger.info(`Response from ExternalEndPoint GetMethod is ${JSON.stringify(response)}`);

      if (response.isSuccessStatusCode) {
        logger.info(`Received success response and deserializing response.Content-${response.content}`);

        // Deserialize response to GooglePurchasableOffersModel (matching .NET deserialization)
        offerResponse = JSON.parse(response.content) as GooglePurchasableOffersModel;

        if (offerResponse?.purchasableOffers?.length) {
          purchasableOffers.push(...offerResponse.purchasableOffers);
        }

        apiResponse = {
          Message: "Success",
          Data: purchasableOffers,
          StatusCode: 200,
          IsError: false
        };
      } else {
        logger.info(`Received errored response and deserialising response.Content-${response.content}`);

        // Handle error response (matching .NET error handling)
        const errorResponse: GoogleErrorResponse = JSON.parse(response.content || response.errorMessage || "{}");

        apiResponse = {
          Message: errorResponse.Error?.Message || "Unknown error occurred",
          StatusCode: errorResponse.Error?.Code || 500,
          IsError: true
        };
        return apiResponse;
      }

      // Update URL for next page (matching .NET: getGooglePurchasableOffersUrl + "&pageToken=" + offerResponse.nextPageToken)
      currentUrl = offerResponse.nextPageToken
        ? `${getGooglePurchasableOffersUrl}&pageToken=${offerResponse.nextPageToken}`
        : "";

    } while (offerResponse.nextPageToken); // Continue while there's a next page token

  } catch (error: any) {
    logger.error(`Error in GetGooglePurchasableOffers service: ${error.message}`);

    apiResponse = {
      Message: `An error occurred: ${error.message}`,
      StatusCode: 500,
      IsError: true
    };
  }

  logger.info(`Going to return GetGooglePurchasableOffers API response- ${JSON.stringify(apiResponse)}`);
  return apiResponse;
}


export async function getGoogleAccountIdFromStore(
  storeId: string,
  brandId?: string
): Promise<string> {
  logger.info(
    `Entered into getGoogleAccountIdFromStore with storeId: ${storeId}, brandId: ${brandId}`
  );

  const accountMappingDetails = await getGoogleAccountMappingDetails(storeId, brandId);
  const googleAccountId = accountMappingDetails[0]?.RedingtonGoogleCloudId;

  if (!googleAccountId) {
    logger.info(`RedingtonGoogleCloudId is missing for the store: ${storeId}`);
    throw new Error(`RedingtonGoogleCloudId is missing for the store: ${storeId}`);
  }

  return googleAccountId;
}


export async function getGoogleAccountMappingDetails(
  storeId: string,
  brandId?: string
): Promise<GoogleBillingAccountMappingModel[]> {
  logger.info(
    `Entered into getGoogleAccountMappingDetails with storeId: ${storeId}, brandId: ${brandId}`
  );

  try {
    const pool = await getSqlServerConnection()
    const request = pool.request();

    const proc = "spGetGoogleAccountDetailsByStoreAndBrand";

    request.input("StoreId", storeId);
    request.input("BrandId", brandId || null);

    logger.info(`Going to hit DB with proc: ${proc}, StoreId: ${storeId}, BrandId: ${brandId || "null"}`);

    const result = await request.execute(proc);
    const rows = result.recordset;

    logger.info(`Received rows from ${proc}: ${JSON.stringify(rows)}`);

    return rows as GoogleBillingAccountMappingModel[];
  } catch (error) {
    logger.error(`Error in getGoogleAccountMappingDetails: ${error}`);
    throw error;
  }
}