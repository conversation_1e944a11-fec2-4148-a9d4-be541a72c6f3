{"compilerOptions": {"target": "ES2021", "esModuleInterop": true, "module": "Node16", "moduleResolution": "Node16", "emitDecoratorMetadata": true, "experimentalDecorators": true, "skipLibCheck": true, "skipDefaultLibCheck": true, "declaration": false, "sourceMap": false, "inlineSourceMap": true, "outDir": "./.medusa/server", "rootDir": "../../", "jsx": "react-jsx", "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "checkJs": false, "strictNullChecks": true, "strict": true, "paths": {"@org/helmet": ["../../libs/helmet/src/index.ts"], "@org/rate_limit": ["../../libs/rate_limit/src/index.ts"], "@org/ses": ["../../libs/ses/src/index.ts"], "@org/guest-user": ["../../shared-app/user/src/modules/cq-user/index.ts"], "@org/guest-user-service": ["../../shared-app/user/src/modules/cq-user/service.ts"], "@org/company": ["./../company/src/modules/company/index"], "@org/company-service": ["./../company/src/modules/company/service.ts"], "@org/excel": ["../../libs/util-excel/src/index.ts"], "@org/utils": ["../../libs/utils/src/index.ts"], "@org/user-link": ["../../shared-app/user/src/links/index.ts"]}}, "ts-node": {"swc": true}, "include": ["**/*", ".medusa/types/*"], "exclude": ["node_modules", ".medusa/server", ".medusa/admin", ".cache"]}