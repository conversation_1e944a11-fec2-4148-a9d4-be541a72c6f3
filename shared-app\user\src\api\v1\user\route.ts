import {
	MedusaRequest,
	MedusaResponse,
	AuthenticatedMedusaRequest,
} from '@medusajs/framework/http';
import { ContainerRegistrationKeys, Modules } from '@medusajs/framework/utils';
import { logger } from '@medusajs/framework';
import { IRegionModuleService } from '@medusajs/framework/types';
import { UpdateGuestUserSchema } from './validators';
import { z } from 'zod';
import { stringConstants } from '@org/utils';
import CQUserService from '../../../modules/cq-user/service';
import { CQUser_MODULE } from '../../../modules/cq-user';

// /**
//  * @openapi
//  * /v1/user/:
//  *   get:
//  *     summary: Retrieve details of a guest user
//  *     tags:
//  *       - Users
//  *     responses:
//  *       200:
//  *         description: Guest user details retrieved successfully
//  *         content:
//  *           application/json:
//  *             schema:
//  *               type: object
//  *               properties:
//  *                 message:
//  *                   type: string
//  *                   example: Guest User fetched successfully
//  *                 data:
//  *                   type: array
//  *                   items:
//  *                     type: object
//  *                     properties:
//  *                       id:
//  *                         type: string
//  *                         example: 01JVMG1TS0056C1ME2JZK7CCKT
//  *                       first_name:
//  *                         type: string
//  *                         example: John
//  *                       last_name:
//  *                         type: string
//  *                         example: Doe
//  *                       email:
//  *                         type: string
//  *                         format: email
//  *                         example: "<EMAIL>"
//  *                       mobile_number:
//  *                         type: string
//  *                         example: "9876543210"
//  *                       company_name:
//  *                         type: string
//  *                         example: Acme Corp
//  *                       profile_pic_url:
//  *                         type: string
//  *                         example: "https://example.com/profile.jpg"
//  *                       iso_2:
//  *                         type: string
//  *                         example: "us"
//  *                       designation:
//  *                         type: object
//  *                         properties:
//  *                           id:
//  *                             type: string
//  *                             nullable: true
//  *                             example: 01JVMG1TS0056C1ME2JZK7CCKT
//  *                           name:
//  *                             type: string
//  *                             nullable: true
//  *                             example: Product Manager
//  *                       region:
//  *                         type: object
//  *                         properties:
//  *                           id:
//  *                             type: string
//  *                             nullable: true
//  *                             example: reg_01JVMAAANX30GG5AR2SC241GNV
//  *                           country:
//  *                             type: string
//  *                             nullable: true
//  *                             example: United States
//  *       404:
//  *         description: Guest user not found
//  *         content:
//  *           application/json:
//  *             schema:
//  *               type: object
//  *               properties:
//  *                 message:
//  *                   type: string
//  *                   example: Guest User not found
//  *       500:
//  *         description: Internal server error
//  *         content:
//  *           application/json:
//  *             schema:
//  *               type: object
//  *               properties:
//  *                 message:
//  *                   type: string
//  *                   example: Internal server error occurred
//  */

export async function GET(
	req: AuthenticatedMedusaRequest,
	res: MedusaResponse
) {
	const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
	try {
		const { data: users } = await query.graph({
			entity: 'cq_user',
			fields: [
				'id',
				'first_name',
				'last_name',
				'email',
				'mobile_number',
				'company_name',
				'profile_pic_url',
				'cq_designation.id',
				'cq_designation.name',
				'country.iso_2',
				'country.display_name',
			],
			filters: { id: req?.auth_context?.actor_id },
		});
		const result = users.map((user) => ({
			id: user.id,
			first_name: user.first_name,
			last_name: user.last_name,
			full_name: `${user.first_name} ${user.last_name}`,
			email: user.email,
			mobile_number: user.mobile_number,
			company_name: user.company_name,
			profile_pic_url: user.profile_pic_file_name || null,
			designation: {
				id: user.cq_designation?.id || null,
				name: user.cq_designation?.name || null,
			},
			iso_2: user?.country?.iso_2 || null,
			persona: 'guest',
		}));
		return res.status(200).json({
			message: stringConstants.FETCHED_SUCCESSFULLY('Guest User'),
			data: result,
		});
	} catch (err: any) {
		logger.error(err);
		return res
			.status(500)
			.json({ message: stringConstants.INTERNAL_SERVER_ERROR() });
	}
}

type GuestUserSchemaType = z.infer<typeof UpdateGuestUserSchema>;

/**
 * @openapi
 * /v1/user/update-profile:
 *   put:
 *     summary: Update an existing guest user
 *     tags:
 *       - Users
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - id
 *               - first_name
 *               - last_name
 *               - designation
 *               - mobile_number
 *               - email
 *               - company_name
 *               - country_code
 *             properties:
 *               id:
 *                 type: string
 *                 example: 009
 *               first_name:
 *                 type: string
 *                 maxLength: 50
 *                 pattern: "^[A-Za-z0-9\\s'-]+$"
 *                 description: First name may include letters, numbers, spaces, hyphens, and apostrophes
 *                 example: John
 *               last_name:
 *                 type: string
 *                 maxLength: 50
 *                 pattern: "^[A-Za-z0-9\\s'-]+$"
 *                 description: Last name may include letters, numbers, spaces, hyphens, and apostrophes
 *                 example: Doe
 *               designation:
 *                 type: string
 *                 maxLength: 100
 *                 pattern: "^[A-Za-z0-9\\s\\-/]+$"
 *                 description: Designation may include letters, numbers, spaces, hyphens, and slashes
 *                 example: 01JVMA9GDP69MRQ3VFZMR19C8W
 *               mobile_number:
 *                 type: string
 *                 maxLength: 15
 *                 pattern: "^[+]?[0-9]{1,4}[-\\s]?[(]?[0-9]{1,4}[)]?[-\\s]?[0-9]+[-\\s]?[0-9]+$"
 *                 description: Must be a valid international format (e.g., +919876543210)
 *                 example: "+91-9876543210"
 *               email:
 *                 type: string
 *                 format: email
 *                 maxLength: 100
 *                 example: <EMAIL>
 *               company_name:
 *                 type: string
 *                 maxLength: 100
 *                 pattern: "^[A-Za-z0-9\\s\\-&]+$"
 *                 description: Company name may include letters, numbers, spaces, hyphens, and ampersands
 *                 example: Acme & Sons
 *               country_code:
 *                 type: string
 *                 example: US
 *     responses:
 *       200:
 *         description: Guest user updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Guest user updated successfully
 *       400:
 *         description: Email or mobile number not verified
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Email or mobile number not verified
 *       404:
 *         description: Guest user or related data not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Country not found
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error occurred
 */

export async function PUT(
	req: AuthenticatedMedusaRequest<GuestUserSchemaType>,
	res: MedusaResponse
) {
	const guestUserService: CQUserService = req.scope.resolve(CQUser_MODULE);
	const regionModuleService: IRegionModuleService = req.scope.resolve(
		Modules.REGION
	);
	const { email, mobile_number, registered_country_id } = req.body;
	try {
		const [users] = await guestUserService.listAndCountCqUsers({
			id: req.auth_context.actor_id,
		});
		const checkGuestUser = users.length ? users[0] : null;
		const [emailVerified, mobileVerified] = await Promise.all([
			guestUserService.listAndCountEmailOtpVerifications({
				email: email?.toLowerCase().trim(),
				is_verified: true,
			}),
			guestUserService.listAndCountEmailOtpVerifications({
				mobile_number: mobile_number,
				is_verified: true,
			}),
		]);

		if (!emailVerified?.length || !mobileVerified?.length) {
			return res.status(400).json({
				message: 'Email or mobile number not verified',
			});
		}
		try {
			await regionModuleService.retrieveCountry(
				registered_country_id as string
			);
		} catch (error) {
			return res.status(404).json({
				message: stringConstants.NOT_FOUND('Country'),
			});
		}
		await guestUserService.updateCqUsers({
			selector: { id: checkGuestUser?.id },
			data: {
				...req.body,
				updated_by_id: req.auth_context.actor_id,
			},
		});
		return res.status(200).json({
			message: stringConstants.UPDATED_SUCCESSFULLY('Guest user'),
		});
	} catch (err: any) {
	return res
		.status(500)
		.json({ message: stringConstants.INTERNAL_SERVER_ERROR() });
	}
}
