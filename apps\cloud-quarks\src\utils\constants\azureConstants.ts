// Azure Unbilled Usage Data Download Constants (matching .NET AzureUnbilledUsageDataDownload struct)
export const AzureUnbilledUsageDataDownload = {
  STATUS_COMPLETED: "COMPLETED",
  STATUS_NOT_PROCESSED: "NP", 
  STATUS_IN_PROGRESS: "INPROGRESS",
  THRESHOLD_DATA_DOWNLOADED: "THRESHOLDDATADOWNLOADED",
  QUEUE_CREATED: "QUEUECREATED",
  QUEUE_PROCESSING: "QUEUEPROCESSING",
  S3_URL_GENERATED: "S3URLGENERATED",
} as const;

// Messages constants (matching .NET Messages class)
export const Messages = {
  ExceptionMessage: "We are currently experiencing difficulties processing your request. Please wait and try again. Error: {0}",
  GenericExceptionMessage: "We are currently experiencing difficulties processing your request. Please wait and try again.",
  AzureUnbilledUsageDownloadRequestAccepted: "We are processing your unbilled usage report download request. Please wait or revisit this section after some time.",
  AzureUnbilledUsageDownloadRequestReady: "The unbilled usage report is successfully downloaded.",
  AzureUnbilledUsageDownloadRequestInProgress: "We have already received your request and are currently processing it. Please allow some time and revisit this section later.",
} as const;

// Configuration defaults (matching .NET configuration keys)
export const AzureConfig = {
  DEFAULT_THRESHOLD_LIMIT: 2000,
  DEFAULT_RETENTION_HOURS: 4,
  DEFAULT_UNBILLED_USAGE_TIMEOUT: 1200,
} as const;

// Configuration helper functions
export function getAzureThresholdLimit(): number {
  return AzureConfig.DEFAULT_THRESHOLD_LIMIT;
}

export function getAzureRetentionHours(): number {
  return AzureConfig.DEFAULT_RETENTION_HOURS;
}

export function getUnbilledUsageTimeout(configModule?: any): number {
  // Try to get from config module first, then environment, then default
  const configValue = configModule?.projectConfig?.appSettings?.unbilledUsageTimeout;
  const envValue = process.env.UNBILLED_USAGE_TIMEOUT;

  if (configValue) return parseInt(configValue, 10);
  if (envValue) return parseInt(envValue, 10);
  return AzureConfig.DEFAULT_UNBILLED_USAGE_TIMEOUT;
}
