import {
	AuthenticatedMedusaRequest,
	MedusaResponse,
} from '@medusajs/framework/http';
import { GetOnboardSchema } from './validator';
import { z } from 'zod';
import { stringConstants } from '@org/utils';
import CompanyRegistrationService from '../../../../modules/company/service';
import { COMPANY_REGISTRATION_MODULE } from '../../../../modules/company';
import { HttpStatusCode } from 'axios';

/**
 * @openapi
 * /v1/company/get-onboarded:
 *   post:
 *     summary: Get customer information
 *     description: Returns customer information based on Redington Account ID from SAP integration.
 *     tags:
 *       - Customer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               redingtonAccountId:
 *                 type: string
 *                 example: "RED-ACC-12345"
 *             required:
 *               - redingtonAccountId       # ✅ Only one "required"
 *     responses:
 *       200:
 *         description: Successfully retrieved customer info
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                   example: "TRUE"
 *                 Message:
 *                   type: string
 *                   example: ""
 *                 CustomerName:
 *                   type: string
 *                   example: "Burhan Tech Comp Co WLL"
 *                 AddressLine1:
 *                   type: string
 *                   example: ""
 *                 AddressLine2:
 *                   type: string
 *                   example: ""
 *                 AddressLine3:
 *                   type: string
 *                   example: "Fisheries Bldg, Floor No. 8, Office No.1"
 *                 City:
 *                   type: string
 *                   example: "Kuwait City"
 *                 Region:
 *                   type: string
 *                   example: "KW"
 *                 Country:
 *                   type: string
 *                   example: "Kuwait"
 *                 POBox:
 *                   type: string
 *                   example: "3214"
 *                 Telephone:
 *                   type: string
 *                   example: "***********"
 *                 FaxNumber:
 *                   type: string
 *                   example: "***********"
 *                 Email:
 *                   type: string
 *                   example: "<EMAIL>"
 *       400:
 *         description: Missing or invalid redingtonAccountId
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "redingtonAccountId is required"
 */

type GetOnboardSchemaType = z.infer<typeof GetOnboardSchema>;
/**
 * POST handler for fetching onboarded customer details based on Redington Account ID.
 *
 * This endpoint expects a POST request with a JSON body containing `redingtonAccountId`.
 * If the ID is present, it returns mocked customer data for demonstration or testing purposes.
 * Otherwise, it returns a 400 error indicating the ID is required.
 *
 * @param {AuthenticatedMedusaRequest} req - AuthenticatedMedusaRequest containing the request body.
 * @param {MedusaResponse} res - MedusaResponse object used to return HTTP responses.
 *
 * @returns A JSON response with customer onboarding details or an error message.
 */
export async function POST(
	req: AuthenticatedMedusaRequest<GetOnboardSchemaType>,
	res: MedusaResponse
) {
	const companyService: CompanyRegistrationService = req.scope.resolve(
		COMPANY_REGISTRATION_MODULE
	);

	try {
		const { redingtonAccountId, country } = req.body as {
			redingtonAccountId: string;
			country: string;
		};

		// 1️⃣ Input validation
		if (!redingtonAccountId) {
			return res.status(400).json({ message: stringConstants.MISSING_INPUT() });
		}
		if (!/^\d{10}$/.test(redingtonAccountId)) {
			return res
				.status(400)
				.json({ message: stringConstants.INVALID_REDINGTON_ID() });
		}

		// 2️⃣ Check existing onboarding
		const [companies] = await companyService.listAndCountCqCompanyOnboardings({
			redington_account_id: redingtonAccountId,
		});
		const existing = companies[0];

		if (existing) {
			const status = existing.status;

			if (status === 'Pending') {
				return res.status(HttpStatusCode.Conflict).json({
					success: false,
					message: stringConstants.ALREADY_REGISTERED_PENDING_ONBOARD(
						'Alex Smith',
						'<EMAIL>'
					),
					statusCode: HttpStatusCode.Conflict,
				});
			}

			if (status === 'Approved') {
				return res.status(HttpStatusCode.Conflict).json({
					success: false,
					message: stringConstants.ALREADY_ONBOARDED(
						'Alex Smith',
						'<EMAIL>'
					),
					statusCode: HttpStatusCode.Conflict,
				});
			}
		}

		// redingtonAccountId doesn't match
		// NOTE: We don't have proper SAP Data as of now, so we return the sample data
		const sampleSAPData = {
			Status: 'FALSE',
			Message: '',
			CustomerName: 'Burhan Tech Comp Co WLL',
			AddressLine1: '',
			AddressLine2: '',
			AddressLine3: 'Fisheries Bldg, Floor No. 8, Office No.1',
			City: 'Kuwait City',
			Region: 'KW',
			Country: 'Kuwait',
			POBox: '3214',
			Telephone: '***********',
			FaxNumber: '***********',
			Email: '<EMAIL>',
		};

		if (sampleSAPData.Status === 'FALSE') {
			return res.status(HttpStatusCode.Conflict).json({
				success: false,
				message: stringConstants.CHECK_COMPANY_STATUS(
					'<EMAIL>'
				),
				statusCode: HttpStatusCode.Conflict,
			});
		}

		// 5️⃣ Valid SAP Data
		return res.status(HttpStatusCode.Ok).json({
			message: stringConstants.NO_COMPANY_MATCHING_SAP_ID(),
			data: sampleSAPData,
		});
	} catch (err: any) {
		console.error('Error during onboarding:', err.message);
		return res
			.status(500)
			.json({ message: stringConstants.INTERNAL_SERVER_ERROR() });
	}
}
