import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import {
  ResponseStatus,
  PlaceTransitionRequest
} from "../../types/responses/customResponse";
import { getCustomerTenantIdAndMPNId, addSubscriptionTransition } from "./transitionDAL";
import { prepareToken } from "./prepareTokenService";
import { validatePlaceNCETransition } from "./validatePlaceNCETransitionService";

/**
 * Place NCE Transition
 * Matches .NET PlaceNCETransition method
 */
export async function placeNCETransitionService(
  req: MedusaRequest,
  request: PlaceTransitionRequest
): Promise<ResponseStatus> {
  let response: ResponseStatus = { Status: false };

  // DB call to get CustomerTenantId and MPNId
  logger.info(`Entered into BL PlaceNCETransition method and Entering into DAL GetCustomerTenantIdAndMPNId method`);
  const getCustomerTenantIdAndMPNIdResponse = await getCustomerTenantIdAndMPNId(
    req,
    request.StoreId || "",
    request.PartnerId || "",
    request.CustomerId || ""
  );
  
  logger.info(`Response from DAL GetCustomerTenantIdAndMPNId-${JSON.stringify(getCustomerTenantIdAndMPNIdResponse)}`);
  
  if (!getCustomerTenantIdAndMPNIdResponse || 
      !getCustomerTenantIdAndMPNIdResponse.CustomerTenantId || 
      getCustomerTenantIdAndMPNIdResponse.CustomerTenantId.trim() === '' ||
      !getCustomerTenantIdAndMPNIdResponse.MPNId || 
      getCustomerTenantIdAndMPNIdResponse.MPNId.trim() === '') {
    response.Status = false;
    response.Message = `For CustId -${request.CustomerId}, CustomerTenantId-${getCustomerTenantIdAndMPNIdResponse?.CustomerTenantId} And MPNId-${getCustomerTenantIdAndMPNIdResponse?.MPNId} not Found`;
    return response;
  }

  // GetToken
  logger.info(`Going to hit PrepareToken method with storeId-${request.StoreId}`);
  const token = await prepareToken(req, request.StoreId || "");
  logger.info(`Response Received from BL PrepareToken`);

  // Validations
  logger.info(`Entering ValidatePlaceNCETransition method`);
  response = await validatePlaceNCETransition(
    req,
    request,
    getCustomerTenantIdAndMPNIdResponse.CustomerTenantId,
    getCustomerTenantIdAndMPNIdResponse.MPNId,
    token
  );
  logger.info(`Response from ValidatePlaceNCETransition method with response ${JSON.stringify(response)}`);
  if (!response.Status) {
    logger.info(`Entered validationResponse is not success and returning with response ${JSON.stringify(response)}`);
    return response;
  }

  // Get CatalogItemId for FromMaterialId
  const productSku = (request.FromMaterialId || "").split(":");
  if (productSku.length !== 2) {
    response.Status = false;
    response.Message = `Invalid FromMaterialId Format - ${request.FromMaterialId}`;
    return response;
  }

  request.FromCatalogItemId = request.FromMaterialId;

  // Save PlaceNCETransition
  logger.info(`Entering into DAL AddSubscriptionTransition method with request-${JSON.stringify(request)}`);
  response = await addSubscriptionTransition(req, request);
  logger.info(`Response from DAL AddSubscriptionTransition method with response-${JSON.stringify(response)}`);

  logger.info(`Exiting from PlaceNCETransition with response ${JSON.stringify(response)}`);
  return response;
}
