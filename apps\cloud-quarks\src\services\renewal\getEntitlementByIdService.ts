import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import { GoogleApiResponse, GoogleEntitlementExtension, GoogleErrorResponse } from "../../types/responses/customResponse";
import { getGoogleTokenService } from "../token/google/getGoogleTokenService";
import { createGoogleRequestHeaders } from "../../config/microsoftEndpoints";
import { GoogleEndpoints } from "../../config/googleEndpoints";
import { getMethod } from "../externalService/externalEndPointService";
import { getGoogleAccountIdFromStore } from "../googleOffer/getGooglePurchasableOfferService";

export async function getEntitlementById(
  storeId: string,
  googleCustomerId: string,
  entitlementId: string,
  req?: MedusaRequest
): Promise<GoogleApiResponse> {
  logger.info(`Going to call DAL method GetGoogleRedingtonAccountId with StoreId ${storeId}`);

  try {
    // Get Google Account ID (equivalent to GetGoogleRedingtonAccountId)
    const googleAccountId = await getGoogleAccountIdFromStore(storeId);
    logger.info(`Got googleAccountId ${googleAccountId}`);

    // Build the URL for GetEntitlementById
    const getEntitlementByIdUrl = GoogleEndpoints.getEntitlementByIdUrl(
      googleAccountId,
      googleCustomerId,
      entitlementId
    );

    logger.info(`Going to hit GetGoogleToken method with storeId-${storeId}`);
    const token = await getGoogleTokenService(req, storeId);
    logger.info(`Got token from BL TokenService method.`);

    // Create Google request headers
    const headerList = req
      ? createGoogleRequestHeaders(req, token, storeId)
      : {
          Authorization: `Bearer ${token}`,
          "x-region": storeId,
        };

    logger.info(`Going to hit ExternalEndPoint GetMethod method with url-${getEntitlementByIdUrl} and headerlist-${JSON.stringify(headerList)}`);

    const configModule = req?.scope?.resolve("configModule");
    const response = await getMethod(
      {
        url: getEntitlementByIdUrl,
        headers: headerList,
        isVendorHit: true,
        module: "GetEntitlementById",
      },
      configModule
    );

    logger.info(`Response from ExternalEndPoint GetMethod is ${JSON.stringify(response)}`);

    if (response.isSuccessStatusCode) {
      logger.info(`Received success response and deserializing response.Content-${response.content}`);

      const entitlementResponse: GoogleEntitlementExtension = JSON.parse(response.content);
      entitlementResponse.GoogleCustomerId = googleCustomerId;
      entitlementResponse.EntitlementId = entitlementResponse.Name.split("/").pop() || entitlementId;

      const apiResponse: GoogleApiResponse = {
        Data: entitlementResponse,
        Message: "",
        StatusCode: 200,
        IsError: false
      };

      logger.info(`Going to return GetEntitlementById API response- ${JSON.stringify(apiResponse)}`);
      return apiResponse;
    } else {
      logger.info(`Received errored response and deserialising response.Content-${response.content}`);

      const errorResponse: GoogleErrorResponse = JSON.parse(response.content);
      const apiResponse: GoogleApiResponse = {
        Message: errorResponse.Error?.Message || "Unknown error",
        StatusCode: errorResponse.Error?.Code || response.httpStatusCode,
        IsError: true,
        Data: null
      };

      logger.info(`Going to return GetEntitlementById API response- ${JSON.stringify(apiResponse)}`);
      return apiResponse;
    }
  } catch (error) {
    logger.error(`Error in GetEntitlementById: ${error}`);

    const apiResponse: GoogleApiResponse = {
      Message: error instanceof Error ? error.message : "Internal server error",
      StatusCode: 500,
      IsError: true,
      Data: null
    };

    return apiResponse;
  }
}