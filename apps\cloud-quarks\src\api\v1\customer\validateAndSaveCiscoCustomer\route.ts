import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import {
  CiscoCustomerValidateDto,
  CiscoValidateCustomerRequest,
  CiscoCustomerModel,
  CiscoStatusResponseType,
  CiscoCustomerValidateParty
} from "../../../../types/responses/customResponse";
import { validateCiscoCustomerValidateDto } from "../../../../validators/customer/ciscoCustomerValidateValidator";
import { validateAndSaveCiscoCustomerService } from "../../../../services/customer/validateAndSaveCiscoCustomerService";
import { CiscoConstants } from "../../../../validators/reseller/addUpdateCiscoResellerValidator";

// Helper function to normalize string comparison (matching .NET ComparableString extension)
function comparableString(str?: string): string {
  return (str || "").trim().toUpperCase();
}

/**
 * @openapi
 * /v1/customer/validateAndSaveCiscoCustomer:
 *   post:
 *     summary: Validate and Save Cisco Customer
 *     description: Validates Cisco customer data and saves it to the database
 *     tags:
 *       - Customer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               custId:
 *                 type: string
 *                 example: "cust123"
 *               storeId:
 *                 type: string
 *                 example: "store123"
 *               partyName:
 *                 type: string
 *                 example: "Example Corp"
 *               addressLine1:
 *                 type: string
 *                 example: "123 Main St"
 *               addressLine2:
 *                 type: string
 *                 example: "Suite 100"
 *               city:
 *                 type: string
 *                 example: "San Francisco"
 *               state:
 *                 type: string
 *                 example: "CA"
 *               postalCode:
 *                 type: string
 *                 example: "94105"
 *               country:
 *                 type: string
 *                 example: "United States"
 *               countryCode:
 *                 type: string
 *                 example: "US"
 *               businessContactName:
 *                 type: string
 *                 example: "John Doe"
 *               businessContactEmail:
 *                 type: string
 *                 example: "<EMAIL>"
 *               businessContactNumber:
 *                 type: string
 *                 example: "+1-555-123-4567"
 *     responses:
 *       200:
 *         description: Successfully validated and updated customer
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                   example: "PASS"
 *                 Message:
 *                   type: string
 *                   example: "Customer updated successfully"
 *                 ActionType:
 *                   type: string
 *                   example: "UPDATE"
 *       201:
 *         description: Successfully validated and created customer
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                   example: "PASS"
 *                 Message:
 *                   type: string
 *                   example: "Customer created successfully"
 *                 ActionType:
 *                   type: string
 *                   example: "INSERT"
 *       400:
 *         description: Bad request - validation errors or business logic failure
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                   example: "FAIL"
 *                 Message:
 *                   type: string
 *                   example: "Validation failed: [error details]"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                   example: "FAIL"
 *                 Message:
 *                   type: string
 *                   example: "An error occurred: [error details]"
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req);

  logger.info(`Entered into ValidateAndSaveCiscoCustomer API with CorrelationId: ${correlationId}`);

  try {
    const customerValidateDto = req.body as CiscoCustomerValidateDto;
    logger.info(`Entered into ValidateAndSaveCiscoCustomer API with details ${JSON.stringify(customerValidateDto)}`);

    logger.info("Going to validate the customer dto object");

    // Validate the request DTO (matching .NET validation)
    const validationResult = validateCiscoCustomerValidateDto(customerValidateDto);
    logger.info(`Validations error count on customer dto object is ${validationResult.errors.length}`);
    logger.info(`Validations error : ${JSON.stringify(validationResult)}`);

    if (validationResult.errors.length > 0) {
      logger.info("Entered into section where validationResult.Errors.Count > 0");
      logger.info("Going to extract errors from validationResult object");

      const validationErrors = validationResult.errors;
      logger.info(`validation errors are ${JSON.stringify(validationErrors)}`);

      logger.info(`Creating bad response object.`);
      const validationErrorResponse: CiscoStatusResponseType = {
        Status: CiscoConstants.StatusFail,
        Message: validationErrors.join(",")
      };
      logger.info(`Going to return bad response ${JSON.stringify(validationErrorResponse)}`);
      return res.status(400).json(validationErrorResponse);
    } else {
      logger.info("Entered into section where there is no validation error");

      // Map DTO to Cisco Request (matching .NET: _mapper.Map<CiscoValidateCustomerRequest>(customerValidateDto))
      logger.info(`Going to map customer dto to CiscoCustomerRequest. CustomerDto: ${JSON.stringify(customerValidateDto)}`);
      const customerModelForCiscoRequest: CiscoValidateCustomerRequest = {
        storeId: customerValidateDto.storeId,
        bodID: 0, // Default value, will be set by service if needed
        timestamp: new Date().toISOString(),
        party: [
          {
            sequenceID: 1,
            partyName: customerValidateDto.partyName,
            postalCode: customerValidateDto.postalCode,
            addressLine1: customerValidateDto.addressLine1,
            addressLine2: customerValidateDto.addressLine2 || "",
            city: customerValidateDto.city,
            countryCode: customerValidateDto.countryCode,
            county: "", // Default empty value
            state: customerValidateDto.state
          } as CiscoCustomerValidateParty
        ]
      };
      logger.info(`Cisco Validate Customer request model after mapping : ${JSON.stringify(customerModelForCiscoRequest)}`);

      // Map DTO to DAL Model (matching .NET: _mapper.Map<CiscoCustomerModel>(customerValidateDto))
      logger.info(`Going to map customer dto to customerModelForDAL. CustomerDto: ${JSON.stringify(customerValidateDto)}`);
      const customerModelForDAL: CiscoCustomerModel = {
        custId: customerValidateDto.custId,
        storeId: customerValidateDto.storeId,
        partyName: customerValidateDto.partyName,
        addressLine1: customerValidateDto.addressLine1,
        addressLine2: customerValidateDto.addressLine2,
        city: customerValidateDto.city,
        state: customerValidateDto.state,
        postalCode: customerValidateDto.postalCode,
        country: customerValidateDto.country,
        countryCode: customerValidateDto.countryCode,
        businessContactName: customerValidateDto.businessContactName,
        businessContactEmail: customerValidateDto.businessContactEmail,
        businessContactNumber: customerValidateDto.businessContactNumber
      };
      logger.info(`Cisco Customer Model for DAL : ${JSON.stringify(customerModelForDAL)}`);

      logger.info(`Going to customer service -- ValidateAndSaveCiscoCustomer method with CiscoCustomerRequestModel ${JSON.stringify(customerModelForCiscoRequest)} and CiscoCustomerModelForDal ${JSON.stringify(customerModelForDAL)}`);

      // Call the service method (matching .NET service call)
      const result = await validateAndSaveCiscoCustomerService(customerModelForCiscoRequest, customerModelForDAL, req);
      logger.info(`Response from customer service: ${JSON.stringify(result)}`);

      // Handle response based on status (matching .NET response handling)
      if (comparableString(result.Status) === comparableString(CiscoConstants.StatusPass)) {
        logger.info(`Entered into response Pass status`);

        if (comparableString(result.ActionType) === comparableString(CiscoConstants.DbActionInsert)) {
          logger.info(`Going to return created status with result : ${JSON.stringify(result)}`);
          return res.status(201).json(result);
        } else if (comparableString(result.ActionType) === comparableString(CiscoConstants.DbActionUpdate)) {
          logger.info(`Going to return ok status with result : ${JSON.stringify(result)}`);
          return res.status(200).json(result);
        }
      } else if (comparableString(result.Status) === comparableString(CiscoConstants.StatusFail)) {
        logger.info(`Going to return Bad request with result : ${JSON.stringify(result)}`);
        return res.status(400).json(result);
      }

      // Default return (matching .NET: return Ok())
      return res.status(200).json({});
    }

  } catch (exp: any) {
    logger.error(`Exception in ValidateAndSaveCiscoCustomer API method. Message: ${exp.message}, StackTrace: ${exp.stack}`);

    const errorResponse: CiscoStatusResponseType = {
      Status: CiscoConstants.StatusFail,
      Message: `An error occurred: ${exp.message}`
    };

    return res.status(500).json(errorResponse);
  }
}