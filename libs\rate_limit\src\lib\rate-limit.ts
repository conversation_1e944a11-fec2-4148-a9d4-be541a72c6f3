import rateLimit, { RateLimitRequestHandler } from 'express-rate-limit';

// Define the middleware type alias from express-rate-limit
type RateLimitMiddleware = RateLimitRequestHandler;

export const createDefaultRateLimiter = (): RateLimitMiddleware => {
  return rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 100,
    message: 'Too many requests from this IP, please try again later',
    standardHeaders: true,
    legacyHeaders: false,
    skipSuccessfulRequests: false, 
  });
};

export const createAuthRateLimiter = (): RateLimitMiddleware => {
  return rateLimit({
    windowMs: 60 * 60 * 1000,
    max: 5,
    message: 'Too many login attempts from this IP, please try again later',
    standardHeaders: true,
    legacyHeaders: false,
    skipSuccessfulRequests: false,
  });
};
