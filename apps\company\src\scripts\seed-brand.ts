import { ExecArgs } from '@medusajs/framework/types';
import PartnerRegistrationModuleService from '../modules/company/service';
import { COMPANY_REGISTRATION_MODULE } from '../modules/company';
import { Modules } from '@medusajs/framework/utils';
import { masterDB } from '../utils/master-db';

export default async function seedBrands({ container }: ExecArgs) {
  const brandService: PartnerRegistrationModuleService = container.resolve(
    COMPANY_REGISTRATION_MODULE
  );

  const targetBrands = [
    { brand_name: 'Toyota', vendor_name: 'Microsoft' },
    { brand_name: 'Honda', vendor_name: 'Google' },
    { brand_name: 'Ford', vendor_name: 'Microsoft' },
    { brand_name: 'BMW', vendor_name: 'Google' },
    { brand_name: 'Tesla', vendor_name: 'Microsoft' },
  ];

  const targetRegionNames = ['India', 'Turkey', 'MEA'];

  // Step 1: Get all vendors and map by name
  const allVendors = await brandService.listVendors();
  const vendorMap = new Map(
    allVendors.map((v: any) => [v.vendor_name.toLowerCase(), v.id])
  );

  // Step 2: Get all regions from master DB and map by name
  const { rows: allRegions } = await masterDB.query('SELECT id, name FROM region');

  const regionMap = new Map(
    allRegions.map((r: any) => [r.name.toLowerCase(), r.id])
  );

  const targetRegionIds = targetRegionNames
    .map((name) => regionMap.get(name.toLowerCase()))
    .filter(Boolean);

  if (targetRegionIds.length !== targetRegionNames.length) {
    console.warn('Some region names were not found in the Region table.');
  }

  // Step 3: Get existing brand names
  const existingBrands = await brandService.listBrands({});
  const existingBrandNames = new Set(
    existingBrands.map((b) => b.brand_name.toLowerCase())
  );

  // Step 4: Prepare new brands with vendor_id mapped
  const brandsToCreate = targetBrands
    .map((b) => {
      const vendor_id = vendorMap.get(b.vendor_name.toLowerCase());
      if (!vendor_id) {
        console.warn(`Vendor not found: ${b.vendor_name}`);
        return null;
      }

      return {
        brand_name: b.brand_name,
        vendor_id,
        metadata: {},
      };
    })
    .filter((b) => {
      if (!b || !b.brand_name || !b.vendor_id) return false;
      return !existingBrandNames.has(b.brand_name.toLowerCase());
    });

  let createdCount = 0;

  for (const brand of brandsToCreate) {
    if (!brand) {
      continue;
    }
    const createdBrand = await brandService.createBrands(brand);
    await Promise.all(
      targetRegionIds.map((region_id) =>
        brandService.createRegionBrands({
          brand_id: createdBrand.id,
          region_id,
          metadata: {},
        })
      )
    );
    createdCount++;
  }

  console.log(`Seeded ${createdCount} brand(s) with region assignments.`);
}
