import { MicrosoftCreateCustomerRequest } from "../../config/microsoftEndpoints";

export function MicrosoftCreateCustomerRequestMapper(dto: any): MicrosoftCreateCustomerRequest {
  return {
    CompanyProfile: {
      Domain: dto.DomainId,
      OrganizationRegistrationNumber: dto.OrganizationRegistrationNumber,
    },
    BillingProfile: {
      Language: "en", // static
      CompanyName: dto.OrgName,
      Email: dto.EmailId,
      Culture: "en-US", // static
      DefaultAddress: {
        FirstName: dto.FirstName,
        LastName: dto.LastName,
        AddressLine1: dto.AddressLine1,
        City: dto.City,
        State: dto.State,
        PostalCode: dto.PostalCode,
        Country: dto.CountryId,
        PhoneNumber: dto.PhoneNumber,
      },
    },
    AssociatedPartnerId: dto.AssociatedPartnerId,
  }
}
