import { MedusaService } from '@medusajs/framework/utils';
import { InjectManager, MedusaContext } from '@medusajs/framework/utils';
import { Context } from '@medusajs/framework/types';
import { EntityManager } from '@mikro-orm/knex';
import { MasterModel } from './models/master-model';

class MasterModuleService extends MedusaService({ MasterModel }) {
  @InjectManager()
  async InsertCountry(
    {
      iso_2,
      iso_3,
      num_code,
      name,
      region_id,
      display_name,
    }: {
      iso_2: string;
      iso_3: string;
      num_code: string;
      name: string;
      region_id: string;
      display_name: string;
    },
    @MedusaContext() sharedContext?: Context<EntityManager>
  ) {
    try {
      const data = await sharedContext?.manager?.execute(
        `
      INSERT INTO region_country (iso_2, iso_3, num_code, name, region_id, display_name)
      VALUES (?, ?, ?, ?, ?, ?)
      `,
        [iso_2, iso_3, num_code, name, region_id, display_name]
      );
      return data;
    } catch (error) {
      throw error;
    }
  }
}

export default MasterModuleService;
