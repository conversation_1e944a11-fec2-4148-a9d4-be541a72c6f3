import {
  MedusaRequest,
  MedusaResponse,
  MedusaNextFunction,
} from '@medusajs/framework/http';
import axios from 'axios';
import jwt from 'jsonwebtoken';
import * as dotenv from 'dotenv';
dotenv.config();
import { stringConstants } from './utils';
declare module '@medusajs/framework/http' {
  interface MedusaRequest {
    auth_context?: {
      actor_id: string;
      actor_type: string;
      user_id: string;
      authenticated: boolean;
      [key: string]: any;
    };
  }
}

interface SessionData {
  token: string;
  actor_id: string;
  user_id: string;
  actor_type: string;
  created_at: string;
  expires_at: string;
}

export async function customAuthenticate(
  req: MedusaRequest,
  res: MedusaResponse,
  next: MedusaNextFunction
) {
  try {
    let userId: string | null = null;
    let actorId: string | null = null;
    let actorType: string = 'cq_user';

    if (req.session) {
      userId = req.cookies.actor_id;
      actorId = req.session.actorId;
      actorType = req.session.actorType || 'cq_user';
    }

    if (!userId && req.cookies?.custom_auth_session) {
      try {
        const cookieValue = decodeURIComponent(req.cookies.custom_auth_session);
        const sessionData: SessionData = JSON.parse(cookieValue);
        if (
          sessionData.expires_at &&
          new Date(sessionData.expires_at) < new Date()
        ) {
          return res.status(401).json({
            message: stringConstants.SESSION_EXPIRED(),
          });
        }

        userId = sessionData.user_id;
        actorId = sessionData.actor_id;
        actorType = sessionData.actor_type;
      } catch (cookieError) {
        console.error('Error parsing auth cookie:', cookieError);
      }
    }

    if (!userId) {
      const authHeader =
        req.headers['authorization'] || req.headers['Authorization'];

      if (
        authHeader &&
        typeof authHeader === 'string' &&
        authHeader.startsWith('Bearer ')
      ) {
        const token = authHeader.slice(7);

        try {
          const decoded: any = jwt.decode(token);
          if (decoded) {
            userId =
              decoded?.user_id ||
              decoded?.sub ||
              decoded?.app_metadata?.cq_user_id;
            actorId = decoded?.actor_id || userId;
          }
        } catch (jwtError) {
          console.error('JWT decode error:', jwtError);
        }
      }
    }

    if (userId) {
      try {
        const headers: any = {
          'Content-Type': 'application/json',
        };

        if (req.headers.cookie) {
          headers['Cookie'] = req.headers.cookie;
        }

        if (req.headers.authorization) {
          headers['Authorization'] = req.headers.authorization;
        }

        const { data } = await axios.get(
          `${
            process.env.USER_SERVICE_URL || 'http://localhost:8080'
          }/v1/user/authenticate-validator`,
          {
            headers,
            withCredentials: true,
            timeout: 5000,
          }
        );

        if (data && data.data && data.authenticated) {
          req.auth_context = {
            ...data.data,
            authenticated: true,
          };

          if (req.session) {
            req.session.userId = data.data.user_id;
            req.session.actorId = data.data.actor_id;
            req.session.actorType = data.data.actor_type;
            req.session.authenticated = true;
          }
          return next();
        } else {
          return res.status(401).json({
            message: stringConstants.AUTHENTICATION_FAILED(),
          });
        }
      } catch (validatorError: any) {
        if (validatorError.response) {
          return res.status(validatorError.response.status).json({
            ...validatorError.response.data,
            source: stringConstants.AUTHENTICATE_VALIDATOR(),
          });
        }

        return res.status(500).json({
          message: stringConstants.AUTHENTICATION_SERVICE_ERROR(),
          error: validatorError.message,
        });
      }
    } else {
      return res.status(401).json({
        message: stringConstants.AUTHENTICATION_REQUIRED(),
      });
    }
  } catch (err: any) {
    const errorMessage = err instanceof Error ? err.message : String(err);
    return res.status(500).json({
      message: stringConstants.INTERNAL_SERVER_ERROR(),
      error: errorMessage,
    });
  }
}
