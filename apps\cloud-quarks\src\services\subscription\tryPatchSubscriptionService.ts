import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import { PatchMethodWithRequestJsonBodyAndHeaders400Handle } from "../externalService/externalEndPointService";
import { MicrosoftEndpoints } from "../../config/microsoftEndpoints";
import { 
  PatchSubscriptionModel, 
  GetSubscriptionResponse, 
  ResponseError 
} from "../../types/responses/customResponse";
import { SubStatus } from "../../utils/constants/subscriptionStatusConstants";
import { getSubscriptionById } from "../entitlement/subscriptionService";
import { getHttpClientTimeout } from "../../utils/constants/subscriptionConstants";

/**
 * Try Patch Subscription
 * Matches .NET TryPatchSubscription method
 */
export async function tryPatchSubscriptionService(
  req: MedusaRequest,
  request: PatchSubscriptionModel,
  token: string,
  endPoint: string
): Promise<GetSubscriptionResponse> {
  logger.info(`Entered into TryPatchSubscription Service method with request ${JSON.stringify(request)}, endPoint ${endPoint}`);

  const status = request.status || "";

  // #region Check if patch is already done at Microsoft
  logger.info(`Checking current subscription status before patching`);
  const subResponse = await getSubscriptionById(req, request.customerId || "", request.subscriptionId || "", token);

  // Check if status change is already applied (matching .NET pre-check logic)
  if ((status === SubStatus.DELETED || status === SubStatus.SUSPENDED || status === SubStatus.ACTIVE) &&
      (subResponse.Status === request.status || subResponse.Status === SubStatus.EXPIRED)) {
    logger.info(`Subscription status is already ${subResponse.Status}, returning existing response`);
    return subResponse;
  }

  if ((status === SubStatus.UP || status === SubStatus.DOWN) && subResponse.Quantity === request.quantity) {
    logger.info(`Subscription quantity is already ${subResponse.Quantity}, returning existing response`);
    return subResponse;
  }
  
  if ((status === SubStatus.AUTORENEWON || status === SubStatus.AUTORENEWOFF) && 
      subResponse.AutoRenewEnabled === (status === SubStatus.AUTORENEWON)) {
    logger.info(`Subscription AutoRenew is already ${subResponse.AutoRenewEnabled}, returning existing response`);
    return subResponse;
  }
  
  if (status === SubStatus.MPNIDUPDATE && subResponse.PartnerId === request.partnerId) {
    logger.info(`Subscription PartnerId is already ${subResponse.PartnerId}, returning existing response`);
    return subResponse;
  }
  // #endregion

  // #region Call Patch API
  logger.info(`Creating dynamic model for patch request based on status: ${status}`);

  // Create dynamic model based on status (matching .NET dynamic model creation)
  let model: any;

  if (status === SubStatus.UP || status === SubStatus.DOWN) {
    model = {
      Id: request.subscriptionId,
      Quantity: request.quantity
    };
  } else if (status === SubStatus.AUTORENEWON || status === SubStatus.AUTORENEWOFF) {
    model = {
      Id: request.subscriptionId,
      AutoRenewEnabled: request.autoRenewToUpdate
    };
  } else if (status === SubStatus.SUSPENDED || status === SubStatus.ACTIVE) {
    model = {
      Id: request.subscriptionId,
      Status: request.status
    };
  } else if (status === SubStatus.MPNIDUPDATE) {
    model = {
      Id: request.subscriptionId,
      PartnerId: request.partnerId
    };
  } else {
    model = {
      Id: request.subscriptionId,
      Status: request.status,
      Quantity: subResponse.Quantity,
      AutoRenewEnabled: subResponse.AutoRenewEnabled
    };
  }

  logger.info(`Request initiated to PatchSubscription endPoint ${endPoint}, request ${JSON.stringify(model)}`);

  // Prepare headers (matching .NET: client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token))
  const headerList = {
    Authorization: `Bearer ${token}`,
    Accept: "application/json",
    "Content-Type": "application/json",
  };

  const configModule = req.scope.resolve("configModule");
  
  // Get HTTP client timeout (matching .NET: client.Timeout = TimeSpan.FromSeconds(httpClientTimeout))
  const httpClientTimeoutSeconds = getHttpClientTimeout(configModule);

  try {
    // Make the PATCH request (matching .NET: client.PatchAsync(new Uri(endPoint), httpContent).Result)
    const response = await PatchMethodWithRequestJsonBodyAndHeaders400Handle(
      {
        url: endPoint,
        body: model,
        headers: headerList,
        isVendorHit: true,
        module: "PatchSubscription",
      },
      configModule
    );

    // Extract correlation IDs from response headers (matching .NET header extraction)
    // Note: In Node.js, we don't have direct access to response headers in this pattern
    // This would need to be implemented in the external service if needed
    
    logger.info(`Response from PatchSubscription, response ${response.content}`);

    if (response.isSuccessStatusCode) {
      // Parse the response body (matching .NET: JsonConvert.DeserializeObject<GetSubscriptionResponse>(dataObjects))
      const subscriptionResponse: GetSubscriptionResponse = JSON.parse(response.content);
      
      // Set status as pending if Microsoft accepts the request (matching .NET status logic)
      // response.StatusCode == HttpStatusCode.Accepted || response.StatusCode == HttpStatusCode.OK
      if (response.httpStatusCode === 202 || response.httpStatusCode === 200) {
        subscriptionResponse.Status = "pending";
      }
      
      logger.info(`Returning response - ${JSON.stringify(subscriptionResponse)}`);
      return subscriptionResponse;
    } else {
      // Handle error response (matching .NET error handling)
      try {
        const error: ResponseError = JSON.parse(response.content);
        logger.info(`Returning Exception - ${error.code}${error.description}`);
        throw new Error("{" + error.code + "} " + error.description);
      } catch (parseError) {
        // If parsing fails, use generic error
        logger.error(`Failed to parse error response: ${parseError}`);
        throw new Error(response.errorMessage || "Error occurred during patch subscription");
      }
    }
  } catch (error: any) {
    logger.error(`Error in tryPatchSubscriptionService: ${error.message}`);
    throw error;
  }
  // #endregion
}
