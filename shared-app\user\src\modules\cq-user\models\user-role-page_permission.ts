import { model } from '@medusajs/framework/utils';
import CQ<PERSON>ser from './cq_user';
import Role<PERSON>agePermission from './role-page-permission';
import Page from './page';
import Permission from './permission';

const UserRolePagePermission = model.define('cq_user_role_page_permission', {
  id: model.id().primaryKey(),
  user: model.belongsTo(() => CQUser, { mappedBy: undefined }).nullable(),
  role_page_permission: model
    .belongsTo(() => RolePagePermission, {
      mappedBy: undefined,
    })
    .nullable(),
  page: model.belongsTo(() => Page, { mappedBy: undefined }).nullable(),
  permission: model
    .belongsTo(() => Permission, { mappedBy: undefined })
    .nullable(),
  is_direct_permission: model.boolean().default(false),
});

export default UserRolePagePermission;
