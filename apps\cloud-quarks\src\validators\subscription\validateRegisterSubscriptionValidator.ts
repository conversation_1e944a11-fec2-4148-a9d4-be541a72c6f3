import { z } from "zod";
import logger from "../../utils/logger";

// Zod schema for SubscriptionObj (matching .NET SubscriptionObj)
export const SubscriptionObjSchema = z.object({
  SubscriptionId: z.string().min(1, "SubscriptionId cannot be blank or null"),
});

export type SubscriptionObjType = z.infer<typeof SubscriptionObjSchema>;

/**
 * Validate ValidateRegisterSubscription request body (matching .NET validation patterns)
 */
export function validateRegisterSubscriptionRequest(requestBody: any): string[] {
  logger.info(`Entered into validateRegisterSubscriptionRequest method with requestBody: ${JSON.stringify(requestBody)}`);
  
  const validationErrors: string[] = [];

  try {
    SubscriptionObjSchema.parse(requestBody);
    logger.info(`Zod validation passed for ValidateRegisterSubscription request`);
  } catch (error: any) {
    if (error.errors) {
      error.errors.forEach((err: any) => {
        const fieldName = err.path.join('.');
        validationErrors.push(`${fieldName}: ${err.message}`);
      });
    }
    logger.error(`Zod validation failed: ${JSON.stringify(error.errors)}`);
  }

  // Additional custom validations (matching .NET patterns)
  if (!requestBody.SubscriptionId || requestBody.SubscriptionId.trim() === '') {
    validationErrors.push("The 'SubscriptionId' parameter is missing or empty in the request body.");
  }

  logger.info(`Validation completed. Errors found: ${validationErrors.length}`);
  return validationErrors;
}
