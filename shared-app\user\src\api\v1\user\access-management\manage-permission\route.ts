import {
  MedusaResponse,
  AuthenticatedMedusaRequest,
} from '@medusajs/framework/http';
import { logger } from '@medusajs/framework';
import { formatPagination, stringConstants } from '@org/utils';
import CQUserService from '../../../../../modules/cq-user/service';
import { CQUser_MODULE } from '../../../../../modules/cq-user';

/**
 * @openapi
 * /v1/user/access-management/manage-permission/:
 *   get:
 *     summary: Retrieve list of modules with associated pages and permissions
 *     tags:
 *       - Modules
 *     parameters:
 *       - name: page
 *         in: query
 *         required: false
 *         schema:
 *           type: integer
 *           default: 1
 *       - name: page_size
 *         in: query
 *         required: false
 *         schema:
 *           type: integer
 *           default: 10
 *     responses:
 *       200:
 *         description: Module list retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Role Module fetched successfully
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         example: 01JZ2EHJSZXZZ1AH946PCCJNR3
 *                       module:
 *                         type: string
 *                         example: Accounts
 *                       order_id:
 *                         type: integer
 *                         example: 2
 *                       persona_id:
 *                         type: string
 *                         example: 01JZ2EHJRX9W4B76RCBRH5985Z
 *                       persona:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             example: 01JZ2EHJRX9W4B76RCBRH5985Z
 *                       pages:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             id:
 *                               type: string
 *                               example: 01JZ79C7APGQPA3AG8ZWTZP2HR
 *                             page:
 *                               type: string
 *                               example: Company Registration
 *                             order_id:
 *                               type: integer
 *                               example: 1
 *                             module_id:
 *                               type: string
 *                               example: 01JZ2EHJSZXZZ1AH946PCCJNR3
 *                             permissions:
 *                               type: array
 *                               items:
 *                                 type: object
 *                                 properties:
 *                                   id:
 *                                     type: string
 *                                     example: 01JZ2EHJR7V21FXE8M5V7RPAZJ
 *                                   permission:
 *                                     type: string
 *                                     example: Edit
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                       example: 1
 *                     pageSize:
 *                       type: integer
 *                       example: 10
 *                     totalItems:
 *                       type: integer
 *                       example: 17
 *                     totalPages:
 *                       type: integer
 *                       example: 2
 *                     hasNextPage:
 *                       type: boolean
 *                       example: true
 *                     hasPrevPage:
 *                       type: boolean
 *                       example: false
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error occurred
 */

export async function GET(
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) {
  const guestUserService: CQUserService = req.scope.resolve(CQUser_MODULE);

  function sanitize(obj: any): any {
    if (Array.isArray(obj)) {
      return obj.map(sanitize);
    } else if (obj && typeof obj === 'object') {
      const {
        metadata,
        created_by_id,
        updated_by_id,
        deleted_by_id,
        created_at,
        updated_at,
        deleted_at,
        created_by,
        updated_by,
        deleted_by,
        ...rest
      } = obj;

      for (const key in rest) {
        if (
          rest[key] &&
          (typeof rest[key] === 'object' || Array.isArray(rest[key]))
        ) {
          rest[key] = sanitize(rest[key]);
        }
      }
      return rest;
    }
    return obj;
  }

  try {
    let page = parseInt(req.query.page as string, 10);
    let pageSize = parseInt(req.query.page_size as string, 10);

    if (isNaN(page) || page < 1) page = 1;
    if (isNaN(pageSize) || pageSize < 1) pageSize = 10;

    const [modules, count] = await guestUserService.listAndCountCqModules(
      {},
      { relations: ['pages', 'pages.permissions'] }
    );

    const sanitizedModules = sanitize(modules);

    const totalItems = count ?? 0;

    return res.status(200).json({
      message: stringConstants.FETCHED_SUCCESSFULLY('Role Module'),
      data: sanitizedModules,
      pagination: formatPagination({ page, pageSize, totalItems }),
    });
  } catch (err: any) {
    logger.error(err);
    return res
      .status(500)
      .json({ message: stringConstants.INTERNAL_SERVER_ERROR() });
  }
}
