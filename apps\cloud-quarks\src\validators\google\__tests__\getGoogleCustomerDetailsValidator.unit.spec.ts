import { validateGetGoogleCustomerDetailsRequest } from "../getGoogleCustomerDetailsValidator";
import { isCustIdValid } from "../../../services/customer/customerValidationService";

// Mock dependencies
jest.mock("../../../services/customer/customerValidationService");

const mockIsCustIdValid = isCustIdValid as jest.MockedFunction<typeof isCustIdValid>;

describe("validateGetGoogleCustomerDetailsRequest", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should return no errors for valid customer ID", async () => {
    // Arrange
    const custId = "customer123";
    mockIsCustIdValid.mockResolvedValue(true);

    // Act
    const result = await validateGetGoogleCustomerDetailsRequest(custId);

    // Assert
    expect(result).toEqual([]);
    expect(mockIsCustIdValid).toHaveBeenCalledWith(custId);
  });

  it("should return error when custId is null", async () => {
    // Arrange
    const custId = "";

    // Act
    const result = await validateGetGoogleCustomerDetailsRequest(custId);

    // Assert
    expect(result).toEqual(["CustId can't be null or empty"]);
    expect(mockIsCustIdValid).not.toHaveBeenCalled();
  });

  it("should return error when custId is whitespace", async () => {
    // Arrange
    const custId = "   ";

    // Act
    const result = await validateGetGoogleCustomerDetailsRequest(custId);

    // Assert
    expect(result).toEqual(["CustId can't be null or empty"]);
    expect(mockIsCustIdValid).not.toHaveBeenCalled();
  });

  it("should return error when custId doesn't exist", async () => {
    // Arrange
    const custId = "nonexistent123";
    mockIsCustIdValid.mockResolvedValue(false);

    // Act
    const result = await validateGetGoogleCustomerDetailsRequest(custId);

    // Assert
    expect(result).toEqual(["CustId doesn't exists"]);
    expect(mockIsCustIdValid).toHaveBeenCalledWith(custId);
  });

  it("should return error when customer validation service throws", async () => {
    // Arrange
    const custId = "customer123";
    mockIsCustIdValid.mockRejectedValue(new Error("Database connection failed"));

    // Act
    const result = await validateGetGoogleCustomerDetailsRequest(custId);

    // Assert
    expect(result).toEqual(["Error validating customer ID"]);
    expect(mockIsCustIdValid).toHaveBeenCalledWith(custId);
  });
});
