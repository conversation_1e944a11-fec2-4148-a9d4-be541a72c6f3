import { MedusaRequest } from "@medusajs/framework";
import logger from "../../utils/logger";
import { 
  GoogleAPIResponse, 
  GoogleValidateEndCustomerRequest,
  GoogleCustomerDetailModel,
  PurchasableOffer,
  CPIdResponseModel
} from "../../types/responses/customResponse";
import { getSubscriptionDetailByMaterialNo, SubscriptionModel } from "../subscription/subscriptionDAL";
import { getCPIDDetailsService } from "../googlePartnerManagement/getCPIDDetailsService";
import { getGoogleCustomerByIdService } from "./getGoogleCustomerByIdService";
import { getGooglePurchasableOfferService } from "../googleOffer/getGooglePurchasableOfferService";
import { Messages } from "./verifyDomainExistsService";

// Helper function to normalize string comparison (matching .NET ComparableString extension)
function comparableString(str?: string): string {
  return (str || "").trim().toUpperCase();
}

/**
 * Validate Google End Customer
 * Matches .NET GoogleCustomerService.ValidateGoogleEndCustomer method
 */
export async function validateGoogleEndCustomerService(
  request: GoogleValidateEndCustomerRequest,
  req?: MedusaRequest
): Promise<GoogleAPIResponse> {
  logger.info(`Entered into ValidateGoogleEndCustomer service method with request: ${JSON.stringify(request)}`);

  let cpIdResponse: CPIdResponseModel = { StatusCode: 200, IsError: false };
  let customerResponse: GoogleAPIResponse = { Message: "Success", StatusCode: 200, IsError: false };
  let offerResponse: GoogleAPIResponse = { Message: "Success", StatusCode: 200, IsError: false };
  let apiResponse: GoogleAPIResponse = { Message: "Success", StatusCode: 200, IsError: false };

  let isEntitlementExists = false;
  let isEntitlementExpired = false;
  let subscription: SubscriptionModel | null = null;

  try {
    // Create material number (matching .NET: request.ProductId + ":" + request.SkuId)
    const materialNo = `${request.productId}:${request.skuId}`;
    
    logger.info(`Going to call DAL GetSubscriptionDetailByMaterialNo with storeId: ${request.storeId}, custId: ${request.custId}, materialNo: ${materialNo}`);
    
    // Get subscription details (matching .NET: _subscriptionDAL.GetSubscriptionDetailByMaterialNo)
    subscription = await getSubscriptionDetailByMaterialNo(request.storeId, request.custId, materialNo);

    isEntitlementExists = subscription !== null;
    isEntitlementExpired = subscription?.Status && (
      comparableString(subscription.Status) === "DELETED" || 
      subscription.EndDate <= new Date()
    ) || false;

    logger.info(`Fetched isEntitlementExists: ${isEntitlementExists} & isEntitlementExpired: ${isEntitlementExpired}`);

    // Create parallel tasks (matching .NET Task.WhenAll pattern)
    const tasks: Promise<void>[] = [
      // Task 1: Get CP ID Details
      (async () => {
        cpIdResponse = await getCPIDDetailsService(
          request.cpId, 
          request.storeId, 
          request.partnerId, 
          request.brandId, 
          req
        );
      })(),
      
      // Task 2: Get Google Customer By ID
      (async () => {
        customerResponse = await getGoogleCustomerByIdService(
          request.storeId, 
          request.googleCustomerId, 
          req
        );
      })()
    ];

    // Task 3: Get Google Purchasable Offer (only if entitlement doesn't exist)
    if (!isEntitlementExists) {
      tasks.push(
        (async () => {
          offerResponse = await getGooglePurchasableOfferService(
            req!,
            request.storeId,
            request.googleCustomerId,
            request.productId,
            request.skuId,
            request.offerId
          );
        })()
      );
    }

    // Wait for all tasks to complete (matching .NET: await Task.WhenAll)
    await Promise.all(tasks);
    logger.info(`Tasks completed`);

    // Extract data from responses (matching .NET casting)
    const customerData = customerResponse.Data as GoogleCustomerDetailModel;
    const offerData = offerResponse.Data as PurchasableOffer;

    logger.info(`cpIdResponse: ${JSON.stringify(cpIdResponse)} ||||| customerData: ${JSON.stringify(customerData)} ||||| offerData: ${JSON.stringify(offerData)}`);

    // Validation logic (matching .NET validation)
    const messageSeparator = "<br/>";
    const errorMessages: string[] = [];

    // CP ID validation (matching .NET logic)
    if (cpIdResponse.IsError) {
      errorMessages.push(Messages.ChannelPartnerIdInActive(request.cpId, messageSeparator + "Error: " + cpIdResponse.Message));
    } else if (cpIdResponse.Data?.CpiStatus && comparableString(cpIdResponse.Data.CpiStatus) !== "ACTIVE") {
      errorMessages.push(Messages.ChannelPartnerIdInActive(request.cpId, ""));
    }

    // Customer validation (matching .NET logic)
    if (customerResponse.IsError) {
      errorMessages.push(Messages.CloudIdentityMappingNotSet(messageSeparator + "Error: " + customerResponse.Message));
    } else if (!customerData || 
               customerData.cloudIdentityId !== request.cloudIdentityId || 
               customerData.channelPartnerId !== request.cpId) {
      errorMessages.push(Messages.CloudIdentityMappingNotSet(""));
    }

    // Offer validation (only if entitlement doesn't exist)
    if (!isEntitlementExists) {
      if (offerResponse.IsError) {
        errorMessages.push(Messages.PurchasebleOfferNotFound(messageSeparator + "Error: " + offerResponse.Message));
      } else if (!offerData || !offerData.offer || !offerData.offer.name) {
        errorMessages.push(Messages.PurchasebleOfferNotFound(""));
      }
    }

    // Entitlement expiration validation (matching .NET logic)
    if (isEntitlementExists && !isEntitlementExpired) {
      errorMessages.push(Messages.TrialEntitlementNotExpired(subscription!.MaterialId, request.skuId));
    }

    logger.info(`errorMessages count = ${errorMessages.length}; Errors : ${JSON.stringify(errorMessages)}`);

    // Build response (matching .NET response building)
    if (errorMessages.length > 0) {
      const formattedMessage = "<ul class='checkout-error-ul'>" + 
        errorMessages.map(x => `<li>${x}</li>`).join("") + 
        "</ul>";

      const statusCode = cpIdResponse.IsError ? cpIdResponse.StatusCode : 
                        (customerResponse.IsError ? customerResponse.StatusCode : 
                        (offerResponse.IsError ? offerResponse.StatusCode : 400));

      apiResponse = {
        IsError: true,
        Message: formattedMessage,
        StatusCode: statusCode
      };
    }

  } catch (error: any) {
    logger.error(`Error in ValidateGoogleEndCustomer service: ${error.message}`);
    
    apiResponse = {
      Message: Messages.ExceptionMessage(error.message),
      StatusCode: 500,
      IsError: true
    };
  }

  logger.info(`Going to return ValidateGoogleEndCustomer API response- ${JSON.stringify(apiResponse)}`);
  return apiResponse;
}
