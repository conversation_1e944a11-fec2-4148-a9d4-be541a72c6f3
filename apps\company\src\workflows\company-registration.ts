import { MedusaError } from '@medusajs/framework/utils';
import {
	createWorkflow,
	createStep,
	StepResponse,
	WorkflowResponse,
} from '@medusajs/framework/workflows-sdk';
import { z } from 'zod';
import { ContainerRegistrationKeys } from '@medusajs/framework/utils';
import { RegistrationSchema } from '../api/v1/company/company-registration/validators';

type CompanyRegistrationSchemaType = z.infer<typeof RegistrationSchema>;

type CreateCompanyRegistrationWorkflowInput = {
	companyDetails: CompanyRegistrationSchemaType;
};

export async function generateCustomCompanyRegistrationId(
	companyInfo: any[],
	regionCode: string
): Promise<string> {
	const date = new Date();
	const year = date.getFullYear().toString().slice(-2);
	let newSequence = '0001';

	if (companyInfo?.length > 0) {
		const lastId = companyInfo[0].id;
		const match = lastId.match(/^CR-[A-Z]+-(\d{2})(\d{4})$/);
		if (match && match[1] === year) {
			const lastNumber = parseInt(match[2], 10);
			if (!isNaN(lastNumber)) {
				newSequence = String(lastNumber + 1).padStart(4, '0');
			}
		}
	}

	return `CR-${regionCode}-${year}${newSequence}`;
}

const createCompanyRegistrationStep = createStep(
	'create-company-registration-step',
	async (
		{
			companyDetails: companyRegistrationData,
		}: Pick<CreateCompanyRegistrationWorkflowInput, 'companyDetails'>,
		{ container }
	) => {
		const companyRegistrationService: any = container.resolve(
			'company_registration'
		);
		const query = container.resolve(ContainerRegistrationKeys.QUERY);

		// Fetch latest company registration for ID generation
		const { data: companyInfo } = await query.graph({
			entity: 'cq_company_registration',
			fields: ['id'],
			filters: {},
			pagination: {
				skip: 0,
				take: 1,
				order: {
					created_at: 'DESC',
				},
			},
		});
		const regionData =
			await companyRegistrationService.getAllRegionsFromForeignTable();

		let regionCode = '';

		if (Array.isArray(regionData) && regionData.length > 0) {
			const matchedRegion = regionData.find(
				(r: any) =>
					r.name === 'India' || r.name === 'Turkey' || r.name === 'MEA'
			);

			if (matchedRegion) {
				if (matchedRegion.name === 'India') regionCode = 'IN';
				else if (matchedRegion.name === 'Turkey') regionCode = 'TUR';
				else if (matchedRegion.name === 'MEA') regionCode = 'MEA';
			}
		}

		const newId = await generateCustomCompanyRegistrationId(
			companyInfo,
			regionCode
		);

		const {
			authorized_signatories,
			company_document,
			profile_info,
			user_brand_preferences,
			...filteredCompanyRegistrationData
		} = companyRegistrationData;

		const companyRegistration =
			await companyRegistrationService.createCqCompanyRegistrations({
				...filteredCompanyRegistrationData,
				id: newId,
			});

		return new StepResponse(companyRegistration);
	}
);

export const createCompanyRegistrationWorkflow = createWorkflow(
	'create-company-registration',
	function (input: CreateCompanyRegistrationWorkflowInput) {
		const company = createCompanyRegistrationStep({
			companyDetails: input.companyDetails,
		});

		return new WorkflowResponse(company);
	}
);

// Step to get all user info from the foreign table
const getAllUsersInfoStep = createStep(
	'get-all-users-info-step',
	async (_input, { container }) => {
		const companyRegistrationService: any = container.resolve(
			'company_registration'
		);
		const users =
			await companyRegistrationService.getAllUsersFromForeignTable();

		if (!users || users.length === 0) {
			throw new MedusaError(
				MedusaError.Types.NOT_FOUND,
				`No users found in cq_user_foreign table`
			);
		}

		return new StepResponse(users);
	}
);

// Workflow to get all user info
export const getCompanyRegistrationWorkflow = createWorkflow(
	'get-all-users-workflow',
	function () {
		const users = getAllUsersInfoStep();
		return new WorkflowResponse(users);
	}
);

// Step to get all region country info from the foreign table
const getAllRegionCountriesInfoStep = createStep(
	'get-all-region-countries-info-step',
	async (_input, { container }) => {
		const companyRegistrationService: any = container.resolve(
			'company_registration'
		);
		const countries =
			await companyRegistrationService.getAllRegionCountriesFromForeignTable();

		if (!countries || countries.length === 0) {
			throw new MedusaError(
				MedusaError.Types.NOT_FOUND,
				`No countries found in cq_region_country_foreign table`
			);
		}

		return new StepResponse(countries);
	}
);

// Workflow to get all region country info
export const getAllRegionCountriesWorkflow = createWorkflow(
	'get-all-region-countries-workflow',
	function () {
		const countries = getAllRegionCountriesInfoStep();
		return new WorkflowResponse(countries);
	}
);

// Step to get 'pending' status ID from the foreign table
const getPendingStatusIdStep = createStep(
	'get-pending-status-id-step',
	async (_input, { container }) => {
		const companyRegistrationService: any = container.resolve(
			'company_registration'
		);

		const statuses =
			await companyRegistrationService.getAllStatusFromForeignTable();

		if (!statuses || statuses.length === 0) {
			throw new MedusaError(
				MedusaError.Types.NOT_FOUND,
				`No statuses found in cq_status_foreign table`
			);
		}

		const pendingStatus = statuses.find((s: any) => s.key === 'pending');

		if (!pendingStatus) {
			throw new MedusaError(
				MedusaError.Types.NOT_FOUND,
				`'pending' status not found in cq_status_foreign table`
			);
		}

		return new StepResponse(pendingStatus.id);
	}
);

export const getPendingStatusIdWorkflow = createWorkflow(
	'get-pending-status-id-workflow',
	function () {
		const pendingStatusId = getPendingStatusIdStep();
		return new WorkflowResponse(pendingStatusId);
	}
);

// Step to get all store info from the foreign table
const getAllStoreInfoStep = createStep(
	'get-all-store-info-step',
	async (_input, { container }) => {
		const companyRegistrationService: any = container.resolve(
			'company_registration'
		);
		const stores =
			await companyRegistrationService.getAllStoresFromForeignTable();

		if (!stores || stores.length === 0) {
			throw new MedusaError(
				MedusaError.Types.NOT_FOUND,
				`No stores found in cq_store_foreign table`
			);
		}

		return new StepResponse(stores);
	}
);

// Workflow to get all store info
export const getAllStoreWorkflow = createWorkflow(
	'get-all-store-workflow',
	function () {
		const stores = getAllStoreInfoStep();
		return new WorkflowResponse(stores);
	}
);
