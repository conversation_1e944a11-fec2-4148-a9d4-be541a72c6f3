import { S3Client, GetObjectCommand } from "@aws-sdk/client-s3";
import { Readable } from "stream";
import logger from "../utils/logger";


interface S3Credentials {
  AWS_ACCESS_KEY_ID: string;
  AWS_SECRET_ACCESS_KEY: string;
  AWS_REGION: string;
  AWS_BUCKETNAME: string;
}

const MAX_RETRIES = 3;
const INITIAL_DELAY_MS = 500;

const delay = (ms: number) => new Promise((res) => setTimeout(res, ms));

async function streamToString(stream: Readable): Promise<string> {
  return new Promise((resolve, reject) => {
    const chunks: any[] = [];
    stream.on("data", (chunk) => chunks.push(chunk));
    stream.on("end", () => resolve(Buffer.concat(chunks).toString("utf-8")));
    stream.on("error", reject);
  });
}

export const getS3ObjectAsync = async (
  s3Credentials: S3Credentials,
  fileName: string
): Promise<string> => {
  logger.info(`Entered getS3ObjectAsync with fileName: ${fileName}`);

  const s3Client = new S3Client({
    region: s3Credentials.AWS_REGION,
    credentials: {
      accessKeyId: s3Credentials.AWS_ACCESS_KEY_ID,
      secretAccessKey: s3Credentials.AWS_SECRET_ACCESS_KEY,
    },
  });

  for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
    try {
      const command = new GetObjectCommand({
        Bucket: s3Credentials.AWS_BUCKETNAME,
        Key: fileName,
      });

      logger.info(`Attempt ${attempt}: Calling S3 GetObjectCommand`);

      const response = await s3Client.send(command);
      const responseBody = await streamToString(response.Body as Readable);

      logger.info(`Successfully retrieved S3 object on attempt ${attempt}`);
      return responseBody;
    } catch (error: any) {
      logger.error(`Attempt ${attempt} failed: ${error.message || error}`);

      if (attempt < MAX_RETRIES) {
        const backoff = INITIAL_DELAY_MS * attempt;
        logger.warn(`Retrying after ${backoff}ms...`);
        await delay(backoff);
      } else {
        logger.error(`All ${MAX_RETRIES} attempts failed for getS3ObjectAsync`);
        throw new Error("Failed to fetch S3 object after retries.");
      }
    }
  }

  throw new Error("Unexpected failure in getS3ObjectAsync");
};
