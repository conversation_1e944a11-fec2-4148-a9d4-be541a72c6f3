import { ExecArgs } from '@medusajs/framework/types';
import { CQUser_MODULE } from '../modules/cq-user';
import CQUserService from '../modules/cq-user/service';

export default async function seedPages({ container }: ExecArgs) {
  const guestUserService: CQUserService = container.resolve(CQUser_MODULE);

  const pageMapping = {
    Accounts: [
      { page: 'Company Registration', order_id: 1 },
      { page: 'Company Onboarding', order_id: 2 },
      { page: 'Companies', order_id: 3 },
      { page: 'Users', order_id: 4 },
    ],
    Aggrements: [
      { page: 'Company Aggrement', order_id: 1 },
      { page: 'User Aggrement', order_id: 2 },
    ],
    'User Interface': [{ page: 'User Roles', order_id: 1 }],
  };

  const existingModules = (await guestUserService.listCqModules?.({})) || [];

  const moduleMap = new Map(
    existingModules.map((m) => [m.module.toLowerCase(), m.id])
  );

  const pagesToCreate = [];

  for (const [moduleName, pages] of Object.entries(pageMapping)) {
    const moduleId = moduleMap.get(moduleName.toLowerCase());
    if (!moduleId) {
      console.warn(`Module not found for pages: ${moduleName}`);
      continue;
    }

    for (const page of pages) {
      pagesToCreate.push({
        ...page,
        module_id: moduleId,
        metadata: {},
      });
    }
  }

  const existingPages = (await guestUserService.listCqPages?.({})) || [];

  const existingKeys = new Set(
    existingPages.map((p) => `${p.page.toLowerCase()}-${p.module_id}`)
  );

  const newPages = pagesToCreate.filter((p) => {
    const key = `${p.page.toLowerCase()}-${p.module_id}`;
    return !existingKeys.has(key);
  });

  if (newPages.length > 0) {
    await Promise.all(newPages.map((p) => guestUserService.createCqPages(p)));
  }

  console.log(`Seeded ${newPages.length} page(s).`);
}
