import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getAzurePlanEntitlementsService } from "../../../../services/entitlement/getAzurePlanEntitlementsService";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";

/**
 * @openapi
 * /v1/entitlement/getAzurePlanEntitlements:
 *   get:
 *     summary: Get Azure Plan Entitlements
 *     description: Retrieves entitlements for a given Azure Plan subscription.
 *     tags:
 *       - Entitlement
 *     parameters:
 *       - in: query
 *         name: subscriptionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Azure subscription ID
 *         example: "sub-123456"
 *     responses:
 *       200:
 *         description: Successfully retrieved entitlements
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                     example: "ent-001"
 *                   friendlyName:
 *                     type: string
 *                     example: "Visual Studio Enterprise"
 *                   status:
 *                     type: string
 *                     example: "Active"
 *                   subscriptionId:
 *                     type: string
 *                     example: "sub-123456"
 *       204:
 *         description: No entitlements found
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal Server Error
 *                 isError:
 *                   type: boolean
 *                   example: true
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const { subscriptionId } = req.query as Record<string, string>;

  const correlationId = getCorrelationId(req);
  logger.info(
    `Entered GET AzurePlanEntitlements API with | CorrelationId: ${correlationId} | subscriptionId - ${subscriptionId}`
  );

  try {
    const items = await getAzurePlanEntitlementsService(req, subscriptionId);

    if (!items || items.length === 0) {
      return res.status(204).send(); // No Content
    }

    return res.status(200).json(items);
  } catch (error) {
    logger.error(`ERROR in GET AzurePlanEntitlements API | Error: ${error}`);
    return res
      .status(500)
      .json({ message: "Internal Server Error", isError: true });
  }
}
