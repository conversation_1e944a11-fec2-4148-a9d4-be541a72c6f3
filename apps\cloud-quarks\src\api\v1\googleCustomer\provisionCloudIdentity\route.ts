import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import { GoogleAPIResponse } from "../../../../types/responses/customResponse";
import { validateProvisionCustomerCloudIdentityRequest } from "../../../../validators/google/provisionCloudIdentityValidator";
import { provisionCustomerCloudIdentityService } from "../../../../services/googleCustomer/provisionCustomerCloudIdentityService";

/**
 * @openapi
 * /v1/googleCustomer/provisionCloudIdentity:
 *   post:
 *     summary: Provision Google Cloud Identity
 *     description: Validates and provisions a Google Cloud Identity for the given store and Google customer ID.
 *     tags:
 *       - GoogleCustomer
 *     parameters:
 *       - in: query
 *         name: storeId
 *         required: true
 *         schema:
 *           type: string
 *         description: Store identifier
 *         example: "AE-EN"
 *       - in: query
 *         name: googleCustomerId
 *         required: true
 *         schema:
 *           type: string
 *         description: Google customer identifier
 *         example: "customers/C12345678"
 *     responses:
 *       200:
 *         description: Google Cloud Identity provisioned successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Message:
 *                   type: string
 *                   example: "Provisioning successful"
 *                 StatusCode:
 *                   type: integer
 *                   example: 200
 *                 IsError:
 *                   type: boolean
 *                   example: false
 *       400:
 *         description: Bad request - validation errors
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Message:
 *                   type: string
 *                   example: "StoreId can't be null or empty"
 *                 StatusCode:
 *                   type: integer
 *                   example: 400
 *                 IsError:
 *                   type: boolean
 *                   example: true
 *       422:
 *         description: Unprocessable entity - internal service error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Message:
 *                   type: string
 *                   example: "An error occurred: Unexpected server error"
 *                 StatusCode:
 *                   type: integer
 *                   example: 422
 *                 IsError:
 *                   type: boolean
 *                   example: true
 */

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req);

  logger.info(`Entered into ProvisionCloudIdentity API with CorrelationId: ${correlationId}`);

  let response: GoogleAPIResponse = {
    Message: "Success",
    StatusCode: 200,
    IsError: false
  };

  try {
    // Extract query parameters (matching .NET: string storeId, string googleCustomerId)
    const { storeId, googleCustomerId } = req.query as { storeId?: string; googleCustomerId?: string };

    logger.info(`Entered into ProvisionCloudIdentity API with details: storeId ${storeId}, googleCustomerId: ${googleCustomerId}`);

    // Validate request parameters (matching .NET: ValidateProvisionCustomerCloudIdentityRequest)
    const validationErrors = await validateProvisionCustomerCloudIdentityRequest(
      storeId || "",
      googleCustomerId || ""
    );

    logger.info(`Validation errors count = ${validationErrors.length}; Errors : ${JSON.stringify(validationErrors)}`);

    if (validationErrors.length > 0) {
      response.Message = validationErrors.join(', ');
      response.IsError = true;
      response.StatusCode = 400; // BadRequest

      logger.info(`Going to send bad request response ${JSON.stringify(response)}`);
      return res.status(400).json(response);
    }

    // Call the service method (matching .NET: _googleCustomerService.ProvisionCustomerCloudIdentity)
    response = await provisionCustomerCloudIdentityService(
      storeId!,
      googleCustomerId!,
      req
    );

    logger.info(`Going to return response from ProvisionCloudIdentity API with response: ${JSON.stringify(response)}`);

    // Return ContentResult equivalent (matching .NET: new ContentResult() { Content = response.toJson(), StatusCode = response?.StatusCode, ContentType = "application/json" })
    return res.status(response.StatusCode).json(response);

  } catch (exp: any) {
    logger.error(`Error in ProvisionCloudIdentity API method. Message : ${exp.message}, StackTrace: ${exp.stack}`);

    response.Message = `An error occurred: ${exp.message}`;
    response.IsError = true;
    response.StatusCode = 422; // UnprocessableEntity

    logger.info(`Going to return error response from ProvisionCloudIdentity API with response: ${JSON.stringify(response)}`);
    return res.status(422).json(response);
  }
}