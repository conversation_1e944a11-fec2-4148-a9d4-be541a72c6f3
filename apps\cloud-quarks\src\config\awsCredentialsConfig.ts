import logger from "../utils/logger";
import { AWSCredentials } from "../types/responses/customResponse";
import { findAWSCredentialsByResource, areAWSCredentialsLoaded, initializeAWSCredentials } from "../services/aws/getAWSCredentials";

/**
 * Get AWS Credentials from configuration
 * Matches .NET StaticConfig.AWSCredentialList.Find(x => x.RESOURCE == resource)
 *
 * This method first tries to get credentials from the database (matching .NET behavior),
 * then falls back to environment variables if database is not available.
 */
export async function getAWSCredentials(resource: string): Promise<AWSCredentials | null> {
  logger.info(`AWS credentials requested for resource: ${resource}`);

  try {
    // Ensure AWS credentials are loaded from database (matching .NET StaticConfig pattern)
    if (!areAWSCredentialsLoaded()) {
      logger.info("AWS credentials not loaded, initializing from database");
      await initializeAWSCredentials();
    }

    // Find credentials by resource (matching .NET StaticConfig.AWSCredentialList.Find)
    const dbCredentials = findAWSCredentialsByResource(resource);
    if (dbCredentials) {
      logger.info(`Using AWS credentials from database for resource: ${resource}`);
      return dbCredentials;
    }

    logger.warn(`No AWS credentials found in database for resource: ${resource}`);
  } catch (error: any) {
    logger.error(`Error retrieving AWS credentials from database: ${error.message}`, error);
    logger.info("Falling back to environment variables");
  }

  // Fallback to environment variables if database lookup fails
  const envCredentials = getAWSCredentialsFromEnv(resource);
  if (envCredentials) {
    logger.info(`Using AWS credentials from environment for resource: ${resource}`);
    return envCredentials;
  }

  logger.warn(`No AWS credentials found for resource: ${resource}`);
  return null;
}

/**
 * Get AWS Credentials from environment variables
 * This is a fallback method when database/config service is not available
 */
function getAWSCredentialsFromEnv(resource: string): AWSCredentials | null {
  // Try resource-specific environment variables first
  const resourcePrefix = resource.toUpperCase().replace(/[^A-Z0-9]/g, '_');
  
  const accessKeyId = process.env[`${resourcePrefix}_AWS_ACCESS_KEY_ID`] || process.env.AWS_ACCESS_KEY_ID;
  const secretAccessKey = process.env[`${resourcePrefix}_AWS_SECRET_ACCESS_KEY`] || process.env.AWS_SECRET_ACCESS_KEY;
  const region = process.env[`${resourcePrefix}_AWS_REGION`] || process.env.AWS_REGION;
  const bucketName = process.env[`${resourcePrefix}_AWS_BUCKETNAME`] || process.env.AWS_BUCKETNAME;
  
  if (!accessKeyId || !secretAccessKey || !region || !bucketName) {
    return null;
  }
  
  return {
    Id: 1, // Placeholder ID
    AWS_ACCESS_KEY_ID: accessKeyId,
    AWS_SECRET_ACCESS_KEY: secretAccessKey,
    AWS_REGION: region,
    RESOURCE: resource,
    AWS_BUCKETNAME: bucketName,
    APPLICATION: process.env[`${resourcePrefix}_APPLICATION`] || "cloud-quarks",
    COMMENTS: `Environment-based credentials for ${resource}`,
    ACTIVE: true,
  };
}

/**
 * Get AWS Credentials for S3 operations
 * Convenience method for S3-specific credentials
 */
export async function getS3Credentials(): Promise<AWSCredentials | null> {
  return await getAWSCredentials("S3");
}

/**
 * Validate AWS Credentials
 * Ensures all required fields are present
 */
export function validateAWSCredentials(credentials: AWSCredentials | null): boolean {
  if (!credentials) {
    logger.error("AWS credentials are null or undefined");
    return false;
  }
  
  const requiredFields = [
    'AWS_ACCESS_KEY_ID',
    'AWS_SECRET_ACCESS_KEY', 
    'AWS_REGION',
    'AWS_BUCKETNAME'
  ];
  
  for (const field of requiredFields) {
    if (!credentials[field as keyof AWSCredentials]) {
      logger.error(`AWS credentials missing required field: ${field}`);
      return false;
    }
  }
  
  return true;
}
