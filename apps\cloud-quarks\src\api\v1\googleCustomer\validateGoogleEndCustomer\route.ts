import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import {
  GoogleValidateEndCustomerDto,
  GoogleValidateEndCustomerRequest,
  GoogleAPIResponse
} from "../../../../types/responses/customResponse";
import { validateGoogleValidateEndCustomerDto } from "../../../../validators/google/googleValidateEndCustomerValidator";
import { validateGoogleEndCustomerService } from "../../../../services/googleCustomer/validateGoogleEndCustomerService";
import { Messages } from "../../../../services/googleCustomer/verifyDomainExistsService";

/**
 * @openapi
 * /v1/googleCustomer/validateGoogleEndCustomer:
 *   post:
 *     summary: Validate Google End Customer
 *     description: Validates Google end customer details including CP ID, customer mapping, and offer availability
 *     tags:
 *       - GoogleCustomer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               storeId:
 *                 type: string
 *                 example: "AE-EN"
 *               brandId:
 *                 type: string
 *                 example: "1"
 *               partnerId:
 *                 type: string
 *                 example: "partner123"
 *               productId:
 *                 type: string
 *                 example: "product123"
 *               skuId:
 *                 type: string
 *                 example: "sku123"
 *               offerId:
 *                 type: string
 *                 example: "offer123"
 *               custId:
 *                 type: string
 *                 example: "customer123"
 *               cpId:
 *                 type: string
 *                 example: "CP12345678"
 *               googleCustomerId:
 *                 type: string
 *                 example: "customers/C12345678"
 *               cloudIdentityId:
 *                 type: string
 *                 example: "cloud123"
 *     responses:
 *       200:
 *         description: Validation completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Message:
 *                   type: string
 *                   example: "Success"
 *                 StatusCode:
 *                   type: integer
 *                   example: 200
 *                 IsError:
 *                   type: boolean
 *                   example: false
 *       400:
 *         description: Bad request - validation errors
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Message:
 *                   type: string
 *                   example: "StoreId cannot be null or empty"
 *                 IsError:
 *                   type: boolean
 *                   example: true
 *                 StatusCode:
 *                   type: integer
 *                   example: 400
 *       422:
 *         description: Unprocessable entity - service error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Message:
 *                   type: string
 *                   example: "An error occurred: [error details]"
 *                 IsError:
 *                   type: boolean
 *                   example: true
 *                 StatusCode:
 *                   type: integer
 *                   example: 422
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req);

  logger.info(`Entered into ValidateGoogleEndCustomer API with CorrelationId: ${correlationId}`);

  let response: GoogleAPIResponse = {
    Message: "Success",
    StatusCode: 200,
    IsError: false
  };

  try {
    const googleValidateEndCustomerDto = req.body as GoogleValidateEndCustomerDto;
    logger.info(`Entered into ValidateGoogleEndCustomer & going to validate googleValidateEndCustomerDto: ${JSON.stringify(googleValidateEndCustomerDto)}`);

    // Validate the request DTO (matching .NET validation)
    const validationResult = await validateGoogleValidateEndCustomerDto(googleValidateEndCustomerDto);
    logger.info(`Validations error count on GoogleValidateEndCustomerDto object is ${validationResult.errors.length}`);

    if (!validationResult.isValid) {
      const validationErrors = validationResult.errors;
      logger.info(`validation errors are ${JSON.stringify(validationErrors)}`);

      response.Message = validationErrors.join(', ');
      response.StatusCode = 400;
      response.IsError = true;

      logger.info(`Going to send bad request response ${JSON.stringify(response)}`);
      return res.status(400).json(response);
    }

    // Map DTO to Request (matching .NET: _mapper.Map<GoogleValidateEndCustomerRequest>(googleValidateEndCustomerDto))
    logger.info(`Going to map Validate Google End Customer request. Dto is ${JSON.stringify(googleValidateEndCustomerDto)}`);
    const request: GoogleValidateEndCustomerRequest = {
      storeId: googleValidateEndCustomerDto.storeId,
      brandId: googleValidateEndCustomerDto.brandId,
      partnerId: googleValidateEndCustomerDto.partnerId,
      productId: googleValidateEndCustomerDto.productId,
      skuId: googleValidateEndCustomerDto.skuId,
      offerId: googleValidateEndCustomerDto.offerId,
      custId: googleValidateEndCustomerDto.custId,
      cpId: googleValidateEndCustomerDto.cpId,
      googleCustomerId: googleValidateEndCustomerDto.googleCustomerId,
      cloudIdentityId: googleValidateEndCustomerDto.cloudIdentityId,
    };
    logger.info(`Mapped dto is: ${JSON.stringify(request)}`);

    // Call the service method (matching .NET service call)
    response = await validateGoogleEndCustomerService(request, req);

  } catch (exp: any) {
    logger.error(`Error in ValidateGoogleEndCustomer API method. Message : ${exp.message}, StackTrace: ${exp.stack}`);

    response.Message = Messages.ExceptionMessage(exp.message);
    response.IsError = true;
    response.StatusCode = 422; // UnprocessableEntity
  }

  logger.info(`Returning ValidateGoogleEndCustomer Ok response - ${JSON.stringify(response)}`);

  // Return ContentResult equivalent (matching .NET return pattern)
  return res.status(response.StatusCode).json(response);
}