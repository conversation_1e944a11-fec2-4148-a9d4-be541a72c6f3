import { z } from 'zod';

export const StateCreateSchema = z.object({
  state_name: z
    .string()
    .min(1, 'State name is required')
    .max(100, 'State name must be at most 100 characters')
    .regex(
      /^[A-Za-z\s\-']+$/,
      'State name can only contain letters, spaces, hyphens, and apostrophes'
    )
    .trim(),
  action: z
    .string()
    .max(50, 'Action must be at most 50 characters')
    .regex(
      /^[A-Za-z0-9\s\-_]+$/,
      'Action can only contain letters, numbers, spaces, hyphens, and underscores'
    )
    .optional()
    .default('edit'),
  region_country_id: z
    .string()
    .min(1, 'Region country ID is required')
    .max(50, 'Region country ID must be at most 10 characters'),
  metadata: z
    .record(z.unknown())
    .optional()
    .default({}),
});

export type StateCreateRequest = z.infer<typeof StateCreateSchema>;