import { model } from "@medusajs/framework/utils";
import CqQuestion from "./question";
import { ProfileInfo } from "./profile-info";

const Answer = model.define("cq_answer", {
	id: model.id().primaryKey(),
	answer: model.text(),
	questions: model.belongsTo(() => CqQuestion),
	order_id: model.autoincrement(),
	profile_info: model.hasMany(() => ProfileInfo, { mappedBy: "answer" }),
});

export default Answer;
