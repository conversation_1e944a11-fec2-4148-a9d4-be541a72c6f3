import swaggerJSDoc from 'swagger-jsdoc';
import yaml from 'js-yaml';
import fs from 'fs';
import path from 'path';

const options = {
	definition: {
		openapi: '3.0.0',
		info: {
			title: 'Medusa Company API',
			version: '1.0.0',
			description:
				'Company API for company registration and comapny onboarding flow.',
		},
		servers: [
			{
				url: 'http://localhost:8082',
				description: 'Local development server',
			},
		],
	},
	// Match all route files under `api/**/route.ts` or `*.ts` as needed
	apis: ['src/api/v1/company/**/*.ts'],
};

const swaggerSpec = swaggerJSDoc(options);
const yamlStr = yaml.dump(swaggerSpec);

const outputPath = path.join(__dirname, '../docs/swagger.yml');
fs.mkdirSync(path.dirname(outputPath), { recursive: true });
fs.writeFileSync(outputPath, yamlStr, 'utf8');

console.log(`✅ Generated Swaggeer YAML at: ${outputPath}`);
