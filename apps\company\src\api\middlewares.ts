import {
  defineMiddlewares,
  authenticate,
  MedusaRequest,
  MedusaResponse,
  validateAndTransformBody,
  validateAndTransformQuery,
  MedusaNextFunction,
} from '@medusajs/framework/http';
// import { helmetMiddleware } from '@org/helmet';
import { parseCorsOrigins } from '@medusajs/framework/utils';
import cors from 'cors';
// import {
//   createDefaultRateLimiter,
//   createAuthRateLimiter,
// } from '@org/rate_limit';

// import { adaptMiddleware } from './middlewares/medusa-middleware-adapter';
import { Modules } from '@medusajs/framework/utils';
const axios = require('axios');
const jwt = require('jsonwebtoken');

import multer from 'multer';

const upload = multer({ storage: multer.memoryStorage() });

import { ConfigModule } from '@medusajs/framework';
import {
  GSTSchema,
  RegionSchema,
  RegistrationSchema,
} from './v1/company/company-registration/validators';
import { customAuthenticate } from '../../../../libs/utils/src/lib/authenticateValidator';

export default defineMiddlewares({
  routes: [
    // Default rate limiter and cors origins for all other routes should be last
    {
      matcher: '/*',
      middlewares: [
        (req: MedusaRequest, res: MedusaResponse, next: MedusaNextFunction) => {
          const configModule: ConfigModule = req.scope.resolve('configModule');
          return cors({
            origin: parseCorsOrigins(configModule.projectConfig.http.storeCors),
            credentials: true,
          })(req, res, next);
        },
        // adaptMiddleware(createDefaultRateLimiter()), helmetMiddleware
      ],
    },
    // Auth rate limiter for specific auth endpoints
    {
      matcher: '/auth/*',
      // middlewares: [adaptMiddleware(createAuthRateLimiter())],
    },
    {
      matcher: '*',
      middlewares: [
        (req: MedusaRequest, res: MedusaResponse, next: MedusaNextFunction) => {
          // Set security headers
          res.setHeader('X-Content-Type-Options', 'nosniff');
          res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
          res.setHeader('X-Frame-Options', 'DENY');
          res.setHeader('X-XSS-Protection', '1; mode=block');

          // You can also set Content-Security-Policy here
          res.setHeader('Content-Security-Policy', "default-src 'self'");

          next();
        },
      ],
    },
    {
      matcher: '/v1/company/company-registration*',
    },
    {
      matcher: '/v1/company/company-registration/gst/verify',
      middlewares: [
        customAuthenticate as unknown as (
          req: MedusaRequest,
          res: MedusaResponse,
          next: MedusaNextFunction
        ) => any,
        validateAndTransformQuery(GSTSchema, {}),
      ],
    },
    {
      matcher: '/v1/company/company-registration/',
      method: 'POST',
      middlewares: [
        customAuthenticate as unknown as (
          req: MedusaRequest,
          res: MedusaResponse,
          next: MedusaNextFunction
        ) => any,
        validateAndTransformBody(RegistrationSchema),
      ],
    },
    {
      matcher: '/v1/company/company-registration/profile',
      middlewares: [
        customAuthenticate as unknown as (
          req: MedusaRequest,
          res: MedusaResponse,
          next: MedusaNextFunction
        ) => any,
        validateAndTransformQuery(RegionSchema, {}),
      ],
    },
    {
      matcher: '/v1/company/company-registration/document',
      method: 'GET',
      middlewares: [
        customAuthenticate as unknown as (
          req: MedusaRequest,
          res: MedusaResponse,
          next: MedusaNextFunction
        ) => any,
      ],
    },
  ],
});
