import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import { normalizeQueryParams, toPascalCaseJson } from "../../../../utils/mappers/getCustomerUsers";
import {
  validateCustomerUserQuery,
  validateSetCustomerBillingInfo,
} from "../../../../validators/customer/customCustomerValidator";
import { SetCustomerBillingInfoSchema } from "../../../../validators/customer/setCustomerBillingInfoValidator";
import { mapToCustomerBillingInfoModel } from "../../../../utils/mappers/customerBillingInfoModel";
import { setCustomerBillingInfoService } from "../../../../services/index";

/**
 * @openapi
 * /v1/customer/setCustomerBillingInfo:
 *   post:
 *     summary: Set customer billing information
 *     description: This API sets or updates the billing information of a customer for a specific brand, store, and tenant.
 *     tags:
 *       - Customer
 *     parameters:
 *       - in: query
 *         name: brandId
 *         schema:
 *           type: string
 *         required: true
 *         description: The brand identifier
 *       - in: query
 *         name: storeId
 *         schema:
 *           type: string
 *         required: true
 *         description: The store identifier
 *       - in: query
 *         name: tenantId
 *         schema:
 *           type: string
 *         required: true
 *         description: The tenant identifier
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *               email:
 *                 type: string
 *               culture:
 *                 type: string
 *               language:
 *                 type: string
 *               companyName:
 *                 type: string
 *               defaultAddress:
 *                 type: object
 *                 properties:
 *                   country:
 *                     type: string
 *                   city:
 *                     type: string
 *                   addressLine1:
 *                     type: string
 *                   postalCode:
 *                     type: string
 *                   phoneNumber:
 *                     type: string
 *                   addressLine2:
 *                     type: string
 *                   firstName:
 *                     type: string
 *                   lastName:
 *                     type: string
 *                   state:
 *                     type: string
 *               links:
 *                 type: object
 *                 properties:
 *                   self:
 *                     type: object
 *                     properties:
 *                       uri:
 *                         type: string
 *                       method:
 *                         type: string
 *                   offer:
 *                     type: object
 *                     properties:
 *                       uri:
 *                         type: string
 *                       method:
 *                         type: string
 *                   next:
 *                     type: object
 *                     properties:
 *                       uri:
 *                         type: string
 *                       method:
 *                         type: string
 *                   availabilities:
 *                     type: object
 *                     properties:
 *                       uri:
 *                         type: string
 *                       method:
 *                         type: string
 *                   skus:
 *                     type: object
 *                     properties:
 *                       uri:
 *                         type: string
 *                       method:
 *                         type: string
 *               attributes:
 *                 type: object
 *                 properties:
 *                   objectType:
 *                     type: string
 *                   etag:
 *                     type: string
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               code:
 *                 type: integer
 *               description:
 *                 type: string
 *     responses:
 *       200:
 *         description: Billing info updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *       400:
 *         description: Bad request (validation or DTO errors)
 *       500:
 *         description: Internal server error
 */

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    let {
      brandId = "",
      storeId = "",
      tenantId = "",
    } = req.query as Record<string, string>;
    const dto = req.body;

    console.log("dto ---->",dto);

    const correlationId = getCorrelationId(req);
    logger.info(
      `Entered into SetCustomerBillingInfo API | CorrelationId: ${correlationId} | brandId: ${brandId}, storeId: ${storeId}, tenantId: ${tenantId}, DTO: ${JSON.stringify(
        dto
      )}`
    );
    logger.info(`Sending request parameter ::`);
    const keyValuePairs = normalizeQueryParams(req.query, [
      "brandId",
      "storeId",
      "tenantId",
    ]);
    logger.info(
      `Request parameter values after cleanup: ${JSON.stringify(keyValuePairs)}`
    );
    brandId = keyValuePairs.brandId;
    storeId = keyValuePairs.storeId;
    tenantId = keyValuePairs.tenantId;

    logger.info(
      `Retrieved and assigned query values => brandId: ${brandId}, storeId: ${storeId}, tenantId: ${tenantId}`
    );
    logger.info(`Validating query parameters...`);
    const validationErrors = validateCustomerUserQuery(
      brandId,
      storeId,
      tenantId
    );

    logger.info(
      `Validation error count = ${
        validationErrors.length
      }; Errors: ${JSON.stringify(validationErrors)}`
    );

    if (validationErrors.length > 0) {
      logger.info(`Returning BadRequest with validation errors`);
      return res.status(400).json({
        statusCode: 400,
        errors: validationErrors,
      });
    }

    const pascalCasedDTO = toPascalCaseJson(dto);
    

    console.log("pascalCasedDTO------------>",pascalCasedDTO)
    const parseResult = SetCustomerBillingInfoSchema.safeParse(pascalCasedDTO);

    console.log("parseResult--------->",parseResult);

    if (!parseResult.success) {
      const dtoValidationErrors = parseResult.error.errors.map(
        (err) => err.message
      );

      logger.info(`Validation errors: -------------- ${JSON.stringify(dtoValidationErrors)}`);
      return res.status(400).json({
        status: "FAIL",
        message: dtoValidationErrors.join(", "),
      });
    }
    const setCustomerBillingInfoDTO = parseResult.data;

    logger.info(
      `setCustomerBillingInfoDTO :: ${JSON.stringify(
        setCustomerBillingInfoDTO
      )}`
    );

    const setCustomerBillingInfovValidationErrors =
      validateSetCustomerBillingInfo(setCustomerBillingInfoDTO);
    if (setCustomerBillingInfovValidationErrors.length > 0) {
      logger.info(
        `Custom validation errors: ${JSON.stringify(
          setCustomerBillingInfovValidationErrors
        )}`
      );
      return res.status(400).json({
        status: "FAIL",
        message: setCustomerBillingInfovValidationErrors.join(", "),
      });
    }
    console.log("setCustomerBillingInfoDTO ---->", setCustomerBillingInfoDTO);
    logger.info(
      `Mapping SetCustomerBillingInfoDTO to model: ${JSON.stringify(
        setCustomerBillingInfoDTO
      )}`
    );
    const billingInfoModel = mapToCustomerBillingInfoModel(
      setCustomerBillingInfoDTO
    );
    logger.info(
      `Mapped billing info model: ${JSON.stringify(billingInfoModel)}`
    );
    logger.info(
      `Calling customerService.setCustomerBillingInfo with brandId=${brandId}, storeId=${storeId}, tenantId=${tenantId}`
    );
    const response = await setCustomerBillingInfoService(
      req,
      brandId,
      storeId,
      tenantId,
      billingInfoModel
    );

    logger.info(`Service response: ${JSON.stringify(response)}`);
    return res.status(200).json(response);
  } catch (err) {
    logger.error("ser Customer Billing Info Error :: ", err as Error);
    return res.status(500).json({
      status: false,
      message: "Internal server error",
    });
  }
}
