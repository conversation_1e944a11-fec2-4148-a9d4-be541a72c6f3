import { getSqlServerConnection } from "../../utils/sqlServerClient";
import logger from "../../utils/logger";
import sql from 'mssql';

// Azure Plan Usage Items type
export type AzurePlanUsageItems = {
  [key: string]: any; // Dynamic properties based on stored procedure response
};

export async function getSubscriptionCountByIdAndPeriod(
  subscriptionId: string,
  period: string
): Promise<number> {
  logger.info(`Entered into GetSubscriptionCountByIdAndPeriod DAL method with subscriptionId:${subscriptionId},period:${period}`);

  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();

    const query = `SELECT Total FROM tblCloudUnbilledTotalCount(nolock) WHERE SubscriptionId = @SubscriptionId and Period = @Period`;

    // Add parameters with proper SQL types
    request.input("SubscriptionId", sql.VarChar, subscriptionId);
    request.input("Period", sql.VarChar, period);

    const result = await request.query<{ Total: number }>(query);

    // QueryFirstOrDefaultAsync<int> behavior - return first result or 0 if no results
    const subscriptionCount = result.recordset.length > 0 ? (result.recordset[0].Total || 0) : 0;

    logger.info(`Subscription count retrieved: ${subscriptionCount}`);
    return subscriptionCount;
  } catch (error) {
    logger.error(`Error in getSubscriptionCountByIdAndPeriod: ${error}`);
    throw error;
  }
}

export async function getAzureUnBilledUsageData(
  customerId: string,
  subscriptionId: string,
  period: string
): Promise<AzurePlanUsageItems[] | null> {
  logger.info(`Entered into GetAzurePlanUsageDetails Service method with customerId:${customerId},subscriptionId:${subscriptionId},period:${period}`);
  
  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();
    
    const proc = "spGetUnbilledAzureUsageData";
    
    request.input("iCustomerId", customerId);
    request.input("iSubscriptionId", subscriptionId);
    request.input("iPeriod", period);
    
    // Note: Timeout configuration would be handled at connection level in SQL Server client
    
    logger.info(`Going to hit DB with proc name: ${proc} with parameters customerId:${customerId}, subscriptionId:${subscriptionId}, period:${period}`);
    
    const result = await request.execute(proc);
    const response = result.recordset;
    
    logger.info(`Response received from proc name: ${proc}`);
    
    if (response && response.length > 0) {
      return response;
    }
    
    return null;
  } catch (error) {
    logger.error(`Error in getAzureUnBilledUsageData: ${error}`);
    throw error;
  }
}

export async function getS3FileDownLoadStatusAndKey(
  subscriptionId: string,
  period: string
): Promise<{ status: string | null; s3FilePath: string | null }> {
  logger.info(`Entered into GetS3FileDownLoadStatusAndKey DAL method with subscriptionId:${subscriptionId},period:${period}`);
  
  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();
    
    const query = `SELECT Status,S3FilePath FROM tblAzureUnbilledUsageDownloadQueue(nolock) WHERE CONVERT(DATE, CreatedDateTime) = CONVERT(DATE, GETDATE()) AND SubscriptionId = @SubscriptionId and Period = @Period And IsError = 0`;
    
    request.input("SubscriptionId", subscriptionId);
    request.input("Period", period);
    
    const result = await request.query(query);

    console.log("result--------->",result)

    const response = result.recordset[0];
    
    return {
      status: response?.Status || null,
      s3FilePath: response?.S3FilePath || null,
    };
  } catch (error) {
    logger.error(`Error in getS3FileDownLoadStatusAndKey: ${error}`);
    throw error;
  }
}

export async function saveAzureUnbilledUsageQueue(
  storeId: string,
  subscriptionId: string,
  customerId: string,
  period: string,
  status: string,
  s3Filepath: string | null = null
): Promise<{ success: boolean; message: string }> {
  logger.info(`Entered into SaveAzureUnbilledUsageQueue DAL method with storeId:${storeId},customerId:${customerId},subscriptionId:${subscriptionId},period:${period},status:${status},s3Filepath:${s3Filepath}`);
  
  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();
    
    const proc = "spCreateAzureUnbilledUsageDataDownloadQueue";
    
    request.input("CustomerId", customerId);
    request.input("SubscriptionId", subscriptionId);
    request.input("Period", period);
    request.input("S3FilePath", s3Filepath);
    request.input("Status", status);
    
    // Output parameters
    request.output("oRetVal", sql.Bit);
    request.output("oMessage", sql.NVarChar(2000));
    
    logger.info(`Going to hit DB with proc name: ${proc} with parameters customerId:${customerId}, subscriptionId:${subscriptionId}, period:${period}, status:${status}, s3Filepath:${s3Filepath}`);
    
    await request.execute(proc);
    
    const isSuccess = request.parameters.oRetVal.value as boolean;
    const message = request.parameters.oMessage.value as string;
    
    logger.info(`Return value is ${isSuccess}`);
    logger.info(`Return message is ${message}`);
    
    if (!isSuccess) {
      logger.error(`Error while SaveAzureUnbilledUsageQueue in DB. Error: ${message}`);
    }
    
    return { success: isSuccess, message };
  } catch (error) {
    logger.error(`Error in saveAzureUnbilledUsageQueue: ${error}`);
    throw error;
  }
}
