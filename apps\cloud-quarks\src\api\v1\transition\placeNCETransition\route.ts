import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import {
  ResponseStatus,
  ErrorResponseModel
} from "../../../../types/responses/customResponse";
import {
  validatePlaceTransitionRequest,
  PlaceTransitionRequestType,
  PlaceTransitionRequestSchema
} from "../../../../validators/transition/placeTransitionValidator";
import { placeNCETransitionService } from "../../../../services/transition/placeNCETransitionService";

/**
 * @openapi
 * /v1/transition/placeNCETransition:
 *   post:
 *     summary: Place NCE Transition request
 *     description: Places a New Commerce Experience (NCE) transition request for subscription upgrades/changes
 *     tags:
 *       - Transition
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - brandId
 *               - storeId
 *               - partnerId
 *               - customerId
 *               - fromSubscriptionId
 *               - fromMaterialId
 *               - quantity
 *               - toCatalogItemId
 *               - term
 *               - billFreq
 *             properties:
 *               brandId:
 *                 type: string
 *                 description: Brand ID
 *                 example: "14"
 *               storeId:
 *                 type: string
 *                 description: Store ID
 *                 example: "store-123"
 *               partnerId:
 *                 type: string
 *                 description: Partner ID
 *                 example: "partner-456"
 *               customerId:
 *                 type: string
 *                 description: Customer ID
 *                 example: "cust-789"
 *               fromSubscriptionId:
 *                 type: string
 *                 description: Source subscription ID to transition from
 *                 example: "sub-from-123"
 *               fromMaterialId:
 *                 type: string
 *                 description: "Source material ID (format: product:sku)"
 *                 example: "product:sku123"
 *               fromCatalogItemId:
 *                 type: string
 *                 description: "Source catalog item ID (optional, auto-populated)"
 *                 example: "catalog-from-123"
 *               quantity:
 *                 type: number
 *                 description: Quantity for the transition
 *                 example: 5
 *               toSubscriptionId:
 *                 type: string
 *                 description: "Target subscription ID (optional)"
 *                 example: "sub-to-456"
 *               toCatalogItemId:
 *                 type: string
 *                 description: Target catalog item ID
 *                 example: "catalog-to-456"
 *               promotionId:
 *                 type: string
 *                 description: "Promotion ID (optional)"
 *                 example: "promo-123"
 *               term:
 *                 type: string
 *                 description: Subscription term
 *                 example: "P1Y"
 *               billFreq:
 *                 type: string
 *                 description: Billing frequency
 *                 example: "Monthly"
 *               updateType:
 *                 type: string
 *                 description: "Type of update (optional)"
 *                 example: "upgrade"
 *               subUpdateType:
 *                 type: string
 *                 description: "Sub update type (optional)"
 *                 example: "license-upgrade"
 *               unitPrice:
 *                 type: number
 *                 description: "Unit price (optional)"
 *                 example: 99.99
 *               taxRate:
 *                 type: number
 *                 description: "Tax rate (optional)"
 *                 example: 0.08
 *               promotionRate:
 *                 type: number
 *                 description: "Promotion rate (optional)"
 *                 example: 0.10
 *               segment:
 *                 type: string
 *                 description: "Customer segment (optional)"
 *                 example: "enterprise"
 *           example:
 *             brandId: "string"
 *             storeId: "string"
 *             partnerId: "string"
 *             customerId: "string"
 *             fromSubscriptionId: "string"
 *             fromMaterialId: "string"
 *             fromCatalogItemId: "string"
 *             quantity: 0
 *             toSubscriptionId: "string"
 *             toCatalogItemId: "string"
 *             promotionId: "string"
 *             term: "string"
 *             billFreq: "string"
 *             updateType: "string"
 *             subUpdateType: "string"
 *             unitPrice: 0
 *             taxRate: 0
 *             promotionRate: 0
 *             segment: "string"
 *     responses:
 *       '200':
 *         description: NCE Transition placed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: boolean
 *                   example: true
 *                 Message:
 *                   type: string
 *                   example: "Transition request processed successfully"
 *       '400':
 *         description: Bad request - validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: string
 *                 statusCode:
 *                   type: number
 *                   example: 400
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: boolean
 *                   example: false
 *                 Message:
 *                   type: string
 *                   example: "Internal server error"
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req);

  logger.info(`[${correlationId}] Entered into PlaceNCETransition API with PlaceTransitionRequest-${JSON.stringify(req.body)}`);

  try {
    // Validate request body
    const validationErrors = validatePlaceTransitionRequest(req.body);

    if (validationErrors.length > 0) {
      logger.error(`[${correlationId}] Validation failed: ${JSON.stringify(validationErrors)}`);

      const errorResponse: ErrorResponseModel = {
        errors: validationErrors,
        statusCode: 400
      };

      return res.status(400).json(errorResponse);
    }

    const request: PlaceTransitionRequestType = PlaceTransitionRequestSchema.parse(req.body);

    // Convert camelCase to PascalCase for service layer (matching .NET structure)
    const serviceRequest = {
      BrandId: request.brandId,
      StoreId: request.storeId,
      PartnerId: request.partnerId,
      CustomerId: request.customerId,
      FromSubscriptionId: request.fromSubscriptionId,
      FromMaterialId: request.fromMaterialId,
      FromCatalogItemId: request.fromCatalogItemId,
      Quantity: request.quantity,
      ToSubscriptionId: request.toSubscriptionId,
      ToCatalogItemId: request.toCatalogItemId,
      PromotionId: request.promotionId,
      Term: request.term,
      BillFreq: request.billFreq,
      UpdateType: request.updateType,
      SubUpdateType: request.subUpdateType,
      UnitPrice: request.unitPrice,
      TaxRate: request.taxRate,
      PromotionRate: request.promotionRate,
      Segment: request.segment,
    };

    // Call the service
    const result: ResponseStatus = await placeNCETransitionService(req, serviceRequest);

    logger.info(`[${correlationId}] PlaceNCETransition completed with result: ${JSON.stringify(result)}`);

    return res.status(200).json(result);

  } catch (ex: any) {
    logger.error(`[${correlationId}] Error in PlaceNCETransition|ErrorDetail,errorMessage : ${ex.message}, StackTrace : ${ex.stack}`);

    const errorResponse: ResponseStatus = {
      Status: false,
      Message: ex.message
    };

    return res.status(500).json(errorResponse);
  }
}