import { getSqlServerConnection } from "../../utils/sqlServerClient";
import logger from "../../utils/logger";

export interface SubscriptionModel {
  SubscriptionId: string;
  EndDate: Date;
  CustomerId: string;
  BrandId: string;
  StoreId: string;
  StoreKey: number;
  PlanType: string;
  Duration: string;
  MaterialId: string;
  MaterialDescription: string;
  Quantity: number;
  Term: string;
  Status: string;
}

export async function getSubscriptionDetailById(
  subscriptionId: string
): Promise<SubscriptionModel | undefined> {
  const pool = await getSqlServerConnection();
  const request = pool.request();
  request.input("subscription_id", subscriptionId);

  logger.info(
    `Calling usp_sel_subscription_by_Subid with subscription_id=${subscriptionId}`
  );

  const result = await request.execute<SubscriptionModel>(
    "usp_sel_subscription_by_Subid"
  );

  const record = result.recordset?.[0];
  logger.info(`Subscription fetch result: ${JSON.stringify(record)}`);

  return record;
}















// import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
// import logger from "../../utils/logger";
// import { getCorrelationId } from "../../config/microsoftEndpoints";

// export async function GET(req: MedusaRequest, res: MedusaResponse) {
//   try {
//     const correlationId = getCorrelationId(req);
//     const { linkedAccountId } = req.query as { linkedAccountId?: string };

//     logger.info(
//       `GetLinkedAccountDetails API called | CorrelationId: ${correlationId}, linkedAccountId=${linkedAccountId}`
//     );

//     const validationMessages = await validateLinkedAccountId(linkedAccountId);

//     if (validationMessages.length > 0) {
//       logger.info(
//         `Validation failed | CorrelationId: ${correlationId} | Errors: ${JSON.stringify(
//           validationMessages
//         )}`
//       );
//       return res.status(400).json({
//         status: "FAIL",
//         message: validationMessages,
//       });
//     }

//     const linkedAccountDetails = await getLinkedAccountDetailsService(linkedAccountId!);

//     logger.info(
//       `Success | CorrelationId: ${correlationId} | Response: ${JSON.stringify(linkedAccountDetails)}`
//     );

//     return res.status(200).json({
//       status: "PASS",
//       message: linkedAccountDetails,
//     });
//   } catch (error) {
//     logger.error("GetLinkedAccountDetails API error", error as Error);
//     return res.status(500).json({
//       status: "FAIL",
//       message: "Internal server error",
//     });
//   }
// }
