import { model } from '@medusajs/framework/utils';
import { ProfileInfo } from './profile-info';
import { UserBrandPreferences } from './user-brand-preferences';
import { AuthorizedSignatory } from './authorized-signatory';
import { CompanyDocument } from './company-document';
import CROfficeCountry from './office-country';

export const CqCompanyRegistration = model.define('cq_company_registration', {
	id: model.text().primaryKey(),
	user_id: model.text().nullable(),
	region_id: model.text().nullable(),
	gst_number: model.text().unique(),
	pan_number: model.text().unique(),
	part_of_sez: model.boolean().default(false),
	cin_number: model.text().unique(),
	company_name: model.text(),
	address_1: model.text(),
	address_2: model.text(),
	country_of_business: model.text(),
	postal_code: model.text(),
	address_name: model.text(),
	state: model.text(),
	city: model.text(),
	country_code: model.text(),
	contact_number: model.text(),
	legal_status_of_company: model.text(),
	website: model.text(),
	social_media_account: model.text(),
	number_of_offices_in_region: model.number(),
	other_countries_offices: model.hasMany(() => CROfficeCountry, {
		mappedBy: 'company_registration',
	}),
	number_of_warehouses_in_region: model.number(),
	number_of_employees: model.number(),
	number_of_sales_staff: model.number(),
	number_of_technical_staff: model.number(),
	twitter_account_url: model.text(),
	linkedin_account_url: model.text(),
	facebook_account_url: model.text(),
	instagram_account_url: model.text(),
	director_first_name: model.text(),
	director_last_name: model.text(),
	director_email: model.text(),
	director_country_code: model.text(),
	director_mobile_number: model.text(),
	sales_first_name: model.text(),
	sales_last_name: model.text(),
	sales_email: model.text(),
	sales_country_code: model.text(),
	sales_mobile_number: model.text(),
	accounts_first_name: model.text(),
	accounts_last_name: model.text(),
	accounts_email: model.text(),
	accounts_country_code: model.text(),
	accounts_mobile_number: model.text(),
	requested_by_admin: model.boolean(),
	requested_source: model.text().nullable(),
	status_id: model.text(),
	// To do store mapping
	store_id: model.text(),
	reason: model.text().nullable(),
	redington_account_id: model.text().unique().nullable(),
	metadata: model.json(),
	profile_info: model.hasMany(() => ProfileInfo, {
		mappedBy: 'company_registration',
	}),
	user_brand_preferences: model.hasMany(() => UserBrandPreferences, {
		mappedBy: 'company_registration',
	}),
	company_document: model.hasMany(() => CompanyDocument, {
		mappedBy: 'company_registration',
	}),
	authorized_signatories: model.hasMany(() => AuthorizedSignatory, {
		mappedBy: 'company_registration',
	}),
	created_by: model.text().nullable(),
	updated_by: model.text().nullable(),
	deleted_by: model.text().nullable(),
});
