import { getStoreDetails } from "../../token/microsoft/getStoreDetailsService"
import logger from "../../../utils/logger"
import { MicrosoftCreateCustomerRequest } from "../../../config/microsoftEndpoints"
import { ComparableString } from "../../../validators/customer/customCustomerValidator"
import { CreateCustomerStatusResponse } from "../../../types/responses/customResponse"
import { UpsertCustomer } from "./upsertCustomer"
import { MicrosoftProcess } from "../../externalService/microsoftProcess"

export async function upsertCustomerService(
  customerModel: any,
  microsoftRequest: MicrosoftCreateCustomerRequest,
  requestType: "CREATE" | "UPDATE"
): Promise<CreateCustomerStatusResponse> {
  try {
    logger.info(
      `Entered into CreateUpdateCustomer service with Type: ${requestType}, customer model: ${JSON.stringify(
        customerModel
      )}, Microsoft request body: ${JSON.stringify(microsoftRequest)}`
    )

    const normalizedType = ComparableString(requestType)
    logger.info(`normalizedType: ${normalizedType}`)

    switch (normalizedType) {
      case "UPDATE": {
        logger.info(
          `Entered into UPDATE section. Going to call UpsertCustomer('UPDATECUSTOMER')`
        )

        const updateRes = await UpsertCustomer("UPDATECUSTOMER", customerModel)

        logger.info(`Update response from DAL is: ${JSON.stringify(updateRes)}`)

        return {
          ...updateRes,
          status: updateRes?.status ?? "PASS",
          message: "Customer updated successfully",
        }
      }

      case "CREATE": {
        const brandId = customerModel.BrandId || customerModel.brandId
        logger.info(`Entered into CREATE section. BrandId is: ${brandId}`)

        if (!brandId) {
          logger.info(
            `BrandId is null. Going to call UpsertCustomer('CREATECUSTOMER')`
          )
          const createRes = await UpsertCustomer("CREATECUSTOMER", customerModel)
          logger.info(
            `Response from DAL for CREATECUSTOMER: ${JSON.stringify(createRes)}`
          )
          return {
            ...createRes,
            status: createRes?.status ?? "PASS",
            message: "Customer created successfully",
          }
        }

        logger.info(
          `BrandId is present. Going to call UpsertCustomer('CREATECUSTOMER') for vendor linking.`
        )
        let response: CreateCustomerStatusResponse = await UpsertCustomer(
          "CREATECUSTOMER",
          customerModel
        )
        logger.info(
          `Response from initial CREATECUSTOMER call: ${JSON.stringify(response)}`
        )

        logger.info(
          `Fetching store details for storeId=${customerModel.StoreId}, brandId=${brandId}`
        )
        const store = await getStoreDetails({
          storeId: customerModel.StoreId,
          brandId,
        })
        logger.info(`Store details: ${JSON.stringify(store)}`)

        const brand = ComparableString(store.brand)

        if (brand === "MICROSOFT") {
          logger.info(
            "Brand is MICROSOFT. Proceeding with Microsoft customer creation flow."
          )
          response = await MicrosoftProcess(customerModel, microsoftRequest, store)
          logger.info(`Microsoft process response: ${JSON.stringify(response)}`)
        } else if (brand === "AMAZON") {
          logger.info(
            "Brand is AMAZON. Proceeding with UpdateCustomer for vendor linking."
          )
          response = await UpsertCustomer("UPDATECUSTOMER", customerModel)
          logger.info(
            `Amazon vendor linking response: ${JSON.stringify(response)}`
          )
        } else {
          logger.warn(
            `Unsupported brand: ${brand}. Only Microsoft & Amazon are allowed.`
          )
          response = {
            status: "FAIL",
            message:
              "Invalid Brand Mapping - Only Microsoft & Amazon allowed for now",
          }
        }

        return {
          ...response,
          status: response?.status ?? "FAIL",
        }
      }

      default:
        logger.warn(`Unsupported request type: ${requestType}`)
        return {
          status: "FAIL",
          message: "Unsupported request type",
        }
    }
  } catch (err) {
    logger.error("Error in upsertCustomerService: ", err as Error)
    return {
      status: "FAIL",
      message: "Internal server error",
    }
  }
}
