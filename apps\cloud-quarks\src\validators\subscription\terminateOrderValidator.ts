import { z } from "zod";
import logger from "../../utils/logger";

// Zod schema for GetSubscriptionRequest (matching .NET GetSubscriptionRequest)
export const GetSubscriptionRequestSchema = z.object({
  Brand: z.string().optional(),
  SubscriptionId: z.string().optional(), // DefaultAzureSubscriptionId used for Azure RI
  OrderId: z.string().min(1, "OrderId cannot be blank or null"),
  CatalogId: z.string().min(1, "CatalogId cannot be blank or null"),
  GrantType: z.string().optional(),
  UserName: z.string().optional(),
  Password: z.string().optional(),
  ClientId: z.string().min(1, "ClientId cannot be blank or null"),
  Resource: z.string().optional(),
  Clientsecret: z.string().min(1, "Clientsecret cannot be blank or null"),
  StoreDomain: z.string().min(1, "StoreDomain cannot be blank or null"),
  Token: z.string().min(1, "Token cannot be blank or null"),
  CustomerId: z.string().min(1, "CustomerId cannot be blank or null"),
  Qty: z.number().optional(),
});

export type GetSubscriptionRequestType = z.infer<typeof GetSubscriptionRequestSchema>;

/**
 * Validate TerminateOrder request (matching .NET validation patterns)
 */
export function validateTerminateOrderRequest(request: any): string[] {
  logger.info(`Entered into validateTerminateOrderRequest method with request: ${JSON.stringify(request)}`);
  
  const validationErrors: string[] = [];

  try {
    GetSubscriptionRequestSchema.parse(request);
    logger.info(`Zod validation passed for TerminateOrder request`);
  } catch (error: any) {
    if (error.errors) {
      error.errors.forEach((err: any) => {
        const fieldName = err.path.join('.');
        validationErrors.push(`${fieldName}: ${err.message}`);
      });
    }
    logger.error(`Zod validation failed: ${JSON.stringify(error.errors)}`);
  }

  // Additional custom validations (matching .NET patterns)
  if (!request.OrderId || request.OrderId.trim() === '') {
    validationErrors.push("The 'OrderId' parameter is missing or empty in the request.");
  }

  if (!request.CustomerId || request.CustomerId.trim() === '') {
    validationErrors.push("The 'CustomerId' parameter is missing or empty in the request.");
  }

  if (!request.CatalogId || request.CatalogId.trim() === '') {
    validationErrors.push("The 'CatalogId' parameter is missing or empty in the request.");
  }

  if (!request.ClientId || request.ClientId.trim() === '') {
    validationErrors.push("The 'ClientId' parameter is missing or empty in the request.");
  }

  if (!request.Clientsecret || request.Clientsecret.trim() === '') {
    validationErrors.push("The 'Clientsecret' parameter is missing or empty in the request.");
  }

  if (!request.StoreDomain || request.StoreDomain.trim() === '') {
    validationErrors.push("The 'StoreDomain' parameter is missing or empty in the request.");
  }

  if (!request.Token || request.Token.trim() === '') {
    validationErrors.push("The 'Token' parameter is missing or empty in the request.");
  }

  logger.info(`Validation completed. Errors found: ${validationErrors.length}`);
  return validationErrors;
}
