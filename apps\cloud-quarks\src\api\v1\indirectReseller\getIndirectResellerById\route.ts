import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import { getIndirectResellerByIdService } from "../../../../services/indirectReseller/getIndirectResellerByIdService";

/**
 * @openapi
 * /v1/indirectReseller/getIndirectResellerById:
 *   get:
 *     summary: Get Indirect Reseller By ID
 *     description: Retrieves details of an indirect reseller for a given store.
 *     tags:
 *       - IndirectReseller
 *     parameters:
 *       - in: query
 *         name: storeId
 *         required: true
 *         schema:
 *           type: string
 *         description: Unique identifier of the store.
 *         example: "store-123"
 *       - in: query
 *         name: indirectResellerId
 *         required: true
 *         schema:
 *           type: string
 *         description: Unique identifier of the indirect reseller.
 *         example: "reseller-456"
 *     responses:
 *       200:
 *         description: Successfully retrieved indirect reseller details.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Id:
 *                   type: string
 *                   example: "reseller-456"
 *                 Name:
 *                   type: string
 *                   example: "ABC Reseller Ltd."
 *                 RelationshipType:
 *                   type: string
 *                   example: "Indirect"
 *                 State:
 *                   type: string
 *                   example: "Active"
 *                 MpnId:
 *                   type: string
 *                   example: "1234567"
 *                 Location:
 *                   type: string
 *                   example: "IN"
 *                 ErrorMessage:
 *                   type: string
 *                   example: ""
 *       400:
 *         description: Bad request due to missing or invalid parameters.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 ErrorMessage:
 *                   type: string
 *                   example: "Store Id cannot be null or empty, Indirect Reseller Id cannot be null or empty"
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 ErrorMessage:
 *                   type: string
 *                   example: "Internal Server Error"
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const { storeId, indirectResellerId } = req.query as Record<string, string>;
  const correlationId = getCorrelationId(req);

  logger.info(
    `Entered into GetIndirectResellerById API | correlationId: ${correlationId} | storeId: ${storeId} | indirectResellerId: ${indirectResellerId}`
  );

  // Validation
  const errors = [];
  if (!storeId) errors.push("Store Id cannot be null or empty");
  if (!indirectResellerId) errors.push("Indirect Reseller Id cannot be null or empty");

  if (errors.length > 0) {
    const validationErrorResponse = {
      ErrorMessage: errors.join(", "),
    };
    logger.info(
      `Validation failed | Errors: ${JSON.stringify(validationErrorResponse)}`
    );
    return res.status(400).json(validationErrorResponse);
  }

  try {
    const response = await getIndirectResellerByIdService(req, storeId, indirectResellerId);
    return res.status(200).json(response);
  } catch (err) {
    logger.error(`Error in GetIndirectResellerById API: ${err}`);
    return res.status(500).json({ ErrorMessage: "Internal Server Error" });
  }
}
