import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { Modules } from '@medusajs/framework/utils';
import { IRegionModuleService } from '@medusajs/framework/types';
import { stringConstants } from '@org/utils';

/**
 * @openapi
 * /v1/user/country:
 *   get:
 *     summary: Get a list of supported countries with region names
 *     tags:
 *       - Countries
 *     parameters:
 *       - in: query
 *         name: region_id
 *         schema:
 *           type: string
 *         required: false
 *         description: Optional region ID to filter countries by a specific region
 *     responses:
 *       200:
 *         description: List of countries fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       iso_2:
 *                         type: string
 *                         example: "ae"
 *                       name:
 *                         type: string
 *                         example: "United Arab Emirates (Middle East)"
 *                       display_name:
 *                         type: string
 *                         example: "UAE (Middle East)"
 *                 message:
 *                   type: string
 *                   example: Countries fetched successfully
 *       404:
 *         description: No countries found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items: {}
 *                 message:
 *                   type: string
 *                   example: Country not found
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error
 *                 error:
 *                   type: string
 *                   example: Internal server error. Please try again later.
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const regionService: IRegionModuleService = req.scope.resolve(Modules.REGION);

  try {
    const regionId = req.query.region_id as string | undefined;

    let regions: any[] = [];

    if (regionId) {
      try {
        const region = await regionService.retrieveRegion(regionId, {
          relations: ['countries'],
        });
        regions = [region];
      } catch (error) {
        return res.status(404).json({
          message: stringConstants.NOT_FOUND('Region'),
        });
      }
    } else {
      regions = await regionService.listRegions(
        {},
        { relations: ['countries'] }
      );
    }

    const filteredCountries = regions.flatMap((region) =>
      (region.countries || []).map((country: any) => ({
        iso_2: country.iso_2,
        name: `${country.name} (${region.name})`,
        display_name: `${country.display_name} (${region.name})`,
      }))
    );

    if (!filteredCountries || filteredCountries.length === 0) {
      return res.status(404).json({
        data: [],
        message: stringConstants.NOT_FOUND('Country'),
      });
    }

    filteredCountries.sort((a, b) => a.name.localeCompare(b.name));

    return res.status(200).json({
      data: filteredCountries,
      message: stringConstants.FETCHED_SUCCESSFULLY('Country'),
    });
  } catch (err: any) {
    return res.status(500).json({
      message: stringConstants.INTERNAL_SERVER_ERROR(),
    });
  }
}
