import logger from "../../utils/logger";
import sql from "mssql";
import { getSqlServerConnection } from "../../utils/sqlServerClient";
import { EntitlementsResponse, SubscriptionEntitlement } from "../../types/responses/customResponse";

/**
 * Get Subscription Entitlements from database
 * Matches .NET EntitlementDAL.GetSubscriptionEntitlements method
 */
export async function getSubscriptionEntitlementsDAL(
  subscriptionIds: string[]
): Promise<EntitlementsResponse> {
  const subscriptionIdsParam = subscriptionIds.join(",");
  logger.info(`Entered into Entitlement DAL GetSubscriptionEntitlements method with subscriptionIds-${subscriptionIdsParam}`);

  const entitlementModel: EntitlementsResponse = {
    TotalCount: 0,
    Entitlements: []
  };

  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();

    const procedure = "Usp_GetSubscriptionEntitlementsForSubscriptionIds"; // Matches .NET Procedures.Usp_GetSubscriptionEntitlementsForSubscriptionIds

    // Create table-valued parameter (matching .NET DataTable logic)
    const table = new sql.Table('dbo.StringList');
    table.columns.add('SubscriptionID', sql.VarChar(255));

    // Add subscription IDs to table (matching .NET: subscriptionIds.ForEach(x => table.Rows.Add(x)))
    subscriptionIds.forEach(id => {
      table.rows.add(id);
    });

    request.input("list", table);

    logger.info(`Going to hit DB with proc name : ${procedure} and parameters ${JSON.stringify({ subscriptionIds: subscriptionIdsParam })}`);

    const result = await request.execute<SubscriptionEntitlement>(procedure);
    entitlementModel.Entitlements = result.recordset || [];

    logger.info(`Response from proc name : ${procedure}  : ${JSON.stringify(entitlementModel.Entitlements)}`);
    logger.info(`Updating Count for Model : ${entitlementModel.Entitlements.length}`);
    entitlementModel.TotalCount = entitlementModel.Entitlements.length;
    logger.info(`Going to return the DAL response from proc name ${JSON.stringify(entitlementModel)}`);

    return entitlementModel;

  } catch (error: any) {
    logger.error(`Error in getSubscriptionEntitlementsDAL: ${error.message}`);

    // Return empty response on error
    return entitlementModel;
  }
}
