import { getStoreDetails } from "../token/microsoft/getStoreDetailsService";
import logger from "../../utils/logger";
import { ComparableString } from "../../validators/customer/customCustomerValidator";
import { GetCustomerBillingInfoResponse } from "../../types/responses/customResponse";
import { getMsToken } from "../token/microsoft/getTokenService";
import {
  getMicrosoftRequestHeader,
  MicrosoftEndpoints,
} from "../../config/microsoftEndpoints";
import { v4 as uuidv4 } from "uuid";
import { PutMethodWithRequestJsonBodyAndHeaders400Handle } from "../externalService/externalEndPointService";

export async function setCustomerBillingInfoService(
  req: any,
  brandId: string,
  storeId: string,
  tenantId: string,
  billingInfoModel: any
): Promise<GetCustomerBillingInfoResponse> {
  try {
    logger.info("Inside setCustomerBillingInfoService");
    console.log("billingInfoModel------------>", billingInfoModel);
    const completeUrlWithParams =
      MicrosoftEndpoints.getCustomerBillingInfoUrl(tenantId);

    logger.info(
      `Calling getStoreDetails with brandId=${brandId}, storeId=${storeId}`
    );
    const storeDetails = await getStoreDetails({ storeId, brandId });
    logger.info(`Store details: ${JSON.stringify(storeDetails)}`);

    const setCustomerBillingInfoResponse: GetCustomerBillingInfoResponse = {
      Id: billingInfoModel.id,
      Email: billingInfoModel.email,
      Culture: billingInfoModel.culture,
      Language: billingInfoModel.language,
      CompanyName: billingInfoModel.companyName,
      DefaultAddress: billingInfoModel.defaultAddress,
      Links: billingInfoModel.links,
      Attributes: billingInfoModel.attributes,
      FirstName: billingInfoModel.firstName,
      LastName: billingInfoModel.lastName,
      Code: -1,
      Description: "Not Processed",
    };
    if (
      !storeDetails.brand ||
      typeof storeDetails.brand !== "string" ||
      storeDetails.brand.toUpperCase().replace(/\s/g, "") !== "MICROSOFT"
    ) {
      return setCustomerBillingInfoResponse;
    }

    logger.info(`Microsoft brand matched. Getting token :: ")}`);
    const token = await getMsToken({
      brand: storeDetails.brand || "",
      client_id: storeDetails.clientid || "",
      client_secret: storeDetails.clientsecret || "",
      grant_type: storeDetails.granttype || "",
      markValue: storeDetails.markvalue?.toString() || "",
      redirect_uri: storeDetails.redirecturi || "",
      refresh_token: storeDetails.token || "",
      resource: storeDetails.resource || "",
      store_domain: storeDetails.storedomain || "",
    });

    if (!token) {
      setCustomerBillingInfoResponse.Description = "Get Token Error";
      return setCustomerBillingInfoResponse;
    }
    logger.info("Token acquired successfully, proceeding with next step...");

    // Generate Microsoft headers
    const correlationId = uuidv4();
    const requestId = uuidv4();
    const headerList = getMicrosoftRequestHeader(
      token.access_token,
      correlationId,
      requestId
    );

    logger.info(
      `Prepared Microsoft Request Headers: ${JSON.stringify(headerList)}`
    );

    const configModule = req.scope.resolve("configModule");
    const externalResponse =
      await PutMethodWithRequestJsonBodyAndHeaders400Handle(
        {
          url: completeUrlWithParams,
          body: billingInfoModel,
          headers: headerList,
          isVendorHit: true,
          module: "SetCustomerBillingInfo",
        },
        configModule
      );

    logger.info(`External PUT response: ${JSON.stringify(externalResponse)}`);

    if (externalResponse.isSuccessStatusCode) {
      const parsed = JSON.parse(externalResponse.content || "{}");

      setCustomerBillingInfoResponse.Code = 1;
      Object.assign(setCustomerBillingInfoResponse, parsed);

      if (!setCustomerBillingInfoResponse.Id) {
        return setCustomerBillingInfoResponse;
      }
    } else {
      setCustomerBillingInfoResponse.Description = "400 Bad Request";
    }

    logger.info(
      `Returning final billing info response: ${JSON.stringify(
        setCustomerBillingInfoResponse
      )}`
    );
    return setCustomerBillingInfoResponse;
  } catch (err) {
    logger.error("Error in Set Customer Billing Info Service: ", err as Error);
    return {
      Id: "",
      Code: -1,
      Description: "Unhandled exception",
    } as GetCustomerBillingInfoResponse;
  }
}
