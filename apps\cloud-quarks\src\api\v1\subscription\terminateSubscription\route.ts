import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import {
  GetSubscriptionResponse,
  ErrorResponseModel
} from "../../../../types/responses/customResponse";
import {
  validatePatchSubscriptionRequest,
  PatchSubscriptionModelType,
  PatchSubscriptionModelSchema
} from "../../../../validators/subscription/patchSubscriptionValidator";
import { patchSubscriptionService } from "../../../../services/subscription/patchSubscriptionService";

/**
 * @openapi
 * /v1/subscription/terminateSubscription:
 *   post:
 *     summary: Terminate subscription by updating status
 *     description: Terminates a subscription by calling the patch subscription service with termination status
 *     tags:
 *       - Subscription
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - storeId
 *               - customerId
 *               - subscriptionId
 *               - status
 *             properties:
 *               storeId:
 *                 type: string
 *                 description: Store ID
 *                 example: "store-123"
 *               customerId:
 *                 type: string
 *                 description: Customer ID
 *                 example: "cust-456"
 *               subscriptionId:
 *                 type: string
 *                 description: Subscription ID to terminate
 *                 example: "sub-789"
 *               status:
 *                 type: string
 *                 description: Termination status (e.g., cancelled, suspended)
 *                 example: "cancelled"
 *               quantity:
 *                 type: number
 *                 description: Quantity to update
 *                 example: 0
 *               requestId:
 *                 type: string
 *                 description: Request ID for tracking
 *                 example: "req-12345"
 *               autoRenewEnabled:
 *                 type: boolean
 *                 description: Whether auto-renew is enabled
 *                 example: true
 *               actualQty:
 *                 type: number
 *                 description: Actual quantity to reflect
 *                 example: 0
 *               updateType:
 *                 type: string
 *                 description: Type of update (e.g., termination)
 *                 example: "termination"
 *               partnerId:
 *                 type: string
 *                 description: Partner or reseller ID
 *                 example: "partner-123"
 *               autoRenewToUpdate:
 *                 type: boolean
 *                 description: Whether auto-renew should be updated
 *                 example: true
 *           example:
 *             storeId: "store-123"
 *             customerId: "cust-456"
 *             subscriptionId: "sub-789"
 *             status: "cancelled"
 *             quantity: 0
 *             requestId: "req-12345"
 *             autoRenewEnabled: false
 *             actualQty: 0
 *             updateType: "termination"
 *             partnerId: "partner-123"
 *             autoRenewToUpdate: false
 *     responses:
 *       '200':
 *         description: Subscription terminated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                   example: "SUCCESS"
 *                 ErrorMessage:
 *                   type: string
 *                   example: null
 *                 SubscriptionId:
 *                   type: string
 *                   example: "sub-789"
 *       '400':
 *         description: Bad request - validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                   example: "FAIL"
 *                 ErrorMessage:
 *                   type: string
 *                   example: "Validation failed"
 *                 Details:
 *                   type: array
 *                   items:
 *                     type: string
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                   example: "FAIL"
 *                 ErrorMessage:
 *                   type: string
 *                   example: "Internal server error"
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req);

  logger.info(`[${correlationId}] Entered into TerminateSubscription API with PatchSubscriptionModel-${JSON.stringify(req.body)} and going to call PatchSubscription service method`);

  try {
    // Validate request body using existing patch subscription validator
    const validationErrors = validatePatchSubscriptionRequest(req.body);

    if (validationErrors.length > 0) {
      logger.error(`[${correlationId}] Validation failed: ${JSON.stringify(validationErrors)}`);

      const errorResponse: ErrorResponseModel = {
        errors: validationErrors,
        statusCode: 400
      };

      return res.status(400).json(errorResponse);
    }

    const model: PatchSubscriptionModelType = PatchSubscriptionModelSchema.parse(req.body);

    const result: GetSubscriptionResponse = await patchSubscriptionService(req, model);

    logger.info(`[${correlationId}] TerminateSubscription completed successfully with result: ${JSON.stringify(result)}`);

    return res.status(200).json(result);

  } catch (ex: any) {
    logger.error(`[${correlationId}] Error in TerminateSubscription|ErrorDetail,errorMessage : ${ex.message}, StackTrace : ${ex.stack}`);

    const errorResponse: GetSubscriptionResponse = {
      Status: "FAIL",
      ErrorMessage: ex.message
    };

    return res.status(500).json(errorResponse);
  }
}