import {
  MedusaService,
  InjectManager,
  MedusaContext,
} from '@medusajs/framework/utils';
import CqQuestion from './models/question';
import AnswerType from './models/answer-type';
import Answer from './models/answer';
import { Brand } from './models/brand';
import { BrandCategory } from './models/brand-category';
import { BrandBrandCategory } from './models/brand-brand-category';
import { Vendor } from './models/vendor';
import { RegionVendor } from './models/region-vendor';
import { RegionBrand } from './models/region-brand';
import { RegionBrandCategory } from './models/region-brand-category';
import { CqCompanyRegistration } from './models/company-registration';
import { ProfileInfo } from './models/profile-info';
import { UserBrandPreferences } from './models/user-brand-preferences';
import { CqCompanyOnboarding } from './models/company-onboarding';
import { AuthorizedSignatory } from './models/authorized-signatory';
import { CompanyDocument } from './models/company-document';
import { Context } from '@medusajs/framework/types';
import { EntityManager } from '@mikro-orm/knex';
import CROfficeCountry from './models/office-country';

export default class CompanyRegistrationService extends MedusaService({
	CqCompanyRegistration,
	CqCompanyOnboarding,
	AuthorizedSignatory,
	ProfileInfo,
	UserBrandPreferences,
	CqQuestion,
	AnswerType,
	Answer,
	Brand,
	BrandCategory,
	BrandBrandCategory,
	Vendor,
	RegionVendor,
	RegionBrand,
	RegionBrandCategory,
	CompanyDocument,
	CROfficeCountry,
}) {
	// This service is used to manage company registrations and related operations.
	@InjectManager()
	async userExistsInForeignTable(
		userId: string,
		@MedusaContext() sharedContext?: Context<EntityManager>
	) {
		if (!sharedContext?.manager) {
			throw new Error('Database context is not available');
		}
		const [user] = await sharedContext.manager.execute(
			`SELECT id FROM cq_user_foreign WHERE id = ?`,
			[userId]
		);
		return !!user;
	}

	@InjectManager()
	async getAllUsersFromForeignTable(
		@MedusaContext() sharedContext?: Context<EntityManager>
	) {
		if (!sharedContext?.manager) {
			throw new Error('Database context is not available');
		}
		const users = await sharedContext.manager.execute(
			`SELECT * FROM cq_user_foreign`
		);
		return users;
	}

	@InjectManager()
	async checkAllRegionCountryIdsExist(
		ids: string[],
		@MedusaContext() sharedContext?: Context<EntityManager>
	) {
		if (!sharedContext?.manager) {
			throw new Error('Database context is not available');
		}
		const placeholders = ids.map(() => '?').join(',');
		const found = await sharedContext.manager.execute(
			`SELECT iso_2 FROM cq_region_country_foreign WHERE iso_2 IN (${placeholders})`,
			ids
		);
		if (found.length !== ids.length) {
			throw new Error('One or more cq_region_country_foreign IDs do not exist');
		}
		return true;
	}

	@InjectManager()
	async getAllRegionCountriesFromForeignTable(
		@MedusaContext() sharedContext?: Context<EntityManager>
	) {
		if (!sharedContext?.manager) {
			throw new Error('Database context is not available');
		}
		const countries = await sharedContext.manager.execute(
			`SELECT * FROM cq_region_country_foreign`
		);
		return countries;
	}

	@InjectManager()
	async checkAllStatusIdsExist(
		ids: string[],
		@MedusaContext() sharedContext?: Context<EntityManager>
	) {
		if (!sharedContext?.manager) {
			throw new Error('Database context is not available');
		}
		const placeholders = ids.map(() => '?').join(',');
		const found = await sharedContext.manager.execute(
			`SELECT id FROM cq_status_foreign WHERE id IN (${placeholders})`,
			ids
		);
		if (found.length !== ids.length) {
			throw new Error('One or more cq_status_foreign IDs do not exist');
		}
		return true;
	}

	@InjectManager()
	async getAllStatusFromForeignTable(
		@MedusaContext() sharedContext?: Context<EntityManager>
	) {
		if (!sharedContext?.manager) {
			throw new Error('Database context is not available');
		}
		const statuses = await sharedContext.manager.execute(
			`SELECT * FROM cq_status_foreign`
		);
		return statuses;
	}

	@InjectManager()
	async checkAllStoreIdsExist(
		ids: string[],
		@MedusaContext() sharedContext?: Context<EntityManager>
	) {
		if (!sharedContext?.manager) {
			throw new Error('Database context is not available');
		}
		const placeholders = ids.map(() => '?').join(',');
		const found = await sharedContext.manager.execute(
			`SELECT id FROM cq_store_foreign WHERE id IN (${placeholders})`,
			ids
		);
		if (found.length !== ids.length) {
			throw new Error('One or more cq_store_foreign IDs do not exist');
		}
		return true;
	}

	@InjectManager()
	async getAllStoresFromForeignTable(
		@MedusaContext() sharedContext?: Context<EntityManager>
	) {
		if (!sharedContext?.manager) {
			throw new Error('Database context is not available');
		}
		const stores = await sharedContext.manager.execute(
			`SELECT * FROM cq_store_foreign`
		);
		return stores;
	}

	@InjectManager()
	async checkAllUserPersonaIdsExist(
		ids: string[],
		@MedusaContext() sharedContext?: Context<EntityManager>
	) {
		if (!sharedContext?.manager) {
			throw new Error('Database context is not available');
		}
		const placeholders = ids.map(() => '?').join(',');
		const found = await sharedContext.manager.execute(
			`SELECT id FROM cq_user_persona_foreign WHERE id IN (${placeholders})`,
			ids
		);
		if (found.length !== ids.length) {
			throw new Error('One or more cq_user_persona_foreign IDs do not exist');
		}
		return true;
	}

	@InjectManager()
	async getAllUserPersonasFromForeignTable(
		@MedusaContext() sharedContext?: Context<EntityManager>
	) {
		if (!sharedContext?.manager) {
			throw new Error('Database context is not available');
		}
		const personas = await sharedContext.manager.execute(
			`SELECT * FROM cq_user_persona_foreign`
		);
		return personas;
	}

	// This method retrieves all regions from a foreign table.
	@InjectManager()
	async getAllRegionsFromForeignTable(
		@MedusaContext() sharedContext?: Context<EntityManager>
	) {
		if (!sharedContext?.manager) {
			throw new Error('Database context is not available');
		}
		const regions = await sharedContext.manager.execute(
			`SELECT * FROM cq_region_foreign`
		);
		return regions;
	}
  @InjectManager()
  async getStatusById(
    id: string,
    @MedusaContext() sharedContext?: Context<EntityManager>
  ) {
    if (!sharedContext?.manager) {
      console.warn('Database context is not available');
      return null;
    }

    const result = await sharedContext.manager.execute(
      `SELECT * FROM cq_status_foreign WHERE id = ?`,
      [id]
    );

    return result[0] || null;
  }

    @InjectManager()
  async getUserByIDInForeignTable(
    userId: string,
    @MedusaContext() sharedContext?: Context<EntityManager>
  ) {
    if (!sharedContext?.manager) {
      throw new Error('Database context is not available');
    }
    const result = await sharedContext.manager.execute(
      `SELECT * FROM cq_user_foreign WHERE id = ?`,
      [userId]
    );
     return result[0] || null;
  }
}
