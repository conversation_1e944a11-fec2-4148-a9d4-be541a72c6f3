import swaggerJSDoc from 'swagger-jsdoc';
import yaml from 'js-yaml';
import fs from 'fs';
import path from 'path';

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Medusa Auth API',
      version: '1.0.0',
      description: 'Authentication API for Medusa partners to securely manage login, tokens, and session validation.',
    },
    servers: [
      {
        url: 'http://localhost:8081',
        description: 'Local development server',
      },
    ],
  },
  // Match all route files under `api/**/route.ts` or `*.ts` as needed
  apis: ['src/api/v1/auth/**/*.ts'],
};

const swaggerSpec = swaggerJSDoc(options);
const yamlStr = yaml.dump(swaggerSpec);

const outputPath = path.join(__dirname, '../docs/swagger.yml');
fs.mkdirSync(path.dirname(outputPath), { recursive: true });
fs.writeFileSync(outputPath, yamlStr, 'utf8');

console.log(`✅ Generated Swaggeer YAML at: ${outputPath}`);
