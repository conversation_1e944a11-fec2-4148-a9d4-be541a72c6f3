import { model } from '@medusajs/framework/utils';
import CQUser from './cq_user';
import Role from './role';

const UserRole = model.define('cq_user_role', {
  id: model.id().primaryKey(),
  user: model.belongsTo(() => CQUser, { mappedBy: undefined }),
  role: model.belongsTo(() => Role, {
    mappedBy: undefined,
  }),
  // #UPDATE need to create a link for company management
  company_id: model.text(),
  assigned_by: model.belongsTo(() => CQUser, { mappedBy: undefined }),
});

export default UserRole;
