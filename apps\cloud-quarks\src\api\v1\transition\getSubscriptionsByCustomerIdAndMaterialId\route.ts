import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";

import {
  validateGetSubscriptionsByCustomerIdAndMaterialIdRequest,
  GetSubscriptionsByCustomerIdAndMaterialIdQueryType
} from "../../../../validators/transition/getSubscriptionsByCustomerIdAndMaterialIdValidator";
import { getSubscriptionsByCustomerIdAndMaterialIdService } from "../../../../services/transition/getSubscriptionsByCustomerIdAndMaterialIdService";

/**
 * @openapi
 * /v1/transition/getSubscriptionsByCustomerIdAndMaterialId:
 *   get:
 *     summary: Get subscriptions by Customer ID and Material ID
 *     tags:
 *       - Transition
 *     parameters:
 *       - in: query
 *         name: customerId
 *         schema:
 *           type: string
 *         required: true
 *         description: Customer ID
 *       - in: query
 *         name: materialId
 *         schema:
 *           type: string
 *         required: true
 *         description: Material ID
 *     responses:
 *       200:
 *         description: Subscriptions retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 Status:
 *                   type: string
 *                   example: "1"
 *                 Message:
 *                   type: string
 *                   example: Subscriptions fetched successfully
 *                 Data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       SubscriptionId:
 *                         type: string
 *                       CustomerId:
 *                         type: string
 *                       MaterialId:
 *                         type: string
 *                       Status:
 *                         type: string
 *                       CreatedDate:
 *                         type: string
 *                         format: date-time
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: string
 *                 example: "CustomerId is required"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Internal server error
 *                 message:
 *                   type: string
 *                   example: Unexpected failure during processing
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req);

  logger.info(`[${correlationId}] Entered into Transition/GetSubscriptionsByCustomerIdAndMaterialId API with customerId-${req.query.customerId}, materialId-${req.query.materialId}`);

  try {
    // Validate the query parameters
    logger.info(`[${correlationId}] Going to validate request items`);
    const validationErrors = validateGetSubscriptionsByCustomerIdAndMaterialIdRequest(req.query);
    logger.info(`[${correlationId}] Got validation messages from validate method: ${JSON.stringify(validationErrors)}`);

    if (validationErrors.length > 0) {
      logger.info(`[${correlationId}] Going to return Badrequest with validationErrors in GetSubscriptionsByCustomerIdAndMaterialId method ${JSON.stringify(validationErrors)}`);
      return res.status(400).json(validationErrors);
    }

    const queryParams: GetSubscriptionsByCustomerIdAndMaterialIdQueryType = req.query as GetSubscriptionsByCustomerIdAndMaterialIdQueryType;

    // Call business logic service 
    logger.info(`[${correlationId}] Going to hit BL GetSubscriptionsByCustomerIdAndMaterialId method with customerId-${queryParams.customerId}, materialId-${queryParams.materialId}`);
    const response = await getSubscriptionsByCustomerIdAndMaterialIdService(queryParams.customerId, queryParams.materialId);
    logger.info(`[${correlationId}] Result from BL GetSubscriptionsByCustomerIdAndMaterialId full process is ${JSON.stringify(response)}`);

    return res.status(200).json(response);

  } catch (error: any) {
    // Log error
    logger.error(`[${correlationId}] Error in GetSubscriptionsByCustomerIdAndMaterialId, errorMessage: ${error.message}, StackTrace: ${error.stack}`);

    // Return internal server error
    return res.status(500).json({
      error: "Internal server error",
      message: error.message
    });
  }
}