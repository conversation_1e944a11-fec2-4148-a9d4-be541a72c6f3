import logger from "../../utils/logger";
import { SubscriptionResponseModel, CiscoAdditionalInformation } from "../../types/responses/customResponse";
import { getSubscriptionsDAL } from "./getSubscriptionsDAL";
import { getSubscriptionEntitlementsDAL } from "../entitlement/getSubscriptionEntitlementsDAL";
import { getCiscoAdditionalInformationDAL } from "../cisco/getCiscoAdditionalInformationDAL";
import { CiscoConstants } from "../../validators/reseller/addUpdateCiscoResellerValidator";

// Helper function to normalize string comparison (matching .NET ComparableString extension)
function comparableString(str?: string): string {
  return (str || "").trim().toUpperCase();
}

/**
 * Add Subscription Entitlements (matching .NET AddSubscriptionEntitlements method)
 */
async function addSubscriptionEntitlements(responseFromDal: SubscriptionResponseModel[]): Promise<void> {
  logger.info(`Creating List of distinct subscription Ids for GetSubscriptionEntitlements`);

  // Extract distinct subscription IDs (matching .NET: responseFromDal.Select(x => x.SubscriptionId).Distinct().ToList())
  const subscriptionIds = [...new Set(responseFromDal.map(x => x.SubscriptionId).filter((id): id is string => Boolean(id)))];
  logger.info(`Fetched List of distinct subscription Ids  ${JSON.stringify(subscriptionIds)}`);

  logger.info(`Going to hit Entitlement DAL GetSubscriptionEntitlements method with list of subscription Ids ${JSON.stringify(subscriptionIds)}`);
  const responseFromEntitlementDal = await getSubscriptionEntitlementsDAL(subscriptionIds);
  logger.info(`Got response from Entitlement DAL GetSubscriptionEntitlements method : ${JSON.stringify(responseFromEntitlementDal)}`);

  logger.info(`Entitlements count: ${responseFromEntitlementDal.TotalCount}`);
  if (responseFromEntitlementDal.TotalCount > 0) {
    logger.info(`Mapping entitlement list to subscription response`);

    // Map entitlements to subscriptions (matching .NET LINQ logic: p.SubscriptionEntitlements = (responseFromEntitlementDal.Entitlements.Where(e => e.SubscriptionId.ToLowerInvariant().Equals(p.SubscriptionId.ToLowerInvariant()))).ToList())
    responseFromDal.forEach(subscription => {
      subscription.SubscriptionEntitlements = responseFromEntitlementDal.Entitlements.filter(entitlement =>
        entitlement.SubscriptionId?.toLowerCase() === subscription.SubscriptionId?.toLowerCase()
      );
    });

    logger.info(`Mapped entitlement list to subscription response : ${JSON.stringify(responseFromDal)}`);
  }

  logger.info(`Added entitlement to subscriptions : ${JSON.stringify(responseFromDal)}`);
}

/**
 * Add Cisco Additional Information (matching .NET AddCiscoAdditionalInformation method)
 */
async function addCiscoAdditionalInformation(responseFromDal: SubscriptionResponseModel[]): Promise<void> {
  try {
    logger.info(`Creating List of distinct subscription Ids with Brand Cisco for AddCiscoAdditionalInformation`);

    // Filter Cisco subscriptions (matching .NET LINQ logic: x.BrandName.ComparableString() == CiscoConstants.BrandName_Cisco.ComparableString())
    const ciscoSubscriptionIds = [...new Set(
      responseFromDal
        .filter(x => comparableString(x.AddAtt3) === comparableString(CiscoConstants.BrandName_Cisco)) // AddAtt3 is BrandName
        .map(x => x.SubscriptionId)
        .filter((id): id is string => Boolean(id))
    )];

    logger.info(`Fetched List of distinct subscription Ids with Brand Cisco-${JSON.stringify(ciscoSubscriptionIds)}`);

    logger.info(`Entering If CiscoSubscriptionIds.Any() with count: ${ciscoSubscriptionIds.length}`);
    if (ciscoSubscriptionIds.length > 0) {
      logger.info(`Entered If CiscoSubscriptionIds.Any() and Going to hit Cisco Dal GetCiscoAdditionalInformation method with subscriptionIds-${JSON.stringify(ciscoSubscriptionIds)}`);

      const ciscoAdditionalInformationResponseDal = await getCiscoAdditionalInformationDAL(ciscoSubscriptionIds);
      logger.info(`Got response from Cisco Dal GetCiscoAdditionalInformation method : ${JSON.stringify(ciscoAdditionalInformationResponseDal)}`);

      logger.info(`Entering Loop of subscriptionList`);

      // Process each Cisco subscription (matching .NET foreach logic: responseFromDal.Where(x => ciscoSubscriptionIds.Contains(x.SubscriptionId)).ToList())
      const ciscoSubscriptions = responseFromDal.filter(x => ciscoSubscriptionIds.includes(x.SubscriptionId!));

      for (const sub of ciscoSubscriptions) {
        logger.info(`Entered Loop of subscriptionList for SubscriptionId-${sub.SubscriptionId}`);

        sub.CiscoAdditionalInformation = {} as CiscoAdditionalInformation;

        // Add Provisioning Attributes (matching .NET logic: Single(e => e.SubscriptionId.ToLowerInvariant().Equals(sub.SubscriptionId.ToLowerInvariant())))
        logger.info(`Getting Provisioning Attribute for SubscriptionId-${sub.SubscriptionId}`);
        const provisioningAttribute = ciscoAdditionalInformationResponseDal.ProvisioningAttributes.find(e =>
          e.SubscriptionId?.toLowerCase() === sub.SubscriptionId?.toLowerCase()
        );

        if (provisioningAttribute) {
          sub.CiscoAdditionalInformation.ProvisioningAttributes = provisioningAttribute;
          logger.info(`Got Provisioning Attribute for SubscriptionId-${sub.SubscriptionId} is ${JSON.stringify(sub.CiscoAdditionalInformation.ProvisioningAttributes)}`);

          // Order Key for this Subscription
          const orderKey = sub.CiscoAdditionalInformation.ProvisioningAttributes.OrderKey;

          // Add Cisco Order Details (matching .NET logic: Where(e => e.OrderKey == orderKey).ToList())
          logger.info(`Getting CiscoOrderDetails for SubscriptionId-${sub.SubscriptionId} and OrderKey-${orderKey}`);

          sub.CiscoAdditionalInformation.CiscoOrderDetails = ciscoAdditionalInformationResponseDal.CiscoOrderDetails.filter(e =>
            e.OrderKey === orderKey
          );

          logger.info(`Got CiscoOrderDetails for SubscriptionId-${sub.SubscriptionId} is ${JSON.stringify(sub.CiscoAdditionalInformation.CiscoOrderDetails)}`);
        }

        logger.info(`Exiting Loop of subscriptionList for SubscriptionId-${sub.SubscriptionId}, subscription-${JSON.stringify(sub)}`);
      }

      logger.info(`Exited Loop of subscriptionList and Exiting Loop of subscriptionList`);
    }

    logger.info(`Added CiscoAdditionalInformation to subscriptions : ${JSON.stringify(responseFromDal)}`);
  } catch (ex: any) {
    logger.error(`Exception in AddCiscoAdditionalInformation Method, ErrorMessage:${ex.message}, StackTrace:${ex.stack}`);
  }
}

/**
 * Get Subscriptions
 * Matches .NET SubscriptionFactory.GetSubscriptions method
 */
export async function getSubscriptionsService(
  storeId: string,
  partnerId: string,
  customerId: string,
  brandId: string,
  subscriptionStatuses: string,
  expiryInDays: string
): Promise<SubscriptionResponseModel[]> {
  logger.info(`Entered into BL GetSubscriptions method with storeId-${storeId}, partner-${partnerId}, customerId-${customerId}, brandId-${brandId}, subscriptionStatuses--${subscriptionStatuses}, expiryInDays--${expiryInDays}`);
  
  logger.info(`Going to hit DAL GetSubscriptions method with storeId-${storeId}, partner-${partnerId}, customerId-${customerId}, brandId-${brandId}, subscriptionStatuses--${subscriptionStatuses}, expiryInDays--${expiryInDays}`);
  const responseFromDal = await getSubscriptionsDAL(storeId, partnerId, customerId, brandId, subscriptionStatuses, expiryInDays);
  logger.info(`Got response from DAL GetSubscriptions method : response count ${responseFromDal.length}`);
  
  if (responseFromDal.length === 0) {
    logger.info(`Received Empty response from DAL GetSubscriptions method and Going to return Empty response`);
    return responseFromDal;
  }

  logger.info(`Entering AddSubscriptionEntitlements Method`);
  await addSubscriptionEntitlements(responseFromDal);
  logger.info(`Exited AddSubscriptionEntitlements Method`);

  logger.info(`Entering AddCiscoAdditionalInformation Method`);
  await addCiscoAdditionalInformation(responseFromDal);
  logger.info(`Exited AddCiscoAdditionalInformation Method`);

  logger.info(`Going to return subscriptions : response count ${responseFromDal.length}`);
  logger.info(`Exiting BL GetSubscriptions method`);
  return responseFromDal;
}
