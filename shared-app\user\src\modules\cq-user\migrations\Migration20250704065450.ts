import { Migration } from '@mikro-orm/migrations';

export class Migration20250704065450 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table if exists "cq_activity_types" drop constraint if exists "cq_activity_types_type_unique";`);
    this.addSql(`alter table if exists "cq_page" drop constraint if exists "cq_page_page_unique";`);
    this.addSql(`alter table if exists "cq_module" drop constraint if exists "cq_module_module_unique";`);
    this.addSql(`alter table if exists "cq_permission" drop constraint if exists "cq_permission_permission_unique";`);
    this.addSql(`alter table if exists "cq_persona" drop constraint if exists "cq_persona_persona_unique";`);
    this.addSql(`alter table if exists "cq_user" drop constraint if exists "cq_user_email_unique";`);
    this.addSql(`create table if not exists "cq_company_type" ("id" text not null, "name" text not null, "key" text not null, "metadata" jsonb not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_company_type_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_company_type_deleted_at" ON "cq_company_type" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_country_extension" ("id" text not null, "country_code" text not null, "country_flag" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_country_extension_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_country_extension_deleted_at" ON "cq_country_extension" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_designation" ("id" text not null, "name" text not null, "key" text not null, "metadata" jsonb not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_designation_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_designation_deleted_at" ON "cq_designation" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_email_otp_verification" ("id" text not null, "email" text null, "mobile_number" text null, "otp_code" text not null, "expires_at" timestamptz not null, "is_verified" boolean not null default false, "is_registered" boolean not null default false, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_email_otp_verification_pkey" primary key ("id"), constraint email_or_mobile_required check ((email IS NOT NULL OR mobile_number IS NOT NULL)));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_email_otp_verification_deleted_at" ON "cq_email_otp_verification" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_status" ("id" text not null, "name" text not null, "key" text not null, "metadata" jsonb not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_status_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_status_deleted_at" ON "cq_status" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_user" ("id" text not null, "first_name" text not null, "last_name" text not null, "email" text not null, "country_code" text not null, "mobile_number" text not null, "company_name" text null, "redington_sales_reference" text null, "profile_pic_file_name" text null, "is_active" boolean not null default false, "is_delete" boolean not null default false, "is_password_set" boolean not null default false, "has_sales_reference" boolean not null default false, "terms_and_conditions" boolean not null default false, "source" text check ("source" in ('Partner', 'Direct End Customer', 'Vendor', 'Redington Admin')) not null default 'Direct End Customer', "is_onboarded" boolean not null default false, "cq_designation_id" text not null, "registered_country_id" text not null, "created_by_id" text null, "updated_by_id" text null, "deleted_by_id" text null, "metadata" jsonb null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_user_pkey" primary key ("id"));`);
    this.addSql(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_cq_user_email_unique" ON "cq_user" (email) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_user_cq_designation_id" ON "cq_user" (cq_designation_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_user_created_by_id" ON "cq_user" (created_by_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_user_updated_by_id" ON "cq_user" (updated_by_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_user_deleted_by_id" ON "cq_user" (deleted_by_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_user_deleted_at" ON "cq_user" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_store" ("id" text not null, "name" text not null, "region_country_id" text not null, "default" boolean not null default false, "metadata" jsonb null, "created_by_id" text null, "updated_by_id" text null, "deleted_by_id" text null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_store_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_store_created_by_id" ON "cq_store" (created_by_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_store_updated_by_id" ON "cq_store" (updated_by_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_store_deleted_by_id" ON "cq_store" (deleted_by_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_store_deleted_at" ON "cq_store" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_persona" ("id" text not null, "persona" text not null, "key" text not null, "metadata" jsonb null, "order_id" integer not null, "created_by_id" text null, "updated_by_id" text null, "deleted_by_id" text null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_persona_pkey" primary key ("id"));`);
    this.addSql(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_cq_persona_persona_unique" ON "cq_persona" (persona) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_persona_created_by_id" ON "cq_persona" (created_by_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_persona_updated_by_id" ON "cq_persona" (updated_by_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_persona_deleted_by_id" ON "cq_persona" (deleted_by_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_persona_deleted_at" ON "cq_persona" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_role" ("id" text not null, "role" text not null, "order_id" integer null, "metadata" jsonb null, "created_by_id" text null, "updated_by_id" text null, "deleted_by_id" text null, "persona_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_role_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_role_created_by_id" ON "cq_role" (created_by_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_role_updated_by_id" ON "cq_role" (updated_by_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_role_deleted_by_id" ON "cq_role" (deleted_by_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_role_persona_id" ON "cq_role" (persona_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_role_deleted_at" ON "cq_role" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_permission" ("id" text not null, "permission" text not null, "metadata" jsonb null, "created_by_id" text null, "updated_by_id" text null, "deleted_by_id" text null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_permission_pkey" primary key ("id"));`);
    this.addSql(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_cq_permission_permission_unique" ON "cq_permission" (permission) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_permission_created_by_id" ON "cq_permission" (created_by_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_permission_updated_by_id" ON "cq_permission" (updated_by_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_permission_deleted_by_id" ON "cq_permission" (deleted_by_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_permission_deleted_at" ON "cq_permission" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_module" ("id" text not null, "module" text not null, "metadata" jsonb null, "order_id" integer not null, "created_by_id" text null, "updated_by_id" text null, "deleted_by_id" text null, "persona_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_module_pkey" primary key ("id"));`);
    this.addSql(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_cq_module_module_unique" ON "cq_module" (module) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_module_created_by_id" ON "cq_module" (created_by_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_module_updated_by_id" ON "cq_module" (updated_by_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_module_deleted_by_id" ON "cq_module" (deleted_by_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_module_persona_id" ON "cq_module" (persona_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_module_deleted_at" ON "cq_module" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_page" ("id" text not null, "page" text not null, "metadata" jsonb null, "order_id" integer not null, "created_by_id" text null, "updated_by_id" text null, "deleted_by_id" text null, "module_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_page_pkey" primary key ("id"));`);
    this.addSql(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_cq_page_page_unique" ON "cq_page" (page) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_page_created_by_id" ON "cq_page" (created_by_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_page_updated_by_id" ON "cq_page" (updated_by_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_page_deleted_by_id" ON "cq_page" (deleted_by_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_page_module_id" ON "cq_page" (module_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_page_deleted_at" ON "cq_page" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_role_page" ("id" text not null, "page_id" text not null, "role_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_role_page_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_role_page_page_id" ON "cq_role_page" (page_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_role_page_role_id" ON "cq_role_page" (role_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_role_page_deleted_at" ON "cq_role_page" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_role_page_permission" ("id" text not null, "role_page_id" text not null, "permission_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_role_page_permission_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_role_page_permission_role_page_id" ON "cq_role_page_permission" (role_page_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_role_page_permission_permission_id" ON "cq_role_page_permission" (permission_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_role_page_permission_deleted_at" ON "cq_role_page_permission" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_page_permission" ("id" text not null, "page_id" text not null, "permission_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_page_permission_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_page_permission_page_id" ON "cq_page_permission" (page_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_page_permission_permission_id" ON "cq_page_permission" (permission_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_page_permission_deleted_at" ON "cq_page_permission" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_activity_types" ("id" text not null, "type" text not null, "module_id" text not null, "description" text null, "is_sensitive" boolean not null default false, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_activity_types_pkey" primary key ("id"));`);
    this.addSql(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_cq_activity_types_type_unique" ON "cq_activity_types" (type) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_activity_types_module_id" ON "cq_activity_types" (module_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_activity_types_deleted_at" ON "cq_activity_types" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_user_activity_logs" ("id" text not null, "user_id" text not null, "activity_type_id" text not null, "status" text check ("status" in ('success', 'error')) not null default 'success', "error_code" text null, "error_message" text null, "ip_address" text null, "device_info" text null, "location" text null, "user_agent" text null, "metadata" jsonb null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_user_activity_logs_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_user_activity_logs_user_id" ON "cq_user_activity_logs" (user_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_user_activity_logs_activity_type_id" ON "cq_user_activity_logs" (activity_type_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_user_activity_logs_deleted_at" ON "cq_user_activity_logs" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_user_persona" ("id" text not null, "user_id" text not null, "persona_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_user_persona_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_user_persona_user_id" ON "cq_user_persona" (user_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_user_persona_persona_id" ON "cq_user_persona" (persona_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_user_persona_deleted_at" ON "cq_user_persona" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_user_role" ("id" text not null, "user_id" text not null, "role_id" text not null, "company_id" text not null, "assigned_by_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_user_role_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_user_role_user_id" ON "cq_user_role" (user_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_user_role_role_id" ON "cq_user_role" (role_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_user_role_assigned_by_id" ON "cq_user_role" (assigned_by_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_user_role_deleted_at" ON "cq_user_role" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_user_role_page" ("id" text not null, "user_id" text not null, "rolepage_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_user_role_page_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_user_role_page_user_id" ON "cq_user_role_page" (user_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_user_role_page_rolepage_id" ON "cq_user_role_page" (rolepage_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_user_role_page_deleted_at" ON "cq_user_role_page" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "cq_user_role_page_permission" ("id" text not null, "user_id" text null, "role_page_permission_id" text null, "page_id" text null, "permission_id" text null, "is_direct_permission" boolean not null default false, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cq_user_role_page_permission_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_user_role_page_permission_user_id" ON "cq_user_role_page_permission" (user_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_user_role_page_permission_role_page_permission_id" ON "cq_user_role_page_permission" (role_page_permission_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_user_role_page_permission_page_id" ON "cq_user_role_page_permission" (page_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_user_role_page_permission_permission_id" ON "cq_user_role_page_permission" (permission_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cq_user_role_page_permission_deleted_at" ON "cq_user_role_page_permission" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`alter table if exists "cq_user" add constraint "cq_user_cq_designation_id_foreign" foreign key ("cq_designation_id") references "cq_designation" ("id") on update cascade;`);
    this.addSql(`alter table if exists "cq_user" add constraint "cq_user_created_by_id_foreign" foreign key ("created_by_id") references "cq_user" ("id") on update cascade on delete set null;`);
    this.addSql(`alter table if exists "cq_user" add constraint "cq_user_updated_by_id_foreign" foreign key ("updated_by_id") references "cq_user" ("id") on update cascade on delete set null;`);
    this.addSql(`alter table if exists "cq_user" add constraint "cq_user_deleted_by_id_foreign" foreign key ("deleted_by_id") references "cq_user" ("id") on update cascade on delete set null;`);

    this.addSql(`alter table if exists "cq_store" add constraint "cq_store_created_by_id_foreign" foreign key ("created_by_id") references "cq_user" ("id") on update cascade on delete set null;`);
    this.addSql(`alter table if exists "cq_store" add constraint "cq_store_updated_by_id_foreign" foreign key ("updated_by_id") references "cq_user" ("id") on update cascade on delete set null;`);
    this.addSql(`alter table if exists "cq_store" add constraint "cq_store_deleted_by_id_foreign" foreign key ("deleted_by_id") references "cq_user" ("id") on update cascade on delete set null;`);

    this.addSql(`alter table if exists "cq_persona" add constraint "cq_persona_created_by_id_foreign" foreign key ("created_by_id") references "cq_user" ("id") on update cascade on delete set null;`);
    this.addSql(`alter table if exists "cq_persona" add constraint "cq_persona_updated_by_id_foreign" foreign key ("updated_by_id") references "cq_user" ("id") on update cascade on delete set null;`);
    this.addSql(`alter table if exists "cq_persona" add constraint "cq_persona_deleted_by_id_foreign" foreign key ("deleted_by_id") references "cq_user" ("id") on update cascade on delete set null;`);

    this.addSql(`alter table if exists "cq_role" add constraint "cq_role_created_by_id_foreign" foreign key ("created_by_id") references "cq_user" ("id") on update cascade on delete set null;`);
    this.addSql(`alter table if exists "cq_role" add constraint "cq_role_updated_by_id_foreign" foreign key ("updated_by_id") references "cq_user" ("id") on update cascade on delete set null;`);
    this.addSql(`alter table if exists "cq_role" add constraint "cq_role_deleted_by_id_foreign" foreign key ("deleted_by_id") references "cq_user" ("id") on update cascade on delete set null;`);
    this.addSql(`alter table if exists "cq_role" add constraint "cq_role_persona_id_foreign" foreign key ("persona_id") references "cq_persona" ("id") on update cascade;`);

    this.addSql(`alter table if exists "cq_permission" add constraint "cq_permission_created_by_id_foreign" foreign key ("created_by_id") references "cq_user" ("id") on update cascade on delete set null;`);
    this.addSql(`alter table if exists "cq_permission" add constraint "cq_permission_updated_by_id_foreign" foreign key ("updated_by_id") references "cq_user" ("id") on update cascade on delete set null;`);
    this.addSql(`alter table if exists "cq_permission" add constraint "cq_permission_deleted_by_id_foreign" foreign key ("deleted_by_id") references "cq_user" ("id") on update cascade on delete set null;`);

    this.addSql(`alter table if exists "cq_module" add constraint "cq_module_created_by_id_foreign" foreign key ("created_by_id") references "cq_user" ("id") on update cascade on delete set null;`);
    this.addSql(`alter table if exists "cq_module" add constraint "cq_module_updated_by_id_foreign" foreign key ("updated_by_id") references "cq_user" ("id") on update cascade on delete set null;`);
    this.addSql(`alter table if exists "cq_module" add constraint "cq_module_deleted_by_id_foreign" foreign key ("deleted_by_id") references "cq_user" ("id") on update cascade on delete set null;`);
    this.addSql(`alter table if exists "cq_module" add constraint "cq_module_persona_id_foreign" foreign key ("persona_id") references "cq_persona" ("id") on update cascade;`);

    this.addSql(`alter table if exists "cq_page" add constraint "cq_page_created_by_id_foreign" foreign key ("created_by_id") references "cq_user" ("id") on update cascade on delete set null;`);
    this.addSql(`alter table if exists "cq_page" add constraint "cq_page_updated_by_id_foreign" foreign key ("updated_by_id") references "cq_user" ("id") on update cascade on delete set null;`);
    this.addSql(`alter table if exists "cq_page" add constraint "cq_page_deleted_by_id_foreign" foreign key ("deleted_by_id") references "cq_user" ("id") on update cascade on delete set null;`);
    this.addSql(`alter table if exists "cq_page" add constraint "cq_page_module_id_foreign" foreign key ("module_id") references "cq_module" ("id") on update cascade;`);

    this.addSql(`alter table if exists "cq_activity_types" add constraint "cq_activity_types_module_id_foreign" foreign key ("module_id") references "cq_module" ("id") on update cascade;`);

    this.addSql(`alter table if exists "cq_user_activity_logs" add constraint "cq_user_activity_logs_user_id_foreign" foreign key ("user_id") references "cq_user" ("id") on update cascade;`);
    this.addSql(`alter table if exists "cq_user_activity_logs" add constraint "cq_user_activity_logs_activity_type_id_foreign" foreign key ("activity_type_id") references "cq_activity_types" ("id") on update cascade;`);

    this.addSql(`alter table if exists "cq_user_role" add constraint "cq_user_role_user_id_foreign" foreign key ("user_id") references "cq_user" ("id") on update cascade;`);
    this.addSql(`alter table if exists "cq_user_role" add constraint "cq_user_role_role_id_foreign" foreign key ("role_id") references "cq_role" ("id") on update cascade;`);
    this.addSql(`alter table if exists "cq_user_role" add constraint "cq_user_role_assigned_by_id_foreign" foreign key ("assigned_by_id") references "cq_user" ("id") on update cascade;`);

    this.addSql(`alter table if exists "cq_user_role_page" add constraint "cq_user_role_page_user_id_foreign" foreign key ("user_id") references "cq_page" ("id") on update cascade;`);

    this.addSql(`alter table if exists "cq_user_role_page_permission" add constraint "cq_user_role_page_permission_user_id_foreign" foreign key ("user_id") references "cq_user" ("id") on update cascade on delete set null;`);
    this.addSql(`alter table if exists "cq_user_role_page_permission" add constraint "cq_user_role_page_permission_role_page_permission_id_foreign" foreign key ("role_page_permission_id") references "cq_role_page_permission" ("id") on update cascade on delete set null;`);
    this.addSql(`alter table if exists "cq_user_role_page_permission" add constraint "cq_user_role_page_permission_page_id_foreign" foreign key ("page_id") references "cq_page" ("id") on update cascade on delete set null;`);
    this.addSql(`alter table if exists "cq_user_role_page_permission" add constraint "cq_user_role_page_permission_permission_id_foreign" foreign key ("permission_id") references "cq_permission" ("id") on update cascade on delete set null;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table if exists "cq_user" drop constraint if exists "cq_user_cq_designation_id_foreign";`);

    this.addSql(`alter table if exists "cq_user" drop constraint if exists "cq_user_created_by_id_foreign";`);

    this.addSql(`alter table if exists "cq_user" drop constraint if exists "cq_user_updated_by_id_foreign";`);

    this.addSql(`alter table if exists "cq_user" drop constraint if exists "cq_user_deleted_by_id_foreign";`);

    this.addSql(`alter table if exists "cq_store" drop constraint if exists "cq_store_created_by_id_foreign";`);

    this.addSql(`alter table if exists "cq_store" drop constraint if exists "cq_store_updated_by_id_foreign";`);

    this.addSql(`alter table if exists "cq_store" drop constraint if exists "cq_store_deleted_by_id_foreign";`);

    this.addSql(`alter table if exists "cq_persona" drop constraint if exists "cq_persona_created_by_id_foreign";`);

    this.addSql(`alter table if exists "cq_persona" drop constraint if exists "cq_persona_updated_by_id_foreign";`);

    this.addSql(`alter table if exists "cq_persona" drop constraint if exists "cq_persona_deleted_by_id_foreign";`);

    this.addSql(`alter table if exists "cq_role" drop constraint if exists "cq_role_created_by_id_foreign";`);

    this.addSql(`alter table if exists "cq_role" drop constraint if exists "cq_role_updated_by_id_foreign";`);

    this.addSql(`alter table if exists "cq_role" drop constraint if exists "cq_role_deleted_by_id_foreign";`);

    this.addSql(`alter table if exists "cq_permission" drop constraint if exists "cq_permission_created_by_id_foreign";`);

    this.addSql(`alter table if exists "cq_permission" drop constraint if exists "cq_permission_updated_by_id_foreign";`);

    this.addSql(`alter table if exists "cq_permission" drop constraint if exists "cq_permission_deleted_by_id_foreign";`);

    this.addSql(`alter table if exists "cq_module" drop constraint if exists "cq_module_created_by_id_foreign";`);

    this.addSql(`alter table if exists "cq_module" drop constraint if exists "cq_module_updated_by_id_foreign";`);

    this.addSql(`alter table if exists "cq_module" drop constraint if exists "cq_module_deleted_by_id_foreign";`);

    this.addSql(`alter table if exists "cq_page" drop constraint if exists "cq_page_created_by_id_foreign";`);

    this.addSql(`alter table if exists "cq_page" drop constraint if exists "cq_page_updated_by_id_foreign";`);

    this.addSql(`alter table if exists "cq_page" drop constraint if exists "cq_page_deleted_by_id_foreign";`);

    this.addSql(`alter table if exists "cq_user_activity_logs" drop constraint if exists "cq_user_activity_logs_user_id_foreign";`);

    this.addSql(`alter table if exists "cq_user_role" drop constraint if exists "cq_user_role_user_id_foreign";`);

    this.addSql(`alter table if exists "cq_user_role" drop constraint if exists "cq_user_role_assigned_by_id_foreign";`);

    this.addSql(`alter table if exists "cq_user_role_page_permission" drop constraint if exists "cq_user_role_page_permission_user_id_foreign";`);

    this.addSql(`alter table if exists "cq_role" drop constraint if exists "cq_role_persona_id_foreign";`);

    this.addSql(`alter table if exists "cq_module" drop constraint if exists "cq_module_persona_id_foreign";`);

    this.addSql(`alter table if exists "cq_user_role" drop constraint if exists "cq_user_role_role_id_foreign";`);

    this.addSql(`alter table if exists "cq_user_role_page_permission" drop constraint if exists "cq_user_role_page_permission_permission_id_foreign";`);

    this.addSql(`alter table if exists "cq_page" drop constraint if exists "cq_page_module_id_foreign";`);

    this.addSql(`alter table if exists "cq_activity_types" drop constraint if exists "cq_activity_types_module_id_foreign";`);

    this.addSql(`alter table if exists "cq_user_role_page" drop constraint if exists "cq_user_role_page_user_id_foreign";`);

    this.addSql(`alter table if exists "cq_user_role_page_permission" drop constraint if exists "cq_user_role_page_permission_page_id_foreign";`);

    this.addSql(`alter table if exists "cq_user_role_page_permission" drop constraint if exists "cq_user_role_page_permission_role_page_permission_id_foreign";`);

    this.addSql(`alter table if exists "cq_user_activity_logs" drop constraint if exists "cq_user_activity_logs_activity_type_id_foreign";`);

    this.addSql(`drop table if exists "cq_company_type" cascade;`);

    this.addSql(`drop table if exists "cq_country_extension" cascade;`);

    this.addSql(`drop table if exists "cq_designation" cascade;`);

    this.addSql(`drop table if exists "cq_email_otp_verification" cascade;`);

    this.addSql(`drop table if exists "cq_status" cascade;`);

    this.addSql(`drop table if exists "cq_user" cascade;`);

    this.addSql(`drop table if exists "cq_store" cascade;`);

    this.addSql(`drop table if exists "cq_persona" cascade;`);

    this.addSql(`drop table if exists "cq_role" cascade;`);

    this.addSql(`drop table if exists "cq_permission" cascade;`);

    this.addSql(`drop table if exists "cq_module" cascade;`);

    this.addSql(`drop table if exists "cq_page" cascade;`);

    this.addSql(`drop table if exists "cq_role_page" cascade;`);

    this.addSql(`drop table if exists "cq_role_page_permission" cascade;`);

    this.addSql(`drop table if exists "cq_page_permission" cascade;`);

    this.addSql(`drop table if exists "cq_activity_types" cascade;`);

    this.addSql(`drop table if exists "cq_user_activity_logs" cascade;`);

    this.addSql(`drop table if exists "cq_user_persona" cascade;`);

    this.addSql(`drop table if exists "cq_user_role" cascade;`);

    this.addSql(`drop table if exists "cq_user_role_page" cascade;`);

    this.addSql(`drop table if exists "cq_user_role_page_permission" cascade;`);
  }

}
