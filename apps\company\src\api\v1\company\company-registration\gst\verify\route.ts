import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { stringConstants } from '@org/utils';
import { logger } from '@medusajs/framework';
import axios, { HttpStatusCode } from 'axios';
import { ContainerRegist<PERSON><PERSON><PERSON><PERSON>, Modules } from '@medusajs/framework/utils';
import { COMPANY_REGISTRATION_MODULE } from '../../../../../../modules/company';
import CompanyService from '../../../../../../modules/company/service';

/**
 * @openapi
 * /v1/company/company-registration/gst/verify:
 *   get:
 *     summary: Verify GST Number
 *     tags:
 *       - Company Registration
 *     parameters:
 *       - in: query
 *         name: gstNo
 *         required: true
 *         schema:
 *           type: string
 *         description: The GST number to verify
 *     responses:
 *       200:
 *         description: GST details fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   properties:
 *                     taxpayerInfo:
 *                       type: object
 *                       properties:
 *                         stjCd:
 *                           type: string
 *                         lgnm:
 *                           type: string
 *                         stj:
 *                           type: string
 *                         dty:
 *                           type: string
 *                         adadr:
 *                           type: array
 *                           items:
 *                             type: string
 *                         cxdt:
 *                           type: string
 *                         gstin:
 *                           type: string
 *                         nba:
 *                           type: array
 *                           items:
 *                             type: string
 *                         rgdt:
 *                           type: string
 *                         ctb:
 *                           type: string
 *                         pradr:
 *                           type: object
 *                           properties:
 *                             addr:
 *                               type: object
 *                               properties:
 *                                 bnm:
 *                                   type: string
 *                                 loc:
 *                                   type: string
 *                                 st:
 *                                   type: string
 *                                 bno:
 *                                   type: string
 *                                 stcd:
 *                                   type: string
 *                                 dst:
 *                                   type: string
 *                                 city:
 *                                   type: string
 *                                 flno:
 *                                   type: string
 *                                 lt:
 *                                   type: string
 *                                 pncd:
 *                                   type: string
 *                                 lg:
 *                                   type: string
 *                             ntr:
 *                               type: string
 *                         tradeNam:
 *                           type: string
 *                         sts:
 *                           type: string
 *                         ctjCd:
 *                           type: string
 *                         ctj:
 *                           type: string
 *                         panNo:
 *                           type: string
 *                     compliance:
 *                       type: object
 *                       properties:
 *                         filingFrequency:
 *                           type: string
 *                           nullable: true
 *                     filing:
 *                       type: array
 *                       items:
 *                         type: object
 *                     error:
 *                       type: boolean
 *                     message:
 *                       type: string
 *                 message:
 *                   type: string
 *             example:
 *               data:
 *                 taxpayerInfo:
 *                   stjCd: "XY123"
 *                   lgnm: "JOHN DOE"
 *                   stj: "City Center - Ward 12"
 *                   dty: "Regular"
 *                   adadr: []
 *                   cxdt: ""
 *                   gstin: "12ABCDE3456F7Z8"
 *                   nba:
 *                     - "Office / Branch"
 *                   rgdt: "01/01/2020"
 *                   ctb: "Private Limited"
 *                   pradr:
 *                     addr:
 *                       bnm: "Building A"
 *                       loc: "Downtown"
 *                       st: "Main Street"
 *                       bno: "1234"
 *                       stcd: "StateX"
 *                       dst: "DistrictY"
 *                       city: "Metropolis"
 *                       flno: "5th Floor"
 *                       lt: ""
 *                       pncd: "560001"
 *                       lg: ""
 *                     ntr: "Office / Branch"
 *                   tradeNam: "Example Technologies"
 *                   sts: "Active"
 *                   ctjCd: "AB1234"
 *                   ctj: "RANGE-I"
 *                   panNo: "**********"
 *                 compliance:
 *                   filingFrequency: null
 *                 filing: []
 *                 error: false
 *                 message: "Use paid credits for GST verification in production. Free credits are only for sandbox testing. Buy credits at https://dashboard.example.com/buy-credits"
 *               message: "GST details fetched successfully"
 *       400:
 *         description: Bad request - GST number is missing
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: GST number is required
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error. Please try again later.
 *       503:
 *         description: GST verification service unavailable
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: GST verification service is unavailable
 */

/**
 * Handles the GET request for verifying GST details.
 * Sends a GET request to the Appyflow API to verify the GST number.
 *
 * @param {MedusaRequest} req - The Medusa request object containing the request details.
 * @param {MedusaResponse} res - The Medusa response object to send the response.
 * @returns {Promise<MedusaResponse>} A promise that resolves to a Medusa response object.
 */
export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<MedusaResponse> {
  const gstNo = req.validatedQuery.gstNo as string;
  const keySecret = process.env.APPYFLOW_KEY_SECRET;

  const cacheModuleService = req.scope.resolve(Modules.CACHE);
  const companyRegistrationService: CompanyService = req.scope.resolve(
    COMPANY_REGISTRATION_MODULE
  );
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

  if (!gstNo) {
    return res.status(400).json({
      message: 'GST number is required',
    });
  }

  const [companies] =
    await companyRegistrationService.listAndCountCqCompanyRegistrations(
      {
        gst_number: gstNo,
      },
      {
        select: ['gst_number', 'user_id', 'status_id'],
      }
    );

  const [onboardings] =
    await companyRegistrationService.listAndCountCqCompanyOnboardings(
      {
        gst_number: gstNo,
      },
      {
        select: ['gst_number', 'user_id', 'status_id'],
      }
    );

  const getUserInfo = async (userId: string) => {
    // const { data } = await query.graph({
    //   entity: 'cq_user_foreign',
    //   fields: ['first_name', 'last_name', 'email'],
    //   filters: { id: userId },
    // });
    const data = await companyRegistrationService.getUserByIDInForeignTable(userId);
  
    const user: any = data;
    
    const fullName = `${user?.first_name || ''} ${
      user?.last_name || ''
    }`.trim();
    const email = user?.email || '';
    return { fullName, email };
  };

  if (
    (companies && companies.length > 0) ||
    (onboardings && onboardings.length > 0)
  ) {
    if (companies && companies.length > 0) {
      const existingCompany: any = companies[0];
      const existingCompanyStatus: any =
        await companyRegistrationService.getStatusById(
          existingCompany.status_id
        );

      if (existingCompanyStatus.key === 'pending') {
        const { fullName, email } = await getUserInfo(existingCompany?.user_id);
        return res.status(HttpStatusCode.Conflict).json({
          success: false,
          // #UPDATE need to pass admin name and email in string constant
          message: stringConstants.PENDING_REGISTRATION_AND_ONBOARDING(
            fullName,
            email
          ),
          statusCode: HttpStatusCode.Conflict,
        });
      } else if (existingCompanyStatus.key === 'approved') {
        
        if (onboardings && onboardings.length > 0) {
          const existingOnboarding: any = onboardings[0];
          const existingOnboardingStatus: any =
            await companyRegistrationService.getStatusById(
              existingOnboarding.status_id
            );

          const { fullName, email } = await getUserInfo(
            existingOnboarding?.user_id
          );
          if (existingOnboardingStatus.key === 'pending') {
            return res.status(HttpStatusCode.Conflict).json({
              success: false,
              // #UPDATE need to pass admin name and email in string constant
              message: stringConstants.ALREADY_REGISTERED_PENDING_ONBOARD(
                fullName,
                email
              ),
              statusCode: HttpStatusCode.Conflict,
            });
          } else if (existingOnboardingStatus.key === 'approved') {
            return res.status(HttpStatusCode.Conflict).json({
              success: false,
              // #UPDATE need to pass admin name and email in string constant
              message: stringConstants.ALREADY_REGISTERED_AND_ONBOARDED(
                fullName,
                email
              ),
              statusCode: HttpStatusCode.Conflict,
            });
          }
        }
      }
    } else if (onboardings && onboardings.length > 0) {
      const existingOnboarding: any = onboardings[0];
         const existingOnboardingStatus: any =
            await companyRegistrationService.getStatusById(
              existingOnboarding.status_id
            );

      // const status=
      // await companyRegistrationService.getAllStatusFromForeignTable
      if (existingOnboardingStatus.key === 'approved') {
        const { fullName, email } = await getUserInfo(
          existingOnboarding.user_id
        );
        return res.status(HttpStatusCode.Conflict).json({
          success: false,
          // #UPDATE need to pass admin name and email in string constant
          message: stringConstants.OFFLINE_REGISTRATION_AND_ONBOARDING_SUCCESS(
            fullName,
            email
          ),
          statusCode: HttpStatusCode.Conflict,
        });
      }
    }
  }

  // if (!keySecret) {
  //   logger.error('APPYFLOW_KEY_SECRET is not set in the environment variables');
  //   return res.status(500).json({
  //     message: templateConstants.INTERNAL_SERVER_ERROR(),
  //   });
  // }

  try {
    const data: any = await cacheModuleService.get(gstNo);

    if (data && data.gst_response) {
      return res.status(200).json({
        data: data.gst_response,
        message: stringConstants.FETCHED_SUCCESSFULLY('GST details'),
      });
    }

    // const response = await axios.get('https://appyflow.in/api/verifyGST', {
    //   params: {
    //     gstNo,
    //     key_secret: keySecret,
    //   },
    //   timeout: 5000,
    // });

    const hardcodedResponse = {
      data: {
        taxpayerInfo: {
          stjCd: 'PB223',
          lgnm: 'DISHANT MAHAJAN',
          stj: 'Ludhiana 3 - Ward No.54',
          dty: 'Regular',
          adadr: [],
          cxdt: '',
          gstin: '03DOXPM4071K1ZE',
          nba: ['Office / Sale Office'],
          rgdt: '17/12/2019',
          ctb: 'Proprietorship',
          pradr: {
            addr: {
              bnm: '',
              loc: 'Ganesh Nagar',
              st: 'Street no 1',
              bno: '3018 Shop no 5',
              stcd: 'Punjab',
              dst: 'Ludhiana',
              city: '',
              flno: '',
              lt: '',
              pncd: '141008',
              lg: '',
            },
            ntr: 'Office / Sale Office',
          },
          tradeNam: 'AppyFlow Technologies',
          sts: 'Active',
          ctjCd: 'ZD0304',
          ctj: 'RANGE-IV',
          panNo: '**********',
        },
        compliance: {
          filingFrequency: null,
        },
        filing: [],
        error: false,
        message:
          'Kindly use paid credits for GST verification in production. Free credits are meant only for testing in sandbox. Link to buy credits: https://dashboard.gstapi.appyflow.in/#/app/buy-credits',
      },
      message: 'GST details fetched successfully',
    };

    // await cacheModuleService.set(gstNo, { gst_response: response.data });
    await cacheModuleService.set(gstNo, {
      gst_response: hardcodedResponse.data,
    });

    if (hardcodedResponse.data?.taxpayerInfo?.sts !== 'Active') {
      return res.status(HttpStatusCode.Conflict).json({
        success: false,
        message: 'As per Goods and Services Tax Network (GSTN), Invalid  GSTN',
        statusCode: HttpStatusCode.Conflict,
      });
    }

    return res.status(200).json({
      // data: response.data,
      data: hardcodedResponse.data,
      message: stringConstants.FETCHED_SUCCESSFULLY('GST details'),
    });
  } catch (error) {
    if (axios.isAxiosError(error)) {
      if (error.response) {
        return res.status(error.response.status).json({
          success: false,
          message:
            'As per Goods and Services Tax Network (GSTN), Invalid  GSTN',
        });
      } else if (error.request) {
        return res.status(503).json({
          message: 'GST verification service is unavailable',
        });
      }
    }
    return res.status(500).json({
      message: stringConstants.INTERNAL_SERVER_ERROR(),
    });
  }
}
