import { databaseConfig } from './database';
import { exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';
import { Client } from 'pg';

const execAsync = promisify(exec);

const REGIONS = ['india', 'mea', 'turkey'] as const;
const MASTER_REGION = 'master';

type MigrationType = 'shared-app' | 'company';

function getTargetDirectory(
  region: keyof typeof databaseConfig
): MigrationType {
  if (region === 'master') {
    return 'shared-app';
  }
  return 'company';
}

async function isMasterMigrated(): Promise<boolean> {
  const config = databaseConfig[MASTER_REGION];
  const client = new Client({ connectionString: config.url });
  try {
    await client.connect();
    const res = await client.query('SELECT COUNT(*) FROM script_migrations');
    return Number(res.rows[0].count) > 0;
  } catch (err) {
    return false;
  } finally {
    await client.end();
  }
}

async function doesDatabaseExist(
  region: keyof typeof databaseConfig
): Promise<boolean> {
  const config = databaseConfig[region];
  if (!config || !config.url) return false;
  const client = new Client({ connectionString: config.url });
  try {
    await client.connect();
    return true;
  } catch (err) {
    return false;
  } finally {
    await client.end().catch(() => {});
  }
}

export async function runMigration(region: keyof typeof databaseConfig) {
  console.log('Running migration for region:', region);
  const config = databaseConfig[region];
  if (!config) {
    console.error(`Invalid region: ${region}`);
    console.error('Available regions:', Object.keys(databaseConfig).join(', '));
    process.exit(1);
  }
  try {
    const migrationType = getTargetDirectory(region);
    const projectRoot = path.resolve(__dirname, '../../../..');
    const targetPath =
      migrationType === 'shared-app'
        ? path.join(projectRoot, 'shared-app/user')
        : path.join(projectRoot, 'apps/company');
    process.chdir(targetPath);
    const command = `cross-env DATABASE_URL="${config.url}" DATABASE_TYPE=${config.type} medusa db:migrate`;
    const { stdout, stderr } = await execAsync(command);
    if (stdout) console.log(stdout);
    if (stderr) console.error(stderr);
    console.log(`Successfully completed migration for ${region}`);
    process.chdir(projectRoot);
  } catch (error) {
    console.error(`Error running migration for ${region}:`, error);
    process.exit(1);
  }
}

async function main() {
  const arg = process.argv[2] || 'all';
  if (!arg) {
    console.error(
      'Please provide a region name (master, india, mea, turkey, all, regions)'
    );
    process.exit(1);
  }
  if (arg === MASTER_REGION) {
    await runMigration(MASTER_REGION);
    return;
  }
  if (arg === 'all') {
    await runMigration(MASTER_REGION);
    const masterMigrated = await isMasterMigrated();
    if (!masterMigrated) {
      console.error(
        'Master migration not completed. Aborting regional migrations.'
      );
      process.exit(1);
    }
    for (const region of REGIONS) {
      const exists = await doesDatabaseExist(region);
      if (exists) {
        await runMigration(region);
      } else {
        console.log(
          `Skipping migration for ${region}: database does not exist or is not accessible.`
        );
      }
    }
    return;
  }
  if (arg.toLowerCase() === 'regions') {
    const masterMigrated = await isMasterMigrated();
    if (!masterMigrated) {
      console.error(
        'Master migration not completed. Aborting regional migrations.'
      );
      process.exit(1);
    }
    for (const region of REGIONS) {
      const exists = await doesDatabaseExist(region);
      if (exists) {
        await runMigration(region);
      } else {
        console.log(
          `Skipping migration for ${region}: database does not exist or is not accessible.`
        );
      }
    }
    return;
  }
  // If a single region is passed
  if (REGIONS.includes(arg as any)) {
    const masterMigrated = await isMasterMigrated();
    if (!masterMigrated) {
      console.log(
        'Master migration not found. Running master migration first...'
      );
      process.exit(1);
      // await runMigration(MASTER_REGION);
    }
    const exists = await doesDatabaseExist(arg as keyof typeof databaseConfig);
    if (exists) {
      await runMigration(arg as keyof typeof databaseConfig);
    } else {
      console.log(
        `Skipping migration for ${arg}: database does not exist or is not accessible.`
      );
    }
    return;
  }
  console.error(
    'Invalid region argument. Use one of: master, india, mea, turkey, all, regions'
  );
  process.exit(1);
}

if (require.main === module) {
  main().catch((err) => {
    console.error('Migration failed:', err);
    process.exit(1);
  });
}
