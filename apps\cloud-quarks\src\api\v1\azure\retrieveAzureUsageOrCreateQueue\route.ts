import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import {
  UnBilledAzureDataDownloadSchema,
  validateUnBilledAzureDataDownloadStore
} from "../../../../validators/azure/unBilledAzureDataDownloadValidator";
import { AWSCredentials } from "../../../../types/responses/customResponse";
import { findAWSCredentialsByResource, retrieveAzureUsageDataOrCreateQueueService } from "../../../../services/index";

// TypeScript interface
interface UnBilledAzureDataDownloadDto {
  storeId: string;
  subscriptionId: string;
  customerId: string;
  period: string;
}

/**
 * @openapi
 * /v1/azure/retrieveAzureUsageOrCreateQueue:
 *   post:
 *     summary: Retrieve Azure usage data or create queue for processing
 *     tags:
 *       - Azure
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - storeId
 *               - subscriptionId
 *               - customerId
 *               - period
 *             properties:
 *               storeId:
 *                 type: string
 *                 example: "store-123"
 *                 description: Store ID
 *               subscriptionId:
 *                 type: string
 *                 example: "sub-456"
 *                 description: Azure subscription ID
 *               customerId:
 *                 type: string
 *                 example: "cust-789"
 *                 description: Customer ID
 *               period:
 *                 type: string
 *                 example: "2024-01"
 *                 description: Period for usage data
 *           example:
 *             storeId: "string"
 *             subscriptionId: "string"
 *             customerId: "string"
 *             period: "string"
 *     responses:
 *       200:
 *         description: Azure usage data retrieved or queue created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Success"
 *                 data:
 *                   type: object
 *                   description: Usage data or queue information
 *                 statusCode:
 *                   type: number
 *                   example: 200
 *                 isError:
 *                   type: boolean
 *                   example: false
 *                 type:
 *                   type: string
 *                   description: Response type indicator
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 statusCode:
 *                   type: number
 *                   example: 400
 *                 isError:
 *                   type: boolean
 *                   example: true
 *       422:
 *         description: Unprocessable entity
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 statusCode:
 *                   type: number
 *                   example: 422
 *                 isError:
 *                   type: boolean
 *                   example: true
 */

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req);

  try {
    const unBilledAzureDataDownloadDto: UnBilledAzureDataDownloadDto = req.body as UnBilledAzureDataDownloadDto;

    logger.info(
      `Entered into RetrieveAzureUsageDataOrCreateQueue API with unBilledAzureDataDownloadDto | CorrelationId: ${correlationId} | DTO: ${JSON.stringify(unBilledAzureDataDownloadDto)}`
    );

    // Validate DTO structure
    const parseResult = UnBilledAzureDataDownloadSchema.safeParse(unBilledAzureDataDownloadDto);
    logger.info(`Validations error count on UnBilledAzureDataDownloadDto object is ${parseResult.success ? 0 : parseResult.error.errors.length}`);

    if (!parseResult.success) {
      const validationErrors = parseResult.error.errors.map((err: any) => err.message);
      const errorMessage = validationErrors.join(', ');

      const apiResponse = {
        isError: true,
        message: errorMessage,
        statusCode: 400,
      };

      logger.info(`Going to send bad request response | CorrelationId: ${correlationId} | Response: ${JSON.stringify(apiResponse)}`);
      return res.status(400).json(apiResponse);
    }

    const validatedDto = parseResult.data;

    // Additional validation for store
    const storeErrors = validateUnBilledAzureDataDownloadStore(validatedDto.storeId);
    if (storeErrors.length > 0) {
      const errorMessage = storeErrors.join(', ');

      const apiResponse = {
        isError: true,
        message: errorMessage,
        statusCode: 400,
      };

      logger.info(`Going to send bad request response | CorrelationId: ${correlationId} | Response: ${JSON.stringify(apiResponse)}`);
      return res.status(400).json(apiResponse);
    }


    // Get AWS credentials from initialized credentials list (matching .NET: StaticConfig.AWSCredentialList.Find(x => x.RESOURCE == "S3"))
    const awsCredentials: AWSCredentials | null = findAWSCredentialsByResource("S3") || null;

    // Map DTO to request model
    const requestModel = {
      StoreId: validatedDto.storeId,
      SubscriptionId: validatedDto.subscriptionId,
      CustomerId: validatedDto.customerId,
      Period: validatedDto.period,
      AWSCredentials: awsCredentials ? {
        Id: awsCredentials.Id,
        AWS_ACCESS_KEY_ID: awsCredentials.AWS_ACCESS_KEY_ID,
        AWS_SECRET_ACCESS_KEY: awsCredentials.AWS_SECRET_ACCESS_KEY,
        AWS_REGION: awsCredentials.AWS_REGION,
        RESOURCE: awsCredentials.RESOURCE,
        AWS_BUCKETNAME: awsCredentials.AWS_BUCKETNAME,
        APPLICATION: awsCredentials.APPLICATION,
        COMMENTS: awsCredentials.COMMENTS,
        ...(awsCredentials.ACTIVE !== undefined && { ACTIVE: awsCredentials.ACTIVE }),
      } : undefined,
    };

    logger.info(`Mapped dto is: ${JSON.stringify(requestModel)}`);

    // Call Azure service
    const apiResponse = await retrieveAzureUsageDataOrCreateQueueService(req, requestModel);

    logger.info(
      `Returning response from RetrieveAzureUsageDataOrCreateQueue API | CorrelationId: ${correlationId} | ApiResponse: ${JSON.stringify(apiResponse)}`
    );

    return res.status(apiResponse.statusCode).json(apiResponse);
  } catch (exp) {
    const errorMessage = exp instanceof Error ? exp.message : "Unknown error occurred";

    const apiResponse = {
      isError: true,
      message: `Exception: ${errorMessage}`,
      statusCode: 422, // UnprocessableEntity
    };

    logger.error(
      `Error in RetrieveAzureUsageDataOrCreateQueue API | CorrelationId: ${correlationId} | Message: ${errorMessage} | StackTrace: ${exp instanceof Error ? exp.stack : 'No stack trace'}`
    );

    logger.error(
      `Returning response from RetrieveAzureUsageDataOrCreateQueue API | CorrelationId: ${correlationId} | ApiResponse: ${JSON.stringify(apiResponse)}`
    );

    return res.status(422).json(apiResponse);
  }
}