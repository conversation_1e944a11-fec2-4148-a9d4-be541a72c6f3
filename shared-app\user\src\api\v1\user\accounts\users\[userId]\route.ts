import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { CQUser_MODULE } from '../../../../../../modules/cq-user';
import CQUserService from '../../../../../../modules/cq-user/service';
import { ContainerRegistrationKeys } from '@medusajs/framework/utils';
import { stringConstants } from '@org/utils';

/**
 * @openapi
 * /v1/user/accounts/users/{userId}:
 *   get:
 *     summary: Get a single user by ID
 *     tags:
 *       - Accounts
 *     parameters:
 *       - in: path
 *         name: userId
 *         schema:
 *           type: string
 *         required: true
 *         description: The unique identifier of the user
 *     responses:
 *       200:
 *         description: User fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     first_name:
 *                       type: string
 *                     last_name:
 *                       type: string
 *                     email:
 *                       type: string
 *                     mobile_number:
 *                       type: string
 *                     company_name:
 *                       type: string
 *                     terms_and_conditions:
 *                       type: boolean
 *                     profile_pic_file_name:
 *                       type: string
 *                     cq_designation:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: string
 *                         name:
 *                           type: string
 *                         key:
 *                           type: string
 *                         metadata:
 *                           type: object
 *                           nullable: true
 *                     country:
 *                       type: object
 *                       properties:
 *                         iso_2:
 *                           type: string
 *                         iso_3:
 *                           type: string
 *                         num_code:
 *                           type: string
 *                         name:
 *                           type: string
 *                         display_name:
 *                           type: string
 *                         region_id:
 *                           type: string
 *                         region:
 *                           type: object
 *                           properties:
 *                             id:
 *                               type: string
 *                         metadata:
 *                           type: object
 *                           nullable: true
 *             example:
 *               data:
 *                 id: "005"
 *                 first_name: "Indhu"
 *                 last_name: "I"
 *                 email: "<EMAIL>"
 *                 mobile_number: "9874865221"
 *                 company_name: "yuv company"
 *                 terms_and_conditions: true
 *                 profile_pic_file_name: "profile459.jpg"
 *                 cq_designation:
 *                   id: "01JZ2RGD8VQXZ7N2C9KJQ0VZHZ"
 *                   name: "CEO"
 *                   key: "ceo"
 *                   metadata: {}
 *                 country:
 *                   iso_2: "in"
 *                   iso_3: "ind"
 *                   num_code: "356"
 *                   name: "INDIA"
 *                   display_name: "India"
 *                   region_id: "reg_01JZ2RG968V8N98E6FRTEW0AAT"
 *                   region:
 *                     id: "reg_01JZ2RG968V8N98E6FRTEW0AAT"
 *                   metadata: null
 *       404:
 *         description: User not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: User not found
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
	try {
		const CQUserService: CQUserService = req.scope.resolve(CQUser_MODULE);
		const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

		const { userId } = req.params;

		const { data } = await query.graph({
			entity: 'cq_user',
			fields: [
				'id',
				'first_name',
				'last_name',
				'email',
				'mobile_number',
				'company_name',
				'terms_and_conditions',
				'profile_pic_file_name',
				'cq_designation.*',
				'country.*',
			],
			filters: {
				id: userId,
			},
		});

		if (data.length === 0) {
			return res.status(404).json({
				message: stringConstants.NOT_FOUND('User'),
			});
		}

		return res.status(200).json({
			data: data[0],
		});
	} catch (err) {
		return res
			.status(500)
			.json({ message: stringConstants.INTERNAL_SERVRER_ERROR() });
	}
}
