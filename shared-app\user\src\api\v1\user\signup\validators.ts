import { z } from 'zod';

export const GuestUserSchema = z.object({
  source: z.string().optional(),
  id: z.string().optional(),
  first_name: z
    .string()
    .max(50)
    .regex(
      /^[A-Za-z0-9\s'-]+$/,
      'First name can only contain letters, numbers, spaces, hyphens, and apostrophes'
    ),
  last_name: z
    .string()
    .max(50)
    .regex(
      /^[A-Za-z0-9\s'-]+$/,
      'Last name can only contain letters, numbers, spaces, hyphens, and apostrophes'
    ),
  designation: z
    .string()
    .max(100)
    .regex(
      /^[A-Za-z0-9\s\-/]+$/,
      'Designation can only contain letters, numbers, spaces, hyphens, and slashes'
    ),
  mobile_number: z
    .string()
    .max(15)
    .regex(
      /^[+]?[0-9]{1,4}[-\s]?[(]?[0-9]{1,4}[)]?[-\s]?[0-9]+[-\s]?[0-9]+$/,
      'Mobile number must be in a valid international format (e.g., +919876543210)'
    ),
  email: z.string().email().max(100),
  company_name: z
    .string()
    .max(100)
    .regex(
      /^[A-Za-z0-9\s\-&]+$/,
      'Company name can only contain letters, numbers, spaces, hyphens, and ampersands'
    ),
  country_code: z.string(),
  has_sales_reference: z.boolean(),
  terms_and_conditions: z.boolean(),
  redington_sales_reference: z
    .string()
    .max(100)
    .regex(
      /^[A-Za-z0-9\s-]+$/,
      'Redington reference can only contain letters, numbers, spaces, and hyphens'
    )
    .optional(),
  profile_pic_file_name: z.string().optional(),
  registered_country_id: z.string(),
  is_active: z.boolean().optional(),
  metadata: z.object({}).optional(),
  region_id: z.string().optional(),
});

export const baseResendOtpSchema = z.object({
  email: z.string().email().max(100).optional(),
  mobile_number: z
    .string()
    .max(15)
    .regex(
      /^[+]?[0-9]{1,4}[-\s]?[(]?[0-9]{1,4}[)]?[-\s]?[0-9]+[-\s]?[0-9]+$/,
      'Mobile number must be in a valid international format (e.g., +919876543210)'
    )
    .optional(),
});

export const forgotPasswordSchema = z.object({
  email: z.string().trim().toLowerCase().email().max(100),
});

export const UpdateGuestUserSchema = z.object({
  id: z.string(),
  first_name: z
    .string()
    .max(50)
    .regex(
      /^[A-Za-z0-9\s'-]+$/,
      'First name can only contain letters, numbers, spaces, hyphens, and apostrophes'
    ),
  last_name: z
    .string()
    .max(50)
    .regex(
      /^[A-Za-z0-9\s'-]+$/,
      'Last name can only contain letters, numbers, spaces, hyphens, and apostrophes'
    ),
  designation: z
    .string()
    .max(100)
    .regex(
      /^[A-Za-z0-9\s\-/]+$/,
      'Designation can only contain letters, numbers, spaces, hyphens, and slashes'
    ),
  mobile_number: z
    .string()
    .max(15)
    .regex(
      /^[+]?[0-9]{1,4}[-\s]?[(]?[0-9]{1,4}[)]?[-\s]?[0-9]+[-\s]?[0-9]+$/,
      'Mobile number must be in a valid international format (e.g., +919876543210)'
    ),
  email: z.string().email().max(100),
  company_name: z
    .string()
    .max(100)
    .regex(
      /^[A-Za-z0-9\s\-&]+$/,
      'Company name can only contain letters, numbers, spaces, hyphens, and ampersands'
    ),
  country_code: z.string(),
  region_id: z.string(),
});

export const GSTSchema = z.object({
  gstNo: z
    .string()
    .regex(
      /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/,
      'Invalid GST number format'
    )
    .refine((val) => val.length === 15, {
      message: 'GST number must be exactly 15 characters long',
    }),
});
