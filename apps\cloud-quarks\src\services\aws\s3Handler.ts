import AWS from 'aws-sdk';
import logger from "../../utils/logger";
import { AWSCredentials } from "../../validators/azure/unBilledAzureDataDownloadValidator";

/**
 * Get pre-signed URL for S3 object
 * Matches .NET S3Handler.GetPreSignedUrl method
 */
export async function getPreSignedUrl(
  awsCred: AWSCredentials,
  keyName: string,
  preSignedUrlRetentionHours: number
): Promise<string> {
  logger.info("Entered into GetPreSignedUrl method.");
  
  try {
    // Configure S3 client with AWS credentials
    const s3Client = new AWS.S3({
      accessKeyId: awsCred.AWS_ACCESS_KEY_ID,
      secretAccessKey: awsCred.AWS_SECRET_ACCESS_KEY,
      region: awsCred.AWS_REGION,
    });

    // Check if object exists first (matching .NET GetObjectMetadataAsync)
    try {
      await s3Client.headObject({
        Bucket: awsCred.AWS_BUCKETNAME,
        Key: keyName,
      }).promise();
    } catch (headError: any) {
      if (headError.statusCode === 404) {
        logger.error(`S3 object not found: ${keyName} in bucket: ${awsCred.AWS_BUCKETNAME}`);
        return "";
      }
      throw headError;
    }

    // Generate pre-signed URL
    const params = {
      Bucket: awsCred.AWS_BUCKETNAME,
      Key: keyName,
      Expires: preSignedUrlRetentionHours * 3600, // Convert hours to seconds
    };

    const preSignedUrl = await s3Client.getSignedUrlPromise('getObject', params);
    
    logger.info(`Generated pre-signed URL successfully for key: ${keyName}`);
    return preSignedUrl;
  } catch (error: any) {
    logger.error(`Error occurred in GetPreSignedUrl method. Error: ${error.message}, stackTrace: ${error.stack}`);
    
    // Handle specific S3 errors
    if (error.statusCode === 404) {
      return "";
    }
    
    throw error;
  }
}

/**
 * Check if S3 object exists
 * Helper method to verify object existence
 */
export async function checkS3ObjectExists(
  awsCred: AWSCredentials,
  keyName: string
): Promise<boolean> {
  try {
    const s3Client = new AWS.S3({
      accessKeyId: awsCred.AWS_ACCESS_KEY_ID,
      secretAccessKey: awsCred.AWS_SECRET_ACCESS_KEY,
      region: awsCred.AWS_REGION,
    });

    await s3Client.headObject({
      Bucket: awsCred.AWS_BUCKETNAME,
      Key: keyName,
    }).promise();

    return true;
  } catch (error: any) {
    if (error.statusCode === 404) {
      return false;
    }
    throw error;
  }
}
