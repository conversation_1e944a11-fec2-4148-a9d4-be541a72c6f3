import { CognitoIdentityServiceProvider } from 'aws-sdk';
import { createHmac } from 'crypto';
 
export class AWSAuthProviderService {
  static identifier = 'aws-auth';
  static DISPLAY_NAME = 'AWS Auth';
  private cognito: CognitoIdentityServiceProvider;
  private options_: any;
  logger_: any;
 
  constructor() {
    this.cognito = new CognitoIdentityServiceProvider(
      {
        region : process.env.S3_REGION
      }
    );
    this.options_ = {
      clientId: process.env.COGNITO_CLIENT_ID,
      clientSecret: process.env.COGNITO_CLIENT_SECRET,
    };
    this.logger_ = console;
  }
 
  computeSecretHash(username: string) {
    const message = username + this.options_.clientId;
    const key = this.options_.clientSecret;
    return createHmac('sha256', key).update(message).digest('base64');
  }
 
  async register(data: any) {
    const { username, password, email } = data.body;
    console.log('username : ', username);
    console.log('password : ', password);
    console.log('email : ', email);
 
 
   
    const secretHash = this.computeSecretHash(username);
 
    const params = {
      ClientId: this.options_.clientId,
      Username: username,
      Password: password,
      UserAttributes: [
        {
          Name: 'email',
          Value: email,
        },
      ],
      SecretHash: secretHash,
    };
 
    try {
      await this.cognito.signUp(params).promise();
      return {
        success: true,
        message: 'User registered successfully',
      };
    } catch (error) {
      this.logger_.error(error);
      return {
        success: false,
        error: 'Registration failed',
      };
    }
  }
 
  async verify(data: any) {
    const { username, code } = data.body;
    const secretHash = this.computeSecretHash(username);
 
    const params = {
      ClientId: this.options_.clientId,
      Username: username,
      ConfirmationCode: code,
      SecretHash: secretHash,
    };
 
    try {
      await this.cognito.confirmSignUp(params).promise();
      return {
        success: true,
        message: 'OTP verified successfully',
      };
    } catch (error) {
      this.logger_.error(error);
      return {
        success: false,
        error: 'OTP verification failed',
      };
    }
  }
 
  async authenticate(data: { body: { username: any; password: any; }; }) {
    const { username, password } = data.body;
    const secretHash = this.computeSecretHash(username);
 
    const params = {
      AuthFlow: 'USER_PASSWORD_AUTH',
      ClientId: this.options_.clientId,
      AuthParameters: {
        USERNAME: username,
        PASSWORD: password,
        SECRET_HASH: secretHash,
      },
    };
 
    try {
      const response = await this.cognito.initiateAuth(params).promise();
      return {
        success: true,
        tokens: response.AuthenticationResult,
      };
    } catch (error) {
      this.logger_.error(error);
      return {
        success: false,
        error: 'Authentication failed',
      };
    }
  }
}
 
export function cognito(): string {
  return 'cognito';
}
