import { model } from '@medusajs/framework/utils';
import { Vendor } from './vendor';
import { UserBrandPreferences } from './user-brand-preferences';
import { Brand } from './brand';

export const BrandCategory = model.define('cq_brand_category', {
  id: model.id().primaryKey(),
  category_name: model.text(),
  brand: model.belongsTo(() => Brand, { mappedBy: 'brand' }),
  // vendor_id: model.belongsTo(() => Vendor, { mappedBy: 'vendor' }),
  user_brand_preferences: model.hasMany(() => UserBrandPreferences, {
    mappedBy: 'brand_category',
  }),
  metadata: model.json(),
});
