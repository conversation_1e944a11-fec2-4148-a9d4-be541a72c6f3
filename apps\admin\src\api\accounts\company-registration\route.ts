// src/api/custom-upload/route.ts
import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { MedusaError } from '@medusajs/framework/utils';
import { ContainerRegistrationKeys, Modules } from '@medusajs/framework/utils';
import { stringConstants } from '@org/utils';

export const PARTNER_REGISTRATION_MODULE = 'partner_registration';

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

    let page = parseInt(req.query.page as string, 10);
    let pageSize = parseInt(req.query.pageSize as string, 10);

    // Set default values for page and pageSize
    if (isNaN(page) || page < 1) page = 1;
    if (isNaN(pageSize) || pageSize < 1) pageSize = 10;

    const skip = (page - 1) * pageSize;

    // Query for data
    // const { data: registrations, metadata } = await query.graph({
    //   entity: PARTNER_REGISTRATION_MODULE,
    //   fields: ['*'],
    //   pagination: {
    //     skip,
    //     take: pageSize,
    //   },
    // });

    const { data: guestUsers, metadata } = await query.graph({
      entity: 'app_user',
      fields: [
        '*',
        'partner_registrations_by_app_user.*',
        'partner_registrations_by_requested_by.*',
      ],
      pagination: {
        skip,
        take: pageSize,
      },
    });

    const totalItems = metadata?.count ?? 0;
    const totalPages = Math.ceil(totalItems / pageSize);

    return res.status(200).json({
      data: guestUsers,
      pagination: {
        page,
        pageSize,
        totalItems,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    });
  } catch (err: any) {
    console.error(err);

    return res.status(500).json({
      message: stringConstants.INTERNAL_SERVER_ERROR(),
    });
  }
}
