import { masterDB } from "../../../utils/db"
import sql from "mssql"
import logger from "../../../utils/logger"
import { getSqlServerConnection } from "../../../utils/sqlServerClient"

export async function updateCustomerUsingSP(
  action: string,
  customerModel: any
): Promise<{ Status: string; Message: string }> {
  logger.info(
    `Entered into updateCustomerRawUsingSP with model: ${JSON.stringify(
      customerModel
    )}`
  );

  const {
    ID,
    SAPCustId,
    SAPCompany,
    StoreId,
    BrandId,
    PartnerId,
    MPNID,
    DomainId,
    NewTenant,
    AssociatedPartnerId,
  } = customerModel;

  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();

    request.input("ID", ID);
    request.input("SAPCustId", SAPCustId ?? null);
    request.input("SAPCompany", SAPCompany ?? null);
    request.input("StoreId", StoreId ?? null);
    request.input("BrandId", BrandId ?? null);
    request.input("PartnerId", PartnerId ?? null);
    request.input("MPNID", MPNID ?? null);
    request.input("DomainId", DomainId ?? null);
    request.input("NewTenant", NewTenant ?? null);
    request.input("AssociatedPartnerId", AssociatedPartnerId ?? null);

    // OUTPUT params
    request.output("oRetVal", sql.VarChar(200));
    request.output("Message", sql.VarChar(200));

    logger.info(
      `Calling stored procedure Usp_Ups_CustomerLinking with StoreId=${StoreId}, BrandId=${BrandId}, PartnerId=${PartnerId}`
    );

    const result = await request.execute("Usp_Ups_CustomerLinking");

    console.log(result);

    const Status = result.output?.RetVal || "FAIL";
    const Message = result.output?.Message || "Unknown error";

    logger.info(`SP Result: Status=${Status}, Message=${Message}`);

    return {
      Status,
      Message,
    };
  } catch (error: any) {
    logger.error(`SP Execution Failed: ${error.message}`);
    return {
      Status: "FAIL",
      Message: error.message,
    };
  }
}



// export async function updateCustomerRaw(action:string, customerModel: any): Promise<{ Status: string; Message: string }> {
//   logger.info(`Entered into updateCustomerRaw with model: ${JSON.stringify(customerModel)}`)

//   const {
//     ID,
//     SAPCustId,
//     SAPCompany,
//     StoreId,
//     BrandId,
//     PartnerId,
//     MPNID,
//     DomainId,
//     NewTenant,
//     AssociatedPartnerId,
//   } = customerModel

//   // Get StoreKey
//   const storeRes = await masterDB.query(
//     `SELECT "StoreKey" FROM "tblMstStore" WHERE "StoreId" = $1`,
//     [StoreId]
//   )
//   const StoreKey = storeRes.rows?.[0]?.StoreKey

//   if (!StoreKey) {
//     return { Status: "FAIL", Message: "Invalid Store" }
//   }

//   // Get BrandKey and BrandGroup
//   const brandRes = await masterDB.query(
//     `SELECT "BrandKey", "BrandGroup" FROM "tblMstBrand" WHERE "BrandId" = $1`,
//     [BrandId]
//   )
//   const BrandKey = brandRes.rows?.[0]?.BrandKey

//   if (!BrandKey) {
//     return { Status: "FAIL", Message: "Invalid Brand Id" }
//   }

//   // Get PartnerKey via function or raw query
//   const partnerRes = await masterDB.query(
//     `SELECT "PartnerKey" FROM "tblMstPartner" WHERE "PartnerId" = $1`,
//     [PartnerId]
//   )
//   const PartnerKey = partnerRes.rows?.[0]?.PartnerKey

//   if (!PartnerKey) {
//     return { Status: "FAIL", Message: "Invalid Partner Id" }
//   }

//   // Get CustKey
//   const custRes = await masterDB.query(
//     `SELECT "CustKey" FROM "tblMstEndCustomer" WHERE "CustId" = $1`,
//     [ID]
//   )
//   const CustKey = custRes.rows?.[0]?.CustKey

//   if (!CustKey) {
//     return { Status: "FAIL", Message: "Invalid Customer Id" }
//   }

//   // Validate MPNID and DomainId (based on AWS rule)
//   const isAwsBrandRes = await masterDB.query(
//     `SELECT "BrandKey" FROM "tblMstBrand" WHERE UPPER("BrandId") = 'AWS'`
//   )
//   const AwsBrandKey = isAwsBrandRes.rows?.[0]?.BrandKey

//   const isAWS_AEEN =
//     BrandKey === AwsBrandKey && StoreId.toUpperCase() === "AE-EN"

//   if (!MPNID || MPNID.trim() === "" || (!DomainId && !isAWS_AEEN)) {
//     return {
//       Status: "FAIL",
//       Message: "Please enter Customer Tenant Id AND Domain Id",
//     }
//   }

//   // Check if already linked
//   const linkRes = await masterDB.query(
//     `SELECT 1 FROM "tblLnkEndCustomerPartnerBrand" 
//      WHERE "CustKey" = $1 AND "PartnerKey" = $2 AND "BrandKey" = $3`,
//     [CustKey, PartnerKey, BrandKey]
//   )
//   const isAlreadyLinked = linkRes.rows.length > 0

//   if (!isAlreadyLinked) {
//     // INSERT
//     await masterDB.query(
//       `INSERT INTO "tblLnkEndCustomerPartnerBrand" (
//          "CustKey", "PartnerKey", "BrandKey",
//          "Attribute1", "Attribute2", "Attribute3", "Attribute4",
//          "Status", "CreatedDate", "NewTenant", "AssociatedPartnerId"
//        )
//        SELECT DISTINCT 
//          $1, $2, $3,
//          $4, $5, $6, $7,
//          '1', NOW(), $8, $9
//        FROM "tblLnkBrandstore"
//        WHERE "StoreKey" = $10 AND "BrandKey" = $3 AND COALESCE("CredentialType", '') = ''
//          AND NOT EXISTS (
//            SELECT 1 FROM "tblLnkEndCustomerPartnerBrand"
//            WHERE "CustKey" = $1 AND "PartnerKey" = $2 AND "BrandKey" = $3
//          )`,
//       [
//         CustKey,
//         PartnerKey,
//         BrandKey,
//         `Domain:${DomainId}`,
//         `CustomerId:${MPNID}`,
//         SAPCustId,
//         SAPCompany,
//         NewTenant,
//         AssociatedPartnerId,
//         StoreKey,
//       ]
//     )
//   } else {
//     // UPDATE
//     await masterDB.query(
//       `UPDATE "tblLnkEndCustomerPartnerBrand"
//        SET "Attribute1" = $1,
//            "Attribute2" = $2,
//            "Attribute3" = $3,
//            "Attribute4" = $4,
//            "Status" = '1',
//            "NewTenant" = $5,
//            "AssociatedPartnerId" = $6,
//            "CreatedDate" = NOW()
//        WHERE "CustKey" = $7 AND "PartnerKey" = $8 AND "BrandKey" = $9
//          AND (Attribute1 IS NULL OR Attribute1 = '')`,
//       [
//         `Domain:${DomainId}`,
//         `CustomerId:${MPNID}`,
//         SAPCustId,
//         SAPCompany,
//         NewTenant,
//         AssociatedPartnerId,
//         CustKey,
//         PartnerKey,
//         BrandKey,
//       ]
//     )
//   }

//   return {
//     Status: "PASS",
//     Message: "Customer mapped successfully",
//   }
// }
