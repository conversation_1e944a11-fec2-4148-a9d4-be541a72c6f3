import fs from 'fs'
import path from 'path'

// Moved log path OUTSIDE cloud-quarks folder to prevent dev restarts
const logDir = path.resolve(process.cwd(), '../../../logs/vendor');

export function logToFile(filename: string, data: any) {
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }

  const filePath = path.join(logDir, `${filename}.json`);

  fs.writeFileSync(filePath, JSON.stringify(data, null, 2), {
    encoding: 'utf-8',
  });
}
