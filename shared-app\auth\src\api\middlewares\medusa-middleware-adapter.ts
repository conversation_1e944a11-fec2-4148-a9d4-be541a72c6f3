import type { RequestH<PERSON><PERSON> } from 'express';
import {
  MedusaRequest,
  MedusaResponse,
  MedusaNextFunction,
} from '@medusajs/framework/http';

/**
 * Adapts an Express-style middleware to work with Medusa's custom request/response types.
 * This adapter is specifically designed to make rate limiting middleware compatible with Medusa's HTTP types.
 *
 * @param middleware - The Express-style middleware (like express-rate-limit) to be adapted
 * @returns A middleware function compatible with Medusa's request/response types
 *
 * Why this adapter is needed:
 * - Express middlewares expect standard Express Request/Response types
 * - Medusa uses custom types (MedusaRequest/MedusaResponse)
 * - This adapter bridges the type gap between Express and Medusa
 *
 * Example usage:
 * const rateLimiter = rateLimit({ max: 100, windowMs: 15 * 60 * 1000 });
 * const medusaCompatibleLimiter = adaptRateLimiter(rateLimiter);
 */
export const adaptMiddleware = (middleware: RequestHandler) => {
  return (
    req: MedusaRequest,
    res: MedusaResponse,
    next: MedusaNextFunction
  ) => {
    return middleware(req as any, res as any, next as any);
  };
};
