import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from '@medusajs/framework';
import { masterDB, indiaDB } from '../../utils/db';
import { ContainerRegistrationKeys, Modules } from '@medusajs/framework/utils';
import { formatPagination, stringConstants } from '@org/utils';

// Helper function for application-level join
async function getPartnerRegistrationsWithUsers_AppLevel(
  req: AuthenticatedMedusaRequest
) {
  try {
    // Get pagination parameters
    let page = parseInt(req.query.page as string, 10) || 1;
    let pageSize = parseInt(req.query.pageSize as string, 10) || 10;
    const skip = (page - 1) * pageSize;

    // Get search and filter parameters
    const search = req.query.search as string;
    const status = req.query.status as string;
    const store = req.query.store as string;
    const requestedBy = req.query.requestedBy as string;
    const companyType = req.query.companyType as string;

    // 1. First get all matching users if there's a search
    let matchingUserIds: string[] = [];
    if (search) {
      const searchQuery = `
        SELECT id FROM "user" 
        WHERE deleted_at IS NULL 
        AND (
          first_name ILIKE $1 OR 
          last_name ILIKE $1 OR 
          email ILIKE $1
        )
      `;
      const userResult = await masterDB.query(searchQuery, [`%${search}%`]);
      matchingUserIds = userResult.rows.map((row) => row.id);

      // If searching and no users match, we can return early
      if (matchingUserIds.length === 0) {
        return {
          data: [],
          pagination: {
            page,
            pageSize,
            totalItems: 0,
            totalPages: 0,
            hasNextPage: false,
            hasPrevPage: false,
          },
        };
      }
    }

    // 2. Build partner registration query
    let query = 'SELECT * FROM partner_registration WHERE deleted_at IS NULL';
    const params: any[] = [];
    let paramIndex = 1;

    // Add search filter if applicable
    if (search) {
      query += ` AND user_id = ANY($${paramIndex})`;
      params.push(matchingUserIds);
      paramIndex++;
    }

    // Add other filters
    if (status) {
      query += ` AND status = $${paramIndex}`;
      params.push(status);
      paramIndex++;
    }

    if (store) {
      query += ` AND store = $${paramIndex}`;
      params.push(store);
      paramIndex++;
    }

    if (companyType) {
      query += ` AND company_info->>'company_type' = $${paramIndex}`;
      params.push(companyType);
      paramIndex++;
    }

    // Get total count before pagination
    const countResult = await indiaDB.query(
      `SELECT COUNT(*) FROM (${query}) AS subquery`,
      params
    );
    const totalItems = parseInt(countResult.rows[0].count);
    const totalPages = Math.ceil(totalItems / pageSize);

    // Add sorting and pagination
    query += ` ORDER BY created_at DESC LIMIT $${paramIndex} OFFSET $${
      paramIndex + 1
    }`;
    params.push(pageSize, skip);

    // Get filtered partner registrations
    const partnerResult = await indiaDB.query(query, params);
    const partners = partnerResult.rows;

    // 3. Get all needed user IDs
    const userIds = [
      ...new Set(partners.map((p) => p.user_id).filter(Boolean)),
    ];
    const requestedByIds = [
      ...new Set(partners.map((p) => p.requested_by).filter(Boolean)),
    ];
    const allNeededUserIds = [...new Set([...userIds, ...requestedByIds])];

    // 4. Fetch all needed users in one query
    let users: any[] = [];
    if (allNeededUserIds.length > 0) {
      const userQuery = `
        SELECT 
          id, 
          email, 
          first_name, 
          last_name
        FROM "user" 
        WHERE deleted_at IS NULL 
        AND id = ANY($1)
      `;
      const userResult = await masterDB.query(userQuery, [allNeededUserIds]);
      users = userResult.rows;
    }

    // 5. Create a map for efficient user lookups
    const userMap = new Map(users.map((user) => [user.id, user]));

    // 6. Combine the data with efficient lookups
    const enrichedPartners = partners.map((partner) => {
      const partnerUser = userMap.get(partner.user_id);
      const requestedByUser = userMap.get(partner.requested_by);

      return {
        ...partner,
        user: partnerUser
          ? {
              id: partnerUser.id,
              email: partnerUser.email,
              first_name: partnerUser.first_name,
              last_name: partnerUser.last_name,
            }
          : null,
        requested_by_user: requestedByUser
          ? {
              id: requestedByUser.id,
              email: requestedByUser.email,
              first_name: requestedByUser.first_name,
              last_name: requestedByUser.last_name,
            }
          : null,
      };
    });
    return {
      data: enrichedPartners,
      pagination: {
        page,
        pageSize,
        totalItems,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    };
  } catch (error) {
    console.error('Error in application-level join:', error);
    throw error;
  }
}