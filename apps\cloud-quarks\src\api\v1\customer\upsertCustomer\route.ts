import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import { upsertCustomerService } from "../../../../services/index";
import logger from "../../../../utils/logger";
import { upsertCustomerSchema } from "../../../../validators/customer/upsertCustomerValidator";
import { ComparableString, validateUpsertCustomer } from "../../../../validators/customer/customCustomerValidator";
import { MicrosoftCreateCustomerRequestMapper } from "../../../../utils/mappers/microsoftCreateCustomerRequest";
import { CustomerMasterModelMapper } from "../../../../utils/mappers/customerMasterModel";

/**
 * @openapi
 * /v1/customer/upsertCustomer:
 *   post:
 *     summary: Upsert (create or update) a customer
 *     description: This API creates or updates a customer based on the input type (`CREATE` or `UPDATE`).
 *     tags:
 *       - Customer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - type
 *               - custId
 *               - orgName
 *               - countryId
 *               - addressLine1
 *               - addressLine2
 *               - emailId
 *               - firstName
 *               - lastName
 *               - storeId
 *               - partnerId
 *               - userName
 *             properties:
 *               type:
 *                 type: string
 *                 enum: [CREATE, UPDATE]
 *               custId:
 *                 type: string
 *               orgName:
 *                 type: string
 *               countryId:
 *                 type: string
 *               addressLine1:
 *                 type: string
 *               addressLine2:
 *                 type: string
 *               emailId:
 *                 type: string
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               storeId:
 *                 type: string
 *               partnerId:
 *                 type: string
 *               userName:
 *                 type: string
 *               domainId:
 *                 type: string
 *               city:
 *                 type: string
 *               state:
 *                 type: string
 *               postalCode:
 *                 type: string
 *               brandId:
 *                 type: string
 *               id:
 *                 type: string
 *               middleName:
 *                 type: string
 *               vatId:
 *                 type: string
 *               phoneNumber:
 *                 type: string
 *               customerVertical:
 *                 type: string
 *               segment:
 *                 type: string
 *               sector:
 *                 type: string
 *               panNumber:
 *                 type: string
 *               newTenant:
 *                 type: boolean
 *               associatedPartnerId:
 *                 type: string
 *     responses:
 *       201:
 *         description: Customer created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *       200:
 *         description: Customer updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *       400:
 *         description: Validation error
 *       422:
 *         description: Business logic failure
 *       500:
 *         description: Internal server error
 */

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const customerDto = upsertCustomerSchema.parse(req.body);

    logger.info(`Entered into UpsertCustomer API with details ${JSON.stringify(customerDto)}`);

    logger.info("Going to validate the customer dto object");
    const validationErrors = validateUpsertCustomer(customerDto);
    logger.info(`Validations error count on customer dto object is ${validationErrors.length}`);

    if (validationErrors.length > 0) {
      logger.info("Going to extract errors from validationResult object");
      logger.info(`Validation errors are ${JSON.stringify(validationErrors)}`);
      return res.status(400).json(validationErrors);
    }

    logger.info(`Going to map customer dto to model. CustomerDto: ${JSON.stringify(customerDto)}`);
    const customerMaterModel = CustomerMasterModelMapper(customerDto);
    logger.info(`Customer model after mapping: ${JSON.stringify(customerMaterModel)}`);

    const microsoftCreateCustomerRequest = MicrosoftCreateCustomerRequestMapper(customerDto);
    logger.info(`Microsoft request model after mapping: ${JSON.stringify(microsoftCreateCustomerRequest)}`);

    logger.info(`Going to hit upsertCustomerService method with Type: ${customerDto.type}, customer model: ${JSON.stringify(customerMaterModel)}, Microsoft req body: ${JSON.stringify(microsoftCreateCustomerRequest)}`);
    const result = await upsertCustomerService(customerMaterModel, microsoftCreateCustomerRequest, customerDto.type);

    logger.info(`Result from BL full process is ${JSON.stringify(result)}`);
    logger.info(`Result's comparable string is ${ComparableString(String(result.status))}`);

    const normalizedStatus = ComparableString(String(result.status));
    const normalizedType = ComparableString(customerDto.type);

    if (normalizedStatus === "PASS") {
      logger.info(`Result from BL after all processes are PASS. Result's status comparable string is ${normalizedStatus}`);

      if (normalizedType === "CREATE") {
        logger.info(`Request is of type ${normalizedType}`);
        logger.info(`Going to return created status with result: ${JSON.stringify(result)}`);
        return res.status(201).json(result);
      }

      if (normalizedType === "UPDATE") {
        logger.info(`Customer's type comparable string is ${normalizedType}`);
        return res.status(200).json(result);
      }
    } else if (normalizedStatus === "FAIL") {
      logger.info(`Request is of type ${normalizedType}`);
      logger.info(`Going to return UnprocessableEntity status from controller with result: ${JSON.stringify(result)}`);
      return res.status(422).json(result);
    }

    logger.info("Going to return OK status from controller");
    return res.status(200).json({ message: "Customer upserted successfully" });

  } catch (err) {
    logger.error("upsertCustomerService error: ", err as Error);
    return res.status(500).json({
      status: false,
      message: "Internal server error",
    });
  }
}
