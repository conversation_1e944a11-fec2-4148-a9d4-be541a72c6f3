export function mapToCustomerBillingInfoModel(dto: any) {
  return {
    Id: dto.Id,
    Email: dto.Email,
    Culture: dto.Culture,
    Language: dto.Language,
    CompanyName: dto.CompanyName,
    DefaultAddress: {
      Country: dto.DefaultAddress?.Country,
      City: dto.DefaultAddress?.City,
      AddressLine1: dto.DefaultAddress?.AddressLine1,
      FirstName: dto.DefaultAddress?.FirstName,
      LastName: dto.DefaultAddress?.LastName,
    },
    Links: dto.Links ?? null,
    Attributes: {
      Etag: dto.Attributes?.Etag,
    },
    FirstName: dto.FirstName,
    LastName: dto.LastName,
    Code: dto.Code,
    Description: dto.Description,
  };
}
