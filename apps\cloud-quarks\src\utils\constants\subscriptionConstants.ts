// Subscription configuration constants (matching .NET AppSettings)
export const SubscriptionConfig = {
  // Retry configuration for subscription registration (matching .NET RetryRegisterSubscription)
  DEFAULT_RETRY_REGISTER_SUBSCRIPTION: 4,
  // Retry configuration for patch subscription (matching .NET RetryPatchSubscriptionCount)
  DEFAULT_RETRY_PATCH_SUBSCRIPTION_COUNT: 3,
  // Retry timespan for patch subscription (matching .NET RetryPatchSubscriptionTimeSpan)
  DEFAULT_RETRY_PATCH_SUBSCRIPTION_TIMESPAN_MS: 10000, // 00:00:10 = 10 seconds
  // HTTP client timeout (matching .NET httpClientTimeout)
  DEFAULT_HTTP_CLIENT_TIMEOUT_SECONDS: 120,
} as const;

export function getRetryRegisterSubscriptionCount(configModule?: any): number {
  // Try to get from config module first, then environment, then default
  const configValue = configModule?.projectConfig?.appSettings?.retryRegisterSubscription;
  const envValue = process.env.RETRY_REGISTER_SUBSCRIPTION;

  if (configValue) return parseInt(configValue, 10);
  if (envValue) return parseInt(envValue, 10);
  return SubscriptionConfig.DEFAULT_RETRY_REGISTER_SUBSCRIPTION;
}

export function getHttpClientTimeout(configModule?: any): number {
  const configValue = configModule?.projectConfig?.appSettings?.httpClientTimeout;
  const envValue = process.env.HTTP_CLIENT_TIMEOUT;

  if (configValue) return parseInt(configValue, 10);
  if (envValue) return parseInt(envValue, 10);
  return SubscriptionConfig.DEFAULT_HTTP_CLIENT_TIMEOUT_SECONDS;
}
