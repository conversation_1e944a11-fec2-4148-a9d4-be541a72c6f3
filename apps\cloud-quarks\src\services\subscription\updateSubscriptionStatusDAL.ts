import logger from "../../utils/logger";
import sql from "mssql";
import { getSqlServerConnection } from "../../utils/sqlServerClient";

/**
 * Update Subscription Status DAL
 * Matches .NET UpdateSubscriptionStatus method
 */
export async function updateSubscriptionStatusDAL(
  subscriptionId: string
): Promise<void> {
  logger.info(`Entered into UpdateSubscriptionStatus Service method with subscriptionId ${subscriptionId}`);

  try {
    const pool = await getSqlServerConnection();
    const request = pool.request();

    // Add input parameter (matching .NET: param[0] = new SqlParameter("@iSubscriptionId", subscriptionId))
    request.input("iSubscriptionId", sql.VarChar(255), subscriptionId);

    const procedure = "UpdateAzurePlanSubscriptionStatus"; // Matches .NET Procedures.UpdateAzurePlanSubscriptionStatus

    // Log trace request (matching .NET logging)
    logger.info(`DB Request intiated for subscriptionId:${subscriptionId} to SP : ${procedure}`);

    // Execute stored procedure (matching .NET: cmd.ExecuteNonQuery())
    await request.execute(procedure);

    // Log trace completion (matching .NET logging)
    logger.info(`DB Request completed.`);

  } catch (error: any) {
    logger.error(`Error in updateSubscriptionStatusDAL: ${error.message}`);
    throw error;
  }
}
