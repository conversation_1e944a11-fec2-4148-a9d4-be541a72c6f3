import logger from "../../utils/logger";
import { GoogleCustomerDetailsModel } from "../../types/responses/customResponse";
import { getCustomerDetailsDAL } from "./googleCustomerDetailsDAL";

/**
 * Get Google customer details
 * Matches .NET GoogleCustomerService.GetCustomerDetails method
 */
export async function getGoogleCustomerDetailsService(custId: string): Promise<GoogleCustomerDetailsModel> {
  logger.info(`Entered into GetCustomerDetails service method with custId: ${custId}`);
  
  try {
    // Call DAL method (matching .NET: _googleCustomerDAL.GetCustomerDetails(custId))
    const customerDetails = await getCustomerDetailsDAL(custId);
    
    logger.info(`Successfully retrieved customer details for custId: ${custId}`);
    return customerDetails;
    
  } catch (error: any) {
    logger.error(`Error in getGoogleCustomerDetailsService: ${error.message}`);
    throw error;
  }
}
