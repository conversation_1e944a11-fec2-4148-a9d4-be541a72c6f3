import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import CQUserService from '../../../../../modules/cq-user/service';
import { CQUser_MODULE } from '../../../../../modules/cq-user';
import { stringConstants } from '@org/utils';

/**
 * @openapi
 * /v1/user/signup/mobile-resend:
 *   post:
 *     summary: Resend OTP to mobile number
 *     tags:
 *       - Signup - Mobile
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - mobile_number
 *             properties:
 *               mobile_number:
 *                 type: string
 *                 example: "+11234567890"
 *     responses:
 *       200:
 *         description: OTP resent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: OTP has been resent successfully.
 *       400:
 *         description: Mobile number not provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Mobile number is required
 *       404:
 *         description: No unverified OTP request found for the mobile number
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Mobile OTP request not found
 *       429:
 *         description: Resend request rate limited
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Please wait 8 seconds before requesting a new OTP.
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error
 */

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const emailOtpService: CQUserService = req.scope.resolve(CQUser_MODULE);
  const { mobile_number } = req.body as { mobile_number: string };

  try {
    if (!mobile_number) {
      return res.status(400).json({ message: 'Mobile number is required' });
    }

    const entries = await emailOtpService.listEmailOtpVerifications({
      mobile_number,
      is_verified: false,
    });

    if (!entries || entries.length === 0) {
      return res
        .status(404)
        .json({ message: stringConstants.NOT_FOUND('Mobile OTP request') });
    }

    const latest = entries[entries.length - 1];

    const now = new Date();
    const lastSentAt = new Date(latest.expires_at.getTime() - 60 * 1000); // Subtract 1 minute from expires_at

    const secondsSinceLastOtp = (now.getTime() - lastSentAt.getTime()) / 1000;
    if (secondsSinceLastOtp < 10) {
      return res.status(429).json({
        message: `Please wait ${Math.ceil(
          10 - secondsSinceLastOtp
        )} seconds before requesting a new OTP.`,
      });
    }

    const newOtp = '123456'; // or generate dynamically
    const newExpiry = new Date(Date.now() + 60 * 1000);

    await emailOtpService.updateEmailOtpVerifications({
      id: latest.id,
      otp_code: newOtp,
      expires_at: newExpiry,
    });

    return res.status(200).json({ message: stringConstants.OTP_RESEND() });
  } catch (err: any) {
    return res
      .status(500)
      .json({ message: stringConstants.INTERNAL_SERVER_ERROR() });
  }
}
