import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import logger from "../../../../utils/logger";
import { getCorrelationId } from "../../../../config/microsoftEndpoints";
import {
  TerminateOrder,
  TerminateLineItems,
  SubscriptionStatusResponse,
  ErrorResponseModel
} from "../../../../types/responses/customResponse";
import {
  validateTerminateOrderRequest,
  GetSubscriptionRequestType
} from "../../../../validators/subscription/terminateOrderValidator";
import { getMsToken } from "../../../../services/token/microsoft/getTokenService";
import { getOrdersDetailFromVendorByIdService } from "../../../../services/subscription/getOrdersDetailFromVendorByIdService";
import { terminateSubscriptionService } from "../../../../services/subscription/terminateSubscriptionService";

/**
 * POST /v1/subscription/terminateOrder
 * Terminate Order API - matches .NET TerminateOrder controller method
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const correlationId = getCorrelationId(req);

  logger.info(`[${correlationId}] Entered into TerminateOrder API with GetSubscriptionRequest-${JSON.stringify(req.body)}`);

  let resp: SubscriptionStatusResponse | null = null;

  try {
    // Validate the request body
    const validationErrors = validateTerminateOrderRequest(req.body);

    if (validationErrors.length > 0) {
      logger.error(`[${correlationId}] Validation failed: ${JSON.stringify(validationErrors)}`);

      const errorResponse: ErrorResponseModel = {
        errors: validationErrors,
        statusCode: 400,
      };

      return res.status(400).json(errorResponse);
    }

    const request: GetSubscriptionRequestType = req.body as GetSubscriptionRequestType;

    logger.info(`[${correlationId}] Request validation passed, proceeding with token generation`);

    // Get Access token from Microsoft (matching .NET token generation logic)
    const token = await getMsToken({
      brand: request.Brand || "",
      client_id: request.ClientId || "",
      client_secret: request.Clientsecret || "",
      grant_type: request.GrantType || "",
      markValue: "0", // Default value as in .NET
      redirect_uri: "http://localhost", // Matches .NET default
      refresh_token: request.Token || "",
      resource: request.Resource || "",
      store_domain: request.StoreDomain || "",
    });

    if (!token || !token.access_token) {
      logger.error(`[${correlationId}] Token generation failed`);

      resp = {
        Status: "Active",
        ErrorMessage: "Failed to generate access token"
      };

      return res.status(500).json(resp);
    }

    logger.info(`[${correlationId}] Token generated successfully, proceeding with order termination`);

    // Create TerminateOrder object (matching .NET TerminateOrder creation)
    const terminate: TerminateOrder = {
      Id: request.OrderId,
      Status: "cancelled",
      LineItems: []
    };

    // Create TerminateLineItems (matching .NET TerminateLineItems creation)
    const item: TerminateLineItems = {
      OfferId: request.CatalogId
    };

    terminate.LineItems = [item];

    logger.info(`[${correlationId}] Created terminate order object: ${JSON.stringify(terminate)}`);

    // Check subscription status before calling TerminationSubscription (matching .NET logic)
    // This will fix case when we try to terminate expired subscription (CQ-1912)
    logger.info(`[${correlationId}] Checking subscription status before termination`);

    const resp_subscriptionStatus = await getOrdersDetailFromVendorByIdService(
      req,
      request.CustomerId || "",
      request.OrderId || "",
      token.access_token
    );

    logger.info(`[${correlationId}] GET| GetOrdersDetailFromVendorByID success |Response:-${JSON.stringify(resp_subscriptionStatus)}`);

    // Check if subscription is already cancelled or deleted (matching .NET logic)
    if (resp_subscriptionStatus.Status === "cancelled" || resp_subscriptionStatus.Status === "deleted") {
      resp = { Status: "cancelled" };
      logger.info(`[${correlationId}] TerminateSubscription - PatchSubscription cancelled as subscription is already cancelled |Response:-${JSON.stringify(resp)}`);
    } else {
      // Call business logic to terminate subscription (matching .NET logic)
      resp = await terminateSubscriptionService(
        req,
        terminate,
        request.CustomerId || "",
        token.access_token
      );

      logger.info(`[${correlationId}] POST| TerminateSubscription - PatchSubscription success |Response:-${JSON.stringify(resp)}`);
    }

    return res.status(200).json(resp);

  } catch (error: any) {
    // Log the exception (matching .NET exception handling)
    logger.error(`[${correlationId}] Error in TerminateSubscription - PatchSubscription, errorMessage: ${error.message}, StackTrace: ${error.stack}`);

    resp = {
      Status: "Active",
      ErrorMessage: error.message
    };

    return res.status(500).json(resp);
  }
}